package com.xylink.sqltranspiler.regression.bugfix;

import com.xylink.sqltranspiler.core.validation.StrictSqlValidator;
import com.xylink.sqltranspiler.core.validation.StrictValidationResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

/**
 * LIMIT/OFFSET误报问题回归测试
 * Bug描述：
 * 用户报告ALTER TABLE语句中没有LIMIT和OFFSET，但出现了相关配置错误告警
 * 根本原因：
 * 原始正则表达式 validation.performance.large_result_sets=LIMIT\\s+[0-9]{4,},OFFSET\\s+[0-9]{4,}
 * 会匹配任何地方出现的LIMIT和OFFSET关键字，包括：
 * - 注释中的文本
 * - 字符串字面量中的内容
 * - 表名或列名中包含这些关键字的情况
 * 修复方案：
 * 1. 改进正则表达式，使用词边界\\b确保只匹配完整的关键字
 * 2. 新增removeCommentsAndStrings()方法，在验证前移除注释和字符串字面量
 * 3. 实现动态阈值配置，基于MySQL 8.4官方文档
 * 4. 添加官方文档链接和详细的错误信息
 * 参考MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/select.html
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
public class LimitOffsetMisreportingRegressionTest {

    private StrictSqlValidator validator;

    @BeforeEach
    void setUp() {
        validator = new StrictSqlValidator();
    }

    @Test
    @DisplayName("回归测试：用户报告的ALTER TABLE误报问题")
    void testUserReportedAlterTableMisreporting() {
        // 用户提供的原始SQL，之前会产生误报
        String userSql = """
            -- 20250627_0016.sql
            alter table ainemo.libra_special_feature_user
                add data_type int default 0 not null;
            
            alter table ainemo.libra_special_feature_user
                add dept_name varchar(255) default '' not null;
            alter table ainemo.libra_special_feature_user
                add update_time datetime default now() null on update now();
            """;

        StrictValidationResult result = validator.validate(userSql, "mysql", "dameng");

        // 验证修复后不再产生LIMIT/OFFSET相关误报
        boolean hasLimitOffsetWarning = result.getWarnings().stream()
            .anyMatch(warning -> {
                String msg = warning.getMessage().toLowerCase();
                return msg.contains("limit") || msg.contains("offset") || msg.contains("大结果集");
            });

        assertFalse(hasLimitOffsetWarning, 
            "修复后，ALTER TABLE语句不应该再产生LIMIT/OFFSET相关的误报告警");
    }

    @Test
    @DisplayName("回归测试：确保真正的大LIMIT值仍然被检测")
    void testRealLargeLimitStillDetected() {
        // 确保修复没有影响真正的大LIMIT值检测
        String sql = "SELECT * FROM users LIMIT 5000;";

        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");

        boolean hasLimitWarning = result.getWarnings().stream()
            .anyMatch(warning -> warning.getMessage().contains("大结果集") && 
                     warning.getMessage().contains("LIMIT"));

        assertTrue(hasLimitWarning, 
            "修复后，真正的大LIMIT值仍然应该被检测并产生告警");
    }

    @Test
    @DisplayName("回归测试：确保真正的大OFFSET值仍然被检测")
    void testRealLargeOffsetStillDetected() {
        // 确保修复没有影响真正的大OFFSET值检测
        String sql = "SELECT * FROM users LIMIT 10 OFFSET 2000;";

        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");

        boolean hasOffsetWarning = result.getWarnings().stream()
            .anyMatch(warning -> warning.getMessage().contains("大偏移量") && 
                     warning.getMessage().contains("OFFSET"));

        assertTrue(hasOffsetWarning, 
            "修复后，真正的大OFFSET值仍然应该被检测并产生告警");
    }

    @Test
    @DisplayName("回归测试：注释中的LIMIT/OFFSET关键字不再误报")
    void testCommentsNoLongerCauseMisreporting() {
        // 测试各种注释格式中的LIMIT/OFFSET关键字
        String sql = """
            -- This query should use LIMIT 10000 for better performance
            /* 
             * OFFSET 5000 might be too large
             * LIMIT should be considered
             */
            # Another comment with LIMIT 9999
            SELECT id FROM users WHERE active = 1;
            """;

        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");

        boolean hasLimitOffsetWarning = result.getWarnings().stream()
            .anyMatch(warning -> {
                String msg = warning.getMessage().toLowerCase();
                return msg.contains("limit") || msg.contains("offset") || msg.contains("大结果集");
            });

        assertFalse(hasLimitOffsetWarning, 
            "修复后，注释中的LIMIT/OFFSET关键字不应该再产生误报");
    }

    @Test
    @DisplayName("回归测试：字符串字面量中的LIMIT/OFFSET关键字不再误报")
    void testStringLiteralsNoLongerCauseMisreporting() {
        // 测试字符串字面量中的LIMIT/OFFSET关键字
        String sql = """
            INSERT INTO query_log (query_text) VALUES ('SELECT * FROM table LIMIT 10000');
            UPDATE settings SET description = "Use OFFSET 5000 for pagination" WHERE key = 'pagination';
            SELECT * FROM logs WHERE message LIKE '%LIMIT%' OR message LIKE '%OFFSET%';
            """;

        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");

        boolean hasLimitOffsetWarning = result.getWarnings().stream()
            .anyMatch(warning -> {
                String msg = warning.getMessage().toLowerCase();
                return msg.contains("limit") || msg.contains("offset") || msg.contains("大结果集");
            });

        assertFalse(hasLimitOffsetWarning, 
            "修复后，字符串字面量中的LIMIT/OFFSET关键字不应该再产生误报");
    }

    @Test
    @DisplayName("回归测试：表名和列名中包含LIMIT/OFFSET不再误报")
    void testTableAndColumnNamesNoLongerCauseMisreporting() {
        // 测试表名和列名中包含LIMIT/OFFSET的情况
        String sql = """
            CREATE TABLE user_limits (
                id INT PRIMARY KEY,
                daily_limit INT,
                offset_hours INT
            );
            
            ALTER TABLE user_limits ADD COLUMN time_limit_seconds INT;
            
            INSERT INTO user_limits (daily_limit, offset_hours) VALUES (1000, 8);
            """;

        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");

        boolean hasLimitOffsetWarning = result.getWarnings().stream()
            .anyMatch(warning -> {
                String msg = warning.getMessage().toLowerCase();
                return msg.contains("limit") || msg.contains("offset") || msg.contains("大结果集");
            });

        assertFalse(hasLimitOffsetWarning, 
            "修复后，表名和列名中包含LIMIT/OFFSET不应该再产生误报");
    }

    @Test
    @DisplayName("回归测试：动态阈值配置正常工作")
    void testDynamicThresholdConfigurationWorks() {
        // 测试边界值情况
        String sqlBelowThreshold = "SELECT * FROM users LIMIT 999;";  // 低于默认阈值1000
        String sqlAtThreshold = "SELECT * FROM users LIMIT 1000;";    // 等于默认阈值1000
        String sqlAboveThreshold = "SELECT * FROM users LIMIT 1001;"; // 高于默认阈值1000

        StrictValidationResult resultBelow = validator.validate(sqlBelowThreshold, "mysql", "dameng");
        StrictValidationResult resultAt = validator.validate(sqlAtThreshold, "mysql", "dameng");
        StrictValidationResult resultAbove = validator.validate(sqlAboveThreshold, "mysql", "dameng");

        // 低于阈值不应该有告警
        boolean hasWarningBelow = resultBelow.getWarnings().stream()
            .anyMatch(warning -> warning.getMessage().contains("大结果集"));
        assertFalse(hasWarningBelow, "低于阈值的LIMIT值不应该产生告警");

        // 等于或高于阈值应该有告警
        boolean hasWarningAt = resultAt.getWarnings().stream()
            .anyMatch(warning -> warning.getMessage().contains("大结果集"));
        assertTrue(hasWarningAt, "等于阈值的LIMIT值应该产生告警");

        boolean hasWarningAbove = resultAbove.getWarnings().stream()
            .anyMatch(warning -> warning.getMessage().contains("大结果集"));
        assertTrue(hasWarningAbove, "高于阈值的LIMIT值应该产生告警");
    }

    @Test
    @DisplayName("回归测试：官方文档链接包含在告警信息中")
    void testOfficialDocumentationLinksIncluded() {
        // 测试告警信息是否包含官方文档链接
        String sql = "SELECT * FROM users LIMIT 5000;";

        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");

        boolean hasDocumentationLink = result.getWarnings().stream()
            .anyMatch(warning -> warning.getMessage().contains("https://dev.mysql.com/doc/refman/8.4/en/select.html"));

        assertTrue(hasDocumentationLink, 
            "修复后，告警信息应该包含MySQL 8.4官方文档链接");
    }
}
