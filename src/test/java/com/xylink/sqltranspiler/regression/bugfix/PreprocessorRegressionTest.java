package com.xylink.sqltranspiler.regression.bugfix;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.infrastructure.parser.PreprocessingResult;
import com.xylink.sqltranspiler.infrastructure.parser.Preprocessor;

/**
 * 预处理器回归测试
 * 记录并验证预处理过程中遇到的各种问题和修复
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
@DisplayName("预处理器回归测试")
public class PreprocessorRegressionTest {

    @BeforeEach
    void setUp() {
        // 测试前准备
    }

    @Test
    @DisplayName("回归测试：正则表达式字符串不应被错误清理")
    void testRegexStringPreservation() {
        // 问题：INSERT语句中的正则表达式 '^OB.*' 被预处理器错误地识别为无效字符并清理
        // 原因：cleanInvalidCharacters方法错误地处理了字符串内的特殊字符
        String sql = "INSERT INTO libra_device_subtype_model_ext VALUES (1632998903710220288,8901,8902,'^OB.*','',10);";
        
        PreprocessingResult result = Preprocessor.preprocess(sql);

        // 验证正则表达式字符串被完整保留
        assertTrue(result.cleanedSql().contains("'^OB.*'"),
            "正则表达式字符串应该被完整保留，不应被预处理器清理");

        // 验证SQL语法仍然正确
        assertTrue(result.cleanedSql().contains("INSERT INTO"),
            "INSERT语句结构应该保持完整");

        // 验证没有破坏性的字符清理
        assertFalse(result.cleanedSql().contains("^^^"),
            "不应该产生无效的字符组合");
    }

    @Test
    @DisplayName("回归测试：注释清理不应影响字符串内容")
    void testCommentRemovalDoesNotAffectStrings() {
        // 问题：注释清理可能错误地处理字符串内包含注释符号的情况
        String sql = "-- 这是注释\n" +
                    "INSERT INTO test VALUES ('value with -- inside', 'another /* comment */ inside');\n" +
                    "/* 多行注释 */\n" +
                    "CREATE TABLE test (id INT);";
        
        PreprocessingResult result = Preprocessor.preprocess(sql);
        String cleaned = result.cleanedSql();

        // 验证字符串内的注释符号被保留
        assertTrue(cleaned.contains("'value with -- inside'"),
            "字符串内的 -- 符号应该被保留");
        assertTrue(cleaned.contains("'another /* comment */ inside'"),
            "字符串内的 /* */ 符号应该被保留");

        // 验证真正的注释被清理
        assertFalse(cleaned.contains("-- 这是注释"),
            "真正的单行注释应该被清理");
        assertFalse(cleaned.contains("/* 多行注释 */"),
            "真正的多行注释应该被清理");

        // 验证SQL结构完整
        assertTrue(cleaned.contains("INSERT INTO test VALUES"),
            "INSERT语句应该保持完整");
        assertTrue(cleaned.contains("CREATE TABLE"),
            "CREATE TABLE语句应该保持完整");
    }

    @Test
    @DisplayName("回归测试：哈希注释清理不应影响字符串")
    void testHashCommentRemovalDoesNotAffectStrings() {
        // 问题：哈希注释清理可能错误地处理字符串内包含#符号的情况
        String sql = "# 这是哈希注释\n" +
                    "INSERT INTO test VALUES ('value with # inside', 'color: #FF0000');\n" +
                    "CREATE TABLE test (id INT);";
        
        PreprocessingResult result = Preprocessor.preprocess(sql);
        String cleaned = result.cleanedSql();

        // 验证字符串内的#符号被保留
        assertTrue(cleaned.contains("'value with # inside'"),
            "字符串内的 # 符号应该被保留");
        assertTrue(cleaned.contains("'color: #FF0000'"),
            "字符串内的颜色代码应该被保留");

        // 验证真正的哈希注释被清理
        assertFalse(cleaned.contains("# 这是哈希注释"),
            "真正的哈希注释应该被清理");

        // 验证SQL结构完整
        assertTrue(cleaned.contains("INSERT INTO test VALUES"),
            "INSERT语句应该保持完整");
    }

    @Test
    @DisplayName("回归测试：复杂正则表达式和特殊字符保留")
    void testComplexRegexAndSpecialCharactersPreservation() {
        // 测试各种复杂的正则表达式和特殊字符
        String sql = "INSERT INTO patterns VALUES " +
                    "('pattern1', '^[A-Z]+$'), " +
                    "('pattern2', '\\d{3}-\\d{3}-\\d{4}'), " +
                    "('pattern3', '[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}'), " +
                    "('pattern4', '^OB.*'), " +
                    "('pattern5', '.*\\$\\$\\$.*'), " +
                    "('pattern6', '###[0-9]+###');";
        
        PreprocessingResult result = Preprocessor.preprocess(sql);
        String cleaned = result.cleanedSql();

        // 验证所有正则表达式模式被保留
        assertTrue(cleaned.contains("'^[A-Z]+$'"), "字母模式应该被保留");
        assertTrue(cleaned.contains("'\\d{3}-\\d{3}-\\d{4}'"), "电话号码模式应该被保留");
        assertTrue(cleaned.contains("'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}'"), "邮箱模式应该被保留");
        assertTrue(cleaned.contains("'^OB.*'"), "OB模式应该被保留");
        assertTrue(cleaned.contains("'.*\\$\\$\\$.*'"), "美元符号模式应该被保留");
        assertTrue(cleaned.contains("'###[0-9]+###'"), "井号模式应该被保留");
    }

    @Test
    @DisplayName("回归测试：混合引号字符串处理")
    void testMixedQuoteStringHandling() {
        // 问题：字符串内可能包含混合的单引号和双引号
        String sql = "INSERT INTO test VALUES " +
                    "(\"string with 'single quotes' inside\"), " +
                    "('string with \"double quotes\" inside'), " +
                    "('O\\'Reilly'), " +
                    "(\"He said \\\"Hello\\\"\");";
        
        PreprocessingResult result = Preprocessor.preprocess(sql);
        String cleaned = result.cleanedSql();

        // 验证混合引号字符串被正确处理
        assertTrue(cleaned.contains("'single quotes'"), "单引号内容应该被保留");
        assertTrue(cleaned.contains("\"double quotes\""), "双引号内容应该被保留");
        assertTrue(cleaned.contains("O\\'Reilly"), "转义单引号应该被保留");
        assertTrue(cleaned.contains("\\\"Hello\\\""), "转义双引号应该被保留");
    }

    @Test
    @DisplayName("回归测试：MySQL条件注释处理")
    void testMySQLConditionalComments() {
        // MySQL条件注释应该被正确处理
        String sql = "/*!40101 SET @saved_cs_client = @@character_set_client */;\n" +
                    "/*!40101 SET character_set_client = utf8 */;\n" +
                    "CREATE TABLE test (id INT);\n" +
                    "/*!40101 SET character_set_client = @saved_cs_client */;";
        
        PreprocessingResult result = Preprocessor.preprocess(sql);
        String cleaned = result.cleanedSql();

        // MySQL条件注释应该被清理（因为我们转换到达梦数据库）
        assertFalse(cleaned.contains("/*!40101"), "MySQL条件注释应该被清理");
        assertFalse(cleaned.contains("@@character_set_client"), "MySQL变量应该被清理");

        // 但CREATE TABLE语句应该保留
        assertTrue(cleaned.contains("CREATE TABLE"), "CREATE TABLE语句应该保留");
    }

    @Test
    @DisplayName("回归测试：空行和空白字符清理")
    void testWhitespaceCleanup() {
        String sql = "\n\n\n" +
                    "   CREATE TABLE test (   \n" +
                    "       id INT,    \n" +
                    "       name VARCHAR(50)   \n" +
                    "   );   \n\n\n" +
                    "   INSERT INTO test VALUES (1, 'test');   \n\n";
        
        PreprocessingResult result = Preprocessor.preprocess(sql);
        String cleaned = result.cleanedSql();

        // 验证多余的空行被清理
        assertFalse(cleaned.startsWith("\n\n\n"), "开头的空行应该被清理");
        assertFalse(cleaned.endsWith("\n\n"), "结尾的空行应该被清理");

        // 但SQL结构应该保持
        assertTrue(cleaned.contains("CREATE TABLE"), "CREATE TABLE应该保留");
        assertTrue(cleaned.contains("INSERT INTO"), "INSERT应该保留");
    }

    @Test
    @DisplayName("回归测试：反引号标识符处理")
    void testBacktickIdentifierHandling() {
        // 测试反引号标识符的正确处理
        String sql = "CREATE TABLE `user` (" +
                    "`id` INT AUTO_INCREMENT, " +
                    "`name` VARCHAR(50), " +
                    "`status` TINYINT, " +
                    "PRIMARY KEY (`id`), " +
                    "KEY `idx_name` (`name`)" +
                    ");";

        PreprocessingResult result = Preprocessor.preprocess(sql);
        String cleaned = result.cleanedSql();

        // 验证预处理器正确处理反引号（为ANTLR解析做准备）
        // 生成器会根据目标数据库添加适当的引号
        assertTrue(cleaned.contains("user"), "表名应该存在");
        assertTrue(cleaned.contains("id"), "列名应该存在");
        assertTrue(cleaned.contains("name"), "列名应该存在");
        assertTrue(cleaned.contains("status"), "保留关键字列名应该存在");
        assertTrue(cleaned.contains("idx_name"), "索引名应该存在");

        // 验证用户明确添加的反引号被保留（新的正确行为）
        // 预处理器现在智能地保留用户定义的标识符反引号
        assertTrue(cleaned.contains("`user`"), "用户定义的表名反引号应该被保留");
        assertTrue(cleaned.contains("`id`"), "用户定义的列名反引号应该被保留");
        assertTrue(cleaned.contains("`name`"), "用户定义的列名反引号应该被保留");
        assertTrue(cleaned.contains("`status`"), "用户定义的列名反引号应该被保留");
        assertTrue(cleaned.contains("`idx_name`"), "用户定义的索引名反引号应该被保留");
    }

    @Test
    @DisplayName("回归测试：大文件预处理性能")
    void testLargeFilePreprocessingPerformance() {
        // 模拟大文件的预处理，确保不会出现性能问题或内存溢出
        StringBuilder largeSQL = new StringBuilder();

        // 构建一个包含多种SQL语句类型的大文件
        for (int i = 0; i < 100; i++) {
            largeSQL.append("-- Comment for table ").append(i).append("\n");
            largeSQL.append("CREATE TABLE `table_").append(i).append("` (\n");
            largeSQL.append("  `id` BIGINT AUTO_INCREMENT,\n");
            largeSQL.append("  `name` VARCHAR(255),\n");
            largeSQL.append("  `pattern` VARCHAR(100) DEFAULT '^[A-Z]+.*',\n");
            largeSQL.append("  PRIMARY KEY (`id`)\n");
            largeSQL.append(");\n\n");

            largeSQL.append("INSERT INTO `table_").append(i).append("` VALUES ");
            for (int j = 0; j < 10; j++) {
                if (j > 0) largeSQL.append(", ");
                largeSQL.append("(").append(j).append(", 'name_").append(j).append("', '^OB.*')");
            }
            largeSQL.append(";\n\n");
        }

        long startTime = System.currentTimeMillis();
        PreprocessingResult result = Preprocessor.preprocess(largeSQL.toString());
        long endTime = System.currentTimeMillis();

        // 验证预处理完成且性能合理（应该在几秒内完成）
        assertNotNull(result, "大文件预处理应该成功完成");
        assertTrue(endTime - startTime < 10000, "大文件预处理应该在10秒内完成");

        // 验证关键内容被正确处理
        String cleaned = result.cleanedSql();
        assertTrue(cleaned.contains("CREATE TABLE"), "CREATE TABLE语句应该被保留");
        assertTrue(cleaned.contains("'^OB.*'"), "正则表达式应该被保留");
        assertFalse(cleaned.contains("-- Comment for table"), "注释应该被清理");
    }

    @Test
    @DisplayName("回归测试：特殊字符编码处理")
    void testSpecialCharacterEncoding() {
        // 测试包含特殊字符编码的SQL
        String sql = "INSERT INTO test VALUES " +
                    "('中文字符'), " +
                    "('emoji: 😀🎉'), " +
                    "('unicode: \\u4e2d\\u6587'), " +
                    "('special: àáâãäåæçèéêë'), " +
                    "('symbols: ©®™€£¥');";

        PreprocessingResult result = Preprocessor.preprocess(sql);
        String cleaned = result.cleanedSql();

        // 验证特殊字符被正确保留
        assertTrue(cleaned.contains("中文字符"), "中文字符应该被保留");
        assertTrue(cleaned.contains("😀🎉"), "emoji应该被保留");
        assertTrue(cleaned.contains("\\u4e2d\\u6587"), "unicode编码应该被保留");
        assertTrue(cleaned.contains("àáâãäåæçèéêë"), "重音字符应该被保留");
        assertTrue(cleaned.contains("©®™€£¥"), "特殊符号应该被保留");
    }

    @Test
    @DisplayName("回归测试：嵌套注释和复杂结构")
    void testNestedCommentsAndComplexStructures() {
        // 测试复杂的注释嵌套和特殊结构
        String sql = "/* 外层注释开始\n" +
                    "   -- 内层单行注释\n" +
                    "   /* 这不是真正的嵌套注释 */\n" +
                    "   外层注释结束 */\n" +
                    "CREATE TABLE test (\n" +
                    "  id INT, -- 行尾注释\n" +
                    "  data VARCHAR(100) DEFAULT '/* 这不是注释 */' -- 另一个行尾注释\n" +
                    ");\n" +
                    "# 哈希注释\n" +
                    "INSERT INTO test VALUES (1, 'value with -- and /* symbols */');";

        PreprocessingResult result = Preprocessor.preprocess(sql);
        String cleaned = result.cleanedSql();

        // 验证注释被正确清理
        assertFalse(cleaned.contains("外层注释开始"), "多行注释应该被清理");
        assertFalse(cleaned.contains("内层单行注释"), "单行注释应该被清理");
        assertFalse(cleaned.contains("行尾注释"), "行尾注释应该被清理");
        assertFalse(cleaned.contains("哈希注释"), "哈希注释应该被清理");

        // 验证字符串内的注释符号被保留
        assertTrue(cleaned.contains("'/* 这不是注释 */'"), "字符串内的注释符号应该被保留");
        assertTrue(cleaned.contains("'value with -- and /* symbols */'"), "字符串内的复杂注释符号应该被保留");

        // 验证SQL结构完整
        assertTrue(cleaned.contains("CREATE TABLE"), "CREATE TABLE应该保留");
        assertTrue(cleaned.contains("INSERT INTO"), "INSERT应该保留");
    }

    @Test
    @DisplayName("回归测试：边界条件处理")
    void testEdgeCases() {
        // 测试各种边界条件

        // 空字符串
        PreprocessingResult emptyResult = Preprocessor.preprocess("");
        assertEquals("", emptyResult.cleanedSql().trim(), "空字符串应该返回空结果");

        // 只有注释
        PreprocessingResult commentOnlyResult = Preprocessor.preprocess("-- 只有注释\n/* 多行注释 */\n# 哈希注释");
        assertTrue(commentOnlyResult.cleanedSql().trim().isEmpty(), "只有注释的SQL应该返回空结果");

        // 只有空白字符
        PreprocessingResult whitespaceOnlyResult = Preprocessor.preprocess("   \n\n\t\t   \n   ");
        assertTrue(whitespaceOnlyResult.cleanedSql().trim().isEmpty(), "只有空白字符应该返回空结果");

        // 单个字符
        PreprocessingResult singleCharResult = Preprocessor.preprocess("A");
        assertEquals("A", singleCharResult.cleanedSql().trim(), "单个字符应该被保留");

        // 只有分号
        PreprocessingResult semicolonOnlyResult = Preprocessor.preprocess(";;;");
        assertEquals(";;;", semicolonOnlyResult.cleanedSql().trim(), "分号应该被保留");
    }

    @Test
    @DisplayName("回归测试：实际问题案例重现")
    void testActualProblemCaseReproduction() {
        // 基于MySQL 8.4官方文档重现实际问题案例
        // 参考: https://dev.mysql.com/doc/refman/8.4/en/create-table.html
        // 参考: https://dev.mysql.com/doc/refman/8.4/en/insert.html
        String problematicSQL = "INSERT INTO libra_device_subtype_model_ext VALUES (1632998903710220288,8901,8902,'^OB.*','',10);\n" +
                               "-- 这是注释\n" +
                               "CREATE TABLE `im_message` (\n" +
                               "  `msg_id` varchar(36) NOT NULL,\n" +
                               "  `status` tinyint(4) DEFAULT NULL,\n" +
                               "  PRIMARY KEY (`msg_id`)\n" +
                               ");";

        PreprocessingResult result = Preprocessor.preprocess(problematicSQL);

        // 基于官方文档验证预处理结果
        validateOfficialDocumentBasedPreprocessingResult(result, problematicSQL);

        System.out.println("    ✅ 基于官方文档的实际问题案例重现测试通过");
    }

    /**
     * 基于官方文档验证预处理结果
     *
     * 官方文档规范：
     * - MySQL 8.4官方文档：CREATE TABLE和INSERT语句语法规范
     * - MySQL 8.4官方文档：标识符和字符串字面量处理规范
     * - MySQL 8.4官方文档：注释语法和处理规范
     */
    private void validateOfficialDocumentBasedPreprocessingResult(PreprocessingResult result, String originalSql) {
        assertNotNull(result, "预处理结果不应为空");
        assertNotNull(result.cleanedSql(), "清理后的SQL不应为空");

        String cleaned = result.cleanedSql();

        // 基于MySQL 8.4官方文档验证字符串字面量保持
        // https://dev.mysql.com/doc/refman/8.4/en/string-literals.html
        if (originalSql.contains("'^OB.*'")) {
            assertTrue(cleaned.contains("'^OB.*'"), "正则表达式字符串字面量必须被保留（符合官方文档）");
            System.out.println("    ✅ MySQL字符串字面量保持正确（符合官方文档）");
        }

        // 基于MySQL 8.4官方文档验证注释处理
        // https://dev.mysql.com/doc/refman/8.4/en/comments.html
        if (originalSql.contains("-- 这是注释")) {
            assertFalse(cleaned.contains("-- 这是注释"), "单行注释必须被清理（符合官方文档）");
            System.out.println("    ✅ MySQL注释清理正确（符合官方文档）");
        }

        // 基于MySQL 8.4官方文档验证标识符处理
        // https://dev.mysql.com/doc/refman/8.4/en/identifiers.html
        if (originalSql.contains("im_message")) {
            assertTrue(cleaned.contains("im_message"), "表名标识符必须存在（符合官方文档）");
            System.out.println("    ✅ MySQL标识符处理正确（符合官方文档）");
        }

        // 基于MySQL 8.4官方文档验证CREATE TABLE语法
        // https://dev.mysql.com/doc/refman/8.4/en/create-table.html
        if (originalSql.toUpperCase().contains("CREATE TABLE")) {
            assertTrue(cleaned.toUpperCase().contains("CREATE TABLE"), "CREATE TABLE语句结构必须完整（符合官方文档）");
            System.out.println("    ✅ MySQL CREATE TABLE语法保持正确（符合官方文档）");
        }

        // 基于MySQL 8.4官方文档验证INSERT语法
        // https://dev.mysql.com/doc/refman/8.4/en/insert.html
        if (originalSql.toUpperCase().contains("INSERT INTO")) {
            assertTrue(cleaned.toUpperCase().contains("INSERT INTO"), "INSERT INTO语句结构必须完整（符合官方文档）");
            System.out.println("    ✅ MySQL INSERT语法保持正确（符合官方文档）");
        }

        // 基于MySQL 8.4官方文档验证PRIMARY KEY约束
        // https://dev.mysql.com/doc/refman/8.4/en/create-table.html#create-table-indexes-keys
        if (originalSql.toUpperCase().contains("PRIMARY KEY")) {
            assertTrue(cleaned.toUpperCase().contains("PRIMARY KEY"), "PRIMARY KEY约束必须完整（符合官方文档）");
            System.out.println("    ✅ MySQL PRIMARY KEY约束保持正确（符合官方文档）");
        }

        // 验证没有产生解析错误的字符（基于ANTLR解析器要求）
        assertFalse(cleaned.contains("^^^"), "不应该产生连续的特殊字符");
        assertFalse(cleaned.contains("$$$"), "不应该产生连续的特殊字符");

        System.out.println("    ✅ 基于官方文档的预处理结果验证完成");
    }
}
