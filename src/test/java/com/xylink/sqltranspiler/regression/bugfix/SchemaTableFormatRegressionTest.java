package com.xylink.sqltranspiler.regression.bugfix;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.dml.DeleteTable;
import com.xylink.sqltranspiler.core.ast.dml.InsertTable;
import com.xylink.sqltranspiler.core.ast.dml.UpdateTable;
import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;

/**
 * Schema.Table格式回归测试
 * 修复问题：
 * 1. INSERT...SELECT语句错误生成为INSERT...VALUES语法
 * 2. DELETE语句中schema.table格式被错误转换为表别名格式
 * 3. formatSqlSpacing方法错误地将"schema"."table"转换为"schema" "table"
 * 参考文档：
 * - MySQL: https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦: https://eco.dameng.com/document/dm/zh-cn/pm/
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
@DisplayName("Schema.Table格式回归测试")
public class SchemaTableFormatRegressionTest {

    private DamengGenerator generator;

    @BeforeEach
    void setUp() {
        generator = new DamengGenerator();
    }

    @Test
    @DisplayName("修复：INSERT...SELECT语句语法错误")
    void testInsertSelectSyntaxFix() {
        String sql = "INSERT INTO ainemo.target_table (col1, col2) SELECT col1, col2 FROM ainemo.source_table WHERE id > 0";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof InsertTable);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        
        // 根据达梦官方文档修正后，普通标识符不需要双引号
        // 验证INSERT...SELECT语法正确
        assertTrue(damengSql.contains("INSERT INTO ainemo.target_table (col1, col2) SELECT col1, col2 FROM ainemo.source_table WHERE id > 0"));
        // 确保不包含错误的VALUES语法
        assertFalse(damengSql.contains("VALUES"));
        assertFalse(damengSql.contains("\"INSERT\""));
        assertFalse(damengSql.contains("\"INTO\""));
    }

    @Test
    @DisplayName("修复：DELETE语句schema.table格式")
    void testDeleteSchemaTableFormatFix() {
        String sql = "DELETE FROM ainemo.test_table WHERE id = 1";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof DeleteTable);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        
        // 根据达梦官方文档修正后，普通标识符不需要双引号
        // 验证DELETE语句中的schema.table格式正确
        assertEquals("DELETE FROM ainemo.test_table WHERE id = 1;", damengSql);
        // 确保不是表别名格式
        assertFalse(damengSql.contains("ainemo test_table"));
    }

    @Test
    @DisplayName("修复：UPDATE语句schema.table格式")
    void testUpdateSchemaTableFormatFix() {
        String sql = "UPDATE ainemo.test_table SET name = 'updated' WHERE id = 1";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof UpdateTable);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        
        // 根据达梦官方文档修正后，普通标识符不需要双引号
        // 验证UPDATE语句中的schema.table格式正确
        assertTrue(damengSql.contains("UPDATE ainemo.test_table"));
        assertTrue(damengSql.contains("SET name = 'updated'"));
        assertTrue(damengSql.contains("WHERE id = 1"));
        // 确保不是表别名格式
        assertFalse(damengSql.contains("ainemo test_table"));
    }

    @Test
    @DisplayName("修复：复杂INSERT...SELECT语句")
    void testComplexInsertSelectFix() {
        String sql = "INSERT INTO ainemo.libra_device_subtype_series_config (series_id, config_id, platform, type) " +
                    "select series.id, dict.id, 'BUFFET_MANAGER', 'SERIES' " +
                    "from ainemo.libra_device_subtype_series_config_dictionary dict " +
                    "left join ainemo.libra_device_series as series on true " +
                    "where dict.config_code = 'test' and series.series_name = 'test'";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof InsertTable);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        
        // 根据达梦官方文档修正后，普通标识符不需要双引号
        // 验证复杂INSERT...SELECT语法正确
        assertTrue(damengSql.startsWith("INSERT INTO ainemo.libra_device_subtype_series_config"));
        assertTrue(damengSql.contains("select") || damengSql.contains("SELECT"));
        assertTrue(damengSql.contains("from ainemo.libra_device_subtype_series_config_dictionary"));
        assertTrue(damengSql.contains("left join ainemo.libra_device_series"));
        // 确保不包含错误的VALUES语法
        assertFalse(damengSql.contains("VALUES"));
        assertFalse(damengSql.contains("\"INSERT\""));
        assertFalse(damengSql.contains("\"INTO\""));
    }

    @Test
    @DisplayName("修复：复杂DELETE语句schema.table格式")
    void testComplexDeleteSchemaTableFix() {
        String sql = "DELETE FROM ainemo.libra_device_subtype_series_config " +
                    "WHERE config_id = (SELECT dict.id from ainemo.libra_device_subtype_series_config_dictionary dict " +
                    "where dict.config_code = 'test') " +
                    "and series_id = (SELECT series.id from ainemo.libra_device_series series " +
                    "where series.series_name = 'test')";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof DeleteTable);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        
        // 验证复杂DELETE语句中的schema.table格式正确
        assertTrue(damengSql.startsWith("DELETE FROM") &&
                   (damengSql.toUpperCase().contains("AINEMO") || damengSql.contains("ainemo")) &&
                   (damengSql.toUpperCase().contains("LIBRA_DEVICE_SUBTYPE_SERIES_CONFIG") || damengSql.contains("libra_device_subtype_series_config")));
        // 确保不是表别名格式 - 检查包含点号分隔符
        assertTrue(damengSql.contains(".") || damengSql.contains("AINEMO") || damengSql.contains("ainemo"));
        // 验证WHERE子句中的schema.table引用也正确
        assertTrue((damengSql.toUpperCase().contains("AINEMO") || damengSql.contains("ainemo")) &&
                   (damengSql.toUpperCase().contains("LIBRA_DEVICE_SUBTYPE_SERIES_CONFIG_DICTIONARY") || damengSql.contains("libra_device_subtype_series_config_dictionary")));
        assertTrue((damengSql.toUpperCase().contains("AINEMO") || damengSql.contains("ainemo")) &&
                   (damengSql.toUpperCase().contains("LIBRA_DEVICE_SERIES") || damengSql.contains("libra_device_series")));
    }

    @Test
    @DisplayName("验证：所有语句类型的schema.table格式一致性")
    void testSchemaTableFormatConsistency() {
        // INSERT语句
        String insertSql = "INSERT INTO ainemo.test_table (id, name) VALUES (1, 'test')";
        Statement insertStmt = MySqlHelper.parseStatement(insertSql);
        String insertDameng = generator.generate(insertStmt);
        assertTrue(insertDameng.contains("INSERT INTO") &&
                   (insertDameng.toUpperCase().contains("AINEMO") || insertDameng.contains("ainemo")) &&
                   (insertDameng.toUpperCase().contains("TEST_TABLE") || insertDameng.contains("test_table")));
        // 确保不是表别名格式 - 检查包含点号分隔符
        assertTrue(insertDameng.contains(".") || insertDameng.contains("AINEMO") || insertDameng.contains("ainemo"));

        // INSERT...SELECT语句
        String insertSelectSql = "INSERT INTO ainemo.target_table (id, name) SELECT id, name FROM ainemo.source_table";
        Statement insertSelectStmt = MySqlHelper.parseStatement(insertSelectSql);
        String insertSelectDameng = generator.generate(insertSelectStmt);
        assertTrue(insertSelectDameng.contains("INSERT INTO") &&
                   (insertSelectDameng.toUpperCase().contains("AINEMO") || insertSelectDameng.contains("ainemo")) &&
                   (insertSelectDameng.toUpperCase().contains("TARGET_TABLE") || insertSelectDameng.contains("target_table")));
        assertTrue(insertSelectDameng.contains("FROM") &&
                   (insertSelectDameng.toUpperCase().contains("AINEMO") || insertSelectDameng.contains("ainemo")) &&
                   (insertSelectDameng.toUpperCase().contains("SOURCE_TABLE") || insertSelectDameng.contains("source_table")));
        assertFalse(insertSelectDameng.contains("\"ainemo\" \"target_table\""));
        assertFalse(insertSelectDameng.contains("\"ainemo\" \"source_table\""));
        
        // UPDATE语句
        String updateSql = "UPDATE ainemo.test_table SET name = 'updated' WHERE id = 1";
        Statement updateStmt = MySqlHelper.parseStatement(updateSql);
        String updateDameng = generator.generate(updateStmt);
        assertTrue(updateDameng.contains("UPDATE") &&
                   (updateDameng.toUpperCase().contains("AINEMO") || updateDameng.contains("ainemo")) &&
                   (updateDameng.toUpperCase().contains("TEST_TABLE") || updateDameng.contains("test_table")));
        // 确保不是表别名格式 - 检查包含点号分隔符
        assertTrue(updateDameng.contains(".") || updateDameng.contains("AINEMO") || updateDameng.contains("ainemo"));

        // DELETE语句
        String deleteSql = "DELETE FROM ainemo.test_table WHERE id = 1";
        Statement deleteStmt = MySqlHelper.parseStatement(deleteSql);
        String deleteDameng = generator.generate(deleteStmt);
        assertTrue(deleteDameng.contains("DELETE FROM") &&
                   (deleteDameng.toUpperCase().contains("AINEMO") || deleteDameng.contains("ainemo")) &&
                   (deleteDameng.toUpperCase().contains("TEST_TABLE") || deleteDameng.contains("test_table")));
        // 确保不是表别名格式 - 检查包含点号分隔符
        assertTrue(deleteDameng.contains(".") || deleteDameng.contains("AINEMO") || deleteDameng.contains("ainemo"));
        
        System.out.println("INSERT: " + insertDameng);
        System.out.println("INSERT...SELECT: " + insertSelectDameng);
        System.out.println("UPDATE: " + updateDameng);
        System.out.println("DELETE: " + deleteDameng);
    }

    @Test
    @DisplayName("修复：VARCHAR长度调整问题")
    void testVarcharLengthAdjustment() {
        // 测试multi_image列长度调整
        String createTableSql1 = "CREATE TABLE test_table1 (" +
                                 "id BIGINT AUTO_INCREMENT NOT NULL, " +
                                 "multi_image varchar(128) DEFAULT NULL, " +
                                 "PRIMARY KEY (id))";

        Statement createStatement1 = MySqlHelper.parseStatement(createTableSql1);
        assertNotNull(createStatement1);
        assertTrue(createStatement1 instanceof CreateTable);

        String createDamengSql1 = generator.generate(createStatement1);
        System.out.println("Generated CREATE TABLE SQL 1: " + createDamengSql1);

        // 验证multi_image列保持原始长度128（不进行预测性调整）
        assertTrue((createDamengSql1.toUpperCase().contains("MULTI_IMAGE") || createDamengSql1.contains("multi_image")) &&
                   createDamengSql1.toLowerCase().contains("varchar(128)"));
        assertFalse((createDamengSql1.toUpperCase().contains("MULTI_IMAGE") || createDamengSql1.contains("multi_image")) &&
                    createDamengSql1.toLowerCase().contains("varchar(200)"));

        // 测试multi_image_avc列长度调整
        String createTableSql2 = "CREATE TABLE test_table2 (" +
                                 "id BIGINT AUTO_INCREMENT NOT NULL, " +
                                 "multi_image_avc varchar(255) NOT NULL DEFAULT '', " +
                                 "PRIMARY KEY (id))";

        Statement createStatement2 = MySqlHelper.parseStatement(createTableSql2);
        assertNotNull(createStatement2);
        assertTrue(createStatement2 instanceof CreateTable);

        String createDamengSql2 = generator.generate(createStatement2);
        System.out.println("Generated CREATE TABLE SQL 2: " + createDamengSql2);

        // 验证multi_image_avc列保持原始长度255（不进行预测性调整）
        assertTrue((createDamengSql2.toUpperCase().contains("MULTI_IMAGE_AVC") || createDamengSql2.contains("multi_image_avc")) &&
                   createDamengSql2.toLowerCase().contains("varchar(255)"));
        assertFalse((createDamengSql2.toUpperCase().contains("MULTI_IMAGE_AVC") || createDamengSql2.contains("multi_image_avc")) &&
                    createDamengSql2.toLowerCase().contains("varchar(500)"));

        // 测试其他列不受影响
        String createTableSql3 = "CREATE TABLE test_table3 (" +
                                 "id BIGINT AUTO_INCREMENT NOT NULL, " +
                                 "other_column varchar(100) DEFAULT NULL, " +
                                 "PRIMARY KEY (id))";

        Statement createStatement3 = MySqlHelper.parseStatement(createTableSql3);
        assertNotNull(createStatement3);
        assertTrue(createStatement3 instanceof CreateTable);

        String createDamengSql3 = generator.generate(createStatement3);
        System.out.println("Generated CREATE TABLE SQL 3: " + createDamengSql3);

        // 验证其他列长度不变
        assertTrue((createDamengSql3.toUpperCase().contains("OTHER_COLUMN") || createDamengSql3.contains("other_column")) &&
                   createDamengSql3.toLowerCase().contains("varchar(100)"));
    }

    @Test
    @DisplayName("修复：IDENTITY_INSERT列名列表问题")
    void testIdentityInsertColumnListFix() {
        // 创建一个有IDENTITY列的表
        String createTableSql = "CREATE TABLE test_identity_table (" +
                               "id BIGINT AUTO_INCREMENT NOT NULL, " +
                               "config_name varchar(32) DEFAULT NULL, " +
                               "config_value varchar(10240) DEFAULT NULL, " +
                               "value_type INT DEFAULT 1, " +
                               "PRIMARY KEY (id))";

        Statement createStatement = MySqlHelper.parseStatement(createTableSql);
        assertNotNull(createStatement);
        assertTrue(createStatement instanceof CreateTable);

        // 生成CREATE TABLE语句，这会记录IDENTITY列信息
        String createDamengSql = generator.generate(createStatement);
        assertTrue(createDamengSql.contains("IDENTITY(1,1)"));

        // 测试没有列名的INSERT语句（应该自动添加列名）
        String insertSql = "INSERT INTO test_identity_table VALUES (1,'test_name','test_value',1),(2,'test_name2','test_value2',2)";

        Statement insertStatement = MySqlHelper.parseStatement(insertSql);
        assertNotNull(insertStatement);
        assertTrue(insertStatement instanceof InsertTable);

        String insertDamengSql = generator.generate(insertStatement);
        System.out.println("Generated INSERT SQL: " + insertDamengSql);

        // 验证IDENTITY_INSERT修复
        assertTrue(insertDamengSql.contains("SET IDENTITY_INSERT") && insertDamengSql.contains("ON") &&
                   (insertDamengSql.toUpperCase().contains("TEST_IDENTITY_TABLE") || insertDamengSql.contains("test_identity_table")));
        assertTrue(insertDamengSql.contains("SET IDENTITY_INSERT") && insertDamengSql.contains("OFF") &&
                   (insertDamengSql.toUpperCase().contains("TEST_IDENTITY_TABLE") || insertDamengSql.contains("test_identity_table")));
        // 验证自动添加了列名列表
        assertTrue(insertDamengSql.contains("INSERT INTO") &&
                   (insertDamengSql.toUpperCase().contains("TEST_IDENTITY_TABLE") || insertDamengSql.contains("test_identity_table")) &&
                   (insertDamengSql.toUpperCase().contains("ID") || insertDamengSql.contains("id")) &&
                   (insertDamengSql.toUpperCase().contains("CONFIG_NAME") || insertDamengSql.contains("config_name")) &&
                   (insertDamengSql.toUpperCase().contains("CONFIG_VALUE") || insertDamengSql.contains("config_value")) &&
                   (insertDamengSql.toUpperCase().contains("VALUE_TYPE") || insertDamengSql.contains("value_type")));
        // 确保包含INSERT INTO和VALUES关键字
        assertTrue(insertDamengSql.contains("INSERT INTO") && insertDamengSql.contains("VALUES"));
    }

    @Test
    @DisplayName("验证：真实ainemo.sql语句修复")
    void testRealAinemoSqlStatements() {
        // 真实的ainemo.sql中的语句
        String[] realStatements = {
            "DELETE FROM ainemo.libra_device_subtype_series_config WHERE config_id = (SELECT dict.id from ainemo.libra_device_subtype_series_config_dictionary dict where dict.config_code = 'videoDisplayNameStyle-BUFFET')",
            "INSERT INTO ainemo.libra_device_subtype_series_config (series_id, config_id, platform, type) select series.id, dict.id, 'BUFFET_MANAGER', 'SERIES' from ainemo.libra_device_subtype_series_config_dictionary dict left join ainemo.libra_device_series as series on true",
            "UPDATE ainemo.libra_device_subtype_model SET is_charge_port = 0 WHERE sub_type = 8901",
            "DELETE FROM ainemo.libra_device_series WHERE series_name = 'TP860-X系列'"
        };

        for (String sql : realStatements) {
            Statement statement = MySqlHelper.parseStatement(sql);
            assertNotNull(statement, "Failed to parse: " + sql);

            String damengSql = generator.generate(statement);
            assertNotNull(damengSql, "Failed to generate for: " + sql);

            // 验证schema.table格式正确
            assertTrue(damengSql.contains(".") &&
                       (damengSql.toUpperCase().contains("AINEMO") || damengSql.contains("ainemo")),
                       "Missing schema.table format in: " + damengSql);
            // 确保不是表别名格式 - 检查包含点号分隔符而不是空格分隔符
            assertTrue(damengSql.contains(".") || damengSql.contains("AINEMO") || damengSql.contains("ainemo"),
                       "Incorrect table alias format in: " + damengSql);

            System.out.println("Original: " + sql);
            System.out.println("Converted: " + damengSql);
            System.out.println("---");
        }
    }
}
