package com.xylink.sqltranspiler.e2e.web;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.web.dto.DialectInfo;
import com.xylink.sqltranspiler.web.dto.TranspileRequest;
import com.xylink.sqltranspiler.web.dto.TranspileResponse;
import com.xylink.sqltranspiler.web.service.SqlTranspilerWebService;

/**
 * SQL转换Web服务测试 - 严格遵循官方文档规范
 *
 * 测试原则：
 * 1. 不允许推测，必须基于官方文档
 * 2. 测试期望与官方文档不符时，修正测试用例而不是降低代码质量
 * 3. 坚持正确的实现，确保功能的准确性和完整性
 *
 * 测试目标：
 * - 验证Web服务的SQL转换功能符合官方文档要求
 * - 确保API响应格式和内容的准确性
 * - 验证错误处理机制的正确性
 *
 * 官方文档依据：
 * - MySQL 8.4: https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦数据库: https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓数据库: https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/
 * - 神通数据库: shentong.md 官方文档
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证Web服务转换的正确性
 * 2. 确保API响应格式符合官方文档规范
 * 3. 验证错误处理机制基于官方文档
 * 4. 测试Web服务的端到端转换流程
 *
 * 基于官方文档的验证逻辑：
 * - 每个Web服务测试都必须有相应数据库官方文档的明确依据
 * - API响应必须符合官方文档的转换规范
 * - 验证逻辑必须引用具体的官方文档章节
 * - Web服务集成测试必须覆盖官方文档中的关键功能
 */
class SqlTranspilerWebServiceTest {
    
    private SqlTranspilerWebService webService;
    
    @BeforeEach
    void setUp() {
        webService = new SqlTranspilerWebService();
    }
    
    @Test
    void testGetSupportedDialects() {
        List<DialectInfo> dialects = webService.getSupportedDialects();
        
        assertThat(dialects).isNotEmpty();
        assertThat(dialects).anyMatch(d -> d.getName().equals("dameng"));
        assertThat(dialects).anyMatch(d -> d.getName().equals("kingbase"));
    }
    
    @Test
    void testIsDialectSupported() {
        assertThat(webService.isDialectSupported("dameng")).isTrue();
        assertThat(webService.isDialectSupported("kingbase")).isTrue();
        assertThat(webService.isDialectSupported("unsupported")).isFalse();
    }
    
    @Test
    void testTranspileSimpleCreateTableWithOfficialDocumentValidation() {
        // 基于MySQL 8.4官方文档的标准CREATE TABLE语法
        // https://dev.mysql.com/doc/refman/8.4/en/create-table.html
        TranspileRequest request = new TranspileRequest();
        request.setSql("CREATE TABLE users (id INT NOT NULL AUTO_INCREMENT PRIMARY KEY, name VARCHAR(100) NOT NULL);");
        request.setSourceDialect("mysql");
        request.setPreserveComments(true);

        TranspileResponse response = webService.transpile(request);

        assertThat(response).isNotNull();
        assertThat(response.isSuccess()).isTrue();
        assertThat(response.getResults()).isNotEmpty();
        assertThat(response.getSourceDialect()).isEqualTo("mysql");

        // 基于官方文档验证各数据库的转换结果
        validateWebServiceOfficialDocumentConversion(response);

        // 检查达梦数据库转换结果
        assertThat(response.getResults()).containsKey("dameng");
        TranspileResponse.DatabaseResult damengResult = response.getResults().get("dameng");
        assertThat(damengResult.isSuccess()).isTrue();
        assertThat(damengResult.getTranslatedSql()).isNotEmpty();
        assertThat(damengResult.getSuccessCount()).isGreaterThan(0);

        // 检查金仓数据库转换结果
        assertThat(response.getResults()).containsKey("kingbase");
        TranspileResponse.DatabaseResult kingbaseResult = response.getResults().get("kingbase");
        assertThat(kingbaseResult.isSuccess()).isTrue();
        assertThat(kingbaseResult.getTranslatedSql()).isNotEmpty();
        assertThat(kingbaseResult.getSuccessCount()).isGreaterThan(0);
    }
    
    @Test
    void testTranspileWithInvalidSQL() {
        TranspileRequest request = new TranspileRequest();
        request.setSql("INVALID SQL STATEMENT;");
        request.setSourceDialect("mysql");

        TranspileResponse response = webService.transpile(request);

        assertThat(response).isNotNull();
        // 即使SQL无效，也会尝试转换所有数据库，但可能会有错误
        assertThat(response.getResults()).isNotEmpty();
    }
    
    @Test
    void testTranspileEmptySQL() {
        TranspileRequest request = new TranspileRequest();
        request.setSql("");
        request.setSourceDialect("mysql");

        TranspileResponse response = webService.transpile(request);

        assertThat(response).isNotNull();
        // 空SQL应该能正常处理，可能返回空结果或空输出
        if (response.getResults() != null && !response.getResults().isEmpty()) {
            for (TranspileResponse.DatabaseResult result : response.getResults().values()) {
                assertThat(result.getTranslatedSql()).isEmpty();
            }
        }
        // 空SQL返回null或空结果集都是合理的行为
    }

    /**
     * 基于官方文档验证Web服务转换结果
     *
     * 验证各数据库的转换结果是否符合官方文档规范
     */
    private void validateWebServiceOfficialDocumentConversion(TranspileResponse response) {
        assertThat(response.getResults()).isNotNull();
        assertThat(response.getResults()).isNotEmpty();

        // 基于达梦官方文档验证转换结果
        if (response.getResults().containsKey("dameng")) {
            TranspileResponse.DatabaseResult damengResult = response.getResults().get("dameng");
            validateDamengWebServiceConversion(damengResult);
        }

        // 基于金仓官方文档验证转换结果
        if (response.getResults().containsKey("kingbase")) {
            TranspileResponse.DatabaseResult kingbaseResult = response.getResults().get("kingbase");
            validateKingbaseWebServiceConversion(kingbaseResult);
        }

        // 基于神通官方文档验证转换结果
        if (response.getResults().containsKey("shentong")) {
            TranspileResponse.DatabaseResult shentongResult = response.getResults().get("shentong");
            validateShentongWebServiceConversion(shentongResult);
        }
    }

    /**
     * 基于达梦官方文档验证Web服务转换结果
     */
    private void validateDamengWebServiceConversion(TranspileResponse.DatabaseResult result) {
        assertThat(result).isNotNull();
        assertThat(result.getTranslatedSql()).isNotNull();
        assertThat(result.getTranslatedSql()).isNotEmpty();

        String translatedSql = result.getTranslatedSql().toUpperCase();

        // 基于达梦官方文档验证AUTO_INCREMENT转换
        if (translatedSql.contains("IDENTITY")) {
            System.out.println("    ✅ Web服务：达梦正确将AUTO_INCREMENT转换为IDENTITY");
        }

        // 验证基本CREATE TABLE结构
        assertThat(translatedSql).contains("CREATE TABLE");
        assertThat(translatedSql).contains("PRIMARY KEY");

        System.out.println("    ✅ Web服务：达梦转换验证通过");
    }

    /**
     * 基于金仓官方文档验证Web服务转换结果
     */
    private void validateKingbaseWebServiceConversion(TranspileResponse.DatabaseResult result) {
        assertThat(result).isNotNull();
        assertThat(result.getTranslatedSql()).isNotNull();
        assertThat(result.getTranslatedSql()).isNotEmpty();

        String translatedSql = result.getTranslatedSql().toUpperCase();

        // 基于金仓官方文档验证MySQL兼容性
        if (translatedSql.contains("AUTO_INCREMENT")) {
            System.out.println("    ✅ Web服务：金仓保持了AUTO_INCREMENT语法（良好兼容性）");
        } else if (translatedSql.contains("SERIAL")) {
            System.out.println("    ✅ Web服务：金仓将AUTO_INCREMENT转换为SERIAL");
        }

        // 验证基本CREATE TABLE结构
        assertThat(translatedSql).contains("CREATE TABLE");
        assertThat(translatedSql).contains("PRIMARY KEY");

        System.out.println("    ✅ Web服务：金仓转换验证通过");
    }

    /**
     * 基于神通官方文档验证Web服务转换结果
     */
    private void validateShentongWebServiceConversion(TranspileResponse.DatabaseResult result) {
        assertThat(result).isNotNull();
        assertThat(result.getTranslatedSql()).isNotNull();
        assertThat(result.getTranslatedSql()).isNotEmpty();

        String translatedSql = result.getTranslatedSql().toUpperCase();

        // 基于神通官方文档验证SERIAL类型支持
        if (translatedSql.contains("SERIAL")) {
            System.out.println("    ✅ Web服务：神通将AUTO_INCREMENT转换为SERIAL类型");
        } else if (translatedSql.contains("AUTO_INCREMENT")) {
            System.out.println("    ✅ Web服务：神通保持了AUTO_INCREMENT语法");
        }

        // 验证基本CREATE TABLE结构
        assertThat(translatedSql).contains("CREATE TABLE");
        assertThat(translatedSql).contains("PRIMARY KEY");

        System.out.println("    ✅ Web服务：神通转换验证通过");
    }
}
