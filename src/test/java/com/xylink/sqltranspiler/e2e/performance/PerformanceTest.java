package com.xylink.sqltranspiler.e2e.performance;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import com.xylink.sqltranspiler.infrastructure.parser.PreprocessingResult;
import com.xylink.sqltranspiler.infrastructure.parser.Preprocessor;

/**
 * SQL转换器性能测试工具 - 严格遵循官方文档规范
 *
 * 测试原则：
 * 1. 不允许推测，必须基于官方文档
 * 2. 测试期望与官方文档不符时，修正测试用例而不是降低代码质量
 * 3. 坚持正确的实现，确保功能的准确性和完整性
 *
 * 测试目标：
 * - 测试大型SQL文件的转换性能
 * - 提供基于官方文档的详细性能分析和建议
 * - 验证转换结果的正确性和完整性
 *
 * 官方文档依据：
 * - MySQL 8.4: https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦数据库: https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓数据库: https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/
 * - 神通数据库: shentong.md 官方文档
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证性能测试的正确性
 * 2. 确保性能基准符合官方文档规范
 * 3. 验证性能测试基于官方文档
 * 4. 测试大规模SQL转换的性能表现
 *
 * 基于官方文档的验证逻辑：
 * - 每个性能测试都必须有相应数据库官方文档的明确依据
 * - 性能基准必须符合官方文档的性能规范
 * - 验证逻辑必须引用具体的官方文档章节
 * - 性能测试必须覆盖官方文档中的关键性能指标
 */
public class PerformanceTest {
    
    public static class ConversionStats {
        public long totalLines = 0;
        public long totalStatements = 0;
        public long createTableCount = 0;
        public long preprocessingTimeMs = 0;
        public long parsingTimeMs = 0;
        public long generationTimeMs = 0;
        public long totalTimeMs = 0;
        public int errorCount = 0;
        
        public void print() {
            System.out.println("\n📊 Conversion Statistics:");
            System.out.println("═══════════════════════════════════════");
            System.out.println("📄 Total lines processed: " + totalLines);
            System.out.println("📝 Total statements: " + totalStatements);
            System.out.println("🏗️  CREATE TABLE statements: " + createTableCount);
            System.out.println("❌ Errors encountered: " + errorCount);
            System.out.println();
            System.out.println("⏱️  Performance Metrics:");
            System.out.println("   Preprocessing: " + preprocessingTimeMs + " ms");
            System.out.println("   Parsing:       " + parsingTimeMs + " ms");
            System.out.println("   Generation:    " + generationTimeMs + " ms");
            System.out.println("   Total time:    " + totalTimeMs + " ms");
            System.out.println();
            if (totalStatements > 0) {
                System.out.println("📈 Throughput:");
                System.out.println("   " + (totalStatements * 1000 / totalTimeMs) + " statements/second");
                System.out.println("   " + (totalLines * 1000 / totalTimeMs) + " lines/second");
            }
        }
    }
    
    public static ConversionStats testFile(String filePath) throws IOException {
        ConversionStats stats = new ConversionStats();
        long startTime = System.currentTimeMillis();
        
        try {
            // Read file
            String content = Files.readString(Paths.get(filePath));
            stats.totalLines = content.split("\n").length;
            
            // Preprocessing
            long prepStart = System.currentTimeMillis();
            PreprocessingResult preprocessingResult = Preprocessor.preprocess(content);
            String preprocessed = preprocessingResult.cleanedSql();
            stats.preprocessingTimeMs = System.currentTimeMillis() - prepStart;
            
            // Parsing
            long parseStart = System.currentTimeMillis();
            List<Statement> statements = MySqlHelper.parseMultiStatement(preprocessed);
            stats.parsingTimeMs = System.currentTimeMillis() - parseStart;
            stats.totalStatements = statements.size();
            
            // Count CREATE TABLE statements
            for (Statement stmt : statements) {
                if (stmt instanceof CreateTable) {
                    stats.createTableCount++;
                }
            }
            
            // Generation
            long genStart = System.currentTimeMillis();
            DamengGenerator generator = new DamengGenerator();
            for (Statement statement : statements) {
                try {
                    generator.generate(statement);
                } catch (Exception e) {
                    stats.errorCount++;
                }
            }
            stats.generationTimeMs = System.currentTimeMillis() - genStart;
            
        } catch (Exception e) {
            stats.errorCount++;
            System.err.println("Error processing file: " + e.getMessage());
            e.printStackTrace();
        }
        
        stats.totalTimeMs = System.currentTimeMillis() - startTime;
        return stats;
    }
    
    public static void main(String[] args) throws IOException {
        if (args.length != 1) {
            System.err.println("Usage: java PerformanceTest <sql-file>");
            System.exit(1);
        }
        
        String filePath = args[0];
        System.out.println("🚀 Starting performance test for: " + filePath);
        
        ConversionStats stats = testFile(filePath);
        stats.print();
        
        // Performance recommendations
        System.out.println("\n💡 Performance Analysis:");
        if (stats.preprocessingTimeMs > stats.parsingTimeMs) {
            System.out.println("⚠️  Preprocessing is the bottleneck - consider optimizing regex patterns");
        }
        if (stats.parsingTimeMs > stats.generationTimeMs * 2) {
            System.out.println("⚠️  Parsing is slow - consider optimizing ANTLR grammar");
        }
        if (stats.errorCount > 0) {
            System.out.println("⚠️  " + stats.errorCount + " errors encountered - check compatibility");
        }
        
        if (stats.totalTimeMs < 1000) {
            System.out.println("✅ Excellent performance!");
        } else if (stats.totalTimeMs < 5000) {
            System.out.println("✅ Good performance");
        } else {
            System.out.println("⚠️  Consider performance optimization");
        }
    }
}
