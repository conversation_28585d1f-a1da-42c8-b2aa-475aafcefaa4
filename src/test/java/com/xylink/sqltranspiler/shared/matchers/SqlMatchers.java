package com.xylink.sqltranspiler.shared.matchers;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import java.util.regex.Pattern;

/**
 * SQL断言匹配器
 * 
 * 提供专门用于SQL验证的自定义断言方法：
 * 1. SQL语法结构验证
 * 2. 数据库特定语法验证
 * 3. 转换正确性验证
 * 4. 语义等价性验证
 * 
 * <AUTHOR>
 */
public class SqlMatchers {

    /**
     * 验证SQL包含指定的表名
     */
    public static void assertContainsTable(String sql, String tableName) {
        assertContainsTable(sql, tableName, "SQL应包含表名: " + tableName);
    }

    /**
     * 验证SQL包含指定的表名（带自定义消息）
     */
    public static void assertContainsTable(String sql, String tableName, String message) {
        Pattern pattern = Pattern.compile("\\b" + Pattern.quote(tableName) + "\\b", Pattern.CASE_INSENSITIVE);
        assertTrue(pattern.matcher(sql).find(), message);
    }

    /**
     * 验证SQL不包含指定的表名
     */
    public static void assertNotContainsTable(String sql, String tableName) {
        assertNotContainsTable(sql, tableName, "SQL不应包含表名: " + tableName);
    }

    /**
     * 验证SQL不包含指定的表名（带自定义消息）
     */
    public static void assertNotContainsTable(String sql, String tableName, String message) {
        Pattern pattern = Pattern.compile("\\b" + Pattern.quote(tableName) + "\\b", Pattern.CASE_INSENSITIVE);
        assertFalse(pattern.matcher(sql).find(), message);
    }

    /**
     * 验证SQL包含指定的列名
     */
    public static void assertContainsColumn(String sql, String columnName) {
        assertContainsColumn(sql, columnName, "SQL应包含列名: " + columnName);
    }

    /**
     * 验证SQL包含指定的列名（带自定义消息）
     */
    public static void assertContainsColumn(String sql, String columnName, String message) {
        Pattern pattern = Pattern.compile("\\b" + Pattern.quote(columnName) + "\\b", Pattern.CASE_INSENSITIVE);
        assertTrue(pattern.matcher(sql).find(), message);
    }

    /**
     * 验证SQL包含指定的数据类型
     */
    public static void assertContainsDataType(String sql, String dataType) {
        assertContainsDataType(sql, dataType, "SQL应包含数据类型: " + dataType);
    }

    /**
     * 验证SQL包含指定的数据类型（带自定义消息）
     */
    public static void assertContainsDataType(String sql, String dataType, String message) {
        Pattern pattern = Pattern.compile("\\b" + Pattern.quote(dataType) + "\\b", Pattern.CASE_INSENSITIVE);
        assertTrue(pattern.matcher(sql).find(), message);
    }

    /**
     * 验证SQL不包含指定的数据类型
     */
    public static void assertNotContainsDataType(String sql, String dataType) {
        assertNotContainsDataType(sql, dataType, "SQL不应包含数据类型: " + dataType);
    }

    /**
     * 验证SQL不包含指定的数据类型（带自定义消息）
     */
    public static void assertNotContainsDataType(String sql, String dataType, String message) {
        Pattern pattern = Pattern.compile("\\b" + Pattern.quote(dataType) + "\\b", Pattern.CASE_INSENSITIVE);
        assertFalse(pattern.matcher(sql).find(), message);
    }

    /**
     * 验证SQL使用双引号标识符
     */
    public static void assertUsesDoubleQuoteIdentifiers(String sql) {
        assertUsesDoubleQuoteIdentifiers(sql, "SQL应使用双引号标识符");
    }

    /**
     * 验证SQL使用双引号标识符（带自定义消息）
     */
    public static void assertUsesDoubleQuoteIdentifiers(String sql, String message) {
        assertTrue(sql.contains("\""), message);
    }

    /**
     * 验证SQL不使用反引号标识符
     */
    public static void assertNotUsesBacktickIdentifiers(String sql) {
        assertNotUsesBacktickIdentifiers(sql, "SQL不应使用反引号标识符");
    }

    /**
     * 验证SQL不使用反引号标识符（带自定义消息）
     */
    public static void assertNotUsesBacktickIdentifiers(String sql, String message) {
        assertFalse(sql.contains("`"), message);
    }

    /**
     * 验证SQL语句以分号结尾
     */
    public static void assertEndsWithSemicolon(String sql) {
        assertEndsWithSemicolon(sql, "SQL语句应以分号结尾");
    }

    /**
     * 验证SQL语句以分号结尾（带自定义消息）
     */
    public static void assertEndsWithSemicolon(String sql, String message) {
        assertTrue(sql.trim().endsWith(";"), message);
    }

    /**
     * 验证SQL包含CREATE TABLE语句
     */
    public static void assertIsCreateTableStatement(String sql) {
        assertIsCreateTableStatement(sql, "SQL应为CREATE TABLE语句");
    }

    /**
     * 验证SQL包含CREATE TABLE语句（带自定义消息）
     */
    public static void assertIsCreateTableStatement(String sql, String message) {
        Pattern pattern = Pattern.compile("CREATE\\s+TABLE", Pattern.CASE_INSENSITIVE);
        assertTrue(pattern.matcher(sql).find(), message);
    }

    /**
     * 验证SQL包含INSERT语句
     */
    public static void assertIsInsertStatement(String sql) {
        assertIsInsertStatement(sql, "SQL应为INSERT语句");
    }

    /**
     * 验证SQL包含INSERT语句（带自定义消息）
     */
    public static void assertIsInsertStatement(String sql, String message) {
        Pattern pattern = Pattern.compile("INSERT\\s+INTO", Pattern.CASE_INSENSITIVE);
        assertTrue(pattern.matcher(sql).find(), message);
    }

    /**
     * 验证SQL包含UPDATE语句
     */
    public static void assertIsUpdateStatement(String sql) {
        assertIsUpdateStatement(sql, "SQL应为UPDATE语句");
    }

    /**
     * 验证SQL包含UPDATE语句（带自定义消息）
     */
    public static void assertIsUpdateStatement(String sql, String message) {
        Pattern pattern = Pattern.compile("UPDATE\\s+", Pattern.CASE_INSENSITIVE);
        assertTrue(pattern.matcher(sql).find(), message);
    }

    /**
     * 验证SQL包含DELETE语句
     */
    public static void assertIsDeleteStatement(String sql) {
        assertIsDeleteStatement(sql, "SQL应为DELETE语句");
    }

    /**
     * 验证SQL包含DELETE语句（带自定义消息）
     */
    public static void assertIsDeleteStatement(String sql, String message) {
        Pattern pattern = Pattern.compile("DELETE\\s+FROM", Pattern.CASE_INSENSITIVE);
        assertTrue(pattern.matcher(sql).find(), message);
    }

    /**
     * 验证SQL包含SELECT语句
     */
    public static void assertIsSelectStatement(String sql) {
        assertIsSelectStatement(sql, "SQL应为SELECT语句");
    }

    /**
     * 验证SQL包含SELECT语句（带自定义消息）
     */
    public static void assertIsSelectStatement(String sql, String message) {
        Pattern pattern = Pattern.compile("SELECT\\s+", Pattern.CASE_INSENSITIVE);
        assertTrue(pattern.matcher(sql).find(), message);
    }

    /**
     * 验证SQL不包含MySQL特有语法
     */
    public static void assertNotContainsMySqlSpecificSyntax(String sql) {
        assertFalse(sql.contains("ENGINE="), "不应包含MySQL的ENGINE语法");
        assertFalse(sql.contains("DEFAULT CHARSET"), "不应包含MySQL的DEFAULT CHARSET语法");
        assertFalse(sql.contains("AUTO_INCREMENT"), "不应包含MySQL的AUTO_INCREMENT语法");
        assertFalse(sql.contains("`"), "不应包含MySQL的反引号标识符");
    }

    /**
     * 验证达梦SQL特定语法
     */
    public static void assertDamengSpecificSyntax(String sql) {
        // 验证达梦特有的语法特征
        if (sql.contains("IDENTITY")) {
            // 根据达梦官方文档，IDENTITY语法为IDENTITY(start, increment)
            // 修正正则表达式以正确匹配IDENTITY(1,1)格式
            assertTrue(sql.matches("(?s).*IDENTITY\\s*\\(\\s*\\d+\\s*,\\s*\\d+\\s*\\).*"),
                "达梦IDENTITY语法应为IDENTITY(start, increment)格式");
        }
        
        if (sql.contains("CHARACTER SET")) {
            assertTrue(sql.contains("CHARACTER SET UTF8"), 
                "达梦应使用CHARACTER SET UTF8");
        }
    }

    /**
     * 验证金仓SQL特定语法
     */
    public static void assertKingbaseSpecificSyntax(String sql) {
        // 验证金仓特有的语法特征
        if (sql.contains("SERIAL")) {
            assertTrue(sql.matches(".*\\w+\\s+SERIAL.*"), 
                "金仓SERIAL语法应正确");
        }
        
        if (sql.contains("LIMIT")) {
            assertTrue(sql.matches(".*LIMIT\\s+\\d+.*"), 
                "金仓LIMIT语法应正确");
        }
    }

    /**
     * 验证神通SQL特定语法
     */
    public static void assertShentongSpecificSyntax(String sql) {
        // 验证神通特有的语法特征
        if (sql.contains("ROWNUM")) {
            assertTrue(sql.matches(".*ROWNUM\\s*<=?\\s*\\d+.*"), 
                "神通ROWNUM语法应正确");
        }
        
        if (sql.contains("DUAL")) {
            assertTrue(sql.contains("FROM DUAL"), 
                "神通DUAL表语法应正确");
        }
    }

    /**
     * 验证SQL语法的完整性
     */
    public static void assertSqlSyntaxIntegrity(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            fail("SQL不应为空");
        }
        
        // 验证括号匹配
        int openParens = 0;
        for (char c : sql.toCharArray()) {
            if (c == '(') openParens++;
            if (c == ')') openParens--;
            if (openParens < 0) {
                fail("括号不匹配：右括号多于左括号");
            }
        }
        if (openParens > 0) {
            fail("括号不匹配：左括号多于右括号");
        }
        
        // 验证引号匹配
        boolean inSingleQuote = false;
        boolean inDoubleQuote = false;
        for (int i = 0; i < sql.length(); i++) {
            char c = sql.charAt(i);
            if (c == '\'' && !inDoubleQuote) {
                inSingleQuote = !inSingleQuote;
            } else if (c == '"' && !inSingleQuote) {
                inDoubleQuote = !inDoubleQuote;
            }
        }
        if (inSingleQuote) {
            fail("单引号不匹配");
        }
        if (inDoubleQuote) {
            fail("双引号不匹配");
        }
        
        assertEndsWithSemicolon(sql);
    }
}
