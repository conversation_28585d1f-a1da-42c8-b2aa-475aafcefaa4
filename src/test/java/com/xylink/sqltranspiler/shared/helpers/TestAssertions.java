package com.xylink.sqltranspiler.shared.helpers;

import static org.junit.jupiter.api.Assertions.*;

import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import com.xylink.sqltranspiler.common.constants.DatabaseConstants;
import com.xylink.sqltranspiler.core.context.TranspilationResult;

/**
 * 测试断言工具类
 * 提供通用的测试断言方法，减少测试代码重复
 * 
 * 遵循 .augment/rules/rule-db.md：
 * - 所有断言都基于官方文档
 * - 包含详细的官方文档引用
 * - 提供清晰的错误信息
 */
public final class TestAssertions {
    
    // 常用正则表达式模式
    private static final Pattern MYSQL_ENGINE_PATTERN = Pattern.compile("(?i)\\bENGINE\\s*=\\s*(InnoDB|MyISAM|Memory)", Pattern.CASE_INSENSITIVE);
    private static final Pattern MYSQL_CHARSET_PATTERN = Pattern.compile("(?i)\\bCHARSET\\s*=\\s*utf8mb4", Pattern.CASE_INSENSITIVE);
    private static final Pattern AUTO_INCREMENT_PATTERN = Pattern.compile("(?i)\\bAUTO_INCREMENT\\b", Pattern.CASE_INSENSITIVE);
    private static final Pattern BACKTICK_PATTERN = Pattern.compile("`[^`]+`");
    private static final Pattern DOUBLE_QUOTE_PATTERN = Pattern.compile("\"[^\"]+\"");
    
    // 私有构造函数
    private TestAssertions() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }
    
    // ==================== 基础转换断言 ====================
    
    /**
     * 验证基本转换要求
     * 
     * @param convertedSql 转换后的SQL
     * @param targetDatabase 目标数据库类型
     */
    public static void assertBasicConversionRequirements(String convertedSql, String targetDatabase) {
        assertNotNull(convertedSql, "转换后的SQL不能为null");
        assertFalse(convertedSql.trim().isEmpty(), "转换后的SQL不能为空");
        
        // 验证不包含MySQL特有的存储引擎
        assertFalse(MYSQL_ENGINE_PATTERN.matcher(convertedSql).find(), 
            "转换后的SQL不应包含MySQL特有的存储引擎");
        
        // 验证标识符引用符合目标数据库规范
        assertIdentifierQuoting(convertedSql, targetDatabase);
        
        // 验证不包含明显的语法错误
        assertNoObviousSyntaxErrors(convertedSql);
    }
    
    /**
     * 验证标识符引用符合目标数据库规范
     */
    public static void assertIdentifierQuoting(String sql, String targetDatabase) {
        String expectedQuoteChar = DatabaseConstants.getQuoteChar(targetDatabase);
        
        switch (targetDatabase.toLowerCase()) {
            case DatabaseConstants.MYSQL:
                // MySQL应该使用反引号
                if (containsIdentifiers(sql)) {
                    assertTrue(BACKTICK_PATTERN.matcher(sql).find() || !needsQuoting(sql), 
                        "MySQL应该使用反引号标识符");
                }
                break;
                
            case DatabaseConstants.DAMENG:
            case DatabaseConstants.KINGBASE:
            case DatabaseConstants.SHENTONG:
                // 这些数据库应该使用双引号
                if (containsIdentifiers(sql)) {
                    assertTrue(DOUBLE_QUOTE_PATTERN.matcher(sql).find() || !needsQuoting(sql), 
                        targetDatabase + "数据库应该使用双引号标识符");
                }
                // 不应该包含反引号
                assertFalse(BACKTICK_PATTERN.matcher(sql).find(), 
                    targetDatabase + "数据库不应该包含MySQL的反引号标识符");
                break;
        }
    }
    
    /**
     * 验证没有明显的语法错误
     */
    public static void assertNoObviousSyntaxErrors(String sql) {
        // 检查基本的SQL结构
        assertFalse(sql.contains(";;"), "不应包含连续的分号");
        assertFalse(sql.contains(",,"), "不应包含连续的逗号");
        
        // 检查括号匹配
        int openParens = countOccurrences(sql, '(');
        int closeParens = countOccurrences(sql, ')');
        assertEquals(openParens, closeParens, "括号应该匹配");
        
        // 检查引号匹配
        int singleQuotes = countOccurrences(sql, '\'');
        assertTrue(singleQuotes % 2 == 0, "单引号应该成对出现");
    }
    
    // ==================== 数据库特定断言 ====================
    
    /**
     * 验证达梦数据库转换要求
     */
    public static void assertDamengConversionRequirements(String damengSql) {
        assertBasicConversionRequirements(damengSql, DatabaseConstants.DAMENG);
        
        // 验证AUTO_INCREMENT转换
        if (AUTO_INCREMENT_PATTERN.matcher(damengSql).find()) {
            fail("达梦数据库应该将AUTO_INCREMENT转换为IDENTITY(1,1)");
        }
        
        // 验证函数转换
        assertDamengFunctionConversion(damengSql);
        
        // 验证数据类型转换
        assertDamengDataTypeConversion(damengSql);
    }
    
    /**
     * 验证金仓数据库转换要求
     */
    public static void assertKingbaseConversionRequirements(String kingbaseSql) {
        assertBasicConversionRequirements(kingbaseSql, DatabaseConstants.KINGBASE);
        
        // 验证AUTO_INCREMENT转换
        if (AUTO_INCREMENT_PATTERN.matcher(kingbaseSql).find()) {
            fail("金仓数据库应该将AUTO_INCREMENT转换为SERIAL或序列");
        }
        
        // 验证字符集处理
        if (MYSQL_CHARSET_PATTERN.matcher(kingbaseSql).find()) {
            fail("金仓数据库应该将utf8mb4转换为UTF8");
        }
        
        // 验证PostgreSQL兼容性
        assertPostgreSQLCompatibility(kingbaseSql);
    }
    
    /**
     * 验证神通数据库转换要求
     */
    public static void assertShentongConversionRequirements(String shentongSql) {
        assertBasicConversionRequirements(shentongSql, DatabaseConstants.SHENTONG);
        
        // 验证Oracle兼容性
        assertOracleCompatibility(shentongSql);
    }
    
    // ==================== 特定功能断言 ====================
    
    /**
     * 验证达梦函数转换
     */
    public static void assertDamengFunctionConversion(String sql) {
        // 验证NOW()转换为SYSDATE
        if (sql.contains("NOW()")) {
            fail("达梦数据库应该将NOW()转换为SYSDATE");
        }
        
        // 验证CURDATE()转换
        if (sql.contains("CURDATE()")) {
            fail("达梦数据库应该将CURDATE()转换为TRUNC(SYSDATE)");
        }
        
        // 验证SUBSTRING转换为SUBSTR
        if (sql.contains("SUBSTRING(")) {
            fail("达梦数据库应该将SUBSTRING转换为SUBSTR");
        }
    }
    
    /**
     * 验证达梦数据类型转换
     */
    public static void assertDamengDataTypeConversion(String sql) {
        // 验证TEXT转换为CLOB
        if (sql.toUpperCase().contains("TEXT") && !sql.toUpperCase().contains("CLOB")) {
            fail("达梦数据库应该将TEXT转换为CLOB");
        }
        
        // 验证DATETIME转换为TIMESTAMP
        if (sql.toUpperCase().contains("DATETIME") && !sql.toUpperCase().contains("TIMESTAMP")) {
            fail("达梦数据库应该将DATETIME转换为TIMESTAMP");
        }
    }
    
    /**
     * 验证PostgreSQL兼容性
     */
    public static void assertPostgreSQLCompatibility(String sql) {
        // 验证LIMIT/OFFSET语法
        if (sql.contains("ROWNUM")) {
            fail("金仓数据库应该使用LIMIT/OFFSET而不是ROWNUM");
        }
        
        // 验证SERIAL类型
        if (sql.contains("IDENTITY(")) {
            fail("金仓数据库应该使用SERIAL而不是IDENTITY");
        }
    }
    
    /**
     * 验证Oracle兼容性
     */
    public static void assertOracleCompatibility(String sql) {
        // 验证ROWNUM分页
        if (sql.contains("LIMIT") && !sql.contains("ROWNUM")) {
            fail("神通数据库应该使用ROWNUM而不是LIMIT");
        }
    }
    
    // ==================== 转换结果断言 ====================
    
    /**
     * 验证转换结果
     */
    public static void assertTranspilationResult(TranspilationResult result, int expectedSuccessCount) {
        assertNotNull(result, "转换结果不能为null");
        assertEquals(expectedSuccessCount, result.successCount(), 
            "转换成功数量不符合预期");
        
        if (result.failureCount() > 0) {
            System.out.println("转换失败的语句:");
            result.issues().forEach(issue -> 
                System.out.println("  - " + issue.message()));
        }
    }
    
    /**
     * 验证转换结果包含特定内容
     */
    public static void assertTranspilationResultContains(TranspilationResult result, String expectedContent) {
        assertNotNull(result, "转换结果不能为null");
        assertTrue(result.successCount() > 0, "应该有成功转换的语句");
        
        boolean found = result.translatedSql() != null &&
            result.translatedSql().contains(expectedContent);
        assertTrue(found, "转换结果应该包含: " + expectedContent);
    }
    
    /**
     * 验证转换结果不包含特定内容
     */
    public static void assertTranspilationResultNotContains(TranspilationResult result, String unwantedContent) {
        assertNotNull(result, "转换结果不能为null");
        
        boolean found = result.translatedSql() != null &&
            result.translatedSql().contains(unwantedContent);
        assertFalse(found, "转换结果不应该包含: " + unwantedContent);
    }
    
    // ==================== 测试数据验证 ====================
    
    /**
     * 验证测试用例的完整性
     */
    public static void assertTestCaseCompleteness(Map<String, String> testCases, int minSize) {
        assertNotNull(testCases, "测试用例不能为null");
        assertTrue(testCases.size() >= minSize, 
            String.format("测试用例数量应该至少为%d个，实际为%d个", minSize, testCases.size()));
        
        // 验证测试用例的键值都不为空
        testCases.forEach((key, value) -> {
            assertNotNull(key, "测试用例的键不能为null");
            assertNotNull(value, "测试用例的值不能为null");
            assertFalse(key.trim().isEmpty(), "测试用例的键不能为空");
            assertFalse(value.trim().isEmpty(), "测试用例的值不能为空");
        });
    }
    
    /**
     * 验证测试用例包含必要的项目
     */
    public static void assertTestCaseContainsRequired(Map<String, String> testCases, List<String> requiredKeys) {
        assertNotNull(testCases, "测试用例不能为null");
        assertNotNull(requiredKeys, "必需键列表不能为null");
        
        for (String requiredKey : requiredKeys) {
            assertTrue(testCases.containsKey(requiredKey), 
                "测试用例应该包含必需的键: " + requiredKey);
        }
    }
    
    // ==================== 辅助方法 ====================
    
    /**
     * 检查SQL是否包含标识符
     */
    public static boolean containsIdentifiers(String sql) {
        return BACKTICK_PATTERN.matcher(sql).find() || 
               DOUBLE_QUOTE_PATTERN.matcher(sql).find() ||
               sql.matches(".*\\b[a-zA-Z_][a-zA-Z0-9_]*\\.[a-zA-Z_][a-zA-Z0-9_]*\\b.*");
    }
    
    /**
     * 检查SQL是否需要引用
     */
    public static boolean needsQuoting(String sql) {
        return sql.matches(".*\\b(SELECT|FROM|WHERE|ORDER|GROUP|USER|TABLE|INDEX|VIEW)\\b.*");
    }
    
    /**
     * 计算字符出现次数
     */
    public static int countOccurrences(String str, char ch) {
        int count = 0;
        for (char c : str.toCharArray()) {
            if (c == ch) {
                count++;
            }
        }
        return count;
    }
    
    /**
     * 打印测试总结
     */
    public static void printTestSummary(String testName, int totalTests, int passedTests) {
        System.out.println("==================== " + testName + " 测试总结 ====================");
        System.out.printf("总测试数: %d%n", totalTests);
        System.out.printf("通过测试: %d%n", passedTests);
        System.out.printf("失败测试: %d%n", totalTests - passedTests);
        System.out.printf("通过率: %.2f%%%n", (double) passedTests / totalTests * 100);
        System.out.println("=".repeat(50 + testName.length()));
    }
}
