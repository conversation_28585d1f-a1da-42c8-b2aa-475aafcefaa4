package com.xylink.sqltranspiler.shared.helpers;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 测试SQL辅助工具类
 * 
 * 提供测试中常用的SQL处理功能：
 * 1. SQL文件加载和保存
 * 2. SQL语句格式化和清理
 * 3. SQL语句比较和验证
 * 4. 测试数据生成
 * 
 * <AUTHOR>
 */
public class TestSqlHelper {

    /**
     * 从资源文件加载SQL内容
     * 
     * @param resourcePath 资源文件路径
     * @return SQL内容
     * @throws IOException 文件读取异常
     */
    public static String loadSqlFromResource(String resourcePath) throws IOException {
        Path filePath = Paths.get("src/test/resources", resourcePath);
        if (!Files.exists(filePath)) {
            throw new IOException("SQL资源文件不存在: " + resourcePath);
        }
        return Files.readString(filePath);
    }

    /**
     * 保存SQL内容到文件
     * 
     * @param sql SQL内容
     * @param filePath 文件路径
     * @throws IOException 文件写入异常
     */
    public static void saveSqlToFile(String sql, String filePath) throws IOException {
        Path path = Paths.get(filePath);
        Files.createDirectories(path.getParent());
        Files.writeString(path, sql);
    }

    /**
     * 清理SQL语句中的注释和空行
     * 
     * @param sql 原始SQL
     * @return 清理后的SQL
     */
    public static String cleanSql(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return "";
        }
        
        return Arrays.stream(sql.split("\n"))
                .map(String::trim)
                .filter(line -> !line.isEmpty())
                .filter(line -> !line.startsWith("--"))
                .filter(line -> !line.startsWith("/*"))
                .collect(Collectors.joining("\n"));
    }

    /**
     * 标准化SQL语句格式
     * 
     * @param sql 原始SQL
     * @return 标准化后的SQL
     */
    public static String normalizeSql(String sql) {
        if (sql == null) {
            return "";
        }
        
        return sql.trim()
                .replaceAll("\\s+", " ")  // 多个空格替换为单个空格
                .replaceAll("\\s*;\\s*$", ";")  // 确保以分号结尾
                .replaceAll("\\s*,\\s*", ", ")  // 标准化逗号后的空格
                .replaceAll("\\s*\\(\\s*", "(")  // 标准化左括号
                .replaceAll("\\s*\\)\\s*", ")")  // 标准化右括号
                .toUpperCase();  // 转换为大写以便比较
    }

    /**
     * 比较两个SQL语句是否语义相等
     * 
     * @param sql1 第一个SQL语句
     * @param sql2 第二个SQL语句
     * @return 是否相等
     */
    public static boolean sqlEquals(String sql1, String sql2) {
        if (sql1 == null && sql2 == null) {
            return true;
        }
        if (sql1 == null || sql2 == null) {
            return false;
        }
        
        String normalized1 = normalizeSql(sql1);
        String normalized2 = normalizeSql(sql2);
        
        return normalized1.equals(normalized2);
    }

    /**
     * 分割多语句SQL为单个语句列表
     * 
     * @param multiSql 多语句SQL
     * @return 单个语句列表
     */
    public static List<String> splitStatements(String multiSql) {
        if (multiSql == null || multiSql.trim().isEmpty()) {
            return List.of();
        }
        
        return Arrays.stream(multiSql.split(";"))
                .map(String::trim)
                .filter(stmt -> !stmt.isEmpty())
                .map(stmt -> stmt + ";")  // 重新添加分号
                .collect(Collectors.toList());
    }

    /**
     * 生成CREATE TABLE测试SQL
     * 
     * @param tableName 表名
     * @param columns 列定义列表
     * @return CREATE TABLE SQL
     */
    public static String generateCreateTableSql(String tableName, List<String> columns) {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE TABLE ").append(tableName).append(" (\n");
        
        for (int i = 0; i < columns.size(); i++) {
            sql.append("    ").append(columns.get(i));
            if (i < columns.size() - 1) {
                sql.append(",");
            }
            sql.append("\n");
        }
        
        sql.append(");");
        return sql.toString();
    }

    /**
     * 生成INSERT测试SQL
     * 
     * @param tableName 表名
     * @param columns 列名列表
     * @param values 值列表
     * @return INSERT SQL
     */
    public static String generateInsertSql(String tableName, List<String> columns, List<String> values) {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO ").append(tableName);
        
        if (!columns.isEmpty()) {
            sql.append(" (").append(String.join(", ", columns)).append(")");
        }
        
        sql.append(" VALUES (").append(String.join(", ", values)).append(");");
        return sql.toString();
    }

    /**
     * 验证SQL语句的基本语法正确性
     * 
     * @param sql SQL语句
     * @return 验证结果消息
     */
    public static String validateSqlSyntax(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return "SQL语句为空";
        }
        
        String trimmed = sql.trim();
        
        // 基本语法检查
        if (!trimmed.endsWith(";")) {
            return "SQL语句应以分号结尾";
        }
        
        // 检查括号匹配
        int openParens = 0;
        for (char c : trimmed.toCharArray()) {
            if (c == '(') openParens++;
            if (c == ')') openParens--;
            if (openParens < 0) {
                return "括号不匹配：右括号多于左括号";
            }
        }
        if (openParens > 0) {
            return "括号不匹配：左括号多于右括号";
        }
        
        // 检查引号匹配
        boolean inSingleQuote = false;
        boolean inDoubleQuote = false;
        for (int i = 0; i < trimmed.length(); i++) {
            char c = trimmed.charAt(i);
            if (c == '\'' && !inDoubleQuote) {
                inSingleQuote = !inSingleQuote;
            } else if (c == '"' && !inSingleQuote) {
                inDoubleQuote = !inDoubleQuote;
            }
        }
        if (inSingleQuote) {
            return "单引号不匹配";
        }
        if (inDoubleQuote) {
            return "双引号不匹配";
        }
        
        return "语法检查通过";
    }

    /**
     * 提取SQL语句中的表名
     * 
     * @param sql SQL语句
     * @return 表名列表
     */
    public static List<String> extractTableNames(String sql) {
        // 简单的表名提取，可以根据需要扩展
        return Arrays.stream(sql.split("\\s+"))
                .filter(word -> word.matches("\\w+"))
                .filter(word -> sql.contains("FROM " + word) || 
                               sql.contains("INTO " + word) || 
                               sql.contains("TABLE " + word) ||
                               sql.contains("UPDATE " + word))
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 检查SQL是否包含特定的数据库特性
     * 
     * @param sql SQL语句
     * @param feature 特性关键字
     * @return 是否包含该特性
     */
    public static boolean containsFeature(String sql, String feature) {
        if (sql == null || feature == null) {
            return false;
        }
        
        return sql.toUpperCase().contains(feature.toUpperCase());
    }

    /**
     * 生成测试用的随机表名
     * 
     * @param prefix 前缀
     * @return 随机表名
     */
    public static String generateRandomTableName(String prefix) {
        long timestamp = System.currentTimeMillis();
        return prefix + "_" + timestamp;
    }
}
