package com.xylink.sqltranspiler.shared.helpers;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 官方文档测试数据验证器
 * 
 * 基于官方文档验证测试SQL数据的质量和准确性
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/index.html
 * - 神通官方文档：@shentong.md
 * 
 * 严格遵循数据库规则：
 * 1. 必须遵守数据库规则，不允许推测，必须查看官方文档
 * 2. 动态验证机制：所有验证都基于官方文档的动态验证方法
 * 3. 官方文档引用体系：每个验证方法都包含详细的官方文档链接
 */
public class OfficialDocumentationTestDataValidator {
    
    private static final Logger logger = LoggerFactory.getLogger(OfficialDocumentationTestDataValidator.class);
    
    // 官方文档引用模式
    private static final Pattern OFFICIAL_DOC_PATTERN = Pattern.compile(
        "-- 官方文档：(https?://[^\\s]+)", Pattern.CASE_INSENSITIVE);
    
    // MySQL官方文档域名验证
    private static final Pattern MYSQL_OFFICIAL_DOMAIN = Pattern.compile(
        "https://dev\\.mysql\\.com/doc/refman/[0-9.]+/en/.*", Pattern.CASE_INSENSITIVE);
    
    // 达梦官方文档域名验证
    private static final Pattern DAMENG_OFFICIAL_DOMAIN = Pattern.compile(
        "https://eco\\.dameng\\.com/document/dm/zh-cn/.*", Pattern.CASE_INSENSITIVE);
    
    // 金仓官方文档域名验证
    private static final Pattern KINGBASE_OFFICIAL_DOMAIN = Pattern.compile(
        "https://help\\.kingbase\\.com\\.cn/v8/.*", Pattern.CASE_INSENSITIVE);
    
    /**
     * 验证测试SQL文件的质量
     * 
     * @param sqlFilePath SQL文件路径
     * @return 验证结果
     */
    public static TestDataValidationResult validateTestSqlFile(String sqlFilePath) {
        logger.info("开始验证测试SQL文件: {}", sqlFilePath);
        
        TestDataValidationResult result = new TestDataValidationResult(sqlFilePath);
        
        try {
            Path filePath = Paths.get(sqlFilePath);
            if (!Files.exists(filePath)) {
                result.addError("文件不存在: " + sqlFilePath);
                return result;
            }
            
            String content = Files.readString(filePath);
            
            // 验证官方文档引用
            validateOfficialDocumentationReferences(content, result);
            
            // 验证SQL语法质量
            validateSqlSyntaxQuality(content, result);
            
            // 验证数据库特性覆盖
            validateDatabaseFeatureCoverage(content, result);
            
            // 验证注释质量
            validateCommentQuality(content, result);
            
            // 验证测试数据完整性
            validateTestDataCompleteness(content, result);
            
        } catch (IOException e) {
            result.addError("文件读取失败: " + e.getMessage());
        }
        
        logger.info("测试SQL文件验证完成: {}, 错误: {}, 警告: {}", 
            sqlFilePath, result.getErrors().size(), result.getWarnings().size());
        
        return result;
    }
    
    /**
     * 验证官方文档引用的准确性
     */
    private static void validateOfficialDocumentationReferences(String content, TestDataValidationResult result) {
        logger.debug("验证官方文档引用");
        
        Matcher matcher = OFFICIAL_DOC_PATTERN.matcher(content);
        int referenceCount = 0;
        
        while (matcher.find()) {
            referenceCount++;
            String docUrl = matcher.group(1);
            
            // 验证URL格式和域名
            if (!isValidOfficialDocumentationUrl(docUrl)) {
                result.addError("无效的官方文档URL: " + docUrl);
            } else {
                result.addInfo("发现有效官方文档引用: " + docUrl);
            }
        }
        
        if (referenceCount == 0) {
            result.addWarning("未发现官方文档引用，建议添加官方文档链接");
        } else {
            result.addInfo("官方文档引用数量: " + referenceCount);
        }
    }
    
    /**
     * 验证SQL语法质量
     */
    private static void validateSqlSyntaxQuality(String content, TestDataValidationResult result) {
        logger.debug("验证SQL语法质量");
        
        String[] lines = content.split("\n");
        int sqlStatementCount = 0;
        int commentLineCount = 0;
        
        for (int i = 0; i < lines.length; i++) {
            String line = lines[i].trim();
            
            if (line.isEmpty()) {
                continue;
            }
            
            if (line.startsWith("--") || line.startsWith("/*")) {
                commentLineCount++;
                continue;
            }
            
            if (line.endsWith(";")) {
                sqlStatementCount++;
                
                // 验证SQL语句格式
                validateSqlStatementFormat(line, i + 1, result);
            }
        }
        
        result.addInfo("SQL语句数量: " + sqlStatementCount);
        result.addInfo("注释行数量: " + commentLineCount);
        
        // 验证注释与代码比例
        if (commentLineCount == 0) {
            result.addWarning("缺少注释，建议添加说明性注释");
        } else {
            double commentRatio = (double) commentLineCount / (commentLineCount + sqlStatementCount);
            if (commentRatio < 0.2) {
                result.addWarning("注释比例较低 (" + String.format("%.1f%%", commentRatio * 100) + 
                    ")，建议增加注释");
            }
        }
    }
    
    /**
     * 验证数据库特性覆盖
     */
    private static void validateDatabaseFeatureCoverage(String content, TestDataValidationResult result) {
        logger.debug("验证数据库特性覆盖");
        
        Map<String, Boolean> featureCoverage = new HashMap<>();
        
        // MySQL核心特性检查（基于官方文档）
        featureCoverage.put("CREATE TABLE", content.toUpperCase().contains("CREATE TABLE"));
        featureCoverage.put("AUTO_INCREMENT", content.toUpperCase().contains("AUTO_INCREMENT"));
        featureCoverage.put("PRIMARY KEY", content.toUpperCase().contains("PRIMARY KEY"));
        featureCoverage.put("FOREIGN KEY", content.toUpperCase().contains("FOREIGN KEY"));
        featureCoverage.put("INDEX", content.toUpperCase().contains("INDEX"));
        featureCoverage.put("UNIQUE", content.toUpperCase().contains("UNIQUE"));
        featureCoverage.put("CHECK", content.toUpperCase().contains("CHECK"));
        featureCoverage.put("DEFAULT", content.toUpperCase().contains("DEFAULT"));
        featureCoverage.put("NOT NULL", content.toUpperCase().contains("NOT NULL"));
        featureCoverage.put("TIMESTAMP", content.toUpperCase().contains("TIMESTAMP"));
        featureCoverage.put("VARCHAR", content.toUpperCase().contains("VARCHAR"));
        featureCoverage.put("DECIMAL", content.toUpperCase().contains("DECIMAL"));
        featureCoverage.put("ENUM", content.toUpperCase().contains("ENUM"));
        featureCoverage.put("JSON", content.toUpperCase().contains("JSON"));
        featureCoverage.put("VIEW", content.toUpperCase().contains("VIEW"));
        featureCoverage.put("PROCEDURE", content.toUpperCase().contains("PROCEDURE"));
        featureCoverage.put("FUNCTION", content.toUpperCase().contains("FUNCTION"));
        featureCoverage.put("TRIGGER", content.toUpperCase().contains("TRIGGER"));
        
        int coveredFeatures = 0;
        for (Map.Entry<String, Boolean> entry : featureCoverage.entrySet()) {
            if (entry.getValue()) {
                coveredFeatures++;
                result.addInfo("覆盖特性: " + entry.getKey());
            }
        }
        
        double coverageRate = (double) coveredFeatures / featureCoverage.size() * 100;
        result.addInfo("特性覆盖率: " + String.format("%.1f%%", coverageRate) + 
            " (" + coveredFeatures + "/" + featureCoverage.size() + ")");
        
        if (coverageRate < 50.0) {
            result.addWarning("特性覆盖率较低，建议增加更多数据库特性的测试用例");
        }
    }
    
    /**
     * 验证注释质量
     */
    private static void validateCommentQuality(String content, TestDataValidationResult result) {
        logger.debug("验证注释质量");
        
        String[] lines = content.split("\n");
        int meaningfulComments = 0;
        int totalComments = 0;
        
        for (String line : lines) {
            String trimmed = line.trim();
            if (trimmed.startsWith("--") || trimmed.startsWith("/*")) {
                totalComments++;
                
                // 检查注释是否包含有意义的信息
                if (containsMeaningfulContent(trimmed)) {
                    meaningfulComments++;
                }
            }
        }
        
        if (totalComments > 0) {
            double meaningfulRatio = (double) meaningfulComments / totalComments * 100;
            result.addInfo("有意义注释比例: " + String.format("%.1f%%", meaningfulRatio) + 
                " (" + meaningfulComments + "/" + totalComments + ")");
            
            if (meaningfulRatio < 80.0) {
                result.addWarning("注释质量有待提升，建议增加更多说明性内容");
            }
        }
    }
    
    /**
     * 验证测试数据完整性
     */
    private static void validateTestDataCompleteness(String content, TestDataValidationResult result) {
        logger.debug("验证测试数据完整性");
        
        // 检查是否包含完整的测试场景
        boolean hasPositiveCase = content.contains("CREATE TABLE") || content.contains("INSERT");
        boolean hasNegativeCase = content.contains("DROP") || content.contains("DELETE");
        boolean hasComplexCase = content.contains("JOIN") || content.contains("SUBQUERY") || 
                                content.contains("UNION");
        
        if (hasPositiveCase) {
            result.addInfo("包含正向测试用例");
        } else {
            result.addWarning("缺少正向测试用例");
        }
        
        if (hasNegativeCase) {
            result.addInfo("包含负向测试用例");
        }
        
        if (hasComplexCase) {
            result.addInfo("包含复杂测试用例");
        }
        
        // 检查数据类型覆盖
        String[] dataTypes = {"INT", "VARCHAR", "DECIMAL", "TIMESTAMP", "DATE", "TEXT", "BOOLEAN"};
        int coveredTypes = 0;
        
        for (String dataType : dataTypes) {
            if (content.toUpperCase().contains(dataType)) {
                coveredTypes++;
            }
        }
        
        double typesCoverage = (double) coveredTypes / dataTypes.length * 100;
        result.addInfo("数据类型覆盖率: " + String.format("%.1f%%", typesCoverage) + 
            " (" + coveredTypes + "/" + dataTypes.length + ")");
    }
    
    /**
     * 验证SQL语句格式
     */
    private static void validateSqlStatementFormat(String statement, int lineNumber, TestDataValidationResult result) {
        // 检查SQL语句的基本格式要求
        if (statement.length() > 1000) {
            result.addWarning("第" + lineNumber + "行SQL语句过长，建议分行");
        }
        
        // 检查是否使用了推荐的格式
        if (statement.toUpperCase().contains("CREATE TABLE") && !statement.contains("(")) {
            result.addError("第" + lineNumber + "行CREATE TABLE语句格式不正确");
        }
    }
    
    /**
     * 验证是否为有效的官方文档URL
     */
    private static boolean isValidOfficialDocumentationUrl(String url) {
        return MYSQL_OFFICIAL_DOMAIN.matcher(url).matches() ||
               DAMENG_OFFICIAL_DOMAIN.matcher(url).matches() ||
               KINGBASE_OFFICIAL_DOMAIN.matcher(url).matches() ||
               url.contains("@shentong.md"); // 神通文档特殊标记
    }
    
    /**
     * 检查注释是否包含有意义的内容
     */
    private static boolean containsMeaningfulContent(String comment) {
        // 移除注释符号
        String content = comment.replaceAll("^--\\s*", "").replaceAll("^/\\*\\s*", "").replaceAll("\\s*\\*/$", "");
        
        // 检查是否包含有意义的关键词
        String[] meaningfulKeywords = {
            "基于", "官方文档", "转换", "验证", "示例", "规范", "标准", "兼容", "支持",
            "based on", "official", "documentation", "convert", "example", "standard"
        };
        
        for (String keyword : meaningfulKeywords) {
            if (content.toLowerCase().contains(keyword.toLowerCase())) {
                return true;
            }
        }
        
        // 检查是否包含URL
        if (content.contains("http://") || content.contains("https://")) {
            return true;
        }
        
        // 检查长度（过短的注释通常意义不大）
        return content.trim().length() > 10;
    }
    
    /**
     * 测试数据验证结果
     */
    public static class TestDataValidationResult {
        private final String filePath;
        private final List<String> errors = new ArrayList<>();
        private final List<String> warnings = new ArrayList<>();
        private final List<String> infos = new ArrayList<>();
        
        public TestDataValidationResult(String filePath) {
            this.filePath = filePath;
        }
        
        public void addError(String error) {
            errors.add(error);
            logger.error("验证错误: {}", error);
        }
        
        public void addWarning(String warning) {
            warnings.add(warning);
            logger.warn("验证警告: {}", warning);
        }
        
        public void addInfo(String info) {
            infos.add(info);
            logger.debug("验证信息: {}", info);
        }
        
        public boolean isValid() {
            return errors.isEmpty();
        }
        
        public String getFilePath() { return filePath; }
        public List<String> getErrors() { return errors; }
        public List<String> getWarnings() { return warnings; }
        public List<String> getInfos() { return infos; }
        
        public String getSummary() {
            StringBuilder summary = new StringBuilder();
            summary.append("文件: ").append(filePath).append("\n");
            summary.append("错误: ").append(errors.size()).append("\n");
            summary.append("警告: ").append(warnings.size()).append("\n");
            summary.append("信息: ").append(infos.size()).append("\n");
            
            if (!errors.isEmpty()) {
                summary.append("\n错误详情:\n");
                for (String error : errors) {
                    summary.append("  - ").append(error).append("\n");
                }
            }
            
            if (!warnings.isEmpty()) {
                summary.append("\n警告详情:\n");
                for (String warning : warnings) {
                    summary.append("  - ").append(warning).append("\n");
                }
            }
            
            return summary.toString();
        }
    }
}
