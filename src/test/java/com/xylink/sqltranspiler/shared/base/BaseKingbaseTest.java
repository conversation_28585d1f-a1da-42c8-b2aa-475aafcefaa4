package com.xylink.sqltranspiler.shared.base;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;

import com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator;

/**
 * 金仓数据库测试基类
 * 
 * 提供金仓数据库转换测试的专用功能：
 * 1. 初始化金仓生成器
 * 2. 提供金仓特定的断言方法
 * 3. 提供金仓转换的便捷方法
 * 
 * 严格遵循金仓官方文档：
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/index.html
 * - MySQL迁移最佳实践：https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/index.html
 * - 兼容性说明：https://help.kingbase.com.cn/v8/development/develop-transfer/kes-vs-mysql/index.html
 * 
 * <AUTHOR>
 */
public abstract class BaseKingbaseTest extends BaseTranspilerTest {

    protected KingbaseGenerator generator;

    @BeforeEach
    @Override
    protected void setUp() {
        super.setUp();
        generator = new KingbaseGenerator();
        generator.clearState(); // 清理状态，确保测试独立性
    }

    /**
     * 转换MySQL SQL为金仓SQL
     */
    protected String convertMySqlToKingbase(String mysqlSql) {
        return convertMySqlTo(mysqlSql, "kingbase");
    }

    /**
     * 验证金仓特定的转换要求
     * 基于金仓官方文档的要求
     */
    protected void assertKingbaseConversionRequirements(String kingbaseSql) {
        assertBasicConversionRequirements(kingbaseSql);
        
        // 根据金仓官方文档，验证标识符规范
        // 金仓数据库使用双引号标识符，但只有保留字、包含特殊字符或大写字母的标识符才需要双引号
        // 普通的小写标识符（如 users, products）不需要双引号
        // 参考：https://help.kingbase.com.cn/v9/development/sql-plsql/compatibility-parameter/compatible-MySQL.html
        // ANSI_QUOTES：表示双引号返回标示符

        // 验证不包含MySQL的反引号标识符
        assertFalse(kingbaseSql.contains("`"), "不应包含MySQL的反引号标识符");
        
        // 验证不包含MySQL特有的存储引擎
        assertFalse(kingbaseSql.contains("ENGINE=InnoDB"), "不应包含MySQL的存储引擎");
        assertFalse(kingbaseSql.contains("ENGINE=MyISAM"), "不应包含MySQL的存储引擎");
        
        // 验证字符集处理
        if (kingbaseSql.contains("utf8mb4")) {
            log.warn("发现utf8mb4字符集，金仓可能需要转换为UTF8");
        }
        
        // 验证AUTO_INCREMENT转换
        if (kingbaseSql.contains("AUTO_INCREMENT")) {
            log.warn("发现未转换的AUTO_INCREMENT，金仓应转换为SERIAL或使用序列");
        }
    }

    /**
     * 验证金仓数据类型转换
     */
    protected void assertKingbaseDataTypeConversions(String kingbaseSql) {
        assertDataTypeConversions(kingbaseSql);
        
        // 金仓特定的数据类型验证
        if (kingbaseSql.contains("TINYINT")) {
            log.warn("发现TINYINT类型，金仓可能需要转换为SMALLINT");
        }
        
        if (kingbaseSql.contains("MEDIUMINT")) {
            log.warn("发现MEDIUMINT类型，金仓应转换为INTEGER");
        }
        
        if (kingbaseSql.contains("LONGTEXT")) {
            log.warn("发现LONGTEXT类型，金仓应转换为TEXT");
        }
    }

    /**
     * 验证金仓函数转换
     */
    protected void assertKingbaseFunctionConversions(String kingbaseSql) {
        // 检查MySQL特有函数是否正确转换
        if (kingbaseSql.contains("IFNULL")) {
            log.warn("发现未转换的IFNULL函数，金仓应转换为COALESCE");
        }
        
        if (kingbaseSql.contains("CONCAT")) {
            // 金仓支持CONCAT函数，但也可以使用||操作符
            log.info("发现CONCAT函数，金仓支持但也可使用||操作符");
        }
        
        if (kingbaseSql.contains("DATE_FORMAT")) {
            log.warn("发现未转换的DATE_FORMAT函数，金仓应转换为TO_CHAR");
        }
        
        if (kingbaseSql.contains("LIMIT")) {
            // 金仓支持LIMIT语法
            log.info("发现LIMIT语法，金仓原生支持");
        }
    }

    /**
     * 验证金仓索引处理
     */
    protected void assertKingbaseIndexHandling(String kingbaseSql) {
        if (kingbaseSql.contains("CREATE INDEX")) {
            // 验证索引名称生成
            assertTrue(kingbaseSql.matches(".*CREATE\\s+INDEX\\s+\\w+.*"), 
                "金仓索引应有明确的索引名");
        }
        
        if (kingbaseSql.contains("UNIQUE KEY")) {
            log.warn("发现UNIQUE KEY语法，金仓应转换为CREATE UNIQUE INDEX");
        }
        
        if (kingbaseSql.contains("KEY ")) {
            log.warn("发现KEY语法，金仓应转换为CREATE INDEX");
        }
    }

    /**
     * 验证金仓分页查询
     */
    protected void assertKingbasePaginationSupport(String kingbaseSql) {
        if (kingbaseSql.contains("LIMIT")) {
            // 金仓原生支持LIMIT语法
            assertTrue(kingbaseSql.matches(".*LIMIT\\s+\\d+.*"), 
                "金仓LIMIT语法应正确");
            
            if (kingbaseSql.contains("OFFSET")) {
                assertTrue(kingbaseSql.matches(".*LIMIT\\s+\\d+\\s+OFFSET\\s+\\d+.*"), 
                    "金仓LIMIT OFFSET语法应正确");
            }
        }
    }

    /**
     * 验证金仓约束处理
     */
    protected void assertKingbaseConstraintHandling(String kingbaseSql) {
        assertConstraintSeparation(kingbaseSql);
        
        // 金仓特定的约束验证
        if (kingbaseSql.contains("CHECK")) {
            log.info("发现CHECK约束，金仓原生支持");
        }
        
        if (kingbaseSql.contains("FOREIGN KEY")) {
            assertTrue(kingbaseSql.contains("REFERENCES"), 
                "外键约束应包含REFERENCES子句");
        }
    }

    /**
     * 验证金仓PostgreSQL兼容性特性
     */
    protected void assertKingbasePostgreSQLCompatibility(String kingbaseSql) {
        // 金仓基于PostgreSQL，应支持PostgreSQL语法
        if (kingbaseSql.contains("SERIAL")) {
            log.info("发现SERIAL类型，金仓支持PostgreSQL兼容语法");
        }
        
        if (kingbaseSql.contains("BOOLEAN")) {
            log.info("发现BOOLEAN类型，金仓支持PostgreSQL兼容语法");
        }
        
        if (kingbaseSql.contains("::")) {
            log.info("发现PostgreSQL类型转换语法，金仓应支持");
        }
    }


}
