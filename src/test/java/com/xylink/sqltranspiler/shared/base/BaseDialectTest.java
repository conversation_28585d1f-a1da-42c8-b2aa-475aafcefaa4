package com.xylink.sqltranspiler.shared.base;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.MethodOrderer.OrderAnnotation;
import org.junit.jupiter.api.Order;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Map;
import java.util.Arrays;
import java.util.List;

import com.xylink.sqltranspiler.core.dialects.SqlDialect;
import com.xylink.sqltranspiler.core.dialects.Generator;
import com.xylink.sqltranspiler.core.functions.FunctionMapper;

/**
 * 方言测试基类
 * 借鉴Apache Calcite的测试框架设计，提供标准化的方言测试能力
 * 
 * 设计原则：
 * 1. 基于官方文档验证方言实现的准确性
 * 2. 标准化测试用例，减少代码重复
 * 3. 支持动态验证机制
 * 4. 提供清晰的成功/警告/错误信息
 * 
 * 遵循 .augment/rules/rule-db.md：
 * - 必须基于官方文档，不允许推测
 * - 动态验证机制：所有测试用例都使用基于官方文档的动态验证方法
 * - 官方文档引用体系：每个验证方法都包含详细的官方文档链接
 * - 质量反馈机制：提供清晰的成功/警告/错误信息
 */
@TestMethodOrder(OrderAnnotation.class)
public abstract class BaseDialectTest {
    
    protected SqlDialect dialect;
    protected Generator generator;
    protected FunctionMapper functionMapper;
    
    @BeforeEach
    void setUp() {
        dialect = createDialect();
        generator = createGenerator();
        functionMapper = new FunctionMapper();
        
        // 验证方言实例创建成功
        assertNotNull(dialect, "方言实例不能为null");
        assertNotNull(generator, "生成器实例不能为null");
        assertNotNull(functionMapper, "函数映射器实例不能为null");
    }
    
    // ==================== 抽象方法 - 子类必须实现 ====================
    
    /**
     * 创建具体的方言实例
     * @return 方言实例
     */
    protected abstract SqlDialect createDialect();
    
    /**
     * 创建具体的生成器实例
     * @return 生成器实例
     */
    protected abstract Generator createGenerator();
    
    /**
     * 获取数据类型测试用例
     * 基于官方文档定义的数据类型映射
     * @return 测试用例映射：MySQL类型 -> 目标数据库类型
     */
    protected abstract Map<String, String> getDataTypeTestCases();
    
    /**
     * 获取函数映射测试用例
     * 基于官方文档定义的函数映射
     * @return 测试用例映射：MySQL函数 -> 目标数据库函数
     */
    protected abstract Map<String, String> getFunctionTestCases();
    
    /**
     * 获取分页语法测试用例
     * 基于官方文档定义的分页语法
     * @return 测试用例映射：(limit, offset) -> 预期SQL
     */
    protected abstract Map<String, String> getPaginationTestCases();
    
    /**
     * 获取官方文档链接
     * 用于测试失败时提供参考文档
     * @return 官方文档URL
     */
    protected abstract String getOfficialDocumentationUrl();
    
    // ==================== 标准化测试方法 ====================
    
    @Test
    @Order(1)
    void testBasicDialectInfo() {
        // 测试基础方言信息
        assertNotNull(dialect.getName(), "方言名称不能为null");
        assertNotNull(dialect.getDatabaseProduct(), "数据库产品名称不能为null");
        assertFalse(dialect.getName().isEmpty(), "方言名称不能为空");
        assertFalse(dialect.getDatabaseProduct().isEmpty(), "数据库产品名称不能为空");
        
        System.out.printf("✅ 方言信息验证通过: %s (%s)%n", 
            dialect.getName(), dialect.getDatabaseProduct());
    }
    
    @Test
    @Order(2)
    void testDataTypeMapping() {
        Map<String, String> testCases = getDataTypeTestCases();
        assertNotNull(testCases, "数据类型测试用例不能为null");
        assertFalse(testCases.isEmpty(), "数据类型测试用例不能为空");
        
        int successCount = 0;
        int totalCount = testCases.size();
        
        for (Map.Entry<String, String> testCase : testCases.entrySet()) {
            String mysqlType = testCase.getKey();
            String expectedType = testCase.getValue();
            
            try {
                String actualType = dialect.mapDataType(mysqlType, null, null, null);
                assertEquals(expectedType, actualType, 
                    String.format("数据类型映射失败: %s -> %s (实际: %s)%n参考文档: %s", 
                        mysqlType, expectedType, actualType, getOfficialDocumentationUrl()));
                successCount++;
            } catch (AssertionError e) {
                System.err.printf("❌ 数据类型映射失败: %s%n", e.getMessage());
                throw e;
            }
        }
        
        System.out.printf("✅ 数据类型映射验证通过: %d/%d 个测试用例%n", successCount, totalCount);
    }
    
    @Test
    @Order(3)
    void testFunctionMapping() {
        Map<String, String> testCases = getFunctionTestCases();
        assertNotNull(testCases, "函数映射测试用例不能为null");
        assertFalse(testCases.isEmpty(), "函数映射测试用例不能为空");
        
        int successCount = 0;
        int totalCount = testCases.size();
        
        for (Map.Entry<String, String> testCase : testCases.entrySet()) {
            String mysqlFunction = testCase.getKey();
            String expectedFunction = testCase.getValue();
            
            try {
                String actualFunction = dialect.mapFunction(mysqlFunction);
                assertEquals(expectedFunction, actualFunction, 
                    String.format("函数映射失败: %s -> %s (实际: %s)%n参考文档: %s", 
                        mysqlFunction, expectedFunction, actualFunction, getOfficialDocumentationUrl()));
                successCount++;
            } catch (AssertionError e) {
                System.err.printf("❌ 函数映射失败: %s%n", e.getMessage());
                throw e;
            }
        }
        
        System.out.printf("✅ 函数映射验证通过: %d/%d 个测试用例%n", successCount, totalCount);
    }
    
    @Test
    @Order(4)
    void testPaginationSyntax() {
        Map<String, String> testCases = getPaginationTestCases();
        assertNotNull(testCases, "分页语法测试用例不能为null");
        assertFalse(testCases.isEmpty(), "分页语法测试用例不能为空");
        
        int successCount = 0;
        int totalCount = testCases.size();
        
        for (Map.Entry<String, String> testCase : testCases.entrySet()) {
            String input = testCase.getKey();
            String expectedSql = testCase.getValue();
            
            try {
                // 解析输入：格式为 "limit,offset" 或 "limit"
                String[] parts = input.split(",");
                Integer limit = parts.length > 0 && !parts[0].isEmpty() ? Integer.parseInt(parts[0]) : null;
                Integer offset = parts.length > 1 && !parts[1].isEmpty() ? Integer.parseInt(parts[1]) : null;
                
                String actualSql = dialect.formatPagination(limit, offset);
                assertEquals(expectedSql, actualSql, 
                    String.format("分页语法失败: limit=%s, offset=%s -> %s (实际: %s)%n参考文档: %s", 
                        limit, offset, expectedSql, actualSql, getOfficialDocumentationUrl()));
                successCount++;
            } catch (Exception e) {
                System.err.printf("❌ 分页语法失败: %s -> %s%n", input, e.getMessage());
                throw new AssertionError("分页语法测试失败: " + input, e);
            }
        }
        
        System.out.printf("✅ 分页语法验证通过: %d/%d 个测试用例%n", successCount, totalCount);
    }
    
    @Test
    @Order(5)
    void testIdentifierQuoting() {
        // 测试标识符引用规则
        
        // 普通标识符不需要引用
        String[] normalIdentifiers = {"table1", "user_id", "name", "created_at"};
        for (String identifier : normalIdentifiers) {
            assertFalse(dialect.requiresQuoting(identifier), 
                String.format("普通标识符不应该需要引用: %s", identifier));
        }
        
        // 保留字需要引用
        String[] reservedWords = {"SELECT", "FROM", "WHERE", "ORDER", "GROUP"};
        for (String word : reservedWords) {
            assertTrue(dialect.requiresQuoting(word), 
                String.format("保留字应该需要引用: %s", word));
        }
        
        // 特殊字符需要引用
        String[] specialIdentifiers = {"table-name", "table name", "123table"};
        for (String identifier : specialIdentifiers) {
            assertTrue(dialect.requiresQuoting(identifier), 
                String.format("特殊标识符应该需要引用: %s", identifier));
        }
        
        System.out.println("✅ 标识符引用规则验证通过");
    }
    
    @Test
    @Order(6)
    void testSqlDialectFeatures() {
        // 测试SQL方言特性支持
        
        // 测试分页支持
        boolean supportsLimit = dialect.supportsLimit();
        boolean supportsOffset = dialect.supportsOffset();
        System.out.printf("📋 分页支持: LIMIT=%s, OFFSET=%s%n", supportsLimit, supportsOffset);
        
        // 测试自增支持
        boolean supportsAutoIncrement = dialect.supportsAutoIncrement();
        if (supportsAutoIncrement) {
            String autoIncrementSyntax = dialect.getAutoIncrementSyntax();
            assertNotNull(autoIncrementSyntax, "自增语法不能为null");
            assertFalse(autoIncrementSyntax.isEmpty(), "自增语法不能为空");
            System.out.printf("📋 自增支持: %s%n", autoIncrementSyntax);
        }
        
        // 测试约束支持
        boolean supportsForeignKey = dialect.supportsForeignKey();
        boolean supportsCheckConstraint = dialect.supportsCheckConstraint();
        System.out.printf("📋 约束支持: 外键=%s, CHECK约束=%s%n", supportsForeignKey, supportsCheckConstraint);
        
        System.out.println("✅ SQL方言特性验证完成");
    }
    
    @Test
    @Order(7)
    void testFunctionMapperIntegration() {
        // 测试函数映射器与方言的集成
        
        List<String> testFunctions = Arrays.asList("NOW", "CONCAT", "LENGTH", "ABS");
        
        for (String function : testFunctions) {
            boolean supported = functionMapper.supportsFunction(function, dialect);
            if (supported) {
                String result = functionMapper.mapFunction(function, Arrays.asList(), dialect);
                assertNotNull(result, String.format("函数映射结果不能为null: %s", function));
                assertFalse(result.isEmpty(), String.format("函数映射结果不能为空: %s", function));
            }
        }
        
        System.out.println("✅ 函数映射器集成验证通过");
    }
    
    // ==================== 辅助方法 ====================
    
    /**
     * 验证测试用例的完整性
     * 确保所有必要的测试用例都已定义
     */
    protected void validateTestCaseCompleteness() {
        Map<String, String> dataTypes = getDataTypeTestCases();
        Map<String, String> functions = getFunctionTestCases();
        Map<String, String> pagination = getPaginationTestCases();
        
        assertTrue(dataTypes.size() >= 10, "数据类型测试用例应该至少包含10个");
        assertTrue(functions.size() >= 5, "函数映射测试用例应该至少包含5个");
        assertTrue(pagination.size() >= 3, "分页语法测试用例应该至少包含3个");
        
        System.out.printf("📊 测试用例统计: 数据类型=%d, 函数映射=%d, 分页语法=%d%n", 
            dataTypes.size(), functions.size(), pagination.size());
    }
    
    /**
     * 打印测试总结
     */
    protected void printTestSummary() {
        System.out.println("==================== 测试总结 ====================");
        System.out.printf("方言名称: %s%n", dialect.getName());
        System.out.printf("数据库产品: %s%n", dialect.getDatabaseProduct());
        System.out.printf("官方文档: %s%n", getOfficialDocumentationUrl());
        System.out.println("所有标准化测试已完成 ✅");
        System.out.println("================================================");
    }
}
