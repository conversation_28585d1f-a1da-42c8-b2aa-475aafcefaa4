package com.xylink.sqltranspiler.shared.base;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;

import com.xylink.sqltranspiler.Transpiler;
import com.xylink.sqltranspiler.core.context.TranspilationResult;
import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;


public abstract class BaseConversionTest {

    protected DamengGenerator damengGenerator;

    // Test resource path constants
    protected static final String MYSQL_BASIC_PATH = "src/test/resources/sql/mysql/basic/";
    protected static final String MYSQL_COMPLEX_PATH = "src/test/resources/sql/mysql/complex/";
    protected static final String DAMENG_EXPECTED_PATH = "src/test/resources/sql/expected/dameng/";

    @BeforeEach
    void setUp() {
        damengGenerator = new DamengGenerator();
    }

    /**
     * Loads a MySQL test SQL file from either the basic or complex directory.
     */
    protected String loadMySqlTestSql(String filename) throws IOException {
        Path basicPath = Paths.get(MYSQL_BASIC_PATH + filename);
        if (Files.exists(basicPath)) {
            return Files.readString(basicPath);
        }

        Path complexPath = Paths.get(MYSQL_COMPLEX_PATH + filename);
        if (Files.exists(complexPath)) {
            return Files.readString(complexPath);
        }
        
        throw new IOException("Test SQL file not found: " + filename);
    }

    /**
     * Loads the expected Dameng conversion result.
     */
    protected String loadExpectedDamengSql(String filename) throws IOException {
        Path expectedPath = Paths.get(DAMENG_EXPECTED_PATH + filename);
        if (Files.exists(expectedPath)) {
            return Files.readString(expectedPath);
        }
        return null; // Expected result file is optional
    }

    /**
     * Executes the full MySQL to Dameng conversion process.
     */
    protected String convertMySqlToDameng(String mysqlSql) throws Exception {
        // 使用Transpiler来确保两阶段生成逻辑被正确执行
        Transpiler transpiler = new Transpiler();
        TranspilationResult result = transpiler.transpile(mysqlSql, "mysql", "dameng");
        return result.translatedSql();
    }

    /**
     * Executes the full MySQL to KingbaseES conversion process.
     */
    protected String convertMySqlToKingbase(String mysqlSql) throws Exception {
        // 使用Transpiler来确保两阶段生成逻辑被正确执行
        Transpiler transpiler = new Transpiler();
        TranspilationResult result = transpiler.transpile(mysqlSql, "mysql", "kingbase");
        return result.translatedSql();
    }

    /**
     * Executes the full MySQL to Shentong conversion process.
     */
    protected String convertMySqlToShentong(String mysqlSql) throws Exception {
        // 使用Transpiler来确保两阶段生成逻辑被正确执行
        Transpiler transpiler = new Transpiler();
        TranspilationResult result = transpiler.transpile(mysqlSql, "mysql", "shentong");
        return result.translatedSql();
    }

    /**
     * Asserts basic conversion requirements.
     */
    protected void assertBasicConversionRequirements(String damengSql) {
        Assertions.assertNotNull(damengSql, "Conversion result should not be null");
        Assertions.assertFalse(damengSql.trim().isEmpty(), "Conversion result should not be an empty string");
        
        // Assert standard Dameng requirements
        // Only check for double quotes if the SQL contains table/column identifiers that need quoting
        if (containsIdentifiersThatNeedQuoting(damengSql)) {
            Assertions.assertTrue(damengSql.contains("\""), "Should use double quotes for identifiers");
        }
        Assertions.assertFalse(damengSql.contains("ENGINE="), "Should not contain MySQL's ENGINE option");
        
        if (damengSql.contains("CHARACTER SET")) {
            Assertions.assertTrue(damengSql.contains("CHARACTER SET UTF8") || 
                      damengSql.contains("CHARACTER SET GBK"), 
                      "Should use standard Dameng charset syntax");
        }
    }

    /**
     * Checks if the SQL contains identifiers that need quoting
     */
    private boolean containsIdentifiersThatNeedQuoting(String sql) {
        // Check for table references with schema (e.g., schema.table)
        if (sql.matches(".*\\b\\w+\\.\\w+\\b.*")) {
            return true;
        }

        // Check for DDL statements that typically have identifiers
        if (sql.toUpperCase().matches(".*(CREATE TABLE|ALTER TABLE|DROP TABLE|CREATE INDEX|DROP INDEX).*")) {
            return true;
        }

        // Check for DML statements with FROM clause (indicating table references)
        if (sql.toUpperCase().matches(".*(FROM|INTO|UPDATE)\\s+\\w+.*")) {
            return true;
        }

        return false;
    }

     /**
     * Asserts data type conversions.
     */
    protected void assertDataTypeConversions(String damengSql) {
        if (damengSql.toUpperCase().contains("TINYINT(1)")) {
            Assertions.fail("TINYINT(1) should be converted to BIT");
        }
        if (damengSql.toUpperCase().contains("LONGTEXT")) {
            Assertions.assertTrue(damengSql.contains("CLOB"), "LONGTEXT should be converted to CLOB");
        }
        if (damengSql.toUpperCase().contains("LONGBLOB")) {
            Assertions.assertTrue(damengSql.contains("BLOB"), "LONGBLOB should be converted to BLOB");
        }
    }

    /**
     * Asserts constraint separation.
     */
    protected void assertConstraintSeparation(String damengSql) {
        if (damengSql.contains("FOREIGN KEY")) {
            Assertions.assertTrue(damengSql.contains("ALTER TABLE"), "FOREIGN KEY should be in a separate ALTER TABLE statement");
        }
        if (damengSql.contains("CREATE INDEX") || damengSql.contains("CREATE UNIQUE INDEX")) {
            Assertions.assertTrue(damengSql.contains("CREATE INDEX") || damengSql.contains("CREATE UNIQUE INDEX"), 
                      "Index should be in a separate CREATE INDEX statement");
        }
    }
}
