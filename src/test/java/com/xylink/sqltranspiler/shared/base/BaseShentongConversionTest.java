package com.xylink.sqltranspiler.shared.base;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.List;

import org.junit.jupiter.api.BeforeEach;

import com.xylink.sqltranspiler.Transpiler;
import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.context.TranspilationResult;
import com.xylink.sqltranspiler.core.dialects.shentong.ShentongGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;

/**
 * 神通数据库转换测试基类
 * 提供通用的测试方法和断言
 * 
 * 基于神通数据库官方特性：
 * - 国产化数据库管理系统
 * - 支持SQL标准
 * - 分布式架构
 * - 海量数据处理能力
 */
public abstract class BaseShentongConversionTest {

    protected ShentongGenerator generator;

    @BeforeEach
    protected void setUp() {
        generator = new ShentongGenerator();
    }

    /**
     * 转换MySQL SQL为神通SQL
     */
    protected String convertMySqlToShentong(String mysqlSql) throws Exception {
        Transpiler transpiler = new Transpiler();
        TranspilationResult result = transpiler.transpile(mysqlSql, "mysql", "shentong");
        return result.translatedSql();
    }

    /**
     * 直接使用生成器转换（用于单元测试）
     */
    protected String convertMySqlToShentongDirect(String mysqlSql) throws Exception {
        List<Statement> statements = MySqlHelper.parseMultiStatement(mysqlSql);
        assertFalse(statements.isEmpty(), "解析的语句不应为空");
        
        StringBuilder result = new StringBuilder();
        for (Statement statement : statements) {
            String convertedSql = generator.generate(statement);
            assertNotNull(convertedSql, "转换结果不应为null");

            result.append(convertedSql);
            if (!convertedSql.trim().endsWith(";")) {
                result.append(";");
            }
            result.append("\n");
        }
        
        return result.toString().trim();
    }

    /**
     * 验证基本转换要求
     * 基于神通数据库的基本要求
     */
    protected void assertBasicConversionRequirements(String shentongSql) {
        assertNotNull(shentongSql, "转换结果不应为null");
        assertFalse(shentongSql.trim().isEmpty(), "转换结果不应为空");
        
        // 验证语句以分号结尾
        assertTrue(shentongSql.trim().endsWith(";"), "SQL语句应以分号结尾");
        
        // 验证不包含MySQL特有的语法
        assertFalse(shentongSql.contains("ENGINE="), "不应包含MySQL的ENGINE语法");
        assertFalse(shentongSql.contains("DEFAULT CHARSET"), "不应包含MySQL的DEFAULT CHARSET语法");
        
        // 验证使用双引号标识符（神通标准）
        assertFalse(shentongSql.contains("`"), "不应包含MySQL的反引号标识符");
    }

    /**
     * 验证数据类型转换
     * 基于神通数据库的数据类型映射
     */
    protected void assertDataTypeConversions(String shentongSql) {
        // 根据神通数据库官方文档，神通完全支持TINYINT类型
        // TINYINT保持不变或转换为SMALLINT都是正确的
        // 这里不做强制断言，由具体测试用例决定

        // 验证MEDIUMINT转换为INT
        assertFalse(shentongSql.contains("MEDIUMINT"), "MEDIUMINT应转换为INT");

        // 验证TINYINT(1)转换为BIT（如果实现了这个转换）
        if (shentongSql.toUpperCase().contains("BIT")) {
            assertTrue(shentongSql.contains("BIT"), "TINYINT(1)应转换为BIT");
        }
        
        // 验证TEXT类型统一
        if (shentongSql.toUpperCase().contains("TEXT")) {
            assertFalse(shentongSql.contains("TINYTEXT"), "TINYTEXT应转换为TEXT");
            assertFalse(shentongSql.contains("MEDIUMTEXT"), "MEDIUMTEXT应转换为TEXT");
            assertFalse(shentongSql.contains("LONGTEXT"), "LONGTEXT应转换为TEXT");
        }
        
        // 验证BLOB类型统一
        if (shentongSql.toUpperCase().contains("BLOB")) {
            assertFalse(shentongSql.contains("TINYBLOB"), "TINYBLOB应转换为BLOB");
            assertFalse(shentongSql.contains("MEDIUMBLOB"), "MEDIUMBLOB应转换为BLOB");
            assertFalse(shentongSql.contains("LONGBLOB"), "LONGBLOB应转换为BLOB");
        }
        
        // 验证YEAR类型转换为SMALLINT
        assertFalse(shentongSql.contains("YEAR"), "YEAR应转换为SMALLINT");
    }

    /**
     * 验证自增字段转换
     * 根据神通数据库官方文档，神通完全支持AUTO_INCREMENT语法
     */
    protected void assertAutoIncrementConversion(String shentongSql) {
        // 只有当SQL包含自增相关内容时才进行验证
        // 检查原始SQL是否包含自增相关关键字（这里简化处理，实际应该检查原始SQL）
        if (shentongSql.toUpperCase().contains("AUTO_INCREMENT") ||
            shentongSql.toUpperCase().contains("IDENTITY") ||
            shentongSql.toUpperCase().contains("SERIAL")) {

            // 根据神通官方文档：支持AUTO_INCREMENT、IDENTITY和SERIAL语法
            // SERIAL是神通数据库的标准自增类型，等价于INTEGER AUTO_INCREMENT
            assertTrue(shentongSql.contains("AUTO_INCREMENT") ||
                       shentongSql.contains("IDENTITY") ||
                       shentongSql.contains("SERIAL"),
                       "应支持AUTO_INCREMENT、IDENTITY或SERIAL语法");

            if (shentongSql.contains("IDENTITY")) {
                assertTrue(shentongSql.contains("IDENTITY(1,1)"), "如果使用IDENTITY应使用IDENTITY(1,1)语法");
            }
        }
        // 如果SQL不包含自增相关内容，则跳过验证
    }

    /**
     * 验证字符集转换
     */
    protected void assertCharacterSetConversion(String shentongSql) {
        // 验证字符集转换
        assertFalse(shentongSql.contains("utf8mb4"), "utf8mb4应转换为UTF8");
        assertFalse(shentongSql.contains("DEFAULT CHARSET"), "DEFAULT CHARSET应转换为CHARACTER SET");
        
        if (shentongSql.contains("CHARACTER SET")) {
            assertTrue(shentongSql.contains("CHARACTER SET UTF8"), "应使用CHARACTER SET UTF8");
        }
    }

    /**
     * 验证神通数据库标准合规性
     */
    protected void assertShentongStandardCompliance(String shentongSql) {
        assertBasicConversionRequirements(shentongSql);
        assertDataTypeConversions(shentongSql);
        assertAutoIncrementConversion(shentongSql);
        assertCharacterSetConversion(shentongSql);
        
        // 验证标识符规范：使用双引号
        if (shentongSql.contains("\"")) {
            assertTrue(shentongSql.contains("\""), "应使用双引号标识符");
        }
    }

    /**
     * 验证CREATE TABLE语句的转换
     */
    protected void assertCreateTableConversion(String shentongSql) {
        assertTrue(shentongSql.toUpperCase().contains("CREATE TABLE"), "应包含CREATE TABLE语句");
        assertShentongStandardCompliance(shentongSql);
    }

    /**
     * 验证INSERT语句的转换
     */
    protected void assertInsertStatementConversion(String shentongSql) {
        assertTrue(shentongSql.toUpperCase().contains("INSERT INTO"), "应包含INSERT INTO语句");
        assertShentongStandardCompliance(shentongSql);
    }

    /**
     * 验证SELECT语句的转换
     */
    protected void assertSelectStatementConversion(String shentongSql) {
        assertTrue(shentongSql.toUpperCase().contains("SELECT"), "应包含SELECT语句");
        assertBasicConversionRequirements(shentongSql);
    }

    /**
     * 验证ALTER TABLE语句的转换
     */
    protected void assertAlterTableConversion(String shentongSql) {
        assertTrue(shentongSql.toUpperCase().contains("ALTER TABLE"), "应包含ALTER TABLE语句");
        assertShentongStandardCompliance(shentongSql);
    }

    /**
     * 验证函数转换
     */
    protected void assertFunctionConversions(String shentongSql) {
        // 验证日期时间函数转换
        assertFalse(shentongSql.contains("NOW()"), "NOW()应转换为CURRENT_TIMESTAMP");
        assertFalse(shentongSql.contains("CURDATE()"), "CURDATE()应转换为CURRENT_DATE");
        assertFalse(shentongSql.contains("CURTIME()"), "CURTIME()应转换为CURRENT_TIME");
        
        // 验证聚合函数转换
        if (shentongSql.contains("LISTAGG")) {
            assertFalse(shentongSql.contains("GROUP_CONCAT"), "GROUP_CONCAT应转换为LISTAGG");
        }
        
        // 验证数学函数转换
        assertFalse(shentongSql.contains("RAND()"), "RAND()应转换为RANDOM()");
    }

    /**
     * 验证数据类型转换的辅助方法
     */
    protected void assertDataTypeConversion(String shentongSql, String expectedType) {
        assertTrue(shentongSql.toUpperCase().contains(expectedType.toUpperCase()), 
                   "应包含数据类型: " + expectedType);
    }

    /**
     * 验证不包含特定内容的辅助方法
     */
    protected void assertNotContains(String shentongSql, String content, String message) {
        assertFalse(shentongSql.contains(content), message);
    }

    /**
     * 验证包含特定内容的辅助方法
     */
    protected void assertContains(String shentongSql, String content, String message) {
        assertTrue(shentongSql.contains(content), message);
    }

    /**
     * 验证标识符长度限制
     * 根据官方文档第81行：标识符的长度必须在1到127之间
     */
    protected void assertIdentifierLengthCompliance(String shentongSql) {
        // 这里可以添加标识符长度检查逻辑
        // 由于需要解析SQL来提取标识符，这里提供基础框架
        assertNotNull(shentongSql, "SQL不应为null");
    }

    /**
     * 验证系统保留前缀
     * 根据官方文档第73-75行：用户定义的对象请不要以SYS_或V_SYS_开头
     */
    protected void assertSystemReservedPrefixCompliance(String shentongSql) {
        // 检查是否有系统保留前缀的使用
        // 这里提供基础检查，具体实现可以在子类中扩展
        if (shentongSql.toUpperCase().contains("SYS_") ||
            shentongSql.toUpperCase().contains("V_SYS_")) {
            // 如果包含系统前缀，应该有适当的处理（如引号包围）
            assertTrue(shentongSql.contains("\"SYS_") ||
                       shentongSql.contains("\"V_SYS_") ||
                       shentongSql.contains("USER_") ||
                       shentongSql.contains("APP_"),
                       "系统保留前缀应被适当处理");
        }
    }

    /**
     * 验证字符集支持
     * 根据官方文档第61行：神通数据库的字符集包括英文大小写字母以及ASCII码为128-255的字符
     */
    protected void assertCharacterSetSupport(String shentongSql) {
        // 验证字符集声明
        if (shentongSql.contains("CHARACTER SET")) {
            assertTrue(shentongSql.contains("CHARACTER SET UTF8"),
                       "应使用CHARACTER SET UTF8");
        }

        // 验证不包含MySQL特有的字符集
        assertFalse(shentongSql.contains("utf8mb4"), "不应包含MySQL的utf8mb4");
        assertFalse(shentongSql.contains("latin1"), "不应包含MySQL的latin1");
    }

    /**
     * 验证注释处理
     * 根据官方文档第118-124行：支持双连字符和C风格注释
     */
    protected void assertCommentHandling(String shentongSql) {
        // 验证注释格式（如果保留注释的话）
        if (shentongSql.contains("--")) {
            // 双连字符注释应该正确格式化
            assertTrue(shentongSql.contains("-- ") ||
                       shentongSql.matches(".*--[^\\s].*"),
                       "双连字符注释应正确格式化");
        }

        if (shentongSql.contains("/*")) {
            // C风格注释应该有对应的结束符
            assertTrue(shentongSql.contains("*/"),
                       "C风格注释应有对应的结束符");
        }
    }

    /**
     * 验证特殊字符处理
     * 根据官方文档第2.4节：特殊字符的正确处理
     */
    protected void assertSpecialCharacterHandling(String shentongSql) {
        // 验证分号结尾
        assertTrue(shentongSql.trim().endsWith(";"),
                   "SQL语句应以分号结尾");

        // 验证美元符号参数（如果存在）
        if (shentongSql.contains("$")) {
            // 美元符号应该在正确的上下文中使用
            assertTrue(shentongSql.matches(".*\\$\\d+.*") ||
                       !shentongSql.contains("$"),
                       "美元符号应用于参数位置或不存在");
        }
    }

    /**
     * 验证伪列支持
     * 根据官方文档第2.6节：ROWID、SYSATTR_ROWVERSION、ROWNUM伪列
     */
    protected void assertPseudoColumnSupport(String shentongSql) {
        // 验证ROWNUM伪列使用
        if (shentongSql.toUpperCase().contains("ROWNUM")) {
            assertTrue(shentongSql.contains("ROWNUM"), "应支持ROWNUM伪列");
        }

        // 验证ROWID伪列使用
        if (shentongSql.toUpperCase().contains("ROWID")) {
            assertTrue(shentongSql.contains("ROWID"), "应支持ROWID伪列");
        }
    }

    /**
     * 验证NULL值处理
     * 根据官方文档第156-166行：NULL值的特殊处理
     */
    protected void assertNullValueHandling(String shentongSql) {
        // 验证NULL值不被错误转换
        if (shentongSql.toUpperCase().contains("NULL")) {
            // NULL应该保持为NULL，不应被转换为0或空字符串
            assertFalse(shentongSql.contains("NULL = 0"),
                        "NULL不应被转换为0");
            assertFalse(shentongSql.contains("NULL = ''"),
                        "NULL不应被转换为空字符串");
        }
    }

    /**
     * 综合验证神通数据库官方文档合规性
     * 包含所有基于官方文档的验证项
     */
    protected void assertOfficialDocumentCompliance(String shentongSql) {
        assertBasicConversionRequirements(shentongSql);
        assertDataTypeConversions(shentongSql);
        assertAutoIncrementConversion(shentongSql);
        assertCharacterSetSupport(shentongSql);
        assertSystemReservedPrefixCompliance(shentongSql);
        assertCommentHandling(shentongSql);
        assertSpecialCharacterHandling(shentongSql);
        assertPseudoColumnSupport(shentongSql);
        assertNullValueHandling(shentongSql);
    }

    /**
     * 验证SQL语句计数
     * 确保转换后的SQL语句数量正确
     */
    protected void assertStatementCount(String shentongSql, int expectedCount) {
        long actualCount = shentongSql.lines()
                .filter(line -> !line.trim().isEmpty() &&
                               !line.trim().startsWith("--") &&
                               !line.trim().startsWith("/*"))
                .filter(line -> line.trim().endsWith(";"))
                .count();

        assertTrue(actualCount >= expectedCount,
                   String.format("应包含至少%d个SQL语句，实际：%d", expectedCount, actualCount));
    }

    /**
     * 验证数据完整性
     * 确保转换过程中数据不丢失
     */
    protected void assertDataIntegrity(String shentongSql, String... expectedData) {
        for (String data : expectedData) {
            assertTrue(shentongSql.contains(data),
                       "应包含数据: " + data);
        }
    }
}
