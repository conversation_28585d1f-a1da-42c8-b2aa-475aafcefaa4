package com.xylink.sqltranspiler.shared.base;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.Transpiler;
import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.create.CreateFunction;
import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import com.xylink.sqltranspiler.core.context.TranspilationResult;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;

/**
 * 统一的SQL转换器测试基类
 * 
 * 提供所有测试类共用的基础功能：
 * 1. 初始化转换器和生成器
 * 2. 提供各种SQL语句解析方法
 * 3. 提供通用的测试工具方法
 * 4. 提供资源文件加载方法
 * 5. 提供通用的断言方法
 * 
 * 严格遵循数据库规则：
 * - 不允许推测，必须查看官方文档
 * - 将准确的官方描述写入到测试用例中
 * - 测试驱动开发，不妥协代码质量
 * 
 * 官方文档：
 * - MySQL官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/index.html
 * - 神通官方文档：@shentong.md
 * 
 * <AUTHOR>
 */
public abstract class BaseTranspilerTest {

    protected static final Logger log = LoggerFactory.getLogger(BaseTranspilerTest.class);
    
    protected Transpiler transpiler;
    
    // 测试资源路径常量
    protected static final String MYSQL_INPUT_PATH = "src/test/resources/sql/input/mysql/";
    protected static final String MYSQL_BASIC_PATH = MYSQL_INPUT_PATH + "basic/";
    protected static final String MYSQL_ADVANCED_PATH = MYSQL_INPUT_PATH + "advanced/";
    protected static final String MYSQL_EDGE_CASES_PATH = MYSQL_INPUT_PATH + "edge-cases/";
    protected static final String MYSQL_REAL_WORLD_PATH = MYSQL_INPUT_PATH + "real-world/";
    
    protected static final String EXPECTED_OUTPUT_PATH = "src/test/resources/sql/expected/";
    protected static final String DAMENG_EXPECTED_PATH = EXPECTED_OUTPUT_PATH + "dameng/";
    protected static final String KINGBASE_EXPECTED_PATH = EXPECTED_OUTPUT_PATH + "kingbase/";
    protected static final String SHENTONG_EXPECTED_PATH = EXPECTED_OUTPUT_PATH + "shentong/";

    @BeforeEach
    protected void setUp() {
        transpiler = new Transpiler();
    }

    /**
     * 解析MySQL的单个SQL语句为AST
     *
     * @param sql MySQL SQL语句
     * @return 解析后的Statement对象
     */
    protected Statement parseStatement(String sql) {
        return MySqlHelper.parseStatement(sql);
    }

    /**
     * 解析CREATE TABLE语句
     *
     * @param sql CREATE TABLE SQL语句
     * @return 解析后的CreateTable对象
     */
    protected CreateTable parseCreateTable(String sql) {
        return (CreateTable) MySqlHelper.parseStatement(sql);
    }

    /**
     * 解析CREATE FUNCTION语句
     *
     * @param sql CREATE FUNCTION SQL语句
     * @return 解析后的CreateFunction对象
     */
    protected CreateFunction parseCreateFunction(String sql) {
        return (CreateFunction) MySqlHelper.parseStatement(sql);
    }

    /**
     * 解析MySQL的多个SQL语句为AST列表
     * 
     * @param sql 包含多个语句的MySQL SQL
     * @return 解析后的Statement列表
     */
    protected List<Statement> parseMultiStatement(String sql) {
        return MySqlHelper.parseMultiStatement(sql);
    }

    /**
     * 执行SQL转换
     * 
     * @param sql 源SQL内容
     * @param sourceDialect 源方言 (目前支持 "mysql")
     * @param targetDialect 目标方言 ("dameng", "kingbase", "shentong")
     * @return 转换结果
     */
    protected TranspilationResult transpile(String sql, String sourceDialect, String targetDialect) {
        return transpiler.transpile(sql, sourceDialect, targetDialect, true);
    }

    /**
     * 执行MySQL到指定数据库的转换
     * 
     * @param mysqlSql MySQL SQL语句
     * @param targetDialect 目标数据库方言
     * @return 转换后的SQL
     */
    protected String convertMySqlTo(String mysqlSql, String targetDialect) {
        TranspilationResult result = transpile(mysqlSql, "mysql", targetDialect);
        return result.translatedSql();
    }

    /**
     * 从文件加载MySQL测试SQL
     * 
     * @param filename 文件名（相对于MySQL输入目录）
     * @return SQL内容
     * @throws IOException 文件读取异常
     */
    protected String loadMySqlTestSql(String filename) throws IOException {
        Path filePath = Paths.get(MYSQL_INPUT_PATH, filename);
        if (!Files.exists(filePath)) {
            // 尝试在basic目录中查找
            filePath = Paths.get(MYSQL_BASIC_PATH, filename);
            if (!Files.exists(filePath)) {
                // 尝试在advanced目录中查找
                filePath = Paths.get(MYSQL_ADVANCED_PATH, filename);
                if (!Files.exists(filePath)) {
                    // 尝试在real-world目录中查找
                    filePath = Paths.get(MYSQL_REAL_WORLD_PATH, filename);
                }
            }
        }
        return Files.readString(filePath);
    }

    /**
     * 从文件加载预期的转换结果
     * 
     * @param filename 文件名
     * @param targetDialect 目标数据库方言
     * @return 预期的SQL内容
     * @throws IOException 文件读取异常
     */
    protected String loadExpectedSql(String filename, String targetDialect) throws IOException {
        String expectedPath = switch (targetDialect.toLowerCase()) {
            case "dameng" -> DAMENG_EXPECTED_PATH;
            case "kingbase" -> KINGBASE_EXPECTED_PATH;
            case "shentong" -> SHENTONG_EXPECTED_PATH;
            default -> throw new IllegalArgumentException("Unsupported target dialect: " + targetDialect);
        };
        
        Path filePath = Paths.get(expectedPath, filename);
        return Files.readString(filePath);
    }

    /**
     * 验证基本转换要求
     * 适用于所有数据库的通用验证
     */
    protected void assertBasicConversionRequirements(String convertedSql) {
        assertNotNull(convertedSql, "转换结果不应为null");
        assertFalse(convertedSql.trim().isEmpty(), "转换结果不应为空");
        
        // 验证语句以分号结尾
        assertTrue(convertedSql.trim().endsWith(";"), "SQL语句应以分号结尾");
        
        // 验证不包含MySQL特有的语法
        assertFalse(convertedSql.contains("ENGINE="), "不应包含MySQL的ENGINE语法");
        assertFalse(convertedSql.contains("DEFAULT CHARSET"), "不应包含MySQL的DEFAULT CHARSET语法");
        
        // 验证不包含MySQL的反引号标识符
        assertFalse(convertedSql.contains("`"), "不应包含MySQL的反引号标识符");
    }

    /**
     * 验证数据类型转换
     * 检查常见的MySQL数据类型是否正确转换
     */
    protected void assertDataTypeConversions(String convertedSql) {
        // 验证AUTO_INCREMENT转换
        if (convertedSql.contains("AUTO_INCREMENT")) {
            log.warn("发现未转换的AUTO_INCREMENT，可能需要转换为IDENTITY或SERIAL");
        }
        
        // 验证TINYINT(1)转换（MySQL布尔类型）
        if (convertedSql.contains("TINYINT(1)")) {
            log.warn("发现未转换的TINYINT(1)，可能需要转换为BOOLEAN或BIT");
        }
    }

    /**
     * 验证约束分离
     * 检查外键约束是否正确分离为独立语句
     */
    protected void assertConstraintSeparation(String convertedSql) {
        if (convertedSql.contains("FOREIGN KEY")) {
            assertTrue(convertedSql.contains("ALTER TABLE"), 
                "包含外键的SQL应该将外键分离为独立的ALTER TABLE语句");
        }
    }

    /**
     * 记录转换结果用于调试
     */
    protected void logConversionResult(String originalSql, String convertedSql, String targetDialect) {
        log.info("=== {} 转换结果 ===", targetDialect.toUpperCase());
        log.info("原始MySQL SQL:");
        log.info(originalSql);
        log.info("转换后的{}SQL:", targetDialect);
        log.info(convertedSql);
        log.info("=== 转换完成 ===");
    }
}
