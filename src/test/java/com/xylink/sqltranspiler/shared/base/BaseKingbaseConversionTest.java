package com.xylink.sqltranspiler.shared.base;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.List;

import org.junit.jupiter.api.BeforeEach;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;

/**
 * 金仓转换测试基类
 * 提供通用的测试方法和断言
 * 
 * 基于金仓官方文档标准：
 * - 官方文档：https://help.kingbase.com.cn/v8/index.html
 * - MySQL迁移最佳实践：https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/index.html
 * - 兼容性说明：https://help.kingbase.com.cn/v8/development/develop-transfer/kes-vs-mysql/index.html
 */
public abstract class BaseKingbaseConversionTest {

    protected KingbaseGenerator generator;

    @BeforeEach
    protected void setUp() {
        generator = new KingbaseGenerator();
        generator.clearState(); // 清理状态，确保测试独立性
    }

    /**
     * 转换MySQL SQL为金仓SQL
     */
    protected String convertMySqlToKingbase(String mysqlSql) throws Exception {
        List<Statement> statements = MySqlHelper.parseMultiStatement(mysqlSql);
        assertFalse(statements.isEmpty(), "解析的语句不应为空");
        
        StringBuilder result = new StringBuilder();
        for (Statement statement : statements) {
            String convertedSql = generator.generate(statement);
            assertNotNull(convertedSql, "转换结果不应为null");

            // 调试输出
            System.out.println("Statement type: " + statement.getClass().getSimpleName());
            System.out.println("Original SQL: " + statement.getSql());
            System.out.println("Converted SQL: " + convertedSql);
            System.out.println("Contains AUTO_INCREMENT: " + convertedSql.contains("AUTO_INCREMENT"));
            System.out.println("---");

            result.append(convertedSql);
            if (!convertedSql.trim().endsWith(";")) {
                result.append(";");
            }
            result.append("\n");
        }
        
        return result.toString().trim();
    }

    /**
     * 验证基本转换要求
     * 基于金仓官方文档的基本要求
     */
    protected void assertBasicConversionRequirements(String kingbaseSql) {
        assertNotNull(kingbaseSql, "转换结果不应为null");
        assertFalse(kingbaseSql.trim().isEmpty(), "转换结果不应为空");
        
        // 验证语句以分号结尾
        assertTrue(kingbaseSql.trim().endsWith(";"), "SQL语句应以分号结尾");
        
        // 验证不包含MySQL特有的语法
        assertFalse(kingbaseSql.contains("ENGINE="), "不应包含MySQL的ENGINE语法");
        assertFalse(kingbaseSql.contains("DEFAULT CHARSET"), "不应包含MySQL的DEFAULT CHARSET语法");
        assertFalse(kingbaseSql.contains("AUTO_INCREMENT"), "不应包含MySQL的AUTO_INCREMENT语法");
        
        // 验证使用双引号标识符（金仓标准）
        // 如果包含标识符，应该使用双引号而不是反引号
        assertFalse(kingbaseSql.contains("`"), "不应包含MySQL的反引号标识符");
    }

    /**
     * 验证数据类型转换
     */
    protected void assertDataTypeConversion(String kingbaseSql, String expectedType) {
        assertTrue(kingbaseSql.contains(expectedType), 
                   "应包含期望的数据类型: " + expectedType);
    }

    /**
     * 验证函数转换
     */
    protected void assertFunctionConversion(String kingbaseSql, String expectedFunction) {
        assertTrue(kingbaseSql.contains(expectedFunction), 
                   "应包含期望的函数: " + expectedFunction);
    }

    /**
     * 验证不包含不支持的内容
     */
    protected void assertNoUnsupportedContent(String kingbaseSql) {
        assertFalse(kingbaseSql.contains("-- Unsupported"), "不应包含不支持的内容标记");
    }

    /**
     * 验证金仓官方标准合规性
     * 基于金仓官方文档的标准要求
     */
    protected void assertKingbaseStandardCompliance(String kingbaseSql) {
        assertBasicConversionRequirements(kingbaseSql);
        
        // 验证标识符规范：使用双引号
        if (kingbaseSql.contains("\"")) {
            // 如果使用了引号标识符，应该是双引号
            assertTrue(kingbaseSql.contains("\""), "应使用双引号标识符");
        }
        
        // 验证不包含MySQL特有的数据类型
        assertFalse(kingbaseSql.contains("TINYINT(1)"), "TINYINT(1)应转换为BOOLEAN");
        
        // 验证SERIAL类型的使用（AUTO_INCREMENT转换）
        if (kingbaseSql.toUpperCase().contains("SERIAL")) {
            assertTrue(kingbaseSql.toUpperCase().contains("SERIAL"), "应使用SERIAL类型替代AUTO_INCREMENT");
        }
    }

    /**
     * 验证CREATE TABLE语句的转换
     */
    protected void assertCreateTableConversion(String kingbaseSql) {
        assertTrue(kingbaseSql.toUpperCase().contains("CREATE TABLE"), "应包含CREATE TABLE语句");
        assertKingbaseStandardCompliance(kingbaseSql);
    }

    /**
     * 验证INSERT语句的转换
     */
    protected void assertInsertStatementConversion(String kingbaseSql) {
        assertTrue(kingbaseSql.toUpperCase().contains("INSERT INTO"), "应包含INSERT INTO语句");
        assertKingbaseStandardCompliance(kingbaseSql);
    }

    /**
     * 验证SELECT语句的转换
     */
    protected void assertSelectStatementConversion(String kingbaseSql) {
        assertTrue(kingbaseSql.toUpperCase().contains("SELECT"), "应包含SELECT语句");
        assertKingbaseStandardCompliance(kingbaseSql);
    }
}
