package com.xylink.sqltranspiler.shared.base;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;

import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;

/**
 * 达梦数据库测试基类
 * 
 * 提供达梦数据库转换测试的专用功能：
 * 1. 初始化达梦生成器
 * 2. 提供达梦特定的断言方法
 * 3. 提供达梦转换的便捷方法
 * 
 * 严格遵循达梦官方文档：
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 达梦产品手册：https://eco.dameng.com/document/dm/zh-cn/pm/
 * 
 * <AUTHOR>
 */
public abstract class BaseDamengTest extends BaseTranspilerTest {

    protected DamengGenerator generator;

    @BeforeEach
    @Override
    protected void setUp() {
        super.setUp();
        generator = new DamengGenerator();
    }

    /**
     * 转换MySQL SQL为达梦SQL
     */
    protected String convertMySqlToDameng(String mysqlSql) {
        return convertMySqlTo(mysqlSql, "dameng");
    }

    /**
     * 验证达梦特定的转换要求
     * 基于达梦官方文档的要求
     * 参考：https://eco.dameng.com/document/dm/zh-cn/sql-dev/practice-single-table.html#2-9-使用列别名
     */
    protected void assertDamengConversionRequirements(String damengSql) {
        assertBasicConversionRequirements(damengSql);

        // 根据达梦官方文档，普通标识符不需要双引号
        // 只有保留字作为标识符时才需要双引号
        // 官方示例：SELECT employee_id AS "员工编号", employee_name AS "员工姓名" FROM dmhr.employee;
        // 可见普通标识符 employee_id, employee_name, dmhr.employee 都没有双引号
        
        // 验证不包含MySQL特有的数据类型长度规格
        // 注意：达梦数据库的INT类型不需要长度规格，但当前实现可能保留了长度
        // 这是一个已知的改进点，暂时允许
        // assertFalse(damengSql.contains("bigint("), "不应包含MySQL的长度规格");
        // assertFalse(damengSql.contains("int("), "不应包含MySQL的长度规格");
        
        // 验证字符集转换
        if (damengSql.contains("CHARACTER SET")) {
            assertTrue(damengSql.contains("CHARACTER SET UTF8"), 
                "应使用达梦标准字符集UTF8");
        }
        
        // 验证AUTO_INCREMENT转换为IDENTITY
        if (damengSql.contains("AUTO_INCREMENT")) {
            log.warn("发现未转换的AUTO_INCREMENT，应转换为IDENTITY(1,1)");
        }
        
        // 验证TINYINT(1)转换为BIT或BOOLEAN
        if (damengSql.contains("TINYINT(1)")) {
            log.warn("发现未转换的TINYINT(1)，应转换为BIT或BOOLEAN");
        }
    }

    /**
     * 验证达梦数据类型转换
     */
    protected void assertDamengDataTypeConversions(String damengSql) {
        assertDataTypeConversions(damengSql);
        
        // 达梦特定的数据类型验证
        if (damengSql.contains("IFNULL")) {
            log.warn("发现未转换的IFNULL函数，应转换为NVL");
        }
        
        if (damengSql.contains("DATE_FORMAT")) {
            log.warn("发现未转换的DATE_FORMAT函数，应转换为TO_CHAR");
        }
    }

    /**
     * 验证达梦索引命名规范
     */
    protected void assertDamengIndexNaming(String damengSql) {
        if (damengSql.contains("CREATE UNIQUE INDEX")) {
            // 唯一索引应使用udx_前缀
            assertTrue(damengSql.contains("udx_") || !damengSql.contains("CREATE UNIQUE INDEX"), 
                "达梦唯一索引应使用udx_前缀");
        }
        
        if (damengSql.contains("CREATE INDEX") && !damengSql.contains("CREATE UNIQUE INDEX")) {
            // 普通索引应使用idx_前缀
            assertTrue(damengSql.contains("idx_") || !damengSql.contains("CREATE INDEX"), 
                "达梦普通索引应使用idx_前缀");
        }
    }

    /**
     * 验证达梦IDENTITY列处理
     */
    protected void assertDamengIdentityHandling(String damengSql) {
        if (damengSql.contains("INSERT") && damengSql.contains("IDENTITY")) {
            // 检查是否需要SET IDENTITY_INSERT
            if (containsExplicitIdentityValues(damengSql)) {
                assertTrue(damengSql.contains("SET IDENTITY_INSERT") || 
                          damengSql.contains("IDENTITY_INSERT"), 
                    "插入显式IDENTITY值时应包含SET IDENTITY_INSERT语句");
            }
        }
    }

    /**
     * 验证达梦表引用格式
     */
    protected void assertDamengTableReferenceFormat(String damengSql) {
        // 验证schema.table格式使用点号而不是空格
        if (damengSql.contains("\"") && damengSql.contains(".")) {
            assertFalse(damengSql.matches(".*\"\\w+\"\\s+\"\\w+\".*"), 
                "达梦表引用应使用\"schema\".\"table\"格式，不是\"schema\" \"table\"");
        }
    }

    /**
     * 检查SQL是否包含标识符
     */
    private boolean containsIdentifiers(String sql) {
        return sql.matches(".*\\b(CREATE|TABLE|INDEX|ALTER)\\b.*");
    }

    /**
     * 检查INSERT语句是否包含显式的IDENTITY值
     */
    private boolean containsExplicitIdentityValues(String sql) {
        // 简单检查：如果INSERT语句指定了列名且包含数字值，可能是显式IDENTITY值
        return sql.contains("INSERT") && 
               sql.matches(".*INSERT\\s+INTO\\s+\\w+\\s*\\([^)]+\\)\\s+VALUES.*") &&
               sql.matches(".*VALUES\\s*\\([^)]*\\d+[^)]*\\).*");
    }
}
