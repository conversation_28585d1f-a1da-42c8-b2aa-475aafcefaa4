package com.xylink.sqltranspiler.shared.suites;

import org.junit.platform.suite.api.SelectPackages;
import org.junit.platform.suite.api.Suite;
import org.junit.platform.suite.api.SuiteDisplayName;

/**
 * 单元测试套件
 * 
 * 运行所有单元测试，包括：
 * - 核心组件单元测试
 * - 基础设施单元测试  
 * - 数据库方言生成器单元测试
 * - 共享测试基础设施测试
 * 
 * 运行方式：
 * mvn test -Dtest=UnitTestSuite
 * 
 * <AUTHOR>
 */
@Suite
@SuiteDisplayName("单元测试套件")
@SelectPackages({
    "com.xylink.sqltranspiler.unit",
    "com.xylink.sqltranspiler.shared"
})
public class UnitTestSuite {
    // 测试套件类不需要实现任何方法
    // JUnit 5会自动发现和运行指定包下的所有测试
}
