package com.xylink.sqltranspiler.shared.suites;

import org.junit.platform.suite.api.SelectPackages;
import org.junit.platform.suite.api.Suite;
import org.junit.platform.suite.api.SuiteDisplayName;

/**
 * 神通数据库测试套件
 * 
 * 运行所有与神通数据库相关的测试，包括：
 * - 神通方言生成器单元测试
 * - 神通转换集成测试
 * - 神通官方文档合规性测试
 * - 神通回归测试
 * 
 * 运行方式：
 * mvn test -Dtest=ShentongTestSuite
 * 
 * 严格遵循神通官方文档：
 * - 神通官方文档：@shentong.md
 * 
 * <AUTHOR>
 */
@Suite
@SuiteDisplayName("神通数据库测试套件")
@SelectPackages({
    "com.xylink.sqltranspiler.unit.dialects.shentong",
    "com.xylink.sqltranspiler.integration.conversion.shentong",
    "com.xylink.sqltranspiler.compliance.shentong",
    "com.xylink.sqltranspiler.regression.bugfix"
})
public class ShentongTestSuite {
    // 测试套件类不需要实现任何方法
    // JUnit 5会自动发现和运行指定包下的所有测试
}
