package com.xylink.sqltranspiler.shared.suites;

import org.junit.platform.suite.api.SelectPackages;
import org.junit.platform.suite.api.Suite;
import org.junit.platform.suite.api.SuiteDisplayName;

/**
 * 达梦数据库测试套件
 * 
 * 运行所有与达梦数据库相关的测试，包括：
 * - 达梦方言生成器单元测试
 * - 达梦转换集成测试
 * - 达梦官方文档合规性测试
 * - 达梦回归测试
 * 
 * 运行方式：
 * mvn test -Dtest=DamengTestSuite
 * 
 * 严格遵循达梦官方文档：
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 达梦产品手册：https://eco.dameng.com/document/dm/zh-cn/pm/
 * 
 * <AUTHOR>
 */
@Suite
@SuiteDisplayName("达梦数据库测试套件")
@SelectPackages({
    "com.xylink.sqltranspiler.unit.dialects.dameng",
    "com.xylink.sqltranspiler.integration.conversion.dameng",
    "com.xylink.sqltranspiler.compliance.dameng",
    "com.xylink.sqltranspiler.regression.bugfix"
})
public class DamengTestSuite {
    // 测试套件类不需要实现任何方法
    // JUnit 5会自动发现和运行指定包下的所有测试
}
