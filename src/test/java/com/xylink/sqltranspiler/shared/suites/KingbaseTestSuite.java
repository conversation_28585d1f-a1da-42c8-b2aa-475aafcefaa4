package com.xylink.sqltranspiler.shared.suites;

import org.junit.platform.suite.api.SelectPackages;
import org.junit.platform.suite.api.Suite;
import org.junit.platform.suite.api.SuiteDisplayName;

/**
 * 金仓数据库测试套件
 * 
 * 运行所有与金仓数据库相关的测试，包括：
 * - 金仓方言生成器单元测试
 * - 金仓转换集成测试
 * - 金仓官方文档合规性测试
 * - 金仓回归测试
 * 
 * 运行方式：
 * mvn test -Dtest=KingbaseTestSuite
 * 
 * 严格遵循金仓官方文档：
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/index.html
 * - MySQL迁移最佳实践：https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/index.html
 * - 兼容性说明：https://help.kingbase.com.cn/v8/development/develop-transfer/kes-vs-mysql/index.html
 * 
 * <AUTHOR>
 */
@Suite
@SuiteDisplayName("金仓数据库测试套件")
@SelectPackages({
    "com.xylink.sqltranspiler.unit.dialects.kingbase",
    "com.xylink.sqltranspiler.integration.conversion.kingbase",
    "com.xylink.sqltranspiler.compliance.kingbase",
    "com.xylink.sqltranspiler.regression.bugfix"
})
public class KingbaseTestSuite {
    // 测试套件类不需要实现任何方法
    // JUnit 5会自动发现和运行指定包下的所有测试
}
