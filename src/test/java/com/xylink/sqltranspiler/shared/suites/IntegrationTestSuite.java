package com.xylink.sqltranspiler.shared.suites;

import org.junit.platform.suite.api.SelectPackages;
import org.junit.platform.suite.api.Suite;
import org.junit.platform.suite.api.SuiteDisplayName;

/**
 * 集成测试套件
 * 
 * 运行所有集成测试，包括：
 * - 转换集成测试
 * - 管道集成测试
 * - 真实场景集成测试
 * 
 * 运行方式：
 * mvn failsafe:integration-test -Dtest=IntegrationTestSuite
 * 
 * <AUTHOR>
 */
@Suite
@SuiteDisplayName("集成测试套件")
@SelectPackages("com.xylink.sqltranspiler.integration")
public class IntegrationTestSuite {
    // 测试套件类不需要实现任何方法
    // JUnit 5会自动发现和运行指定包下的所有测试
}
