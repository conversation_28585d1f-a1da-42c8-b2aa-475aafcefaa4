package com.xylink.sqltranspiler.unit.core;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator;
import com.xylink.sqltranspiler.core.dialects.shentong.ShentongGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * LOAD DATA语句处理测试
 * 根据rule-db.md要求，验证各数据库对MySQL特有LOAD DATA语句的正确处理
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
@DisplayName("LOAD DATA语句处理测试")
class LoadDataStatementTest {

    private final DamengGenerator damengGenerator = new DamengGenerator();
    private final KingbaseGenerator kingbaseGenerator = new KingbaseGenerator();
    private final ShentongGenerator shentongGenerator = new ShentongGenerator();

    @Test
    @DisplayName("达梦数据库 - LOAD DATA语句应被正确支持")
    void testDamengLoadDataStatement() {
        String sql = "LOAD DATA INFILE '/tmp/data.csv' INTO TABLE test_table " +
                    "FIELDS TERMINATED BY ',' LINES TERMINATED BY '\\n';";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String result = damengGenerator.generate(statement);

        System.out.println("达梦数据库 LOAD DATA 处理结果:");
        System.out.println(result);
        System.out.println("Statement type: " + statement.getClass().getSimpleName());

        // 验证结果是正确的SQL语句（达梦数据库支持LOAD DATA）
        assertTrue(result.contains("LOAD DATA INFILE"));
        assertTrue(result.contains("test_table"));
        assertFalse(result.contains("Unsupported"), "达梦数据库应该支持LOAD DATA语句");
    }

    @Test
    @DisplayName("金仓数据库 - LOAD DATA语句应被标记为不支持")
    void testKingbaseLoadDataStatement() {
        String sql = "LOAD DATA LOCAL INFILE '/path/to/file.csv' INTO TABLE users " +
                    "FIELDS TERMINATED BY ',' ENCLOSED BY '\"' LINES TERMINATED BY '\\n' " +
                    "IGNORE 1 ROWS;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String result = kingbaseGenerator.generate(statement);
        
        // 验证结果包含不支持的说明
        assertTrue(result.contains("LOAD DATA INFILE is MySQL-specific and not supported in KingbaseES database"));
        assertTrue(result.contains("https://dev.mysql.com/doc/refman/8.4/en/load-data.html"));
        assertTrue(result.contains("Please use KingbaseES COPY command or INSERT statements"));
        
        System.out.println("金仓数据库 LOAD DATA 处理结果:");
        System.out.println(result);
    }

    @Test
    @DisplayName("神通数据库 - LOAD DATA语句应被标记为不支持")
    void testShentongLoadDataStatement() {
        String sql = "LOAD DATA INFILE 'data.txt' INTO TABLE products " +
                    "FIELDS TERMINATED BY '\\t' LINES TERMINATED BY '\\r\\n' " +
                    "(id, name, price);";

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);

        String result = shentongGenerator.generate(statement);

        // 验证结果包含不支持的说明
        assertTrue(result.contains("LOAD DATA INFILE is MySQL-specific and not supported in Shentong database"));
        assertTrue(result.contains("https://dev.mysql.com/doc/refman/8.4/en/load-data.html"));
        assertTrue(result.contains("Please use Shentong data import tools or INSERT statements"));

        System.out.println("神通数据库 LOAD DATA 处理结果:");
        System.out.println(result);
    }

    @Test
    @DisplayName("达梦数据库 - SHOW语句应被正确支持")
    void testDamengShowStatement() {
        String sql = "SHOW TABLES;";

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);

        String result = damengGenerator.generate(statement);

        // 验证结果是正确的SQL语句（达梦数据库支持SHOW语句）
        assertTrue(result.contains("SHOW TABLES"));
        assertFalse(result.contains("Unsupported"), "达梦数据库应该支持SHOW语句");

        System.out.println("达梦数据库 SHOW 处理结果:");
        System.out.println(result);
    }

    @Test
    @DisplayName("金仓数据库 - SHOW语句应被正确支持")
    void testKingbaseShowStatement() {
        String sql = "SHOW COLUMNS FROM users;";

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);

        String result = kingbaseGenerator.generate(statement);

        // 验证结果是正确的SQL语句（金仓数据库支持SHOW语句）
        assertTrue(result.contains("SHOW COLUMNS FROM users"));
        assertFalse(result.contains("Unsupported"), "金仓数据库应该支持SHOW语句");

        System.out.println("金仓数据库 SHOW 处理结果:");
        System.out.println(result);
    }

    @Test
    @DisplayName("复杂LOAD DATA语句 - 包含多种选项")
    void testComplexLoadDataStatement() {
        String sql = "LOAD DATA LOW_PRIORITY LOCAL INFILE '/tmp/complex.csv' " +
                    "REPLACE INTO TABLE inventory " +
                    "CHARACTER SET utf8mb4 " +
                    "FIELDS TERMINATED BY ',' OPTIONALLY ENCLOSED BY '\"' ESCAPED BY '\\\\' " +
                    "LINES STARTING BY 'DATA:' TERMINATED BY '\\n' " +
                    "IGNORE 2 LINES " +
                    "(product_id, @var1, quantity, @var2) " +
                    "SET price = @var1 * 1.1, description = @var2;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        // 测试所有三个数据库都正确处理复杂的LOAD DATA语句
        String damengResult = damengGenerator.generate(statement);
        String kingbaseResult = kingbaseGenerator.generate(statement);
        String shentongResult = shentongGenerator.generate(statement);
        
        // 验证各数据库的正确处理
        assertTrue(damengResult.contains("LOAD DATA"), "达梦数据库应该支持LOAD DATA");
        assertFalse(damengResult.contains("Unsupported"), "达梦数据库应该支持LOAD DATA");
        assertTrue(kingbaseResult.contains("LOAD DATA INFILE is MySQL-specific"));
        assertTrue(shentongResult.contains("LOAD DATA INFILE is MySQL-specific"));
        
        // 验证金仓和神通包含官方文档引用（它们不支持LOAD DATA）
        // 达梦支持LOAD DATA，所以不会包含"不支持"的文档引用
        assertTrue(kingbaseResult.contains("https://dev.mysql.com/doc/refman/8.4/en/load-data.html"));
        assertTrue(shentongResult.contains("https://dev.mysql.com/doc/refman/8.4/en/load-data.html"));
        
        System.out.println("复杂 LOAD DATA 语句处理结果:");
        System.out.println("达梦: " + damengResult.substring(0, Math.min(100, damengResult.length())) + "...");
        System.out.println("金仓: " + kingbaseResult.substring(0, Math.min(100, kingbaseResult.length())) + "...");
        System.out.println("神通: " + shentongResult.substring(0, Math.min(100, shentongResult.length())) + "...");
    }
}
