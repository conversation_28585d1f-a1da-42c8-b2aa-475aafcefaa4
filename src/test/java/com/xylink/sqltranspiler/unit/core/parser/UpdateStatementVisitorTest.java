package com.xylink.sqltranspiler.unit.core.parser;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.dml.MultiTableUpdate;
import com.xylink.sqltranspiler.core.ast.dml.UpdateTable;
import com.xylink.sqltranspiler.infrastructure.parser.generated.MySqlLexer;
import com.xylink.sqltranspiler.infrastructure.parser.generated.MySqlParser;
import com.xylink.sqltranspiler.infrastructure.parser.visitors.UpdateStatementVisitor;
import org.antlr.v4.runtime.CharStreams;
import org.antlr.v4.runtime.CommonTokenStream;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * UpdateStatementVisitor的测试类
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
class UpdateStatementVisitorTest {

    private Statement parseUpdateStatement(String sql) {
        MySqlLexer lexer = new MySqlLexer(CharStreams.fromString(sql));
        CommonTokenStream tokens = new CommonTokenStream(lexer);
        MySqlParser parser = new MySqlParser(tokens);
        
        UpdateStatementVisitor visitor = new UpdateStatementVisitor();
        
        // 尝试解析单表UPDATE
        try {
            MySqlParser.SingleUpdateStatementContext singleCtx = parser.singleUpdateStatement();
            if (singleCtx != null) {
                return visitor.visitSingleUpdateStatement(singleCtx);
            }
        } catch (Exception e) {
            // 忽略解析错误，尝试多表UPDATE
        }
        
        // 重置parser
        lexer = new MySqlLexer(CharStreams.fromString(sql));
        tokens = new CommonTokenStream(lexer);
        parser = new MySqlParser(tokens);
        
        // 尝试解析多表UPDATE
        try {
            MySqlParser.MultipleUpdateStatementContext multiCtx = parser.multipleUpdateStatement();
            if (multiCtx != null) {
                return visitor.visitMultipleUpdateStatement(multiCtx);
            }
        } catch (Exception e) {
            // 解析失败
        }
        
        return null;
    }

    @Test
    @DisplayName("测试单表UPDATE解析")
    void testSingleTableUpdate() {
        String sql = "UPDATE users SET name = 'John', email = '<EMAIL>' WHERE id = 1";
        
        Statement statement = parseUpdateStatement(sql);
        assertNotNull(statement, "应该能够解析单表UPDATE语句");
        assertTrue(statement instanceof UpdateTable, "应该解析为UpdateTable");
        
        UpdateTable updateTable = (UpdateTable) statement;
        assertEquals("users", updateTable.getTableId().getTableName());
        assertNotNull(updateTable.getSetClauses());
        assertTrue(updateTable.getSetClauses().size() >= 2, "应该有至少2个SET子句");
        
        System.out.println("Single table UPDATE: " + updateTable);
    }

    @Test
    @DisplayName("测试多表UPDATE解析（通过SingleUpdateStatement）")
    void testMultiTableUpdateViaSingle() {
        String sql = "UPDATE table1 t1 INNER JOIN table2 t2 ON t1.id = t2.id SET t1.status = 'updated', t2.last_modified = NOW() WHERE t1.active = 1";
        
        Statement statement = parseUpdateStatement(sql);
        assertNotNull(statement, "应该能够解析多表UPDATE语句");
        
        if (statement instanceof MultiTableUpdate) {
            MultiTableUpdate multiUpdate = (MultiTableUpdate) statement;
            assertNotNull(multiUpdate.getTableReferences(), "应该有表引用");
            assertNotNull(multiUpdate.getSetClauses(), "应该有SET子句");
            assertTrue(multiUpdate.getSetClauses().size() >= 2, "应该有至少2个SET子句");
            
            System.out.println("Multi-table UPDATE (via single): " + multiUpdate);
            System.out.println("Table references: " + multiUpdate.getTableReferences());
            System.out.println("SET clauses: " + multiUpdate.getSetClauses());
        } else {
            System.out.println("Parsed as: " + statement.getClass().getSimpleName());
            System.out.println("Statement: " + statement);
        }
    }

    @Test
    @DisplayName("测试多表UPDATE解析（通过MultipleUpdateStatement）")
    void testMultiTableUpdateViaMultiple() {
        String sql = "UPDATE table1 t1, table2 t2 SET t1.name = 'updated', t2.status = 'processed' WHERE t1.id = t2.id";
        
        Statement statement = parseUpdateStatement(sql);
        assertNotNull(statement, "应该能够解析多表UPDATE语句");
        
        if (statement instanceof MultiTableUpdate) {
            MultiTableUpdate multiUpdate = (MultiTableUpdate) statement;
            assertNotNull(multiUpdate.getTableReferences(), "应该有表引用");
            assertNotNull(multiUpdate.getSetClauses(), "应该有SET子句");
            
            System.out.println("Multi-table UPDATE (via multiple): " + multiUpdate);
            System.out.println("Table references: " + multiUpdate.getTableReferences());
            System.out.println("SET clauses: " + multiUpdate.getSetClauses());
        } else {
            System.out.println("Parsed as: " + statement.getClass().getSimpleName());
            System.out.println("Statement: " + statement);
        }
    }

    @Test
    @DisplayName("测试带修饰符的UPDATE")
    void testUpdateWithModifiers() {
        String sql = "UPDATE LOW_PRIORITY IGNORE users SET email = '<EMAIL>' WHERE active = 1";
        
        Statement statement = parseUpdateStatement(sql);
        assertNotNull(statement, "应该能够解析带修饰符的UPDATE语句");
        assertTrue(statement instanceof UpdateTable, "应该解析为UpdateTable");
        
        UpdateTable updateTable = (UpdateTable) statement;
        assertTrue(updateTable.isLowPriority(), "应该有LOW_PRIORITY修饰符");
        assertTrue(updateTable.isIgnore(), "应该有IGNORE修饰符");
        
        System.out.println("UPDATE with modifiers: " + updateTable);
    }

    @Test
    @DisplayName("测试多表UPDATE的SET子句解析")
    void testMultiTableSetClauseParsing() {
        String sql = "UPDATE users u JOIN profiles p ON u.id = p.user_id SET u.name = 'John', p.bio = 'Updated bio' WHERE u.active = 1";
        
        Statement statement = parseUpdateStatement(sql);
        assertNotNull(statement, "应该能够解析多表UPDATE语句");
        
        if (statement instanceof MultiTableUpdate) {
            MultiTableUpdate multiUpdate = (MultiTableUpdate) statement;
            
            // 验证SET子句
            assertNotNull(multiUpdate.getSetClauses());
            assertTrue(multiUpdate.getSetClauses().size() >= 2, "应该有至少2个SET子句");
            
            // 检查SET子句的表前缀
            for (MultiTableUpdate.SetClause clause : multiUpdate.getSetClauses()) {
                System.out.println("SET clause: " + clause);
                if (clause.getTableId() != null) {
                    System.out.println("  Table: " + clause.getTableId().getTableName());
                    System.out.println("  Column: " + clause.getColumnName());
                    System.out.println("  Value: " + clause.getValue());
                }
            }
        } else {
            System.out.println("Parsed as: " + statement.getClass().getSimpleName());
        }
    }

    @Test
    @DisplayName("测试错误处理")
    void testErrorHandling() {
        // 测试无效的SQL
        String invalidSql = "UPDATE SET WHERE";
        
        Statement statement = parseUpdateStatement(invalidSql);
        // 即使SQL无效，也不应该抛出异常
        System.out.println("Invalid SQL result: " + (statement != null ? statement.getClass().getSimpleName() : "null"));
    }
}
