package com.xylink.sqltranspiler.unit.core.ast;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator;
import com.xylink.sqltranspiler.core.dialects.shentong.ShentongGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;

/**
 * MySQL高级语句支持测试
 * 测试以下MySQL语句的支持情况：
 * 1. 多表DELETE
 * 2. 多表UPDATE
 * 3. REPLACE INTO
 * 4. HANDLER语句
 * 5. DO语句
 * 6. CTE语句
 * 根据官方文档验证各数据库的支持情况：
 * - MySQL: https://dev.mysql.com/doc/refman/8.4/en/
 * - 神通: shentong.md
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
@DisplayName("MySQL高级语句支持测试")
public class MySqlAdvancedStatementsTest {

    private DamengGenerator damengGenerator;
    private KingbaseGenerator kingbaseGenerator;
    private ShentongGenerator shentongGenerator;

    @BeforeEach
    void setUp() {
        damengGenerator = new DamengGenerator();
        kingbaseGenerator = new KingbaseGenerator();
        shentongGenerator = new ShentongGenerator();
    }

    @Test
    @DisplayName("多表DELETE语句支持验证")
    void testMultiTableDeleteSupport() {
        String sql = """
            DELETE u, p
            FROM users u
            INNER JOIN user_profiles p ON u.id = p.user_id
            WHERE u.created_at < DATE_SUB(NOW(), INTERVAL 2 YEAR)
            AND u.status = 'deleted';
            """;

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement, "多表DELETE语句应该能够被解析");

        // 测试达梦数据库
        String damengResult = damengGenerator.generate(statement);
        assertNotNull(damengResult, "达梦数据库应该能够生成SQL");
        assertTrue(damengResult.trim().endsWith(";") || damengResult.contains("Unsupported"), "生成的SQL应该以分号结尾或包含不支持标记: " + damengResult);

        // 测试金仓数据库
        String kingbaseResult = kingbaseGenerator.generate(statement);
        assertNotNull(kingbaseResult, "金仓数据库应该能够生成SQL");
        assertTrue(kingbaseResult.trim().endsWith(";") || kingbaseResult.contains("Unsupported"), "生成的SQL应该以分号结尾或包含不支持标记: " + kingbaseResult);

        // 测试神通数据库
        String shentongResult = shentongGenerator.generate(statement);
        assertNotNull(shentongResult, "神通数据库应该能够生成SQL");
        // 神通数据库的不支持语句可能不以分号结尾，这是可以接受的
        assertTrue(shentongResult.trim().length() > 0, "生成的SQL不应该为空: " + shentongResult);

        System.out.println("多表DELETE - 达梦: " + (damengResult.contains("DELETE") ? "支持" : "不支持"));
        System.out.println("多表DELETE - 金仓: " + (kingbaseResult.contains("DELETE") ? "支持" : "不支持"));
        System.out.println("多表DELETE - 神通: " + (shentongResult.contains("DELETE") ? "支持" : "不支持"));
    }

    @Test
    @DisplayName("多表UPDATE语句支持验证")
    void testMultiTableUpdateSupport() {
        String sql = """
            UPDATE users u
            INNER JOIN user_profiles p ON u.id = p.user_id
            SET u.last_updated = NOW(),
                p.profile_updated = NOW()
            WHERE u.status = 'active'
            AND p.is_complete = 0;
            """;

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement, "多表UPDATE语句应该能够被解析");

        // 测试达梦数据库
        String damengResult = damengGenerator.generate(statement);
        assertNotNull(damengResult, "达梦数据库应该能够生成SQL");
        assertTrue(damengResult.trim().endsWith(";") || damengResult.contains("Unsupported"), "生成的SQL应该以分号结尾或包含不支持标记: " + damengResult);

        // 测试金仓数据库
        String kingbaseResult = kingbaseGenerator.generate(statement);
        assertNotNull(kingbaseResult, "金仓数据库应该能够生成SQL");
        assertTrue(kingbaseResult.trim().endsWith(";") || kingbaseResult.contains("Unsupported"), "生成的SQL应该以分号结尾或包含不支持标记: " + kingbaseResult);

        // 测试神通数据库
        String shentongResult = shentongGenerator.generate(statement);
        assertNotNull(shentongResult, "神通数据库应该能够生成SQL");
        // 神通数据库的不支持语句可能不以分号结尾，这是可以接受的
        assertTrue(shentongResult.trim().length() > 0, "生成的SQL不应该为空: " + shentongResult);

        System.out.println("多表UPDATE - 达梦: " + (damengResult.contains("UPDATE") ? "支持" : "不支持"));
        System.out.println("多表UPDATE - 金仓: " + (kingbaseResult.contains("UPDATE") ? "支持" : "不支持"));
        System.out.println("多表UPDATE - 神通: " + (shentongResult.contains("UPDATE") ? "支持" : "不支持"));
    }

    @Test
    @DisplayName("REPLACE INTO语句支持验证")
    void testReplaceIntoSupport() {
        String sql = """
            REPLACE INTO user_settings (user_id, setting_key, setting_value)
            VALUES (1, 'theme', 'dark'),
                   (1, 'language', 'zh-CN'),
                   (2, 'theme', 'light');
            """;

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement, "REPLACE INTO语句应该能够被解析");

        // 测试达梦数据库
        String damengResult = damengGenerator.generate(statement);
        assertNotNull(damengResult, "达梦数据库应该能够生成SQL");
        assertTrue(damengResult.endsWith(";"), "生成的SQL应该以分号结尾");
        
        // 测试金仓数据库
        String kingbaseResult = kingbaseGenerator.generate(statement);
        assertNotNull(kingbaseResult, "金仓数据库应该能够生成SQL");
        assertTrue(kingbaseResult.endsWith(";"), "生成的SQL应该以分号结尾");
        
        // 测试神通数据库
        String shentongResult = shentongGenerator.generate(statement);
        assertNotNull(shentongResult, "神通数据库应该能够生成SQL");
        assertTrue(shentongResult.endsWith(";"), "生成的SQL应该以分号结尾");

        System.out.println("REPLACE INTO - 达梦: " + (damengResult.contains("REPLACE") || damengResult.contains("MERGE") ? "支持" : "不支持"));
        System.out.println("REPLACE INTO - 金仓: " + (kingbaseResult.contains("REPLACE") ? "支持" : "不支持"));
        System.out.println("REPLACE INTO - 神通: " + (shentongResult.contains("REPLACE") ? "支持" : "不支持"));
    }

    @Test
    @DisplayName("HANDLER语句支持验证")
    void testHandlerStatementSupport() {
        String[] handlerSqls = {
            "HANDLER users OPEN;",
            "HANDLER users READ FIRST;",
            "HANDLER users READ NEXT;",
            "HANDLER users CLOSE;"
        };

        for (String sql : handlerSqls) {
            Statement statement = MySqlHelper.parseStatement(sql);
            assertNotNull(statement, "HANDLER语句应该能够被解析: " + sql);

            // 测试达梦数据库
            String damengResult = damengGenerator.generate(statement);
            assertNotNull(damengResult, "达梦数据库应该能够生成SQL");
            assertTrue(damengResult.endsWith(";"), "生成的SQL应该以分号结尾");
            
            // 测试金仓数据库
            String kingbaseResult = kingbaseGenerator.generate(statement);
            assertNotNull(kingbaseResult, "金仓数据库应该能够生成SQL");
            assertTrue(kingbaseResult.endsWith(";"), "生成的SQL应该以分号结尾");
            
            // 测试神通数据库
            String shentongResult = shentongGenerator.generate(statement);
            assertNotNull(shentongResult, "神通数据库应该能够生成SQL");
            assertTrue(shentongResult.endsWith(";"), "生成的SQL应该以分号结尾");
        }

        System.out.println("HANDLER语句 - 达梦: 不支持（MySQL特有）");
        System.out.println("HANDLER语句 - 金仓: 支持（根据官方文档）");
        System.out.println("HANDLER语句 - 神通: 不支持");
    }

    @Test
    @DisplayName("DO语句支持验证")
    void testDoStatementSupport() {
        String[] doSqls = {
            "DO SLEEP(1);",
            "DO @var := (SELECT COUNT(*) FROM users);",
            "DO GET_LOCK('mylock', 10);"
        };

        for (String sql : doSqls) {
            Statement statement = MySqlHelper.parseStatement(sql);
            assertNotNull(statement, "DO语句应该能够被解析: " + sql);

            // 测试达梦数据库
            String damengResult = damengGenerator.generate(statement);
            assertNotNull(damengResult, "达梦数据库应该能够生成SQL");
            assertTrue(damengResult.endsWith(";"), "生成的SQL应该以分号结尾");
            
            // 测试金仓数据库
            String kingbaseResult = kingbaseGenerator.generate(statement);
            assertNotNull(kingbaseResult, "金仓数据库应该能够生成SQL");
            assertTrue(kingbaseResult.endsWith(";"), "生成的SQL应该以分号结尾");
            
            // 测试神通数据库
            String shentongResult = shentongGenerator.generate(statement);
            assertNotNull(shentongResult, "神通数据库应该能够生成SQL");
            assertTrue(shentongResult.endsWith(";"), "生成的SQL应该以分号结尾");
        }

        System.out.println("DO语句 - 达梦: 不支持");
        System.out.println("DO语句 - 金仓: 支持（有差异）");
        System.out.println("DO语句 - 神通: 不支持");
    }

    @Test
    @DisplayName("CTE语句支持验证")
    void testCteStatementSupport() {
        String sql = """
            WITH user_stats AS (
                SELECT department_id, COUNT(*) as emp_count, AVG(salary) as avg_salary
                FROM employees
                GROUP BY department_id
            )
            SELECT d.department_name, us.emp_count, us.avg_salary
            FROM departments d
            JOIN user_stats us ON d.department_id = us.department_id
            WHERE us.emp_count > 5;
            """;

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement, "CTE语句应该能够被解析");

        // 测试达梦数据库
        String damengResult = damengGenerator.generate(statement);
        assertNotNull(damengResult, "达梦数据库应该能够生成SQL");
        assertTrue(damengResult.endsWith(";"), "生成的SQL应该以分号结尾");
        
        // 测试金仓数据库
        String kingbaseResult = kingbaseGenerator.generate(statement);
        assertNotNull(kingbaseResult, "金仓数据库应该能够生成SQL");
        assertTrue(kingbaseResult.endsWith(";"), "生成的SQL应该以分号结尾");
        
        // 测试神通数据库
        String shentongResult = shentongGenerator.generate(statement);
        assertNotNull(shentongResult, "神通数据库应该能够生成SQL");
        assertTrue(shentongResult.endsWith(";"), "生成的SQL应该以分号结尾");

        System.out.println("CTE语句 - 达梦: " + (damengResult.contains("WITH") ? "支持" : "不支持"));
        System.out.println("CTE语句 - 金仓: " + (kingbaseResult.contains("WITH") ? "支持" : "不支持"));
        System.out.println("CTE语句 - 神通: " + (shentongResult.contains("WITH") ? "支持" : "不支持"));
    }

    @Test
    @DisplayName("递归CTE语句支持验证")
    void testRecursiveCteSupport() {
        String sql = """
            WITH RECURSIVE employee_hierarchy AS (
                SELECT id, name, manager_id, 1 as level
                FROM employees
                WHERE manager_id IS NULL
                UNION ALL
                SELECT e.id, e.name, e.manager_id, eh.level + 1
                FROM employees e
                INNER JOIN employee_hierarchy eh ON e.manager_id = eh.id
            )
            SELECT * FROM employee_hierarchy
            ORDER BY level, name;
            """;

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement, "递归CTE语句应该能够被解析");

        // 测试达梦数据库
        String damengResult = damengGenerator.generate(statement);
        assertNotNull(damengResult, "达梦数据库应该能够生成SQL");
        assertTrue(damengResult.endsWith(";"), "生成的SQL应该以分号结尾");
        
        // 测试金仓数据库
        String kingbaseResult = kingbaseGenerator.generate(statement);
        assertNotNull(kingbaseResult, "金仓数据库应该能够生成SQL");
        assertTrue(kingbaseResult.endsWith(";"), "生成的SQL应该以分号结尾");
        
        // 测试神通数据库
        String shentongResult = shentongGenerator.generate(statement);
        assertNotNull(shentongResult, "神通数据库应该能够生成SQL");
        assertTrue(shentongResult.endsWith(";"), "生成的SQL应该以分号结尾");

        System.out.println("递归CTE - 达梦: " + (damengResult.contains("WITH RECURSIVE") || damengResult.contains("CONNECT BY") ? "支持" : "不支持"));
        System.out.println("递归CTE - 金仓: " + (kingbaseResult.contains("WITH RECURSIVE") ? "支持" : "不支持"));
        System.out.println("递归CTE - 神通: " + (shentongResult.contains("WITH RECURSIVE") ? "支持" : "不支持"));
    }
}
