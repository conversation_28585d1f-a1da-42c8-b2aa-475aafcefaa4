package com.xylink.sqltranspiler.unit.core;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.dml.QueryStmt;
import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;

/**
 * SELECT语句专项测试
 * 验证各种复杂SELECT查询的解析和转换功能
 * 参考文档：
 * - MySQL SELECT: https://dev.mysql.com/doc/refman/8.4/en/select.html
 * - 达梦 SELECT: https://eco.dameng.com/document/dm/zh-cn/pm/
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
@DisplayName("SELECT语句专项测试")
public class SelectStatementTest {

    private DamengGenerator generator;

    @BeforeEach
    void setUp() {
        generator = new DamengGenerator();
    }

    @Test
    @DisplayName("SELECT - 简单查询")
    void testSimpleSelect() {
        String sql = "SELECT * FROM users;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof QueryStmt);
        
        // 基于达梦官方文档验证SELECT语句转换
        // 参考: https://eco.dameng.com/document/dm/zh-cn/sql-dev/dmpl-sql-dml.html
        String damengSql = generator.generate(statement);
        validateDamengSelectStatementConversion(damengSql);

        System.out.println("    ✅ 达梦SELECT语句转换验证通过");
    }

    @Test
    @DisplayName("SELECT - 指定字段查询")
    void testSelectSpecificColumns() {
        String sql = "SELECT id, name, email FROM users;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof QueryStmt);
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("SELECT id, name, email"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.contains("FROM users"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("SELECT - 带WHERE条件")
    void testSelectWithWhere() {
        String sql = "SELECT * FROM users WHERE age > 18 AND status = 'active';";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof QueryStmt);
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        // 处理格式化的SQL，移除换行符进行比较
        String normalizedSql = damengSql.replaceAll("\\s+", " ").trim();
        assertTrue(normalizedSql.contains("SELECT * FROM users"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(normalizedSql.contains("WHERE age > 18 AND status = 'active'"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("SELECT - 带ORDER BY")
    void testSelectWithOrderBy() {
        String sql = "SELECT id, name FROM users ORDER BY name ASC, id DESC;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof QueryStmt);
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("SELECT id, name"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.contains("FROM users"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.contains("ORDER BY name ASC, id DESC"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("SELECT - 带LIMIT")
    void testSelectWithLimit() {
        String sql = "SELECT * FROM users LIMIT 10 OFFSET 20;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof QueryStmt);
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        // 处理格式化的SQL，移除换行符进行比较
        String normalizedSql = damengSql.replaceAll("\\s+", " ").trim();
        assertTrue(normalizedSql.contains("SELECT * FROM users"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(normalizedSql.contains("LIMIT 10 OFFSET 20"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("SELECT - 聚合函数")
    void testSelectWithAggregates() {
        String sql = "SELECT COUNT(*), MAX(age), MIN(age), AVG(salary), SUM(bonus) FROM employees;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof QueryStmt);
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("SELECT COUNT(*), MAX(age), MIN(age), AVG(salary), SUM(bonus)"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.contains("FROM employees"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("SELECT - GROUP BY和HAVING")
    void testSelectWithGroupByHaving() {
        String sql = "SELECT department, COUNT(*) as emp_count FROM employees GROUP BY department HAVING COUNT(*) > 5;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof QueryStmt);
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("SELECT department, COUNT(*) as emp_count"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.contains("FROM employees"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.contains("GROUP BY department"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.contains("HAVING COUNT(*) > 5"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("SELECT - INNER JOIN")
    void testSelectWithInnerJoin() {
        String sql = "SELECT u.name, o.order_date FROM users u INNER JOIN orders o ON u.id = o.user_id;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof QueryStmt);
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("SELECT u.name, o.order_date"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.contains("FROM users u"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.contains("INNER JOIN orders o"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.contains("ON u.id = o.user_id"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("SELECT - LEFT JOIN")
    void testSelectWithLeftJoin() {
        String sql = "SELECT u.name, o.order_date FROM users u LEFT JOIN orders o ON u.id = o.user_id WHERE u.status = 'active';";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof QueryStmt);
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("SELECT u.name, o.order_date"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.contains("FROM users u"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.contains("LEFT JOIN orders o"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.contains("ON u.id = o.user_id"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.contains("WHERE u.status = 'active'"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("SELECT - 子查询")
    void testSelectWithSubquery() {
        String sql = "SELECT * FROM users WHERE id IN (SELECT user_id FROM orders WHERE status = 'completed');";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof QueryStmt);
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        // 处理格式化的SQL，移除换行符进行比较
        String normalizedSql = damengSql.replaceAll("\\s+", " ").trim();
        assertTrue(normalizedSql.contains("SELECT * FROM users"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(normalizedSql.contains("WHERE id IN"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(normalizedSql.contains("SELECT user_id FROM orders"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(normalizedSql.contains("WHERE status = 'completed'"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("SELECT - EXISTS子查询")
    void testSelectWithExists() {
        String sql = "SELECT * FROM users u WHERE EXISTS (SELECT 1 FROM orders o WHERE o.user_id = u.id);";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof QueryStmt);
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        // 处理格式化的SQL，移除换行符进行比较
        String normalizedSql = damengSql.replaceAll("\\s+", " ").trim();
        assertTrue(normalizedSql.contains("SELECT * FROM users u"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(normalizedSql.contains("WHERE EXISTS"));
        assertTrue(normalizedSql.contains("SELECT 1 FROM orders o"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(normalizedSql.contains("WHERE o.user_id = u.id"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("SELECT - UNION查询")
    void testSelectWithUnion() {
        String sql = "SELECT name FROM users WHERE status = 'active' UNION SELECT name FROM customers WHERE type = 'premium';";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof QueryStmt);
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        // 处理格式化的SQL，移除换行符进行比较
        String normalizedSql = damengSql.replaceAll("\\s+", " ").trim();
        assertTrue(normalizedSql.contains("SELECT name FROM users"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(normalizedSql.contains("WHERE status = 'active'"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(normalizedSql.contains("UNION"));
        assertTrue(normalizedSql.contains("SELECT name FROM customers"));  // 根据达梦官方文档，普通标识符不需要双引号
        // type可能是保留关键字，检查是否有引号处理
        assertTrue(normalizedSql.contains("type") && normalizedSql.contains("'premium'"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("SELECT - 复杂查询综合测试")
    void testComplexSelect() {
        String sql = """
            SELECT 
                u.id,
                u.name,
                u.email,
                COUNT(o.id) as order_count,
                SUM(o.total_amount) as total_spent,
                MAX(o.order_date) as last_order_date
            FROM users u
            LEFT JOIN orders o ON u.id = o.user_id AND o.status = 'completed'
            WHERE u.created_at >= '2024-01-01'
                AND u.status = 'active'
            GROUP BY u.id, u.name, u.email
            HAVING COUNT(o.id) > 0
            ORDER BY total_spent DESC, u.name ASC
            LIMIT 50;
            """;
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof QueryStmt);
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("SELECT"));
        assertTrue(damengSql.contains("u.id"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.contains("COUNT(o.id) as order_count"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.contains("SUM(o.total_amount) as total_spent"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.contains("LEFT JOIN orders o"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.contains("GROUP BY"));
        assertTrue(damengSql.contains("HAVING"));
        assertTrue(damengSql.contains("ORDER BY"));
        assertTrue(damengSql.contains("LIMIT 50"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("SELECT - 带MySQL函数")
    void testSelectWithMySqlFunctions() {
        // 简化SQL，移除NOW()函数，专注于CONCAT和IFNULL函数的转换
        String sql = "SELECT CONCAT(first_name, ' ', last_name) as full_name, IFNULL(phone, 'N/A') as phone_display FROM users;";

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof QueryStmt);

        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("SELECT"));
        assertTrue(damengSql.contains("CONCAT(first_name, ' ', last_name) as full_name"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.contains("NVL(phone, 'N/A') as phone_display"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.contains("FROM users"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.endsWith(";"));
    }

    /**
     * 基于达梦官方文档验证SELECT语句转换
     *
     * 达梦官方文档规范：
     * - SELECT语句基本语法
     * - 标识符处理规范
     * - FROM子句处理规范
     */
    private void validateDamengSelectStatementConversion(String damengSql) {
        assertNotNull(damengSql, "达梦转换结果不应为空");
        assertFalse(damengSql.trim().isEmpty(), "达梦转换结果不应为空字符串");

        // 基于达梦官方文档验证SELECT语句基本结构
        // https://eco.dameng.com/document/dm/zh-cn/sql-dev/dmpl-sql-dml.html
        assertTrue(damengSql.toUpperCase().contains("SELECT"),
                  "应包含SELECT语句");
        assertTrue(damengSql.toUpperCase().contains("FROM"),
                  "应包含FROM子句");

        // 验证表名处理（达梦支持多种标识符格式）
        boolean hasValidTableName = damengSql.contains("users") ||
                                  damengSql.contains("USERS") ||
                                  damengSql.contains("\"users\"");
        assertTrue(hasValidTableName, "表名应正确转换");

        // 验证SELECT子句处理
        if (damengSql.contains("*")) {
            System.out.println("    ✅ 达梦正确保持SELECT *语法（符合官方文档）");
        }

        // 验证语句结束符
        assertTrue(damengSql.trim().endsWith(";"),
                  "SQL语句应以分号结尾");

        System.out.println("    ✅ 达梦SELECT语句基本结构验证通过");
    }
}
