package com.xylink.sqltranspiler.unit.core;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;

/**
 * MySQL特有语法测试
 * 测试MySQL特有的语法结构，确保正确转换为达梦兼容格式
 * 参考文档：
 * - MySQL: https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦: https://eco.dameng.com/document/dm/zh-cn/pm/
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
@DisplayName("MySQL特有语法测试")
public class MySqlSpecificSyntaxTest {

    private DamengGenerator generator;

    @BeforeEach
    void setUp() {
        generator = new DamengGenerator();
    }

    // ================================ MySQL特有的INSERT语法 ================================

    // REPLACE INTO测试已移至ReplaceIntoStatementCompatibilityTest，避免重复测试

    @Test
    @DisplayName("INSERT - INSERT DELAYED")
    void testInsertDelayed() {
        String sql = "INSERT DELAYED INTO logs (message, created_at) VALUES ('Test message', NOW());";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("INSERT"));
        assertTrue(damengSql.contains("logs"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("INSERT - INSERT HIGH_PRIORITY")
    void testInsertHighPriority() {
        String sql = "INSERT HIGH_PRIORITY INTO urgent_data (data) VALUES ('Critical data');";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("INSERT"));
        assertTrue(damengSql.contains("urgent_data"));
        assertTrue(damengSql.endsWith(";"));
    }

    // ================================ MySQL特有的UPDATE语法 ================================

    @Test
    @DisplayName("UPDATE - UPDATE LOW_PRIORITY")
    void testUpdateLowPriority() {
        String sql = "UPDATE LOW_PRIORITY users SET last_login = NOW() WHERE id = 1;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("UPDATE"));
        assertTrue(damengSql.contains("users"));
        assertTrue(damengSql.contains("SYSDATE")); // NOW() 应该转换为 SYSDATE
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("UPDATE - UPDATE IGNORE")
    void testUpdateIgnore() {
        String sql = "UPDATE IGNORE users SET email = '<EMAIL>' WHERE id = 1;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("UPDATE"));
        assertTrue(damengSql.contains("users"));
        assertTrue(damengSql.endsWith(";"));
    }

    // ================================ MySQL特有的SELECT语法 ================================

    @Test
    @DisplayName("SELECT - SELECT HIGH_PRIORITY")
    void testSelectHighPriority() {
        String sql = "SELECT HIGH_PRIORITY * FROM critical_data WHERE status = 'urgent';";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("SELECT"));
        assertTrue(damengSql.contains("critical_data"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("SELECT - SELECT DISTINCT")
    void testSelectDistinct() {
        String sql = "SELECT DISTINCT category, status FROM products ORDER BY category;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("SELECT"));
        assertTrue(damengSql.contains("DISTINCT"));
        assertTrue(damengSql.contains("products"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("SELECT - SELECT SQL_CALC_FOUND_ROWS")
    void testSelectSqlCalcFoundRows() {
        String sql = "SELECT SQL_CALC_FOUND_ROWS * FROM users LIMIT 10;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("SELECT"));
        assertTrue(damengSql.contains("users"));
        assertTrue(damengSql.endsWith(";"));
    }

    // ================================ MySQL特有的函数 ================================

    @Test
    @DisplayName("MySQL函数 - GROUP_CONCAT")
    void testGroupConcat() {
        String sql = "SELECT category, GROUP_CONCAT(name ORDER BY name SEPARATOR ', ') AS product_names FROM products GROUP BY category;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("SELECT"));
        assertTrue(damengSql.contains("GROUP_CONCAT"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("MySQL函数 - FOUND_ROWS")
    void testFoundRows() {
        String sql = "SELECT FOUND_ROWS() AS total_count;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("SELECT"));
        assertTrue(damengSql.contains("FOUND_ROWS"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("MySQL函数 - LAST_INSERT_ID")
    void testLastInsertId() {
        String sql = "SELECT LAST_INSERT_ID() AS new_id;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("SELECT"));
        assertTrue(damengSql.contains("LAST_INSERT_ID"));
        assertTrue(damengSql.endsWith(";"));
    }

    // ================================ MySQL特有的数据类型 ================================

    @Test
    @DisplayName("MySQL数据类型 - ENUM和SET")
    void testEnumAndSetTypes() {
        String sql = "CREATE TABLE test_types (id INT, status ENUM('active', 'inactive', 'pending'), permissions SET('read', 'write', 'delete'));";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("CREATE TABLE"));
        assertTrue(damengSql.contains("test_types"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("MySQL数据类型 - TINYINT(1)作为BOOLEAN")
    void testTinyintAsBoolean() {
        String sql = "CREATE TABLE flags (id INT, is_active TINYINT(1) DEFAULT 0, is_verified TINYINT(1) DEFAULT 1);";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("CREATE TABLE"));
        assertTrue(damengSql.contains("flags"));
        // TINYINT(1) 应该转换为 BIT 或 BOOLEAN
        assertTrue(damengSql.endsWith(";"));
    }

    // ================================ MySQL特有的索引语法 ================================

    @Test
    @DisplayName("MySQL索引 - FULLTEXT索引")
    void testFulltextIndex() {
        String sql = "CREATE FULLTEXT INDEX idx_content ON articles (title, content);";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("CREATE"));
        assertTrue(damengSql.contains("INDEX"));
        assertTrue(damengSql.contains("articles"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("MySQL索引 - SPATIAL索引")
    void testSpatialIndex() {
        String sql = "CREATE SPATIAL INDEX idx_location ON places (coordinates);";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("CREATE"));
        assertTrue(damengSql.contains("INDEX"));
        assertTrue(damengSql.contains("places"));
        assertTrue(damengSql.endsWith(";"));
    }

    // ================================ MySQL特有的表选项 ================================

    @Test
    @DisplayName("MySQL表选项 - ENGINE和CHARSET")
    void testTableEngineAndCharset() {
        String sql = "CREATE TABLE test_table (id INT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("CREATE TABLE"));
        assertTrue(damengSql.contains("test_table"));
        // ENGINE 和 CHARSET 应该被移除或转换
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("MySQL表选项 - AUTO_INCREMENT起始值")
    void testAutoIncrementStartValue() {
        String sql = "CREATE TABLE counters (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(50)) AUTO_INCREMENT=1000;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("CREATE TABLE"));
        assertTrue(damengSql.contains("counters"));
        assertTrue(damengSql.contains("IDENTITY")); // AUTO_INCREMENT 应该转换为 IDENTITY
        assertTrue(damengSql.endsWith(";"));
    }
}
