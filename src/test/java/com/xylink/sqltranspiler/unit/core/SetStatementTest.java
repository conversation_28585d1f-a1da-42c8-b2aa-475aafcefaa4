package com.xylink.sqltranspiler.unit.core;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.common.SetStatement;
import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;

/**
 * SET语句转换测试
 * 验证MySQL SET语句到达梦数据库的转换功能
 * 参考文档：
 * - MySQL: https://dev.mysql.com/doc/refman/8.4/en/set-variable.html
 * - 达梦: https://eco.dameng.com/document/dm/zh-cn/pm/
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
public class SetStatementTest {

    private DamengGenerator generator;
    private com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator kingbaseGenerator;

    @BeforeEach
    void setUp() {
        generator = new DamengGenerator();
        kingbaseGenerator = new com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator();
    }

    @Test
    void testSetForeignKeyChecks() {
        String sql = "SET `foreign_key_checks` = OFF";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement, "解析结果不应为null");
        assertTrue(statement instanceof SetStatement, "应该解析为SetStatement类型");
        
        SetStatement setStatement = (SetStatement) statement;
        assertTrue(setStatement.getVariableName().contains("foreign_key_checks"));
        assertEquals("OFF", setStatement.getValue());
        
        // 测试转换为达梦SQL - 应该转换为注释
        String damengSql = generator.generate(statement);
        assertNotNull(damengSql);
        assertTrue(damengSql.startsWith("--"));
        assertTrue(damengSql.contains("foreign_key_checks"));
        assertTrue(damengSql.contains("达梦"));
    }

    @Test
    void testSetNames() {
        String sql = "SET NAMES utf8mb4";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof SetStatement);
        
        SetStatement setStatement = (SetStatement) statement;
        assertEquals("names", setStatement.getVariableName());
        assertEquals("utf8mb4", setStatement.getValue());
        
        // 测试转换为达梦SQL - 应该转换为注释
        String damengSql = generator.generate(statement);
        assertNotNull(damengSql);
        assertTrue(damengSql.startsWith("--"));
        assertTrue(damengSql.contains("NAMES"));
        assertTrue(damengSql.contains("达梦"));
    }

    @Test
    void testSetCharacterSet() {
        String sql = "SET CHARACTER SET utf8";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof SetStatement);
        
        SetStatement setStatement = (SetStatement) statement;
        assertEquals("character_set_client", setStatement.getVariableName());
        assertEquals("utf8", setStatement.getValue());
        
        // 测试转换为达梦SQL - 应该转换为注释
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.startsWith("--"));
        assertTrue(damengSql.contains("CHARACTER SET"));
        assertTrue(damengSql.contains("达梦"));
    }

    @Test
    void testSetAutocommitOn() {
        String sql = "SET AUTOCOMMIT = 1";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof SetStatement);
        
        SetStatement setStatement = (SetStatement) statement;
        assertEquals("autocommit", setStatement.getVariableName());
        assertEquals("1", setStatement.getValue());
        
        // 测试转换为达梦SQL - 应该保持AUTOCOMMIT设置
        String damengSql = generator.generate(statement);
        assertNotNull(damengSql);
        assertFalse(damengSql.startsWith("--"));
        assertTrue(damengSql.contains("SET AUTOCOMMIT ON"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    void testSetAutocommitOff() {
        String sql = "SET AUTOCOMMIT = 0";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof SetStatement);
        
        SetStatement setStatement = (SetStatement) statement;
        assertEquals("autocommit", setStatement.getVariableName());
        assertEquals("0", setStatement.getValue());
        
        // 测试转换为达梦SQL - 应该保持AUTOCOMMIT设置
        String damengSql = generator.generate(statement);
        assertNotNull(damengSql);
        assertFalse(damengSql.startsWith("--"));
        assertTrue(damengSql.contains("SET AUTOCOMMIT OFF"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    void testSetSqlMode() {
        String sql = "SET sql_mode = 'STRICT_TRANS_TABLES'";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof SetStatement);
        
        SetStatement setStatement = (SetStatement) statement;
        assertTrue(setStatement.getVariableName().contains("sql_mode"));
        assertEquals("'STRICT_TRANS_TABLES'", setStatement.getValue());
        
        // 测试转换为达梦SQL - 应该转换为注释
        String damengSql = generator.generate(statement);
        assertNotNull(damengSql);
        assertTrue(damengSql.startsWith("--"));
        assertTrue(damengSql.contains("sql_mode"));
        assertTrue(damengSql.contains("达梦"));
    }

    @Test
    void testSetTimeZone() {
        String sql = "SET time_zone = '+08:00'";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof SetStatement);
        
        SetStatement setStatement = (SetStatement) statement;
        assertTrue(setStatement.getVariableName().contains("time_zone"));
        assertEquals("'+08:00'", setStatement.getValue());
        
        // 测试转换为达梦SQL - 应该转换为注释
        String damengSql = generator.generate(statement);
        assertNotNull(damengSql);
        assertTrue(damengSql.startsWith("--"));
        assertTrue(damengSql.contains("time_zone"));
        assertTrue(damengSql.contains("达梦"));
    }

    @Test
    void testSetCustomVariable() {
        String sql = "SET @custom_var = 'value'";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof SetStatement);
        
        SetStatement setStatement = (SetStatement) statement;
        assertTrue(setStatement.getVariableName().contains("@custom_var"));
        assertEquals("'value'", setStatement.getValue());
        
        // 测试转换为达梦SQL - 自定义变量应该直接转换
        String damengSql = generator.generate(statement);
        assertNotNull(damengSql);
        assertFalse(damengSql.startsWith("--"));
        assertTrue(damengSql.contains("SET"));
        assertTrue(damengSql.contains("@custom_var"));
        assertTrue(damengSql.contains("'value'"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    void testSetVariableWithExpression() {
        String sql = "SET @counter = @counter + 1";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof SetStatement);
        
        SetStatement setStatement = (SetStatement) statement;
        assertTrue(setStatement.getVariableName().contains("@counter"));
        assertTrue(setStatement.getValue().contains("@counter") && setStatement.getValue().contains("1"));
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        assertNotNull(damengSql);
        assertFalse(damengSql.startsWith("--"));
        assertTrue(damengSql.contains("SET"));
        assertTrue(damengSql.contains("@counter"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    void testSetSessionVariable() {
        String sql = "SET SESSION wait_timeout = 28800";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof SetStatement);
        
        SetStatement setStatement = (SetStatement) statement;
        assertTrue(setStatement.getVariableName().contains("wait_timeout"));
        assertEquals("28800", setStatement.getValue());
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        assertNotNull(damengSql);
        assertFalse(damengSql.startsWith("--"));
        assertTrue(damengSql.contains("SET"));
        assertTrue(damengSql.contains("wait_timeout"));
        assertTrue(damengSql.contains("28800"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    void testSetGlobalVariable() {
        String sql = "SET GLOBAL max_connections = 1000";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof SetStatement);
        
        SetStatement setStatement = (SetStatement) statement;
        assertTrue(setStatement.getVariableName().contains("max_connections"));
        assertEquals("1000", setStatement.getValue());
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        assertNotNull(damengSql);
        assertFalse(damengSql.startsWith("--"));
        assertTrue(damengSql.contains("SET"));
        assertTrue(damengSql.contains("max_connections"));
        assertTrue(damengSql.contains("1000"));
        assertTrue(damengSql.endsWith(";"));
    }

    // ================================== 金仓数据库测试 ==================================

    @Test
    void testSetNamesUtf8mb4ForKingbase() {
        String sql = "SET NAMES utf8mb4";

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof SetStatement);

        SetStatement setStatement = (SetStatement) statement;
        assertEquals("names", setStatement.getVariableName());
        assertEquals("utf8mb4", setStatement.getValue());

        // 基于金仓官方文档验证SET NAMES语句转换
        // 参考: https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/
        String kingbaseSql = kingbaseGenerator.generate(statement);
        validateKingbaseSetNamesConversion(kingbaseSql);

        System.out.println("    ✅ 金仓SET NAMES语句转换验证通过");
    }

    @Test
    void testSetNamesLatin1ForKingbase() {
        String sql = "SET NAMES latin1";

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof SetStatement);

        SetStatement setStatement = (SetStatement) statement;
        assertEquals("names", setStatement.getVariableName());
        assertEquals("latin1", setStatement.getValue());

        // 测试转换为金仓SQL
        String kingbaseSql = kingbaseGenerator.generate(statement);
        assertNotNull(kingbaseSql);
        assertEquals("SET NAMES LATIN1;", kingbaseSql);
        assertTrue(kingbaseSql.contains("LATIN1"), "应包含金仓的LATIN1字符集");
    }

    @Test
    void testSetNamesGbkForKingbase() {
        String sql = "SET NAMES gbk";

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof SetStatement);

        SetStatement setStatement = (SetStatement) statement;
        assertEquals("names", setStatement.getVariableName());
        assertEquals("gbk", setStatement.getValue());

        // 测试转换为金仓SQL
        String kingbaseSql = kingbaseGenerator.generate(statement);
        assertNotNull(kingbaseSql);
        assertEquals("SET NAMES GBK;", kingbaseSql);
        assertTrue(kingbaseSql.contains("GBK"), "应包含金仓的GBK字符集");
    }

    @Test
    void testSetNamesCharsetMappingForKingbase() {
        // 测试字符集映射表
        String[][] charsetMappings = {
            {"utf8mb4", "UTF8"},
            {"utf8", "UTF8"},
            {"latin1", "LATIN1"},
            {"ascii", "SQL_ASCII"},
            {"gbk", "GBK"},
            {"gb2312", "EUC_CN"}
        };

        for (String[] mapping : charsetMappings) {
            String mysqlCharset = mapping[0];
            String expectedKingbaseCharset = mapping[1];

            String sql = "SET NAMES " + mysqlCharset;
            Statement statement = MySqlHelper.parseStatement(sql);
            String kingbaseSql = kingbaseGenerator.generate(statement);

            assertEquals("SET NAMES " + expectedKingbaseCharset + ";", kingbaseSql,
                        "字符集映射错误: " + mysqlCharset + " -> " + expectedKingbaseCharset);
        }
    }

    /**
     * 基于金仓官方文档验证SET NAMES语句转换
     *
     * 金仓官方文档规范：
     * - MySQL字符集兼容性
     * - 字符集映射规则
     * - SET NAMES语句处理
     */
    private void validateKingbaseSetNamesConversion(String kingbaseSql) {
        assertNotNull(kingbaseSql, "金仓转换结果不应为空");
        assertFalse(kingbaseSql.trim().isEmpty(), "金仓转换结果不应为空字符串");

        // 基于金仓官方文档验证SET NAMES语句基本结构
        // https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/
        assertTrue(kingbaseSql.toUpperCase().contains("SET NAMES"),
                  "应包含SET NAMES语句");

        // 验证字符集转换（基于金仓MySQL兼容性文档）
        if (kingbaseSql.contains("UTF8")) {
            System.out.println("    ✅ 金仓正确将utf8mb4转换为UTF8（符合官方文档）");
            assertFalse(kingbaseSql.contains("utf8mb4"),
                       "不应包含MySQL的utf8mb4字符集");
            assertTrue(kingbaseSql.contains("UTF8"),
                      "应包含金仓的UTF8字符集");
        }

        // 验证语句结束符
        assertTrue(kingbaseSql.trim().endsWith(";"),
                  "SQL语句应以分号结尾");

        System.out.println("    ✅ 金仓SET NAMES语句基本结构验证通过");
    }
}
