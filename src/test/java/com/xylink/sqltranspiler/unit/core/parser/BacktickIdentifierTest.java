package com.xylink.sqltranspiler.unit.core.parser;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import com.xylink.sqltranspiler.core.ast.dml.InsertTable;
import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
/**
 * 测试MySQL反引号标识符的解析和转换
 * 这个测试类验证了我们修复的ANTLR词法规则冲突问题：
 * - STRING_LITERAL 规则不再包含 BQUOTA_STRING
 * - 反引号标识符被正确识别为 REVERSE_QUOTE_ID
 * - MySQL关键字作为字段名时能正确处理
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
@DisplayName("反引号标识符支持测试")
public class BacktickIdentifierTest {

    private DamengGenerator generator;

    @BeforeEach
    void setUp() {
        generator = new DamengGenerator();
    }

    @Test
    @DisplayName("测试简单反引号表名")
    void testSimpleBacktickTableName() {
        String sql = "CREATE TABLE `test` (id INT);";

        List<Statement> statements = MySqlHelper.parseMultiStatement(sql);
        assertEquals(1, statements.size());

        CreateTable createTable = (CreateTable) statements.get(0);
        assertEquals("test", createTable.getTableId().getTableName());

        String result = generator.generate(createTable);
        // 根据达梦官方文档，普通标识符不需要双引号，达梦会自动处理大小写
        // 重要的是反引号被正确解析，而不是是否添加双引号
        assertTrue(result.contains("CREATE TABLE test"));
    }

    @Test
    @DisplayName("测试反引号字段名 - MySQL关键字")
    void testBacktickColumnNamesWithKeywords() {
        String sql = "CREATE TABLE test_table (" +
                     "id INT PRIMARY KEY, " +
                     "`name` VARCHAR(100), " +
                     "`status` VARCHAR(50), " +
                     "`type` VARCHAR(50)" +
                     ");";
        
        List<Statement> statements = MySqlHelper.parseMultiStatement(sql);
        assertEquals(1, statements.size());
        
        CreateTable createTable = (CreateTable) statements.get(0);
        String result = generator.generate(createTable);
        System.out.println("Generated result: " + result);

        // 根据达梦官方文档，只有真正的保留关键字才需要双引号
        // TYPE和USER是达梦保留关键字，需要双引号
        // NAME和STATUS不是达梦保留关键字，不需要双引号
        assertTrue(result.contains("name")); // 普通标识符，无需双引号
        assertTrue(result.contains("status")); // 普通标识符，无需双引号
        assertTrue(result.contains("\"type\"")); // 达梦保留关键字，需要双引号
    }

    @Test
    @DisplayName("测试混合使用反引号和普通标识符")
    void testMixedBacktickAndNormalIdentifiers() {
        String sql = "CREATE TABLE `users` (" +
                     "id INT, " +
                     "`name` VARCHAR(100), " +
                     "email VARCHAR(255), " +
                     "`created_at` TIMESTAMP" +
                     ");";
        
        List<Statement> statements = MySqlHelper.parseMultiStatement(sql);
        assertEquals(1, statements.size());

        CreateTable createTable = (CreateTable) statements.get(0);
        String result = generator.generate(createTable);
        
        // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("CREATE TABLE users")); // 普通表名，无需双引号
        assertTrue(result.contains("name")); // 普通标识符，无需双引号
        assertTrue(result.contains("email")); // 普通标识符，无需双引号
        assertTrue(result.contains("created_at")); // 普通标识符，无需双引号
    }

    @Test
    @DisplayName("测试INSERT语句中的反引号标识符")
    void testBacktickInInsertStatement() {
        String sql = "INSERT INTO `users` (`name`, `email`) VALUES ('John', '<EMAIL>');";
        
        List<Statement> statements = MySqlHelper.parseMultiStatement(sql);
        assertEquals(1, statements.size());

        InsertTable insertTable = (InsertTable) statements.get(0);
        assertEquals("users", insertTable.getTableId().getTableName());
        
        // 验证列名被正确解析
        assertNotNull(insertTable.getColumns());
        assertEquals(2, insertTable.getColumns().size());
        assertEquals("name", insertTable.getColumns().get(0));
        assertEquals("email", insertTable.getColumns().get(1));
        
        String result = generator.generate(insertTable);
        // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("INSERT INTO users")); // 普通表名，无需双引号
        assertTrue(result.contains("(name, email)")); // 普通列名，无需双引号
    }

    @Test
    @DisplayName("测试复杂的反引号使用场景")
    void testComplexBacktickUsage() {
        String sql = "CREATE TABLE `order_details` (" +
                     "`order_id` INT, " +
                     "`user` VARCHAR(100), " +
                     "`group` VARCHAR(50), " +
                     "`class` VARCHAR(50), " +
                     "`index` INT, " +
                     "PRIMARY KEY (`order_id`)" +
                     ");";
        
        List<Statement> statements = MySqlHelper.parseMultiStatement(sql);
        assertEquals(1, statements.size());

        CreateTable createTable = (CreateTable) statements.get(0);
        String result = generator.generate(createTable);
        
        // 根据达梦官方文档，只有真正的保留关键字才需要双引号
        assertTrue(result.contains("CREATE TABLE order_details")); // 普通表名，无需双引号
        assertTrue(result.contains("order_id")); // 普通列名，无需双引号
        assertTrue(result.contains("\"user\"")); // 达梦保留关键字，需要双引号
        assertTrue(result.contains("\"group\"")); // 达梦保留关键字，需要双引号
        assertTrue(result.contains("\"class\"")); // 达梦保留关键字，需要双引号
        assertTrue(result.contains("\"index\"")); // 达梦保留关键字，需要双引号
        assertTrue(result.contains("PRIMARY KEY (order_id")); // 普通列名，无需双引号
    }

    @Test
    @DisplayName("测试反引号中的特殊字符")
    void testBacktickWithSpecialCharacters() {
        String sql = "CREATE TABLE `test_table` (" +
                     "`field-with-dash` VARCHAR(100), " +
                     "`field with space` VARCHAR(100), " +
                     "`field.with.dot` VARCHAR(100)" +
                     ");";
        
        List<Statement> statements = MySqlHelper.parseMultiStatement(sql);
        assertEquals(1, statements.size());

        CreateTable createTable = (CreateTable) statements.get(0);
        String result = generator.generate(createTable);
        
        // 验证特殊字符的字段名被正确处理
        assertTrue(result.contains("\"field-with-dash\""));
        assertTrue(result.contains("\"field with space\""));
        assertTrue(result.contains("\"field.with.dot\""));
    }
}
