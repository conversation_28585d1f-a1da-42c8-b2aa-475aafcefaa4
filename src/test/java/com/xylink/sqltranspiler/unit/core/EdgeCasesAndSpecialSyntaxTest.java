package com.xylink.sqltranspiler.unit.core;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;

/**
 * 边界情况和特殊语法测试
 * 测试各种边界情况、特殊字符、保留字、复杂表达式等
 * 参考文档：
 * - MySQL: https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦: https://eco.dameng.com/document/dm/zh-cn/pm/
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
@DisplayName("边界情况和特殊语法测试")
public class EdgeCasesAndSpecialSyntaxTest {

    private DamengGenerator generator;

    @BeforeEach
    void setUp() {
        generator = new DamengGenerator();
    }

    // ================================ 反引号和标识符测试 ================================

    @Test
    @DisplayName("反引号 - 保留字作为表名和字段名")
    void testBackticksWithReservedWords() {
        String sql = "SELECT `order`, `group`, `select` FROM `table` WHERE `where` = 'test';";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("SELECT"));
        assertTrue(damengSql.contains("\"order\""));
        assertTrue(damengSql.contains("\"group\""));
        assertTrue(damengSql.contains("\"select\""));
        assertTrue(damengSql.contains("\"table\""));
        assertTrue(damengSql.contains("\"where\""));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("反引号 - 特殊字符和空格")
    void testBackticksWithSpecialCharacters() {
        String sql = "SELECT `user name`, `email-address`, `phone#number` FROM `user table` WHERE `user id` = 1;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("SELECT"));
        assertTrue(damengSql.contains("\"user name\""));
        assertTrue(damengSql.contains("\"email-address\""));
        assertTrue(damengSql.contains("\"phone#number\""));
        assertTrue(damengSql.contains("\"user table\""));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("反引号 - 中文字段名")
    void testBackticksWithChineseCharacters() {
        String sql = "SELECT `用户名`, `邮箱地址`, `创建时间` FROM `用户表` WHERE `状态` = '激活';";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("SELECT"));
        assertTrue(damengSql.contains("\"用户名\""));
        assertTrue(damengSql.contains("\"邮箱地址\""));
        assertTrue(damengSql.contains("\"创建时间\""));
        assertTrue(damengSql.contains("\"用户表\""));
        assertTrue(damengSql.endsWith(";"));
    }

    // ================================ 复杂表达式和函数测试 ================================

    @Test
    @DisplayName("复杂表达式 - 数学运算")
    void testComplexMathExpressions() {
        String sql = "SELECT id, price * quantity * (1 - discount / 100) AS total_price, ROUND(price * 1.2, 2) AS price_with_tax FROM orders;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("SELECT"));
        assertTrue(damengSql.contains("price"));
        assertTrue(damengSql.contains("quantity"));
        assertTrue(damengSql.contains("discount"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("复杂表达式 - 字符串函数")
    void testComplexStringFunctions() {
        String sql = "SELECT CONCAT(UPPER(first_name), ' ', LOWER(last_name)) AS full_name, SUBSTRING(email, 1, LOCATE('@', email) - 1) AS username FROM users;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("SELECT"));
        assertTrue(damengSql.contains("CONCAT"));
        assertTrue(damengSql.contains("UPPER"));
        assertTrue(damengSql.contains("LOWER"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("复杂表达式 - 日期时间函数")
    void testComplexDateTimeFunctions() {
        String sql = "SELECT DATE_FORMAT(created_at, '%Y-%m-%d') AS date_only, TIMESTAMPDIFF(DAY, created_at, NOW()) AS days_ago, DATE_ADD(created_at, INTERVAL 30 DAY) AS expires_at FROM users;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("SELECT"));
        assertTrue(damengSql.contains("TO_CHAR")); // DATE_FORMAT 应该转换为 TO_CHAR
        assertTrue(damengSql.contains("SYSDATE")); // NOW() 应该转换为 SYSDATE
        assertTrue(damengSql.endsWith(";"));
    }

    // ================================ NULL值和默认值测试 ================================

    @Test
    @DisplayName("NULL值处理 - IFNULL和COALESCE")
    void testNullHandling() {
        String sql = "SELECT IFNULL(phone, 'N/A') AS phone_display, COALESCE(middle_name, first_name, 'Unknown') AS display_name FROM users;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("SELECT"));
        assertTrue(damengSql.contains("NVL")); // IFNULL 应该转换为 NVL
        assertTrue(damengSql.contains("COALESCE"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("NULL值处理 - IS NULL和IS NOT NULL")
    void testNullComparisons() {
        String sql = "SELECT * FROM users WHERE email IS NOT NULL AND phone IS NULL AND (description IS NULL OR description = '');";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("SELECT"));
        assertTrue(damengSql.contains("IS NOT NULL"));
        assertTrue(damengSql.contains("IS NULL"));
        assertTrue(damengSql.endsWith(";"));
    }

    // ================================ 特殊字符和转义测试 ================================

    @Test
    @DisplayName("特殊字符 - 单引号转义")
    void testSingleQuoteEscaping() {
        String sql = "INSERT INTO messages (content) VALUES ('It\\'s a beautiful day'), ('Don\\'t worry, be happy');";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("INSERT"));
        assertTrue(damengSql.contains("messages"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("特殊字符 - 换行符和制表符")
    void testNewlineAndTabCharacters() {
        String sql = "INSERT INTO logs (message) VALUES ('Line 1\\nLine 2\\tTabbed');";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("INSERT"));
        assertTrue(damengSql.contains("logs"));
        assertTrue(damengSql.endsWith(";"));
    }

    // ================================ 大数据和性能测试 ================================

    @Test
    @DisplayName("大数据 - 长字符串")
    void testLongStrings() {
        String longString = "A".repeat(1000);
        String sql = "INSERT INTO large_data (content) VALUES ('" + longString + "');";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL length: " + damengSql.length());
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("INSERT"));
        assertTrue(damengSql.contains("large_data"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("大数据 - 大量列")
    void testManyColumns() {
        StringBuilder sql = new StringBuilder("SELECT ");
        for (int i = 1; i <= 50; i++) {
            if (i > 1) sql.append(", ");
            sql.append("col").append(i);
        }
        sql.append(" FROM wide_table;");
        
        Statement statement = MySqlHelper.parseStatement(sql.toString());
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql.substring(0, Math.min(200, damengSql.length())) + "...");
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("SELECT"));
        assertTrue(damengSql.contains("col1"));
        assertTrue(damengSql.contains("col50"));
        assertTrue(damengSql.endsWith(";"));
    }

    // ================================ 边界值测试 ================================

    @Test
    @DisplayName("边界值 - 极大和极小数值")
    void testExtremeNumbers() {
        String sql = "INSERT INTO numbers (big_int, small_decimal, negative) VALUES (9223372036854775807, 0.000000001, -9223372036854775808);";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        // 基于达梦官方文档验证极值数值处理
        // 参考: https://eco.dameng.com/document/dm/zh-cn/sql-dev/dmpl-sql-datatype.html
        String damengSql = generator.generate(statement);
        validateDamengExtremeNumberHandling(damengSql);

        System.out.println("    ✅ 达梦极值数值处理验证通过");
    }

    @Test
    @DisplayName("边界值 - 空字符串和空白字符")
    void testEmptyAndWhitespaceStrings() {
        String sql = "INSERT INTO test_strings (empty, space, tab, newline) VALUES ('', ' ', '\t', '\n');";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("INSERT"));
        assertTrue(damengSql.contains("test_strings"));
        assertTrue(damengSql.endsWith(";"));
    }

    /**
     * 基于达梦官方文档验证极值数值处理
     *
     * 达梦官方文档规范：
     * - 数值类型的取值范围
     * - 精度和标度处理
     * - 极值数值的正确处理
     */
    private void validateDamengExtremeNumberHandling(String damengSql) {
        assertNotNull(damengSql, "达梦转换结果不应为空");
        assertFalse(damengSql.trim().isEmpty(), "达梦转换结果不应为空字符串");

        // 基于达梦官方文档验证INSERT语句基本结构
        // https://eco.dameng.com/document/dm/zh-cn/sql-dev/dmpl-sql-dml.html
        assertTrue(damengSql.toUpperCase().contains("INSERT"),
                  "应包含INSERT语句");
        assertTrue(damengSql.toUpperCase().contains("VALUES"),
                  "应包含VALUES子句");

        // 验证表名处理
        boolean hasValidTableName = damengSql.contains("numbers") ||
                                  damengSql.contains("NUMBERS") ||
                                  damengSql.contains("\"numbers\"");
        assertTrue(hasValidTableName, "表名应正确转换");

        // 验证极值数值保持（达梦支持大整数）
        if (damengSql.contains("9223372036854775807")) {
            System.out.println("    ✅ 达梦正确保持BIGINT最大值（符合官方文档）");
        }
        if (damengSql.contains("-9223372036854775808")) {
            System.out.println("    ✅ 达梦正确保持BIGINT最小值（符合官方文档）");
        }
        if (damengSql.contains("0.000000001")) {
            System.out.println("    ✅ 达梦正确保持小数精度（符合官方文档）");
        }

        // 验证语句结束符
        assertTrue(damengSql.trim().endsWith(";"),
                  "SQL语句应以分号结尾");

        System.out.println("    ✅ 达梦极值数值INSERT语句结构验证通过");
    }
}
