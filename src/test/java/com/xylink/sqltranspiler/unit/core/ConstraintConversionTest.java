package com.xylink.sqltranspiler.unit.core;

import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.shared.base.BaseConversionTest;

/**
 * 约束转换测试
 *
 * 测试原则：严格基于各数据库官方文档进行约束转换验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证约束转换的正确性
 * 2. 确保外键、主键、唯一约束转换符合官方文档规范
 * 3. 验证约束分离和重组基于官方文档
 * 4. 测试复杂约束场景的转换正确性
 *
 * <AUTHOR>
 */
@DisplayName("约束转换测试")
public class ConstraintConversionTest extends BaseConversionTest {

    @Test
    @DisplayName("外键约束分离测试")
    void testForeignKeyConstraintSeparation() throws Exception {
        // 使用简化的外键测试SQL，避免引用不存在的表
        String mysqlSql = """
            CREATE TABLE parent_table (
                id bigint(20) NOT NULL AUTO_INCREMENT,
                name varchar(100) NOT NULL,
                PRIMARY KEY (id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

            CREATE TABLE child_table (
                id bigint(20) NOT NULL AUTO_INCREMENT,
                parent_id bigint(20) NOT NULL,
                description varchar(255),
                PRIMARY KEY (id),
                CONSTRAINT fk_parent FOREIGN KEY (parent_id) REFERENCES parent_table (id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """;

        String damengSql = convertMySqlToDameng(mysqlSql);

        assertBasicConversionRequirements(damengSql);
        assertConstraintSeparation(damengSql);
        assertTrue(damengSql.contains("ALTER TABLE \"child_table\" ADD CONSTRAINT \"fk_parent\" FOREIGN KEY (\"parent_id\") REFERENCES \"parent_table\" (\"id\")"),
                   "应包含标准的外键约束语句");
    }

    @Test
    @DisplayName("唯一索引约束分离测试")
    void testUniqueIndexConstraintSeparation() throws Exception {
        String mysqlSql = "CREATE TABLE `idx_test` (`id` int, `email` varchar(255), UNIQUE KEY `uk_email` (`email`));";
        String damengSql = convertMySqlToDameng(mysqlSql);

        assertBasicConversionRequirements(damengSql);
        assertConstraintSeparation(damengSql);
        assertTrue(damengSql.contains("CREATE UNIQUE INDEX \"udx_idx_test_uk_email\" ON \"idx_test\" (\"email\")"),
                   "应包含标准的唯一索引创建语句");
    }
}
