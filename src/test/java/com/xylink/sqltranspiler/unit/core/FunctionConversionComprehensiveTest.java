package com.xylink.sqltranspiler.unit.core;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.shared.base.BaseConversionTest;

/**
 * 函数转换综合测试
 * 验证项目对各种MySQL函数转换为达梦和金仓的支持
 * 严格按照MySQL、达梦、金仓官方文档标准
 *
 * 测试原则：严格基于各数据库官方文档进行函数转换验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4函数参考：https://dev.mysql.com/doc/refman/8.4/en/built-in-function-reference.html
 * - MySQL数学函数：https://dev.mysql.com/doc/refman/8.4/en/mathematical-functions.html
 * - MySQL字符串函数：https://dev.mysql.com/doc/refman/8.4/en/string-functions.html
 * - MySQL日期时间函数：https://dev.mysql.com/doc/refman/8.4/en/date-and-time-functions.html
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 *
 * 验证策略：
 * 1. 根据MySQL官方文档验证源函数的正确性
 * 2. 根据目标数据库官方文档验证转换结果的正确性
 * 3. 确保函数转换符合官方文档规范
 * 4. 测试复杂函数组合的转换正确性
 *
 * <AUTHOR>
 */
public class FunctionConversionComprehensiveTest extends BaseConversionTest {

    @Test
    @DisplayName("12. MD5(UUID()) 函数转换 - 达梦")
    void testMd5UuidFunctionDameng() throws Exception {
        String sql = "SELECT md5(uuid()) as hash_value;";
        String damengSql = convertMySqlToDameng(sql);
        
        assertBasicConversionRequirements(damengSql);
        
        // 验证基本转换
        assertTrue(damengSql.contains("SELECT"), "应包含SELECT");
        assertTrue(damengSql.contains("hash_value"), "应包含别名");
        
        // MD5和UUID函数在达梦中的处理
        // 根据达梦官方文档，MD5函数存在但返回格式可能不同
        // UUID函数可能需要转换为SYS_GUID()或其他等价函数
        assertTrue(damengSql.contains("md5") || damengSql.contains("MD5") || 
                   damengSql.contains("HASH") || damengSql.contains("CRYPTO_HASH"), 
                   "应包含MD5或等价的哈希函数");
        
        assertTrue(damengSql.contains("uuid") || damengSql.contains("UUID") || 
                   damengSql.contains("SYS_GUID") || damengSql.contains("NEWID"), 
                   "应包含UUID或等价的唯一标识符生成函数");
    }

    @Test
    @DisplayName("12. MD5(UUID()) 函数转换 - 金仓")
    void testMd5UuidFunctionKingbase() throws Exception {
        String sql = "SELECT md5(uuid()) as hash_value;";
        String kingbaseSql = convertMySqlToKingbase(sql);
        
        assertBasicConversionRequirements(kingbaseSql);
        
        // 验证基本转换
        assertTrue(kingbaseSql.contains("SELECT"), "应包含SELECT");
        assertTrue(kingbaseSql.contains("hash_value"), "应包含别名");
        
        // 金仓作为PostgreSQL兼容数据库，应该支持MD5和UUID函数
        assertTrue(kingbaseSql.contains("md5") || kingbaseSql.contains("MD5"), 
                   "应包含MD5函数");
        assertTrue(kingbaseSql.contains("uuid") || kingbaseSql.contains("UUID") || 
                   kingbaseSql.contains("gen_random_uuid"), 
                   "应包含UUID或等价函数");
    }

    @Test
    @DisplayName("13. SELECT type FROM 语法错误处理")
    void testSelectTypeFromSyntaxError() throws Exception {
        String sql = "select type from;";
        
        // 这是一个语法错误的SQL，应该被正确处理
        try {
            String damengSql = convertMySqlToDameng(sql);
            // 如果转换成功，验证基本结构
            if (damengSql != null && !damengSql.trim().isEmpty()) {
                assertTrue(damengSql.contains("SELECT") || damengSql.contains("--"), 
                           "应包含SELECT或注释标记");
            }
        } catch (Exception e) {
            // 语法错误应该被适当处理，不应该导致程序崩溃
            assertNotNull(e.getMessage(), "异常应该有明确的错误信息");
        }
        
        try {
            String kingbaseSql = convertMySqlToKingbase(sql);
            // 如果转换成功，验证基本结构
            if (kingbaseSql != null && !kingbaseSql.trim().isEmpty()) {
                assertTrue(kingbaseSql.contains("select") || kingbaseSql.contains("--"), 
                           "应包含select或注释标记");
            }
        } catch (Exception e) {
            // 语法错误应该被适当处理
            assertNotNull(e.getMessage(), "异常应该有明确的错误信息");
        }
    }

    @Test
    @DisplayName("14. UNIX_TIMESTAMP(CURRENT_TIMESTAMP()) 函数转换 - 达梦")
    void testUnixTimestampCurrentTimestampDameng() throws Exception {
        String sql = "SELECT unix_timestamp(current_timestamp()) as timestamp_value;";
        String damengSql = convertMySqlToDameng(sql);
        
        assertBasicConversionRequirements(damengSql);
        
        // 验证基本转换
        assertTrue(damengSql.contains("SELECT"), "应包含SELECT");
        assertTrue(damengSql.contains("timestamp_value"), "应包含别名");
        
        // CURRENT_TIMESTAMP应转换为SYSDATE
        assertTrue(damengSql.contains("SYSDATE") || damengSql.contains("current_timestamp"), 
                   "CURRENT_TIMESTAMP应转换为SYSDATE或保持原样");
        
        // UNIX_TIMESTAMP在达梦中可能需要转换为其他函数
        assertTrue(damengSql.contains("unix_timestamp") || damengSql.contains("UNIX_TIMESTAMP") ||
                   damengSql.contains("EXTRACT") || damengSql.contains("DATEDIFF") ||
                   damengSql.contains("TIMESTAMPDIFF"), 
                   "应包含UNIX_TIMESTAMP或等价的时间戳转换函数");
    }

    @Test
    @DisplayName("14. UNIX_TIMESTAMP(CURRENT_TIMESTAMP()) 函数转换 - 金仓")
    void testUnixTimestampCurrentTimestampKingbase() throws Exception {
        String sql = "SELECT unix_timestamp(current_timestamp()) as timestamp_value;";
        String kingbaseSql = convertMySqlToKingbase(sql);
        
        assertBasicConversionRequirements(kingbaseSql);
        
        // 验证基本转换
        assertTrue(kingbaseSql.contains("SELECT"), "应包含SELECT");
        assertTrue(kingbaseSql.contains("timestamp_value"), "应包含别名");
        
        // 金仓可能支持CURRENT_TIMESTAMP或需要转换
        assertTrue(kingbaseSql.contains("current_timestamp") || kingbaseSql.contains("now()"), 
                   "应包含current_timestamp或now()");
        
        // UNIX_TIMESTAMP在金仓中可能需要转换为EXTRACT函数
        assertTrue(kingbaseSql.contains("unix_timestamp") || kingbaseSql.contains("extract") ||
                   kingbaseSql.contains("EXTRACT"), 
                   "应包含unix_timestamp或extract函数");
    }

    @Test
    @DisplayName("15. FROM_UNIXTIME(1679564628) 函数转换 - 达梦")
    void testFromUnixtimeDameng() throws Exception {
        String sql = "SELECT from_unixtime(1679564628) as datetime_value;";
        String damengSql = convertMySqlToDameng(sql);
        
        assertBasicConversionRequirements(damengSql);
        
        // 验证基本转换
        assertTrue(damengSql.contains("SELECT"), "应包含SELECT");
        assertTrue(damengSql.contains("datetime_value"), "应包含别名");
        assertTrue(damengSql.contains("1679564628"), "应包含时间戳数值");
        
        // FROM_UNIXTIME在达梦中可能需要转换为其他函数
        assertTrue(damengSql.contains("from_unixtime") || damengSql.contains("FROM_UNIXTIME") ||
                   damengSql.contains("TO_DATE") || damengSql.contains("TO_TIMESTAMP") ||
                   damengSql.contains("DATE_ADD"), 
                   "应包含FROM_UNIXTIME或等价的时间戳转换函数");
    }

    @Test
    @DisplayName("15. FROM_UNIXTIME(1679564628) 函数转换 - 金仓")
    void testFromUnixtimeKingbase() throws Exception {
        String sql = "SELECT from_unixtime(1679564628) as datetime_value;";
        String kingbaseSql = convertMySqlToKingbase(sql);
        
        assertBasicConversionRequirements(kingbaseSql);
        
        // 验证基本转换
        assertTrue(kingbaseSql.contains("SELECT"), "应包含SELECT");
        assertTrue(kingbaseSql.contains("datetime_value"), "应包含别名");
        assertTrue(kingbaseSql.contains("1679564628"), "应包含时间戳数值");
        
        // FROM_UNIXTIME在金仓中可能需要转换为to_timestamp函数
        assertTrue(kingbaseSql.contains("from_unixtime") || kingbaseSql.contains("to_timestamp") ||
                   kingbaseSql.contains("TO_TIMESTAMP"), 
                   "应包含from_unixtime或to_timestamp函数");
    }

    @Test
    @DisplayName("16. 复杂DELETE语句中的时间函数转换 - 达梦")
    void testComplexDeleteWithTimeFunctionsDameng() throws Exception {
        String sql = "DELETE FROM datareal.call_collection WHERE timestamp<unix_timestamp(DATE_ADD(now(),INTERVAL -3 day))*1000;";
        String damengSql = convertMySqlToDameng(sql);
        
        assertBasicConversionRequirements(damengSql);
        
        // 验证基本转换 - 这是一个复杂的DELETE语句，可能转换失败
        if (damengSql.contains("DELETE FROM")) {
            // 如果转换成功，验证格式
            assertTrue(damengSql.contains("WHERE"), "应包含WHERE子句");
            assertTrue(damengSql.contains("1000"), "应包含乘数");
        } else {
            // 如果转换失败，应该包含错误注释
            assertTrue(damengSql.contains("CONVERSION ERROR") || damengSql.contains("--"),
                       "复杂语句转换失败时应包含错误注释");
        }
        
        // NOW()应转换为SYSDATE
        assertTrue(damengSql.contains("SYSDATE") || damengSql.contains("now"), 
                   "NOW()应转换为SYSDATE或保持原样");
        
        // DATE_ADD应转换为达梦的日期运算
        assertTrue(damengSql.contains("DATE_ADD") || damengSql.contains("INTERVAL") ||
                   damengSql.contains("+") || damengSql.contains("-"), 
                   "应包含日期运算");
        
        // UNIX_TIMESTAMP应转换或保持
        assertTrue(damengSql.contains("unix_timestamp") || damengSql.contains("UNIX_TIMESTAMP") ||
                   damengSql.contains("EXTRACT") || damengSql.contains("DATEDIFF"), 
                   "应包含时间戳转换函数");
    }

    @Test
    @DisplayName("16. 复杂DELETE语句中的时间函数转换 - 金仓")
    void testComplexDeleteWithTimeFunctionsKingbase() throws Exception {
        String sql = "DELETE FROM datareal.call_collection WHERE timestamp<unix_timestamp(DATE_ADD(now(),INTERVAL -3 day))*1000;";
        String kingbaseSql = convertMySqlToKingbase(sql);
        
        assertBasicConversionRequirements(kingbaseSql);
        
        // 验证基本转换 - 这是一个复杂的DELETE语句，可能转换失败
        if (kingbaseSql.toUpperCase().contains("DELETE FROM")) {
            // 如果转换成功，验证格式
            assertTrue(kingbaseSql.toUpperCase().contains("WHERE"), "应包含WHERE子句");
            assertTrue(kingbaseSql.contains("1000"), "应包含乘数");
        } else {
            // 如果转换失败，应该包含错误注释
            assertTrue(kingbaseSql.contains("CONVERSION ERROR") || kingbaseSql.contains("--"),
                       "复杂语句转换失败时应包含错误注释");
        }
        
        // NOW()在金仓中可能保持或转换
        assertTrue(kingbaseSql.contains("now") || kingbaseSql.contains("current_timestamp"), 
                   "应包含now或current_timestamp");
        
        // DATE_ADD在金仓中可能转换为PostgreSQL语法
        assertTrue(kingbaseSql.contains("DATE_ADD") || kingbaseSql.contains("INTERVAL") ||
                   kingbaseSql.contains("+") || kingbaseSql.contains("-"), 
                   "应包含日期运算");
        
        // UNIX_TIMESTAMP在金仓中可能转换为extract函数
        assertTrue(kingbaseSql.contains("unix_timestamp") || kingbaseSql.contains("extract") ||
                   kingbaseSql.contains("EXTRACT"), 
                   "应包含时间戳转换函数");
    }
}
