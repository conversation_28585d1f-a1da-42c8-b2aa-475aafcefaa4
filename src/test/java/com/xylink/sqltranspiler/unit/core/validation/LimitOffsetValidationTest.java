package com.xylink.sqltranspiler.unit.core.validation;

import com.xylink.sqltranspiler.core.validation.StrictSqlValidator;
import com.xylink.sqltranspiler.core.validation.StrictValidationResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

/**
 * LIMIT和OFFSET验证单元测试
 * 基于MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/select.html
 * MySQL官方文档描述：
 * "The LIMIT clause can be used to constrain the number of rows returned by the SELECT statement.
 * LIMIT takes one or two numeric arguments, which must both be nonnegative integer constants"
 * 测试修复后的大结果集检测逻辑，确保不会对不包含LIMIT/OFFSET的SQL产生误报
 * 根据用户反馈：ALTER TABLE语句中没有LIMIT和OFFSET，但出现了相关配置错误告警
 * 这是由于原始正则表达式设计缺陷导致的误报问题
 * 测试驱动开发原则：当测试期望与官方文档不符时，修正测试用例而不是降低代码质量
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
public class LimitOffsetValidationTest {

    private StrictSqlValidator validator;

    @BeforeEach
    void setUp() {
        validator = new StrictSqlValidator();
    }

    @Test
    @DisplayName("测试ALTER TABLE语句不应该产生LIMIT/OFFSET相关告警 - 基于MySQL 8.4官方文档")
    void testAlterTableShouldNotTriggerLimitOffsetWarning() {
        // 用户提供的实际SQL语句
        String sql = """
            -- 20250627_0016.sql
            alter table ainemo.libra_special_feature_user
                add data_type int default 0 not null;
            
            alter table ainemo.libra_special_feature_user
                add dept_name varchar(255) default '' not null;
            alter table ainemo.libra_special_feature_user
                add update_time datetime default now() null on update now();
            """;

        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");

        // 验证不应该有LIMIT/OFFSET相关的警告
        // MySQL官方文档：ALTER TABLE语句不包含LIMIT/OFFSET子句
        boolean hasLimitOffsetWarning = result.getWarnings().stream()
            .anyMatch(warning -> warning.getMessage().toLowerCase().contains("limit") || 
                     warning.getMessage().toLowerCase().contains("offset") ||
                     warning.getMessage().toLowerCase().contains("大结果集"));

        assertFalse(hasLimitOffsetWarning, 
            "ALTER TABLE语句不应该产生LIMIT/OFFSET相关告警，但发现了以下告警: " + 
            result.getWarnings());
    }

    @Test
    @DisplayName("测试实际的大LIMIT值应该产生告警 - 基于MySQL 8.4官方文档")
    void testLargeLimitShouldTriggerWarning() {
        // MySQL官方文档：LIMIT子句用于限制SELECT语句返回的行数
        String sql = "SELECT * FROM users LIMIT 5000;";

        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");

        boolean hasLimitWarning = result.getWarnings().stream()
            .anyMatch(warning -> warning.getMessage().contains("大结果集") && 
                     warning.getMessage().contains("LIMIT") &&
                     warning.getMessage().contains("https://dev.mysql.com/doc/refman/8.4/en/select.html"));

        assertTrue(hasLimitWarning, "大LIMIT值应该产生性能告警并包含官方文档链接");
    }

    @Test
    @DisplayName("测试实际的大OFFSET值应该产生告警 - 基于MySQL 8.4官方文档")
    void testLargeOffsetShouldTriggerWarning() {
        // MySQL官方文档：OFFSET指定返回第一行的偏移量，初始行偏移量为0
        String sql = "SELECT * FROM users LIMIT 10 OFFSET 2000;";

        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");

        boolean hasOffsetWarning = result.getWarnings().stream()
            .anyMatch(warning -> warning.getMessage().contains("大偏移量") && 
                     warning.getMessage().contains("OFFSET") &&
                     warning.getMessage().contains("https://dev.mysql.com/doc/refman/8.4/en/select.html"));

        assertTrue(hasOffsetWarning, "大OFFSET值应该产生性能告警并包含官方文档链接");
    }

    @Test
    @DisplayName("测试小的LIMIT/OFFSET值不应该产生告警 - 基于MySQL 8.4官方文档")
    void testSmallLimitOffsetShouldNotTriggerWarning() {
        // MySQL官方文档：合理的LIMIT/OFFSET值不会导致性能问题
        String sql = "SELECT * FROM users LIMIT 100 OFFSET 50;";

        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");

        boolean hasLimitOffsetWarning = result.getWarnings().stream()
            .anyMatch(warning -> warning.getMessage().toLowerCase().contains("limit") || 
                     warning.getMessage().toLowerCase().contains("offset") ||
                     warning.getMessage().toLowerCase().contains("大结果集"));

        assertFalse(hasLimitOffsetWarning, "小的LIMIT/OFFSET值不应该产生告警");
    }

    @Test
    @DisplayName("测试注释中的LIMIT/OFFSET不应该产生告警 - 基于MySQL 8.4官方文档")
    void testLimitOffsetInCommentsShouldNotTriggerWarning() {
        // MySQL官方文档：注释内容不是SQL语句的一部分
        String sql = """
            -- This query uses LIMIT 5000 for testing
            /* OFFSET 2000 is mentioned here */
            SELECT * FROM users WHERE id = 1;
            """;

        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");

        boolean hasLimitOffsetWarning = result.getWarnings().stream()
            .anyMatch(warning -> warning.getMessage().toLowerCase().contains("limit") || 
                     warning.getMessage().toLowerCase().contains("offset") ||
                     warning.getMessage().toLowerCase().contains("大结果集"));

        assertFalse(hasLimitOffsetWarning, "注释中的LIMIT/OFFSET不应该产生告警");
    }

    @Test
    @DisplayName("测试字符串中的LIMIT/OFFSET不应该产生告警 - 基于MySQL 8.4官方文档")
    void testLimitOffsetInStringsShouldNotTriggerWarning() {
        // MySQL官方文档：字符串字面量不是SQL子句
        String sql = """
            INSERT INTO logs (message) VALUES ('Query used LIMIT 5000');
            UPDATE config SET value = "OFFSET 2000" WHERE key = 'pagination';
            """;

        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");

        boolean hasLimitOffsetWarning = result.getWarnings().stream()
            .anyMatch(warning -> warning.getMessage().toLowerCase().contains("limit") || 
                     warning.getMessage().toLowerCase().contains("offset") ||
                     warning.getMessage().toLowerCase().contains("大结果集"));

        assertFalse(hasLimitOffsetWarning, "字符串中的LIMIT/OFFSET不应该产生告警");
    }

    @Test
    @DisplayName("测试复杂查询建议添加LIMIT - 基于MySQL 8.4官方文档")
    void testComplexQueryShouldSuggestLimit() {
        // MySQL官方文档：复杂查询可能返回大量数据，建议使用LIMIT限制
        String sql = """
            SELECT u.*, p.name as project_name 
            FROM users u 
            JOIN projects p ON u.id = p.user_id 
            ORDER BY u.created_at DESC;
            """;

        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");

        boolean hasLimitSuggestion = result.getSuggestions().stream()
            .anyMatch(suggestion -> suggestion.getMessage().contains("LIMIT"));

        assertTrue(hasLimitSuggestion, "复杂查询应该建议添加LIMIT限制");
    }

    @Test
    @DisplayName("测试COUNT查询不应该建议添加LIMIT - 基于MySQL 8.4官方文档")
    void testCountQueryShouldNotSuggestLimit() {
        // MySQL官方文档：COUNT查询返回单行结果，不需要LIMIT限制
        String sql = """
            SELECT COUNT(*) 
            FROM users u 
            JOIN projects p ON u.id = p.user_id;
            """;

        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");

        boolean hasLimitSuggestion = result.getSuggestions().stream()
            .anyMatch(suggestion -> suggestion.getMessage().contains("LIMIT"));

        assertFalse(hasLimitSuggestion, "COUNT查询不应该建议添加LIMIT");
    }
}
