package com.xylink.sqltranspiler.unit.core.context;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.validation.EnhancedTranspilationResult;
import com.xylink.sqltranspiler.core.validation.EnhancedTranspiler;
import com.xylink.sqltranspiler.core.validation.ValidationSummary;
import com.xylink.sqltranspiler.core.validation.VarcharValidationConfig;

/**
 * 增强转换器集成测试
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
class EnhancedTranspilerTest {
    
    private EnhancedTranspiler enhancedTranspiler;
    
    @BeforeEach
    void setUp() {
        enhancedTranspiler = new EnhancedTranspiler();
    }
    
    @Test
    @DisplayName("默认情况下VARCHAR验证应该是禁用的")
    void testDefaultVarcharValidationDisabled() {
        assertFalse(enhancedTranspiler.isVarcharValidationEnabled(), 
                   "默认情况下VARCHAR验证应该是禁用的");
    }
    
    @Test
    @DisplayName("可以启用VARCHAR验证")
    void testEnableVarcharValidation() {
        VarcharValidationConfig config = VarcharValidationConfig.enabledConfig();
        enhancedTranspiler.configureVarcharValidation(config);
        
        assertTrue(enhancedTranspiler.isVarcharValidationEnabled(), 
                  "配置后VARCHAR验证应该是启用的");
    }
    
    @Test
    @DisplayName("增强转换 - 简单CREATE TABLE语句")
    void testEnhancedTranspileSimpleCreateTable() {
        String sql = """
            CREATE TABLE users (
                id INT PRIMARY KEY,
                name VARCHAR(50),
                email VARCHAR(100)
            );
            """;
        
        // 启用VARCHAR验证
        VarcharValidationConfig config = VarcharValidationConfig.enabledConfig();
        enhancedTranspiler.configureVarcharValidation(config);
        
        EnhancedTranspilationResult result = enhancedTranspiler.transpileEnhanced(
            sql, "mysql", "dameng");
        
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTranslatedSql(), "转换后的SQL不应为null");
        assertFalse(result.getTranslatedSql().isEmpty(), "转换后的SQL不应为空");
        
        // 验证VARCHAR验证信息
        ValidationSummary validationSummary = result.getValidationSummary();
        assertNotNull(validationSummary, "验证摘要不应为null");
        assertTrue(validationSummary.isEnabled(), "验证应该是启用的");
        assertEquals(0, validationSummary.getConflictCount(), "不应该有冲突");
        
        // 打印结果用于调试
        System.out.println("=== Enhanced Transpilation Result ===");
        System.out.println(result.getDetailedReport());
        System.out.println("Generated SQL:");
        System.out.println(result.getTranslatedSql());
    }
    
    @Test
    @DisplayName("增强转换 - 空SQL内容")
    void testEnhancedTranspileEmptyContent() {
        EnhancedTranspilationResult result = enhancedTranspiler.transpileEnhanced(
            "", "mysql", "dameng");
        
        assertNotNull(result);
        assertFalse(result.isSuccess(), "空内容应该失败");
        assertNotNull(result.getErrorMessage(), "应该有错误消息");
        assertTrue(result.getErrorMessage().contains("empty"), "错误消息应该提到空内容");
    }
    
    @Test
    @DisplayName("增强转换 - null SQL内容")
    void testEnhancedTranspileNullContent() {
        EnhancedTranspilationResult result = enhancedTranspiler.transpileEnhanced(
            null, "mysql", "dameng");
        
        assertNotNull(result);
        assertFalse(result.isSuccess(), "null内容应该失败");
        assertNotNull(result.getErrorMessage(), "应该有错误消息");
        assertTrue(result.getErrorMessage().contains("null"), "错误消息应该提到null内容");
    }
    
    @Test
    @DisplayName("兼容性转换 - 应该与原始转换器行为一致")
    void testCompatibilityTranspile() {
        String sql = """
            CREATE TABLE test_table (
                id INT PRIMARY KEY,
                name VARCHAR(50)
            );
            """;
        
        // 使用兼容性方法
        var result = enhancedTranspiler.transpile(sql, "mysql", "dameng");
        
        assertNotNull(result, "结果不应为null");
        assertNotNull(result.translatedSql(), "转换后的SQL不应为null");
        assertFalse(result.translatedSql().isEmpty(), "转换后的SQL不应为空");
        
        // 验证基本转换功能
        assertTrue(result.translatedSql().contains("CREATE TABLE"), "应该包含CREATE TABLE");
        assertTrue(result.translatedSql().toUpperCase().contains("TEST_TABLE") ||
                   result.translatedSql().contains("test_table"), "表名应该被正确转换");
        
        System.out.println("Compatibility result:");
        System.out.println(result.translatedSql());
    }
    
    @Test
    @DisplayName("VARCHAR验证配置测试")
    void testVarcharValidationConfiguration() {
        // 测试不同的配置策略
        VarcharValidationConfig logOnlyConfig = VarcharValidationConfig.enabledConfig();
        logOnlyConfig.setStrategy(VarcharValidationConfig.ConflictHandlingStrategy.LOG_ONLY);
        logOnlyConfig.setDetailedReporting(true);
        logOnlyConfig.setMaxReportedConflicts(50);
        
        enhancedTranspiler.configureVarcharValidation(logOnlyConfig);
        
        VarcharValidationConfig retrievedConfig = enhancedTranspiler.getVarcharValidationConfig();
        assertNotNull(retrievedConfig);
        assertTrue(retrievedConfig.isEnabled());
        assertEquals(VarcharValidationConfig.ConflictHandlingStrategy.LOG_ONLY, 
                    retrievedConfig.getStrategy());
        assertTrue(retrievedConfig.isDetailedReporting());
        assertEquals(50, retrievedConfig.getMaxReportedConflicts());
    }
    
    @Test
    @DisplayName("复杂SQL转换测试")
    void testComplexSqlTranspilation() {
        String sql = """
            CREATE DATABASE test_db;
            
            CREATE TABLE users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) NOT NULL,
                email VARCHAR(100) UNIQUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            CREATE TABLE posts (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT,
                title VARCHAR(200),
                content TEXT,
                FOREIGN KEY (user_id) REFERENCES users(id)
            );
            """;
        
        // 启用VARCHAR验证
        VarcharValidationConfig config = VarcharValidationConfig.enabledConfig();
        config.setDetailedReporting(true);
        enhancedTranspiler.configureVarcharValidation(config);
        
        EnhancedTranspilationResult result = enhancedTranspiler.transpileEnhanced(
            sql, "mysql", "dameng");
        
        assertNotNull(result);
        assertTrue(result.isSuccess(), "复杂SQL转换应该成功");
        
        String translatedSql = result.getTranslatedSql();
        assertNotNull(translatedSql);
        assertFalse(translatedSql.isEmpty());
        
        // 验证基本转换
        assertTrue(translatedSql.contains("CREATE SCHEMA"), "应该包含CREATE SCHEMA");
        assertTrue(translatedSql.contains("IDENTITY(1,1)"), "应该包含IDENTITY");
        
        // 验证VARCHAR验证
        ValidationSummary validationSummary = result.getValidationSummary();
        assertTrue(validationSummary.isEnabled());
        assertTrue(validationSummary.getTotalValidations() > 0, "应该进行了验证");
        
        System.out.println("=== Complex SQL Result ===");
        System.out.println(result.getBriefReport());
        System.out.println("Validation: " + validationSummary.getBriefReport());
    }
    
    @Test
    @DisplayName("toString方法测试")
    void testToStringMethod() {
        String defaultString = enhancedTranspiler.toString();
        assertNotNull(defaultString);
        assertTrue(defaultString.contains("disabled"), "默认应该显示disabled");
        
        // 启用后测试
        VarcharValidationConfig config = VarcharValidationConfig.enabledConfig();
        enhancedTranspiler.configureVarcharValidation(config);
        
        String enabledString = enhancedTranspiler.toString();
        assertNotNull(enabledString);
        assertTrue(enabledString.contains("enabled"), "启用后应该显示enabled");
    }
}
