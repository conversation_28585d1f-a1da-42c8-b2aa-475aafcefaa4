package com.xylink.sqltranspiler.unit.core;

import com.xylink.sqltranspiler.Transpiler;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import com.xylink.sqltranspiler.core.context.TranspilationResult;

import static org.junit.jupiter.api.Assertions.*;

/**
 * CREATE TABLE LIKE语句转换测试
 * 测试MySQL的CREATE TABLE ... LIKE语法在各个数据库方言中的转换
 * 参考文档：
 * - MySQL: https://dev.mysql.com/doc/refman/8.4/en/create-table-like.html
 * - 达梦: https://eco.dameng.com/document/dm/zh-cn/pm/definition-statement.html
 * - 金仓: https://help.kingbase.com.cn/v8/development/sql-plsql/sql/SQL_Statements_3.html#create-table
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
@DisplayName("CREATE TABLE LIKE语句转换测试")
public class CreateTableLikeTest {

    private Transpiler transpiler;

    @BeforeEach
    void setUp() {
        transpiler = new Transpiler();
    }

    @Test
    @DisplayName("达梦数据库 - CREATE TABLE LIKE转换")
    void testDamengCreateTableLike() {
        String sql = "CREATE TABLE backup_users LIKE users;";
        
        TranspilationResult result = transpiler.transpile(sql, "mysql", "dameng", false);
        
        assertNotNull(result);
        assertEquals(0, result.issues().size(), "转换应该没有错误");
        
        String converted = result.translatedSql();
        assertNotNull(converted);

        // 达梦数据库不支持CREATE TABLE LIKE，应该转换为CREATE TABLE AS SELECT
        assertTrue(converted.contains("CREATE TABLE backup_users AS SELECT * FROM users WHERE 1=0"),
                "应该转换为CREATE TABLE AS SELECT语法");
        assertTrue(converted.trim().endsWith(";"), "应该以分号结尾");
    }

    @Test
    @DisplayName("金仓数据库 - CREATE TABLE LIKE转换")
    void testKingbaseCreateTableLike() {
        String sql = "CREATE TABLE backup_users LIKE users;";
        
        TranspilationResult result = transpiler.transpile(sql, "mysql", "kingbase", false);
        
        assertNotNull(result);
        assertEquals(0, result.issues().size(), "转换应该没有错误");
        
        String converted = result.translatedSql();
        assertNotNull(converted);

        // 金仓数据库支持PostgreSQL兼容的CREATE TABLE (LIKE ...)语法
        // 检查关键组件是否存在（忽略格式化的空白字符）
        assertTrue(converted.contains("CREATE TABLE \"backup_users\""),
                "应该包含正确的表名");
        assertTrue(converted.contains("LIKE \"users\" INCLUDING ALL"),
                "应该转换为PostgreSQL兼容的LIKE语法");
        assertTrue(converted.trim().endsWith(";"), "应该以分号结尾");
    }

    @Test
    @DisplayName("神通数据库 - CREATE TABLE LIKE转换")
    void testShentongCreateTableLike() {
        String sql = "CREATE TABLE backup_users LIKE users;";
        
        TranspilationResult result = transpiler.transpile(sql, "mysql", "shentong", false);
        
        assertNotNull(result);
        assertEquals(0, result.issues().size(), "转换应该没有错误");
        
        String converted = result.translatedSql();
        assertNotNull(converted);

        // 神通数据库不支持CREATE TABLE LIKE，应该转换为CREATE TABLE AS SELECT
        assertTrue(converted.contains("CREATE TABLE \"backup_users\" AS SELECT * FROM \"users\" WHERE 1=0"),
                "应该转换为CREATE TABLE AS SELECT语法");
        assertTrue(converted.trim().endsWith(";"), "应该以分号结尾");
    }

    @Test
    @DisplayName("带IF NOT EXISTS的CREATE TABLE LIKE转换")
    void testCreateTableLikeWithIfNotExists() {
        String sql = "CREATE TABLE IF NOT EXISTS backup_users LIKE users;";
        
        // 测试达梦
        TranspilationResult damengResult = transpiler.transpile(sql, "mysql", "dameng", false);
        assertTrue(damengResult.translatedSql().contains("CREATE TABLE IF NOT EXISTS backup_users"),
                "达梦应该保留IF NOT EXISTS");
        
        // 测试金仓
        TranspilationResult kingbaseResult = transpiler.transpile(sql, "mysql", "kingbase", false);
        assertTrue(kingbaseResult.translatedSql().contains("CREATE TABLE IF NOT EXISTS \"backup_users\""),
                "金仓应该保留IF NOT EXISTS");
        
        // 测试神通
        TranspilationResult shentongResult = transpiler.transpile(sql, "mysql", "shentong", false);
        assertTrue(shentongResult.translatedSql().contains("CREATE TABLE IF NOT EXISTS \"backup_users\""),
                "神通应该保留IF NOT EXISTS");
    }

    @Test
    @DisplayName("带schema的CREATE TABLE LIKE转换")
    void testCreateTableLikeWithSchema() {
        String sql = "CREATE TABLE ainemo.libra_live_video_backup LIKE ainemo.libra_live_video;";
        
        // 测试达梦
        TranspilationResult damengResult = transpiler.transpile(sql, "mysql", "dameng", false);
        String damengSql = damengResult.translatedSql();
        assertTrue(damengSql.contains("ainemo.libra_live_video_backup AS SELECT * FROM ainemo.libra_live_video"),
                "达梦应该保留schema名称");
        
        // 测试金仓
        TranspilationResult kingbaseResult = transpiler.transpile(sql, "mysql", "kingbase", false);
        String kingbaseSql = kingbaseResult.translatedSql();

        assertTrue(kingbaseSql.contains("\"ainemo\".\"libra_live_video_backup\"") &&
                   kingbaseSql.contains("LIKE \"ainemo\".\"libra_live_video\""),
                "金仓应该使用双引号包围schema和表名");
        
        // 测试神通
        TranspilationResult shentongResult = transpiler.transpile(sql, "mysql", "shentong", false);
        String shentongSql = shentongResult.translatedSql();
        assertTrue(shentongSql.contains("\"ainemo\".\"libra_live_video_backup\" AS SELECT * FROM \"ainemo\".\"libra_live_video\""),
                "神通应该使用双引号包围schema和表名");
    }

    @Test
    @DisplayName("多个CREATE TABLE LIKE语句批量转换")
    void testMultipleCreateTableLikeStatements() {
        String sql = """
            CREATE TABLE backup_users LIKE users;
            CREATE TABLE backup_orders LIKE orders;
            CREATE TABLE backup_products LIKE products;
            """;
        
        TranspilationResult result = transpiler.transpile(sql, "mysql", "dameng", false);
        
        assertNotNull(result);
        // 根据MySQL 8.4官方文档，CREATE TABLE LIKE语法是正确的，只检查转换失败数量
        assertEquals(0, result.failureCount(), "转换应该没有失败");
        
        String converted = result.translatedSql();
        assertNotNull(converted);
        
        // 验证所有语句都被正确转换
        assertTrue(converted.contains("backup_users AS SELECT * FROM users WHERE 1=0"),
                "第一个语句应该被正确转换");
        assertTrue(converted.contains("backup_orders AS SELECT * FROM orders WHERE 1=0"),
                "第二个语句应该被正确转换");
        assertTrue(converted.contains("backup_products AS SELECT * FROM products WHERE 1=0"),
                "第三个语句应该被正确转换");
    }
}
