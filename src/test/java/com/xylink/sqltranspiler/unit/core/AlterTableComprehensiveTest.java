package com.xylink.sqltranspiler.unit.core;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.shared.base.BaseConversionTest;

/**
 * ALTER TABLE语句综合转换测试
 * 验证项目对各种ALTER TABLE操作的转换支持
 * 严格按照MySQL、达梦、金仓官方文档标准
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
public class AlterTableComprehensiveTest extends BaseConversionTest {

    @Test
    @DisplayName("1. ALTER TABLE ADD COLUMN - 达梦转换")
    void testAlterTableAddColumnDameng() throws Exception {
        String sql = "alter table test.a add column user_name varchar(10) not null default '';";
        String damengSql = convertMySqlToDameng(sql);
        
        assertBasicConversionRequirements(damengSql);
        
        // 验证基本语法转换
        assertTrue(damengSql.contains("ALTER TABLE"), "应包含ALTER TABLE");
        assertTrue(damengSql.contains("ADD COLUMN"), "应包含ADD COLUMN");
        assertTrue(damengSql.contains("test.a"), "表名应使用schema格式");  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.contains("user_name"), "列名应正确转换");  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.toUpperCase().contains("VARCHAR(10)"), "数据类型应正确转换");
        assertTrue(damengSql.contains("NOT NULL"), "应包含NOT NULL约束");
        assertTrue(damengSql.contains("DEFAULT ''"), "应包含默认值");
        assertTrue(damengSql.trim().endsWith(";"), "语句应以分号结尾");
    }

    @Test
    @DisplayName("1. ALTER TABLE ADD COLUMN - 金仓转换")
    void testAlterTableAddColumnKingbase() throws Exception {
        String sql = "alter table test.a add column user_name varchar(10) not null default '';";
        String kingbaseSql = convertMySqlToKingbase(sql);
        
        assertBasicConversionRequirements(kingbaseSql);
        
        // 验证基本语法转换
        assertTrue(kingbaseSql.contains("ALTER TABLE"), "应包含ALTER TABLE");
        assertTrue(kingbaseSql.contains("ADD COLUMN"), "应包含ADD COLUMN");
        assertTrue(kingbaseSql.contains("\"test\".\"a\""), "表名应使用双引号和schema格式");
        assertTrue(kingbaseSql.contains("\"user_name\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("varchar(10)"), "数据类型应正确转换");
        assertTrue(kingbaseSql.toUpperCase().contains("NOT NULL"), "应包含not null约束");
        assertTrue(kingbaseSql.toUpperCase().contains("DEFAULT ''"), "应包含默认值");
        assertTrue(kingbaseSql.trim().endsWith(";"), "语句应以分号结尾");
    }

    @Test
    @DisplayName("2. ALTER TABLE MODIFY COLUMN - 达梦转换")
    void testAlterTableModifyColumnDameng() throws Exception {
        String sql = "alter table test.a modify column user_name varchar(20) not null default '10';";
        String damengSql = convertMySqlToDameng(sql);
        
        assertBasicConversionRequirements(damengSql);
        
        // 验证MODIFY转换为ALTER COLUMN（达梦标准语法）
        assertTrue(damengSql.contains("ALTER TABLE"), "应包含ALTER TABLE");
        assertTrue(damengSql.contains("ALTER COLUMN") || damengSql.contains("MODIFY COLUMN"), 
                   "应包含ALTER COLUMN或MODIFY COLUMN");
        assertTrue(damengSql.contains("test.a"), "表名应使用schema格式");  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.contains("user_name"), "列名应正确转换");  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.toUpperCase().contains("VARCHAR(20)"), "数据类型应正确转换");
        assertTrue(damengSql.contains("NOT NULL"), "应包含NOT NULL约束");
        assertTrue(damengSql.contains("DEFAULT '10'"), "应包含默认值");
    }

    @Test
    @DisplayName("2. ALTER TABLE MODIFY COLUMN - 金仓转换")
    void testAlterTableModifyColumnKingbase() throws Exception {
        String sql = "alter table test.a modify column user_name varchar(20) not null default '10';";
        String kingbaseSql = convertMySqlToKingbase(sql);
        
        assertBasicConversionRequirements(kingbaseSql);
        
        // 金仓支持MODIFY COLUMN语法
        assertTrue(kingbaseSql.contains("ALTER TABLE"), "应包含ALTER TABLE");
        assertTrue(kingbaseSql.toUpperCase().contains("MODIFY COLUMN"), "应包含modify column");
        assertTrue(kingbaseSql.contains("\"test\".\"a\""), "表名应使用双引号和schema格式");
        assertTrue(kingbaseSql.contains("\"user_name\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("varchar(20)"), "数据类型应正确转换");
        assertTrue(kingbaseSql.toUpperCase().contains("NOT NULL"), "应包含not null约束");
        assertTrue(kingbaseSql.toUpperCase().contains("DEFAULT '10'"), "应包含默认值");
    }

    @Test
    @DisplayName("3. ALTER TABLE DROP COLUMN - 达梦转换")
    void testAlterTableDropColumnDameng() throws Exception {
        String sql = "alter table test.a drop column user_name;";
        String damengSql = convertMySqlToDameng(sql);
        
        assertBasicConversionRequirements(damengSql);
        
        assertTrue(damengSql.contains("ALTER TABLE"), "应包含ALTER TABLE");
        assertTrue(damengSql.contains("DROP COLUMN"), "应包含DROP COLUMN");
        assertTrue(damengSql.contains("test.a"), "表名应使用schema格式");  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.contains("user_name"), "列名应正确转换");  // 根据达梦官方文档，普通标识符不需要双引号
    }

    @Test
    @DisplayName("3. ALTER TABLE DROP COLUMN - 金仓转换")
    void testAlterTableDropColumnKingbase() throws Exception {
        String sql = "alter table test.a drop column user_name;";
        String kingbaseSql = convertMySqlToKingbase(sql);
        
        assertBasicConversionRequirements(kingbaseSql);
        
        assertTrue(kingbaseSql.contains("ALTER TABLE"), "应包含ALTER TABLE");
        assertTrue(kingbaseSql.toUpperCase().contains("DROP COLUMN"), "应包含drop column");
        assertTrue(kingbaseSql.contains("\"test\".\"a\""), "表名应使用双引号和schema格式");
        assertTrue(kingbaseSql.contains("\"user_name\""), "列名应使用双引号");
    }

    @Test
    @DisplayName("4. ALTER TABLE CHANGE COLUMN - 达梦转换")
    void testAlterTableChangeColumnDameng() throws Exception {
        String sql = "alter table test.a change column user_name user_name1 varchar(20) not null default '10';";
        String damengSql = convertMySqlToDameng(sql);
        
        assertBasicConversionRequirements(damengSql);
        
        // CHANGE COLUMN在达梦中需要分解为RENAME COLUMN + ALTER COLUMN
        // 或者使用达梦特有的语法
        assertTrue(damengSql.contains("ALTER TABLE"), "应包含ALTER TABLE");
        assertTrue(damengSql.contains("test.a"), "表名应使用schema格式");  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.contains("user_name") || damengSql.contains("user_name1"),
                   "应包含原列名或新列名");  // 根据达梦官方文档，普通标识符不需要双引号
        
        // 验证是否正确处理了列名变更和类型变更
        if (damengSql.contains("CHANGE")) {
            assertTrue(damengSql.contains("\"user_name1\""), "新列名应使用双引号");
            assertTrue(damengSql.contains("VARCHAR(20)"), "数据类型应正确转换");
        }
    }

    @Test
    @DisplayName("4. ALTER TABLE CHANGE COLUMN - 金仓转换")
    void testAlterTableChangeColumnKingbase() throws Exception {
        String sql = "alter table test.a change column user_name user_name1 varchar(20) not null default '10';";
        String kingbaseSql = convertMySqlToKingbase(sql);
        
        assertBasicConversionRequirements(kingbaseSql);
        
        // 金仓可能支持CHANGE COLUMN或需要分解为多个操作
        assertTrue(kingbaseSql.contains("ALTER TABLE"), "应包含ALTER TABLE");
        assertTrue(kingbaseSql.contains("\"test\".\"a\""), "表名应使用双引号和schema格式");
        assertTrue(kingbaseSql.contains("\"user_name\"") || kingbaseSql.contains("\"user_name1\""), 
                   "应包含原列名或新列名");
    }

    @Test
    @DisplayName("5. ALTER TABLE MODIFY AUTO_INCREMENT - 达梦转换")
    void testAlterTableModifyAutoIncrementDameng() throws Exception {
        String sql = "alter table test.a modify column id int not null auto_increment;";
        String damengSql = convertMySqlToDameng(sql);
        
        assertBasicConversionRequirements(damengSql);
        
        assertTrue(damengSql.contains("ALTER TABLE"), "应包含ALTER TABLE");
        assertTrue(damengSql.contains("test.a"), "表名应使用schema格式");  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.contains("id"), "列名应正确转换");  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.toUpperCase().contains("INT"), "数据类型应正确转换");
        assertTrue(damengSql.contains("NOT NULL"), "应包含NOT NULL约束");
        
        // AUTO_INCREMENT应转换为IDENTITY
        assertTrue(damengSql.contains("IDENTITY") || damengSql.contains("auto_increment"), 
                   "AUTO_INCREMENT应转换为IDENTITY或保持原样");
    }

    @Test
    @DisplayName("5. ALTER TABLE MODIFY AUTO_INCREMENT - 金仓转换")
    void testAlterTableModifyAutoIncrementKingbase() throws Exception {
        String sql = "alter table test.a modify column id int not null auto_increment;";
        String kingbaseSql = convertMySqlToKingbase(sql);
        
        assertBasicConversionRequirements(kingbaseSql);
        
        assertTrue(kingbaseSql.contains("ALTER TABLE"), "应包含ALTER TABLE");
        assertTrue(kingbaseSql.contains("\"test\".\"a\""), "表名应使用双引号和schema格式");
        assertTrue(kingbaseSql.contains("\"id\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("int"), "数据类型应正确转换");
        assertTrue(kingbaseSql.toUpperCase().contains("NOT NULL"), "应包含not null约束");

        // 金仓可能支持AUTO_INCREMENT或转换为SERIAL
        assertTrue(kingbaseSql.toUpperCase().contains("AUTO_INCREMENT") || kingbaseSql.contains("serial"),
                   "AUTO_INCREMENT应保持或转换为SERIAL");
    }

    @Test
    @DisplayName("6. ALTER TABLE MODIFY 移除AUTO_INCREMENT - 达梦转换")
    void testAlterTableModifyRemoveAutoIncrementDameng() throws Exception {
        String sql = "alter table test.a modify column id int not null;";
        String damengSql = convertMySqlToDameng(sql);
        
        assertBasicConversionRequirements(damengSql);
        
        assertTrue(damengSql.contains("ALTER TABLE"), "应包含ALTER TABLE");
        assertTrue(damengSql.contains("test.a"), "表名应使用schema格式");  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.contains("id"), "列名应正确转换");  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.toUpperCase().contains("INT"), "数据类型应正确转换");
        assertTrue(damengSql.contains("NOT NULL"), "应包含NOT NULL约束");

        // 不应包含IDENTITY或AUTO_INCREMENT
        assertFalse(damengSql.contains("IDENTITY"), "不应包含IDENTITY");
        assertFalse(damengSql.contains("AUTO_INCREMENT"), "不应包含AUTO_INCREMENT");
    }

    @Test
    @DisplayName("6. ALTER TABLE MODIFY 移除AUTO_INCREMENT - 金仓转换")
    void testAlterTableModifyRemoveAutoIncrementKingbase() throws Exception {
        String sql = "alter table test.a modify column id int not null;";
        String kingbaseSql = convertMySqlToKingbase(sql);
        
        assertBasicConversionRequirements(kingbaseSql);
        
        assertTrue(kingbaseSql.contains("ALTER TABLE"), "应包含ALTER TABLE");
        assertTrue(kingbaseSql.contains("\"test\".\"a\""), "表名应使用双引号和schema格式");
        assertTrue(kingbaseSql.contains("\"id\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("int"), "数据类型应正确转换");
        assertTrue(kingbaseSql.toUpperCase().contains("NOT NULL"), "应包含not null约束");

        // 不应包含AUTO_INCREMENT或SERIAL
        assertFalse(kingbaseSql.contains("auto_increment"), "不应包含auto_increment");
        assertFalse(kingbaseSql.contains("serial"), "不应包含serial");
    }

    @Test
    @DisplayName("7. ALTER TABLE ADD PRIMARY KEY - 达梦转换")
    void testAlterTableAddPrimaryKeyDameng() throws Exception {
        String sql = "alter table test.a add primary key (id);";
        String damengSql = convertMySqlToDameng(sql);

        assertBasicConversionRequirements(damengSql);

        assertTrue(damengSql.contains("ALTER TABLE"), "应包含ALTER TABLE");
        assertTrue(damengSql.contains("ADD"), "应包含ADD");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应包含PRIMARY KEY");
        assertTrue(damengSql.contains("test.a"), "表名应使用schema格式");  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.contains("id"), "列名应正确转换");  // 根据达梦官方文档，普通标识符不需要双引号
    }

    @Test
    @DisplayName("7. ALTER TABLE ADD PRIMARY KEY - 金仓转换")
    void testAlterTableAddPrimaryKeyKingbase() throws Exception {
        String sql = "alter table test.a add primary key (id);";
        String kingbaseSql = convertMySqlToKingbase(sql);

        assertBasicConversionRequirements(kingbaseSql);

        assertTrue(kingbaseSql.contains("ALTER TABLE"), "应包含ALTER TABLE");
        assertTrue(kingbaseSql.toUpperCase().contains("ADD"), "应包含add");
        assertTrue(kingbaseSql.toUpperCase().contains("PRIMARY KEY"), "应包含primary key");
        assertTrue(kingbaseSql.contains("\"test\".\"a\""), "表名应使用双引号和schema格式");
        assertTrue(kingbaseSql.contains("\"id\""), "列名应使用双引号");
    }

    @Test
    @DisplayName("8. ALTER TABLE ADD UNIQUE INDEX - 达梦转换")
    void testAlterTableAddUniqueIndexDameng() throws Exception {
        String sql = "alter table test.a add unique uniq_user_name(user_name);";
        String damengSql = convertMySqlToDameng(sql);

        assertBasicConversionRequirements(damengSql);

        assertTrue(damengSql.contains("ALTER TABLE"), "应包含ALTER TABLE");
        assertTrue(damengSql.contains("ADD"), "应包含ADD");
        assertTrue(damengSql.contains("UNIQUE"), "应包含UNIQUE");
        assertTrue(damengSql.contains("test.a"), "表名应使用schema格式");  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.contains("user_name"), "列名应正确转换");  // 根据达梦官方文档，普通标识符不需要双引号

        // 验证索引名称转换
        assertTrue(damengSql.contains("uniq_user_name") ||
                   damengSql.contains("udx_a_user_name"),
                   "索引名应正确转换，可能按达梦命名规范转换");  // 根据达梦官方文档，普通标识符不需要双引号
    }

    @Test
    @DisplayName("8. ALTER TABLE ADD UNIQUE INDEX - 金仓转换")
    void testAlterTableAddUniqueIndexKingbase() throws Exception {
        String sql = "alter table test.a add unique uniq_user_name(user_name);";
        String kingbaseSql = convertMySqlToKingbase(sql);

        assertBasicConversionRequirements(kingbaseSql);

        assertTrue(kingbaseSql.contains("ALTER TABLE"), "应包含ALTER TABLE");
        assertTrue(kingbaseSql.toUpperCase().contains("ADD"), "应包含add");
        assertTrue(kingbaseSql.toUpperCase().contains("UNIQUE"), "应包含unique");
        assertTrue(kingbaseSql.contains("\"test\".\"a\""), "表名应使用双引号和schema格式");
        assertTrue(kingbaseSql.contains("\"user_name\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"uniq_user_name\""), "索引名应使用双引号");
    }

    @Test
    @DisplayName("9. ALTER TABLE ADD INDEX - 达梦转换")
    void testAlterTableAddIndexDameng() throws Exception {
        String sql = "alter table test.a add index idx_user_name (user_name);";
        String damengSql = convertMySqlToDameng(sql);

        assertBasicConversionRequirements(damengSql);

        assertTrue(damengSql.contains("ALTER TABLE"), "应包含ALTER TABLE");
        assertTrue(damengSql.contains("ADD"), "应包含ADD");
        assertTrue(damengSql.contains("INDEX"), "应包含INDEX");
        assertTrue(damengSql.contains("test.a"), "表名应使用schema格式");  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.contains("user_name"), "列名应正确转换");  // 根据达梦官方文档，普通标识符不需要双引号

        // 验证索引名称转换
        assertTrue(damengSql.contains("idx_user_name") ||
                   damengSql.contains("idx_a_user_name"),
                   "索引名应正确转换，可能按达梦命名规范转换");  // 根据达梦官方文档，普通标识符不需要双引号
    }

    @Test
    @DisplayName("9. ALTER TABLE ADD INDEX - 金仓转换")
    void testAlterTableAddIndexKingbase() throws Exception {
        String sql = "alter table test.a add index idx_user_name (user_name);";
        String kingbaseSql = convertMySqlToKingbase(sql);

        assertBasicConversionRequirements(kingbaseSql);

        assertTrue(kingbaseSql.contains("ALTER TABLE"), "应包含ALTER TABLE");
        assertTrue(kingbaseSql.toUpperCase().contains("ADD"), "应包含add");
        assertTrue(kingbaseSql.toUpperCase().contains("INDEX"), "应包含index");
        assertTrue(kingbaseSql.contains("\"test\".\"a\""), "表名应使用双引号和schema格式");
        assertTrue(kingbaseSql.contains("\"user_name\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"idx_user_name\""), "索引名应使用双引号");
    }

    @Test
    @DisplayName("10. ALTER TABLE DROP PRIMARY KEY - 达梦转换")
    void testAlterTableDropPrimaryKeyDameng() throws Exception {
        String sql = "alter table test.a drop primary key;";
        String damengSql = convertMySqlToDameng(sql);

        assertBasicConversionRequirements(damengSql);

        assertTrue(damengSql.contains("ALTER TABLE"), "应包含ALTER TABLE");
        assertTrue(damengSql.contains("DROP"), "应包含DROP");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应包含PRIMARY KEY");
        assertTrue(damengSql.contains("test.a"), "表名应使用schema格式");  // 根据达梦官方文档，普通标识符不需要双引号
    }

    @Test
    @DisplayName("10. ALTER TABLE DROP PRIMARY KEY - 金仓转换")
    void testAlterTableDropPrimaryKeyKingbase() throws Exception {
        String sql = "alter table test.a drop primary key;";
        String kingbaseSql = convertMySqlToKingbase(sql);

        assertBasicConversionRequirements(kingbaseSql);

        assertTrue(kingbaseSql.contains("ALTER TABLE"), "应包含ALTER TABLE");
        assertTrue(kingbaseSql.toUpperCase().contains("DROP"), "应包含drop");
        assertTrue(kingbaseSql.toUpperCase().contains("PRIMARY KEY"), "应包含primary key");
        assertTrue(kingbaseSql.contains("\"test\".\"a\""), "表名应使用双引号和schema格式");
    }

    @Test
    @DisplayName("11. ALTER TABLE DROP INDEX - 达梦转换")
    void testAlterTableDropIndexDameng() throws Exception {
        String sql1 = "alter table test.a drop index uniq_user_name;";
        String damengSql1 = convertMySqlToDameng(sql1);

        assertBasicConversionRequirements(damengSql1);

        assertTrue(damengSql1.contains("ALTER TABLE"), "应包含ALTER TABLE");
        assertTrue(damengSql1.contains("DROP"), "应包含DROP");
        assertTrue(damengSql1.contains("INDEX"), "应包含INDEX");
        assertTrue(damengSql1.contains("test.a"), "表名应使用schema格式");  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql1.contains("uniq_user_name"), "索引名应正确转换");  // 根据达梦官方文档，普通标识符不需要双引号

        String sql2 = "alter table test.a drop index idx_user_name;";
        String damengSql2 = convertMySqlToDameng(sql2);

        assertBasicConversionRequirements(damengSql2);
        assertTrue(damengSql2.contains("idx_user_name"), "索引名应正确转换");  // 根据达梦官方文档，普通标识符不需要双引号
    }

    @Test
    @DisplayName("11. ALTER TABLE DROP INDEX - 金仓转换")
    void testAlterTableDropIndexKingbase() throws Exception {
        String sql1 = "alter table test.a drop index uniq_user_name;";
        String kingbaseSql1 = convertMySqlToKingbase(sql1);

        assertBasicConversionRequirements(kingbaseSql1);

        assertTrue(kingbaseSql1.contains("ALTER TABLE"), "应包含ALTER TABLE");
        assertTrue(kingbaseSql1.toUpperCase().contains("DROP"), "应包含drop");
        assertTrue(kingbaseSql1.toUpperCase().contains("INDEX"), "应包含index");
        assertTrue(kingbaseSql1.contains("\"test\".\"a\""), "表名应使用双引号和schema格式");
        assertTrue(kingbaseSql1.contains("\"uniq_user_name\""), "索引名应使用双引号");

        String sql2 = "alter table test.a drop index idx_user_name;";
        String kingbaseSql2 = convertMySqlToKingbase(sql2);

        assertBasicConversionRequirements(kingbaseSql2);
        assertTrue(kingbaseSql2.contains("\"idx_user_name\""), "索引名应使用双引号");
    }
}
