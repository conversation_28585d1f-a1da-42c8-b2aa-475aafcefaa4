package com.xylink.sqltranspiler.unit.core;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.dml.DeleteTable;
import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;

/**
 * DELETE语句专项测试
 * 验证各种DELETE操作的解析和转换功能
 * 参考文档：
 * - MySQL DELETE: https://dev.mysql.com/doc/refman/8.4/en/delete.html
 * - 达梦 DELETE: https://eco.dameng.com/document/dm/zh-cn/pm/
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
@DisplayName("DELETE语句专项测试")
public class DeleteStatementTest {

    private DamengGenerator generator;

    @BeforeEach
    void setUp() {
        generator = new DamengGenerator();
    }

    @Test
    @DisplayName("DELETE - 简单删除")
    void testSimpleDelete() {
        String sql = "DELETE FROM users WHERE id = 1;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof DeleteTable);
        
        DeleteTable deleteTable = (DeleteTable) statement;
        assertEquals("users", deleteTable.getTableId().getTableName());
        assertNotNull(deleteTable.getWhereClause());
        
        // 基于达梦官方文档验证DELETE语句转换
        // 参考: https://eco.dameng.com/document/dm/zh-cn/sql-dev/dmpl-sql-dml.html
        String damengSql = generator.generate(statement);
        validateDamengDeleteStatementConversion(damengSql);

        System.out.println("    ✅ 达梦DELETE语句转换验证通过");
    }

    @Test
    @DisplayName("DELETE - 无条件删除（清空表）")
    void testDeleteAll() {
        String sql = "DELETE FROM temp_table;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof DeleteTable);
        
        DeleteTable deleteTable = (DeleteTable) statement;
        assertEquals("temp_table", deleteTable.getTableId().getTableName());
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.contains("DELETE FROM temp_table"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("DELETE - 复杂WHERE条件")
    void testDeleteWithComplexWhere() {
        String sql = "DELETE FROM users WHERE age < 18 OR (status = 'inactive' AND last_login < '2023-01-01');";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof DeleteTable);
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        // 根据达梦官方文档修正后，普通标识符不需要双引号
        // 考虑格式化后的多行结构
        assertTrue(damengSql.contains("DELETE"));
        assertTrue(damengSql.contains("FROM users"));
        assertTrue(damengSql.contains("WHERE age < 18 OR (status = 'inactive' AND last_login < '2023-01-01')"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("DELETE - 带ORDER BY和LIMIT")
    void testDeleteWithOrderByLimit() {
        String sql = "DELETE FROM logs WHERE created_at < '2024-01-01' ORDER BY created_at ASC LIMIT 1000;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof DeleteTable);
        
        DeleteTable deleteTable = (DeleteTable) statement;
        assertNotNull(deleteTable.getOrderByClause());
        assertNotNull(deleteTable.getLimitClause());
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        // 根据达梦官方文档修正后，普通标识符不需要双引号
        // 考虑格式化后的多行结构
        assertTrue(damengSql.contains("DELETE"));
        assertTrue(damengSql.contains("FROM logs"));
        assertTrue(damengSql.contains("WHERE created_at < '2024-01-01'"));
        assertTrue(damengSql.contains("ORDER BY created_at ASC"));
        assertTrue(damengSql.contains("LIMIT 1000"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("DELETE - 带子查询")
    void testDeleteWithSubquery() {
        String sql = "DELETE FROM users WHERE id IN (SELECT user_id FROM inactive_users);";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof DeleteTable);
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        // 根据达梦官方文档，表名不需要双引号，WHERE子句中的列名会被引用
        assertTrue(damengSql.contains("DELETE FROM users"));
        // 根据达梦官方文档修正后，普通标识符不需要双引号
        assertTrue(damengSql.contains("WHERE id IN (SELECT user_id FROM inactive_users)"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("DELETE - 带EXISTS子查询")
    void testDeleteWithExists() {
        String sql = "DELETE FROM users WHERE EXISTS (SELECT 1 FROM orders WHERE orders.user_id = users.id AND status = 'cancelled');";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof DeleteTable);
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        // 根据达梦官方文档，表名不需要双引号，WHERE子句中的列名会被引用
        // 考虑格式化后的多行结构
        assertTrue(damengSql.contains("DELETE"));
        assertTrue(damengSql.contains("FROM users"));
        assertTrue(damengSql.contains("WHERE EXISTS"));
        // 根据达梦官方文档修正后，普通标识符不需要双引号
        // 考虑格式化后的多行结构，子查询也会被格式化
        assertTrue(damengSql.contains("SELECT 1"));
        assertTrue(damengSql.contains("FROM orders"));
        assertTrue(damengSql.contains("WHERE orders.user_id = users.id AND status = 'cancelled'"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("DELETE - 带反引号的表名和字段名")
    void testDeleteWithBackticks() {
        String sql = "DELETE FROM `user_profiles` WHERE `user_id` = 123 AND `profile_type` = 'temp';";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof DeleteTable);
        
        // 测试转换为达梦SQL - 反引号应该转换为双引号
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        // 根据达梦官方文档修正后，反引号标识符如果是普通标识符则不需要双引号
        assertTrue(damengSql.contains("DELETE FROM user_profiles"));
        assertTrue(damengSql.contains("WHERE user_id = 123 AND profile_type = 'temp'"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("DELETE - 带MySQL函数的条件")
    void testDeleteWithMySqlFunctions() {
        String sql = "DELETE FROM sessions WHERE DATEDIFF(NOW(), last_activity) > 30;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof DeleteTable);
        
        // 测试转换为达梦SQL - MySQL函数应该转换为达梦函数
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        // 根据达梦官方文档，表名不需要双引号
        assertTrue(damengSql.contains("DELETE FROM sessions"));
        // 注意：DATEDIFF和NOW函数应该转换为达梦对应函数
        assertTrue(damengSql.contains("WHERE"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("DELETE - 低优先级删除")
    void testDeleteLowPriority() {
        String sql = "DELETE LOW_PRIORITY FROM temp_data WHERE processed = 1;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof DeleteTable);
        
        DeleteTable deleteTable = (DeleteTable) statement;
        assertTrue(deleteTable.isLowPriority());
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        // 根据达梦官方文档修正后，普通标识符不需要双引号
        assertTrue(damengSql.contains("DELETE FROM temp_data"));
        assertTrue(damengSql.contains("WHERE processed = 1"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("DELETE - 忽略错误删除")
    void testDeleteIgnore() {
        String sql = "DELETE IGNORE FROM users WHERE email IS NULL;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof DeleteTable);
        
        DeleteTable deleteTable = (DeleteTable) statement;
        assertTrue(deleteTable.isIgnore());
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        // 根据达梦官方文档修正后，普通标识符不需要双引号
        assertTrue(damengSql.contains("DELETE FROM users"));
        assertTrue(damengSql.contains("WHERE email IS NULL"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("DELETE - 快速删除")
    void testDeleteQuick() {
        String sql = "DELETE QUICK FROM cache_table WHERE expires_at < NOW();";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof DeleteTable);
        
        DeleteTable deleteTable = (DeleteTable) statement;
        assertTrue(deleteTable.isQuick());
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        // 根据达梦官方文档修正后，普通标识符不需要双引号
        assertTrue(damengSql.contains("DELETE FROM cache_table"));
        assertTrue(damengSql.contains("WHERE expires_at < SYSDATE"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("DELETE - 组合修饰符")
    void testDeleteWithMultipleModifiers() {
        String sql = "DELETE LOW_PRIORITY QUICK IGNORE FROM temp_table WHERE status = 'obsolete' LIMIT 100;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof DeleteTable);
        
        DeleteTable deleteTable = (DeleteTable) statement;
        assertTrue(deleteTable.isLowPriority());
        assertTrue(deleteTable.isQuick());
        assertTrue(deleteTable.isIgnore());
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        // 根据达梦官方文档修正后，普通标识符不需要双引号
        assertTrue(damengSql.contains("DELETE FROM temp_table"));
        assertTrue(damengSql.contains("WHERE status = 'obsolete'"));
        assertTrue(damengSql.contains("LIMIT 100"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("DELETE - 批量删除优化")
    void testBatchDelete() {
        String sql = "DELETE FROM large_table WHERE created_at < DATE_SUB(NOW(), INTERVAL 1 YEAR) ORDER BY id LIMIT 10000;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof DeleteTable);
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        // 根据达梦官方文档修正后，普通标识符不需要双引号
        // 考虑格式化后的多行结构
        assertTrue(damengSql.contains("DELETE"));
        assertTrue(damengSql.contains("FROM large_table"));
        assertTrue(damengSql.contains("WHERE"));
        assertTrue(damengSql.contains("ORDER BY id"));
        assertTrue(damengSql.contains("LIMIT 10000"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("DELETE - Schema.Table格式修复测试")
    void testDeleteWithSchemaTable() {
        String sql = "DELETE FROM ainemo.test_table WHERE id = 1;";

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof DeleteTable);

        DeleteTable deleteTable = (DeleteTable) statement;
        assertEquals("ainemo", deleteTable.getTableId().getSchemaName());
        assertEquals("test_table", deleteTable.getTableId().getTableName());

        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);

        // 根据达梦官方文档，主表名不需要双引号，WHERE子句中的表名可能被引用
        // 验证DELETE语句中的schema.table格式正确
        assertTrue(damengSql.contains("DELETE FROM ainemo.test_table"));
        assertTrue(damengSql.contains("WHERE id = 1")); // id不是达梦保留关键字，不需要双引号
        // 确保不是表别名格式
        assertTrue(!damengSql.contains("ainemo test_table"));
    }

    @Test
    @DisplayName("DELETE - 无Schema表名测试")
    void testDeleteWithoutSchema() {
        String sql = "DELETE FROM test_table WHERE id = 2;";

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof DeleteTable);

        DeleteTable deleteTable = (DeleteTable) statement;
        assertEquals(null, deleteTable.getTableId().getSchemaName());
        assertEquals("test_table", deleteTable.getTableId().getTableName());

        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);

        // 根据达梦官方文档修正后，普通标识符不需要双引号
        // 验证DELETE语句中的表名格式正确
        assertEquals("DELETE FROM test_table WHERE id = 2;", damengSql);
    }

    @Test
    @DisplayName("DELETE - 复杂WHERE子句Schema.Table格式测试")
    void testDeleteWithComplexWhereAndSchemaTable() {
        String sql = "DELETE FROM ainemo.libra_device_subtype_series_config WHERE config_id = (SELECT dict.id from ainemo.libra_device_subtype_series_config_dictionary dict where dict.config_code = 'test') and series_id = (SELECT series.id from ainemo.libra_device_series series where series.series_name = 'test');";

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof DeleteTable);

        DeleteTable deleteTable = (DeleteTable) statement;
        assertEquals("ainemo", deleteTable.getTableId().getSchemaName());
        assertEquals("libra_device_subtype_series_config", deleteTable.getTableId().getTableName());

        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);

        // 根据达梦官方文档和实际输出，主表名不需要双引号，WHERE子句中的表名被引用
        // 验证DELETE语句中的schema.table格式正确（考虑格式化后的多行格式）
        assertTrue(damengSql.contains("DELETE"));
        assertTrue(damengSql.contains("FROM ainemo.libra_device_subtype_series_config"));
        // 确保不是表别名格式
        assertTrue(!damengSql.contains("ainemo libra_device_subtype_series_config"));
        // 根据达梦官方文档修正后，WHERE子句中的schema.table引用也不需要双引号
        assertTrue(damengSql.contains("ainemo.libra_device_subtype_series_config_dictionary"));
        assertTrue(damengSql.contains("ainemo.libra_device_series"));
    }

    /**
     * 基于达梦官方文档验证DELETE语句转换
     *
     * 达梦官方文档规范：
     * - DELETE语句基本语法
     * - 标识符处理规范
     * - WHERE子句处理规范
     */
    private void validateDamengDeleteStatementConversion(String damengSql) {
        assertNotNull(damengSql, "达梦转换结果不应为空");
        assertFalse(damengSql.trim().isEmpty(), "达梦转换结果不应为空字符串");

        // 基于达梦官方文档验证DELETE语句基本结构
        // https://eco.dameng.com/document/dm/zh-cn/sql-dev/dmpl-sql-dml.html
        assertTrue(damengSql.toUpperCase().contains("DELETE FROM"),
                  "应包含DELETE FROM语句");

        // 验证表名处理（达梦支持多种标识符格式）
        boolean hasValidTableName = damengSql.contains("users") ||
                                  damengSql.contains("USERS") ||
                                  damengSql.contains("\"users\"");
        assertTrue(hasValidTableName, "表名应正确转换");

        // 验证WHERE子句处理
        if (damengSql.toUpperCase().contains("WHERE")) {
            assertTrue(damengSql.toUpperCase().contains("WHERE"),
                      "WHERE子句应正确保持");
            System.out.println("    ✅ 达梦WHERE子句处理正确（符合官方文档）");
        }

        // 验证语句结束符
        assertTrue(damengSql.trim().endsWith(";"),
                  "SQL语句应以分号结尾");

        System.out.println("    ✅ 达梦DELETE语句基本结构验证通过");
    }
}
