package com.xylink.sqltranspiler.unit.core;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import com.xylink.sqltranspiler.core.ast.dml.DeleteTable;
import com.xylink.sqltranspiler.core.ast.dml.InsertTable;
import com.xylink.sqltranspiler.core.ast.dml.QueryStmt;
import com.xylink.sqltranspiler.core.ast.dml.UpdateTable;
import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;

/**
 * CRUD操作完整测试套件 - 严格遵循官方文档规范
 * 验证CREATE、SELECT、INSERT、UPDATE、DELETE语句的解析和转换功能
 *
 * 测试原则：
 * 1. 不允许推测，必须基于官方文档
 * 2. 测试期望与官方文档不符时，修正测试用例而不是降低代码质量
 * 3. 坚持正确的实现，确保功能的准确性和完整性
 *
 * 官方文档依据：
 * - MySQL 8.4 CRUD操作: https://dev.mysql.com/doc/refman/8.4/en/sql-data-manipulation-statements.html
 * - MySQL 8.4 DDL语句: https://dev.mysql.com/doc/refman/8.4/en/sql-data-definition-statements.html
 * - 达梦数据库SQL开发: https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 达梦数据库产品手册: https://eco.dameng.com/document/dm/zh-cn/pm/
 */
@DisplayName("CRUD操作完整测试")
public class CrudOperationsTest {

    private DamengGenerator generator;

    @BeforeEach
    void setUp() {
        generator = new DamengGenerator();
    }

    // ================================ CREATE TABLE 测试 ================================

    @Test
    @DisplayName("CREATE TABLE - 基本表创建")
    void testCreateTableBasic() {
        String sql = "CREATE TABLE users (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(100) NOT NULL, email VARCHAR(255) UNIQUE);";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof CreateTable);
        
        CreateTable createTable = (CreateTable) statement;
        assertEquals("users", createTable.getTableId().getTableName());
        assertNotNull(createTable.getColumnRels());
        assertEquals(3, createTable.getColumnRels().size());
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        assertNotNull(damengSql);
        // 根据达梦官方文档修正后，普通标识符不需要双引号
        assertTrue(damengSql.contains("CREATE TABLE users"));
        assertTrue(damengSql.contains("id INT IDENTITY(1,1)"));
        assertTrue(damengSql.contains("name VARCHAR(100) NOT NULL"));
        assertTrue(damengSql.contains("email VARCHAR(255)"));
        assertTrue(damengSql.endsWith(";"));
        assertFalse(damengSql.contains("AUTO_INCREMENT"));
    }

    @Test
    @DisplayName("CREATE TABLE - 复杂表结构")
    void testCreateTableComplex() {
        String sql = """
            CREATE TABLE orders (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                product_name VARCHAR(255) NOT NULL,
                quantity INT DEFAULT 1,
                price DECIMAL(10,2) NOT NULL,
                status ENUM('pending', 'completed', 'cancelled') DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """;
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof CreateTable);
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        assertNotNull(damengSql);
        // 根据达梦官方文档修正后，普通标识符不需要双引号
        assertTrue(damengSql.contains("CREATE TABLE orders"));
        assertTrue(damengSql.contains("id BIGINT IDENTITY(1,1)"));
        assertTrue(damengSql.contains("user_id INT NOT NULL"));
        assertTrue(damengSql.contains("price DECIMAL(10,2) NOT NULL"));
        assertTrue(damengSql.contains("created_at TIMESTAMP DEFAULT SYSDATE"));
        assertFalse(damengSql.contains("ENGINE=InnoDB"));
        assertFalse(damengSql.contains("DEFAULT CHARSET"));
        assertTrue(damengSql.endsWith(";"));
    }

    // ================================ SELECT 测试 ================================

    @Test
    @DisplayName("SELECT - 基本查询")
    void testSelectBasic() {
        String sql = "SELECT id, name, email FROM users WHERE id > 10;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        System.out.println("Parsed statement type: " + statement.getClass().getSimpleName());
        assertTrue(statement instanceof QueryStmt);
        
        QueryStmt queryStmt = (QueryStmt) statement;
        assertNotNull(queryStmt.getInputTables());
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("SELECT"));
        assertTrue(damengSql.contains("FROM"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("SELECT - 复杂查询")
    void testSelectComplex() {
        String sql = """
            SELECT u.id, u.name, u.email, COUNT(o.id) as order_count, SUM(o.price) as total_amount
            FROM users u
            LEFT JOIN orders o ON u.id = o.user_id
            WHERE u.created_at >= '2024-01-01'
            GROUP BY u.id, u.name, u.email
            HAVING COUNT(o.id) > 0
            ORDER BY total_amount DESC
            LIMIT 10;
            """;

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof QueryStmt);

        QueryStmt queryStmt = (QueryStmt) statement;
        assertNotNull(queryStmt.getSql());
        assertTrue(queryStmt.getSql().contains("SELECT"));
        assertTrue(queryStmt.getSql().contains("LEFT JOIN"));
        assertTrue(queryStmt.getSql().contains("GROUP BY"));
        assertTrue(queryStmt.getSql().contains("ORDER BY"));
        assertTrue(queryStmt.getSql().contains("LIMIT"));

        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("SELECT"));
        assertTrue(damengSql.contains("LEFT JOIN"));
        assertTrue(damengSql.contains("GROUP BY"));
        assertTrue(damengSql.contains("ORDER BY"));
        assertTrue(damengSql.contains("LIMIT"));
        assertTrue(damengSql.endsWith(";"));

        // 验证达梦SQL不包含MySQL特有语法
        assertFalse(damengSql.contains("`")); // 不应该包含反引号
    }



    @Test
    @DisplayName("SELECT - 子查询")
    void testSelectSubquery() {
        String sql = "SELECT * FROM users WHERE id IN (SELECT user_id FROM orders WHERE status = 'completed');";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof QueryStmt);
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("SELECT"));
        assertTrue(damengSql.contains("users"));
        assertTrue(damengSql.contains("WHERE"));
        assertTrue(damengSql.contains("IN"));
        assertTrue(damengSql.endsWith(";"));
    }

    // ================================ INSERT 测试 ================================

    @Test
    @DisplayName("INSERT - 基本插入")
    void testInsertBasic() {
        String sql = "INSERT INTO users (name, email) VALUES ('John Doe', '<EMAIL>');";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof InsertTable);
        
        InsertTable insertTable = (InsertTable) statement;
        assertEquals("users", insertTable.getTableId().getTableName());
        assertNotNull(insertTable.getColumns());
        assertEquals(2, insertTable.getColumns().size());
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("INSERT INTO users (name, email)"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.contains("VALUES ('John Doe', '<EMAIL>')"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("INSERT - 批量插入")
    void testInsertMultiple() {
        String sql = """
            INSERT INTO users (name, email) VALUES 
            ('John Doe', '<EMAIL>'),
            ('Jane Smith', '<EMAIL>'),
            ('Bob Johnson', '<EMAIL>');
            """;
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof InsertTable);
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("INSERT INTO users (name, email)"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.contains("VALUES ('John Doe', '<EMAIL>')"));
        assertTrue(damengSql.contains("('Jane Smith', '<EMAIL>')"));
        assertTrue(damengSql.contains("('Bob Johnson', '<EMAIL>')"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("INSERT - 无列名插入")
    void testInsertWithoutColumns() {
        String sql = "INSERT INTO users VALUES (1, 'John Doe', '<EMAIL>');";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof InsertTable);
        
        InsertTable insertTable = (InsertTable) statement;
        assertTrue(insertTable.getColumns() == null || insertTable.getColumns().isEmpty());
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("INSERT INTO users"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql.contains("VALUES (1, 'John Doe', '<EMAIL>')"));
        assertTrue(damengSql.endsWith(";"));
    }

    // ================================ UPDATE 测试 ================================

    @Test
    @DisplayName("UPDATE - 基本更新")
    void testUpdateBasic() {
        String sql = "UPDATE users SET name = 'John Smith', email = '<EMAIL>' WHERE id = 1;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof UpdateTable);
        
        UpdateTable updateTable = (UpdateTable) statement;
        assertEquals("users", updateTable.getTableId().getTableName());
        assertNotNull(updateTable.getSetClauses());
        assertEquals(2, updateTable.getSetClauses().size());
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("UPDATE"));
        assertTrue(damengSql.contains("users"));
        assertTrue(damengSql.contains("name"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("UPDATE - 带函数的更新")
    void testUpdateWithFunctions() {
        String sql = "UPDATE users SET name = UPPER(name), updated_at = NOW() WHERE created_at < '2024-01-01';";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof UpdateTable);
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("UPDATE"));
        assertTrue(damengSql.contains("users"));
        assertTrue(damengSql.contains("SET"));
        assertTrue(damengSql.contains("SYSDATE")); // NOW() 应该转换为 SYSDATE
        assertTrue(damengSql.endsWith(";"));
    }

    // ================================ DELETE 测试 ================================

    @Test
    @DisplayName("DELETE - 基本删除")
    void testDeleteBasic() {
        String sql = "DELETE FROM users WHERE id = 1;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof DeleteTable);
        
        DeleteTable deleteTable = (DeleteTable) statement;
        assertEquals("users", deleteTable.getTableId().getTableName());
        assertNotNull(deleteTable.getWhereClause());
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("DELETE FROM"));
        assertTrue(damengSql.contains("users"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("DELETE - 条件删除")
    void testDeleteWithConditions() {
        String sql = "DELETE FROM users WHERE created_at < '2023-01-01' AND status = 'inactive';";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof DeleteTable);
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("DELETE FROM"));
        assertTrue(damengSql.contains("users"));
        assertTrue(damengSql.contains("WHERE"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("DELETE - 带LIMIT的删除")
    void testDeleteWithLimit() {
        String sql = "DELETE FROM users WHERE status = 'inactive' ORDER BY created_at ASC LIMIT 100;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof DeleteTable);
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("DELETE FROM"));
        assertTrue(damengSql.contains("users"));
        assertTrue(damengSql.contains("WHERE"));
        assertTrue(damengSql.contains("ORDER BY"));
        assertTrue(damengSql.contains("LIMIT"));
        assertTrue(damengSql.endsWith(";"));
    }

    // ================================ MySQL到达梦转换规范测试 ================================

    @Test
    @DisplayName("CRUD - MySQL函数转换测试")
    void testMySqlFunctionConversion() {
        // 测试IFNULL转换为NVL
        String sql1 = "SELECT IFNULL(name, 'Unknown') FROM users;";
        Statement stmt1 = MySqlHelper.parseStatement(sql1);
        String damengSql1 = generator.generate(stmt1);
        System.out.println("IFNULL test - Generated SQL: " + damengSql1);
        assertTrue(damengSql1.contains("NVL"));
        assertFalse(damengSql1.contains("IFNULL"));

        // 测试CONCAT函数保持不变（根据达梦官方文档，达梦完全支持CONCAT函数）
        String sql2 = "SELECT CONCAT(first_name, ' ', last_name) as full_name FROM users;";
        Statement stmt2 = MySqlHelper.parseStatement(sql2);
        String damengSql2 = generator.generate(stmt2);
        System.out.println("CONCAT test - Generated SQL: " + damengSql2);
        assertTrue(damengSql2.contains("CONCAT"));
        assertFalse(damengSql2.contains("||"));

        // 测试DATE_FORMAT转换为TO_CHAR
        String sql3 = "SELECT DATE_FORMAT(created_at, '%Y-%m-%d') FROM users;";
        Statement stmt3 = MySqlHelper.parseStatement(sql3);
        String damengSql3 = generator.generate(stmt3);
        System.out.println("DATE_FORMAT test - Generated SQL: " + damengSql3);
        assertTrue(damengSql3.contains("TO_CHAR"));
        assertFalse(damengSql3.contains("DATE_FORMAT"));
    }

    @Test
    @DisplayName("CRUD - 反引号转换测试")
    void testBacktickConversion() {
        // 测试表名反引号转换
        String sql1 = "SELECT * FROM `users` WHERE `id` = 1;";
        Statement stmt1 = MySqlHelper.parseStatement(sql1);
        String damengSql1 = generator.generate(stmt1);
        assertFalse(damengSql1.contains("`"));
        assertTrue(damengSql1.contains("users"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql1.contains("id"));     // 根据达梦官方文档，普通标识符不需要双引号

        // 测试UPDATE中的反引号转换
        String sql2 = "UPDATE `user_profiles` SET `last_login` = NOW() WHERE `user_id` = 1;";
        Statement stmt2 = MySqlHelper.parseStatement(sql2);
        String damengSql2 = generator.generate(stmt2);
        assertFalse(damengSql2.contains("`"));
        assertTrue(damengSql2.contains("user_profiles"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql2.contains("last_login"));     // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql2.contains("user_id"));        // 根据达梦官方文档，普通标识符不需要双引号
    }

    @Test
    @DisplayName("CRUD - 复杂JOIN查询")
    void testComplexJoinQuery() {
        String sql = """
            SELECT u.id, u.name, p.title, COUNT(o.id) as order_count
            FROM users u
            INNER JOIN profiles p ON u.id = p.user_id
            LEFT JOIN orders o ON u.id = o.user_id
            WHERE u.status = 'active' AND p.is_public = 1
            GROUP BY u.id, u.name, p.title
            HAVING COUNT(o.id) >= 5
            ORDER BY order_count DESC, u.name ASC
            LIMIT 20;
            """;

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof QueryStmt);

        String damengSql = generator.generate(statement);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("INNER JOIN"));
        assertTrue(damengSql.contains("LEFT JOIN"));
        assertTrue(damengSql.contains("GROUP BY"));
        assertTrue(damengSql.contains("HAVING"));
        assertTrue(damengSql.contains("ORDER BY"));
        assertTrue(damengSql.contains("LIMIT"));
        assertTrue(damengSql.endsWith(";"));
    }

    // ================================ 高级SQL功能测试 ================================

    // @Test
    @DisplayName("高级SQL - 窗口函数 - 暂不支持")
    void testWindowFunctions() {
        String sql = """
            SELECT
                id, name, salary,
                ROW_NUMBER() OVER (ORDER BY salary DESC) as rank_num,
                RANK() OVER (PARTITION BY department_id ORDER BY salary DESC) as dept_rank,
                LAG(salary, 1) OVER (ORDER BY salary) as prev_salary,
                LEAD(salary, 1) OVER (ORDER BY salary) as next_salary
            FROM employees
            WHERE department_id IS NOT NULL;
            """;

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof QueryStmt);

        String damengSql = generator.generate(statement);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("ROW_NUMBER()"));
        assertTrue(damengSql.contains("RANK()"));
        assertTrue(damengSql.contains("LAG("));
        assertTrue(damengSql.contains("LEAD("));
        assertTrue(damengSql.contains("OVER"));
        assertTrue(damengSql.contains("PARTITION BY"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("高级SQL - CTE公用表表达式 - 达梦数据库支持")
    void testCommonTableExpression() {
        String sql = "WITH RECURSIVE employee_hierarchy AS (SELECT id, name, manager_id, 1 as level FROM employees WHERE manager_id IS NULL UNION ALL SELECT e.id, e.name, e.manager_id, eh.level + 1 FROM employees e INNER JOIN employee_hierarchy eh ON e.manager_id = eh.id) SELECT * FROM employee_hierarchy ORDER BY level, name;";

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        // CTE被解析为QueryStmt类型

        String damengSql = generator.generate(statement);
        assertNotNull(damengSql);
        // 根据达梦官方文档，达梦数据库支持CTE，应该保留WITH语法
        assertTrue(damengSql.contains("WITH") || damengSql.contains("-- Unsupported: WITH"),
                  "达梦数据库应该支持CTE或明确标记为不支持");
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("高级SQL - 复杂子查询和EXISTS")
    void testComplexSubqueryAndExists() {
        // 复杂的EXISTS查询 - 保持原始多行格式
        String sql = """
            SELECT u.id, u.name, u.email
            FROM users u
            WHERE EXISTS (
                SELECT 1 FROM orders o
                WHERE o.user_id = u.id
                AND o.status = 'completed'
                AND o.created_at >= '2024-01-01'
            )
            AND NOT EXISTS (
                SELECT 1 FROM user_blocks ub
                WHERE ub.blocked_user_id = u.id
                AND ub.is_active = 1
            )
            ORDER BY u.created_at DESC;
            """;

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof QueryStmt);

        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("SELECT"));
        assertTrue(damengSql.contains("EXISTS"));
        assertTrue(damengSql.contains("NOT EXISTS"));
        assertTrue(damengSql.contains("ORDER BY"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("高级SQL - CASE WHEN表达式")
    void testCaseWhenExpression() {
        String sql = """
            SELECT
                id, name, age,
                CASE
                    WHEN age < 18 THEN '未成年'
                    WHEN age BETWEEN 18 AND 65 THEN '成年人'
                    ELSE '老年人'
                END as age_group,
                CASE status
                    WHEN 'active' THEN '活跃'
                    WHEN 'inactive' THEN '非活跃'
                    WHEN 'suspended' THEN '暂停'
                    ELSE '未知'
                END as status_desc
            FROM users
            WHERE status IN ('active', 'inactive', 'suspended');
            """;

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof QueryStmt);

        String damengSql = generator.generate(statement);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("CASE"));
        assertTrue(damengSql.contains("WHEN"));
        assertTrue(damengSql.contains("THEN"));
        assertTrue(damengSql.contains("ELSE"));
        assertTrue(damengSql.contains("END"));
        assertTrue(damengSql.contains("BETWEEN"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("高级SQL - 聚合函数和分组")
    void testAggregationAndGrouping() {
        String sql = """
            SELECT
                department_id,
                COUNT(*) as employee_count,
                AVG(salary) as avg_salary,
                MIN(salary) as min_salary,
                MAX(salary) as max_salary,
                SUM(salary) as total_salary,
                STDDEV(salary) as salary_stddev,
                GROUP_CONCAT(name ORDER BY name SEPARATOR ', ') as employee_names
            FROM employees
            WHERE hire_date >= '2020-01-01'
            GROUP BY department_id
            HAVING COUNT(*) >= 5 AND AVG(salary) > 50000
            ORDER BY avg_salary DESC;
            """;

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof QueryStmt);

        String damengSql = generator.generate(statement);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("COUNT(*)"));
        assertTrue(damengSql.contains("AVG("));
        assertTrue(damengSql.contains("MIN("));
        assertTrue(damengSql.contains("MAX("));
        assertTrue(damengSql.contains("SUM("));
        assertTrue(damengSql.contains("GROUP BY"));
        assertTrue(damengSql.contains("HAVING"));
        // GROUP_CONCAT在达梦中可能需要转换为LISTAGG或其他函数
        assertTrue(damengSql.endsWith(";"));
    }

    // ================================ 复杂DML操作测试 ================================

    @Test
    @DisplayName("复杂DML - INSERT INTO...SELECT")
    void testInsertIntoSelect() {
        String sql = """
            INSERT INTO user_backup (id, name, email, created_at)
            SELECT id, name, email, created_at
            FROM users
            WHERE created_at < DATE_SUB(NOW(), INTERVAL 1 YEAR)
            AND status = 'inactive';
            """;

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof InsertTable);

        String damengSql = generator.generate(statement);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("INSERT INTO"));
        assertTrue(damengSql.contains("SELECT"));
        assertTrue(damengSql.contains("FROM"));
        assertTrue(damengSql.contains("WHERE"));
        assertTrue(damengSql.contains("SYSDATE")); // NOW()应该转换为SYSDATE
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("复杂DML - INSERT ON DUPLICATE KEY UPDATE")
    void testInsertOnDuplicateKeyUpdate() {
        String sql = """
            INSERT INTO user_stats (user_id, login_count, last_login)
            VALUES (1, 1, NOW())
            ON DUPLICATE KEY UPDATE
                login_count = login_count + 1,
                last_login = NOW();
            """;

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof InsertTable);

        String damengSql = generator.generate(statement);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("INSERT INTO"));
        assertTrue(damengSql.contains("VALUES"));
        // 达梦数据库使用MERGE语句或其他方式实现UPSERT
        // ON DUPLICATE KEY UPDATE是MySQL特有语法，需要转换
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("复杂DML - 多表UPDATE")
    void testMultiTableUpdate() {
        String sql = """
            UPDATE users u
            INNER JOIN user_profiles p ON u.id = p.user_id
            SET u.last_updated = NOW(),
                p.profile_updated = NOW()
            WHERE u.status = 'active'
            AND p.is_complete = 0;
            """;

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        // 多表UPDATE应该被解析为MultiTableUpdate
        assertTrue(statement instanceof com.xylink.sqltranspiler.core.ast.dml.MultiTableUpdate,
                  "Multi-table UPDATE should be parsed as MultiTableUpdate, but got: " + statement.getClass().getSimpleName());

        String damengSql = generator.generate(statement);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("UPDATE"));
        assertTrue(damengSql.contains("SET"));
        assertTrue(damengSql.contains("WHERE"));
        assertTrue(damengSql.contains("SYSDATE")); // NOW()转换为SYSDATE
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("复杂DML - 多表DELETE")
    void testMultiTableDelete() {
        String sql = """
            DELETE u, p
            FROM users u
            INNER JOIN user_profiles p ON u.id = p.user_id
            WHERE u.created_at < DATE_SUB(NOW(), INTERVAL 2 YEAR)
            AND u.status = 'deleted';
            """;

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        // 多表DELETE应该被解析为MultiTableDelete
        assertTrue(statement instanceof com.xylink.sqltranspiler.core.ast.dml.MultiTableDelete,
                  "Multi-table DELETE should be parsed as MultiTableDelete, but got: " + statement.getClass().getSimpleName());

        String damengSql = generator.generate(statement);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("DELETE"));
        assertTrue(damengSql.contains("FROM"));
        assertTrue(damengSql.contains("WHERE"));
        assertTrue(damengSql.endsWith(";"));
    }

    // REPLACE INTO测试已移至ReplaceIntoStatementCompatibilityTest，避免重复测试

    // ================================ 数据类型和约束测试 ================================

    @Test
    @DisplayName("数据类型 - MySQL特有类型转换")
    void testMySqlSpecificDataTypes() {
        String sql = """
            CREATE TABLE test_types (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                is_active TINYINT(1) DEFAULT 0,
                content TEXT,
                data LONGTEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                price DECIMAL(10,2) NOT NULL,
                status ENUM('pending', 'active', 'inactive') DEFAULT 'pending',
                tags SET('tag1', 'tag2', 'tag3'),
                binary_data VARBINARY(255)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """;

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof CreateTable);

        String damengSql = generator.generate(statement);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("CREATE TABLE"));
        assertTrue(damengSql.contains("IDENTITY(1,1)")); // AUTO_INCREMENT转换
        assertTrue(damengSql.contains("BIT")); // TINYINT(1)转换为BIT
        assertTrue(damengSql.contains("CLOB")); // TEXT转换为CLOB
        assertTrue(damengSql.contains("SYSDATE")); // CURRENT_TIMESTAMP转换
        assertFalse(damengSql.contains("ENGINE=InnoDB")); // 移除MySQL特有语法
        assertFalse(damengSql.contains("DEFAULT CHARSET")); // 移除MySQL特有语法
        assertTrue(damengSql.endsWith(";"));
    }

    // ================================ 索引和约束测试 ================================

    @Test
    @DisplayName("索引 - CREATE INDEX语句")
    void testCreateIndex() {
        String sql1 = "CREATE INDEX idx_users_email ON users (email);";
        Statement stmt1 = MySqlHelper.parseStatement(sql1);
        assertNotNull(stmt1);

        String damengSql1 = generator.generate(stmt1);
        assertNotNull(damengSql1);
        assertTrue(damengSql1.contains("CREATE INDEX"));
        assertTrue(damengSql1.contains("idx_users_email"));
        assertTrue(damengSql1.contains("users"));   // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql1.contains("email"));   // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(damengSql1.endsWith(";"));

        // 测试唯一索引
        String sql2 = "CREATE UNIQUE INDEX udx_users_username ON users (username);";
        Statement stmt2 = MySqlHelper.parseStatement(sql2);
        assertNotNull(stmt2);

        String damengSql2 = generator.generate(stmt2);
        assertNotNull(damengSql2);
        assertTrue(damengSql2.contains("CREATE UNIQUE INDEX"));
        assertTrue(damengSql2.contains("udx_users_username"));
        assertTrue(damengSql2.endsWith(";"));
    }

    @Test
    @DisplayName("索引 - 复合索引")
    void testCompositeIndex() {
        String sql = "CREATE INDEX idx_orders_user_status_date ON orders (user_id, status, created_at DESC);";

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);

        String damengSql = generator.generate(statement);
        assertNotNull(damengSql);
        // 根据达梦官方文档修正后，普通标识符不需要双引号
        assertTrue(damengSql.contains("CREATE INDEX"));
        assertTrue(damengSql.contains("idx_orders_user_id_status_created_at"));  // 新的统一达梦命名标准
        assertTrue(damengSql.contains("orders"));
        assertTrue(damengSql.contains("user_id"));
        assertTrue(damengSql.contains("status"));
        assertTrue(damengSql.contains("created_at"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("约束 - ALTER TABLE添加约束 - 达梦数据库支持")
    void testAlterTableAddConstraints() {
        // 添加外键约束
        String sql1 = "ALTER TABLE orders ADD CONSTRAINT fk_orders_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;";
        Statement stmt1 = MySqlHelper.parseStatement(sql1);
        assertNotNull(stmt1);

        String damengSql1 = generator.generate(stmt1);
        assertNotNull(damengSql1);
        System.out.println("达梦数据库ADD CONSTRAINT转换结果: " + damengSql1);
        assertTrue(damengSql1.contains("ALTER TABLE"));
        assertTrue(damengSql1.contains("orders") || damengSql1.contains("\"orders\""));
        assertTrue(damengSql1.contains("FOREIGN KEY") || damengSql1.contains("ADD CONSTRAINT"));
        assertTrue(damengSql1.contains("REFERENCES") || damengSql1.contains("-- Unsupported"));
        assertTrue(damengSql1.endsWith(";"));

        // 添加检查约束
        String sql2 = "ALTER TABLE products ADD CONSTRAINT chk_price_positive CHECK (price > 0);";
        Statement stmt2 = MySqlHelper.parseStatement(sql2);
        assertNotNull(stmt2);

        String damengSql2 = generator.generate(stmt2);
        assertNotNull(damengSql2);
        assertTrue(damengSql2.contains("ALTER TABLE"));
        assertTrue(damengSql2.contains("CHECK"));
        assertTrue(damengSql2.endsWith(";"));
    }

    // ================================ 存储过程和函数测试 ================================

    // @Test
    @DisplayName("存储过程 - 基本存储过程 - 达梦数据库支持")
    void testStoredProcedure() {
        String sql = """
            CREATE PROCEDURE GetUserOrders(IN user_id INT)
            BEGIN
                SELECT o.id, o.product_name, o.price, o.status
                FROM orders o
                WHERE o.user_id = user_id
                ORDER BY o.created_at DESC;
            END;
            """;

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);

        String damengSql = generator.generate(statement);
        assertNotNull(damengSql);
        // 达梦数据库支持存储过程，但语法可能略有不同
        assertTrue(damengSql.contains("CREATE"));
        assertTrue(damengSql.contains("PROCEDURE"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("函数 - 用户定义函数 - 达梦数据库支持")
    void testUserDefinedFunction() {
        String sql = """
            CREATE FUNCTION CalculateAge(birth_date DATE)
            RETURNS INT
            READS SQL DATA
            DETERMINISTIC
            BEGIN
                RETURN YEAR(CURDATE()) - YEAR(birth_date);
            END;
            """;

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);

        String damengSql = generator.generate(statement);
        assertNotNull(damengSql);
        // 根据达梦官方文档，达梦数据库支持用户定义函数
        assertTrue(damengSql.contains("CREATE") || damengSql.contains("-- Unsupported"),
                  "达梦数据库应该支持用户定义函数或明确标记为不支持");
        assertTrue(damengSql.endsWith(";"));
    }

    // ================================ 事务和锁测试 ================================

    // @Test
    @DisplayName("事务 - 事务控制语句 - 达梦数据库支持")
    void testTransactionControl() {
        // START TRANSACTION
        String sql1 = "START TRANSACTION;";
        Statement stmt1 = MySqlHelper.parseStatement(sql1);
        assertNotNull(stmt1);

        String damengSql1 = generator.generate(stmt1);
        assertNotNull(damengSql1);
        // 达梦数据库使用BEGIN TRANSACTION或BEGIN
        assertTrue(damengSql1.endsWith(";"));

        // COMMIT
        String sql2 = "COMMIT;";
        Statement stmt2 = MySqlHelper.parseStatement(sql2);
        assertNotNull(stmt2);

        String damengSql2 = generator.generate(stmt2);
        assertNotNull(damengSql2);
        assertTrue(damengSql2.contains("COMMIT"));
        assertTrue(damengSql2.endsWith(";"));

        // ROLLBACK
        String sql3 = "ROLLBACK;";
        Statement stmt3 = MySqlHelper.parseStatement(sql3);
        assertNotNull(stmt3);

        String damengSql3 = generator.generate(stmt3);
        assertNotNull(damengSql3);
        assertTrue(damengSql3.contains("ROLLBACK"));
        assertTrue(damengSql3.endsWith(";"));
    }

    // ================================ 用户指定的复杂SQL测试 ================================

    @Test
    @DisplayName("复杂SQL - 用户指定的复杂查询")
    void testUserSpecifiedComplexQuery() {
        String sql = """
            SELECT u.id, u.name, u.email, COUNT(o.id) as order_count, SUM(o.price) as total_amount
            FROM users u
            LEFT JOIN orders o ON u.id = o.user_id
            WHERE u.created_at >= '2024-01-01'
            GROUP BY u.id, u.name, u.email
            HAVING COUNT(o.id) > 0
            ORDER BY total_amount DESC
            LIMIT 10;
            """;

        System.out.println("Testing complex SQL: " + sql);

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement, "SQL语句应该能够成功解析");
        assertTrue(statement instanceof QueryStmt, "应该解析为QueryStmt类型");

        String damengSql = generator.generate(statement);
        assertNotNull(damengSql, "应该能够生成达梦SQL");
        System.out.println("Generated Dameng SQL: " + damengSql);

        // 验证SQL包含所有必要的关键字和结构
        assertTrue(damengSql.contains("SELECT"), "应该包含SELECT关键字");
        assertTrue(damengSql.contains("COUNT("), "应该包含COUNT聚合函数");
        assertTrue(damengSql.contains("SUM("), "应该包含SUM聚合函数");
        assertTrue(damengSql.contains("LEFT JOIN"), "应该包含LEFT JOIN");
        assertTrue(damengSql.contains("WHERE"), "应该包含WHERE子句");
        assertTrue(damengSql.contains("GROUP BY"), "应该包含GROUP BY子句");
        assertTrue(damengSql.contains("HAVING"), "应该包含HAVING子句");
        assertTrue(damengSql.contains("ORDER BY"), "应该包含ORDER BY子句");
        assertTrue(damengSql.contains("LIMIT"), "应该包含LIMIT子句");
        assertTrue(damengSql.endsWith(";"), "应该以分号结尾");

        // 验证达梦SQL不包含MySQL特有语法
        assertFalse(damengSql.contains("`"), "不应该包含反引号");

        // 验证字段名和表名使用双引号（如果需要的话）
        // 注意：简单的标识符可能不需要引号，这取决于具体实现
    }

    @Test
    @DisplayName("复杂SQL - 更复杂的多表JOIN查询")
    void testEvenMoreComplexQuery() {
        String sql = """
            SELECT
                u.id, u.name, u.email,
                p.title as profile_title,
                COUNT(DISTINCT o.id) as order_count,
                SUM(oi.quantity * oi.price) as total_amount,
                AVG(r.rating) as avg_rating,
                MAX(o.created_at) as last_order_date
            FROM users u
            INNER JOIN user_profiles p ON u.id = p.user_id
            LEFT JOIN orders o ON u.id = o.user_id AND o.status = 'completed'
            LEFT JOIN order_items oi ON o.id = oi.order_id
            LEFT JOIN reviews r ON u.id = r.user_id
            WHERE u.created_at >= '2024-01-01'
                AND u.status = 'active'
                AND (p.is_public = 1 OR u.is_premium = 1)
            GROUP BY u.id, u.name, u.email, p.title
            HAVING COUNT(DISTINCT o.id) >= 2
                AND SUM(oi.quantity * oi.price) > 100.00
            ORDER BY total_amount DESC, avg_rating DESC, u.name ASC
            LIMIT 20 OFFSET 10;
            """;

        System.out.println("Testing even more complex SQL: " + sql);

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement, "复杂SQL语句应该能够成功解析");
        assertTrue(statement instanceof QueryStmt, "应该解析为QueryStmt类型");

        String damengSql = generator.generate(statement);
        assertNotNull(damengSql, "应该能够生成达梦SQL");
        System.out.println("Generated Dameng SQL: " + damengSql);

        // 验证所有关键字和结构
        assertTrue(damengSql.contains("SELECT"), "应该包含SELECT");
        assertTrue(damengSql.contains("INNER JOIN"), "应该包含INNER JOIN");
        assertTrue(damengSql.contains("LEFT JOIN"), "应该包含LEFT JOIN");
        assertTrue(damengSql.contains("COUNT(DISTINCT"), "应该包含COUNT(DISTINCT)");
        assertTrue(damengSql.contains("SUM("), "应该包含SUM函数");
        assertTrue(damengSql.contains("AVG("), "应该包含AVG函数");
        assertTrue(damengSql.contains("MAX("), "应该包含MAX函数");
        assertTrue(damengSql.contains("WHERE"), "应该包含WHERE子句");
        assertTrue(damengSql.contains("GROUP BY"), "应该包含GROUP BY");
        assertTrue(damengSql.contains("HAVING"), "应该包含HAVING");
        assertTrue(damengSql.contains("ORDER BY"), "应该包含ORDER BY");
        assertTrue(damengSql.contains("LIMIT"), "应该包含LIMIT");
        assertTrue(damengSql.contains("OFFSET"), "应该包含OFFSET");
        assertTrue(damengSql.endsWith(";"), "应该以分号结尾");
    }

    @Test
    @DisplayName("复杂SQL - 带反引号的复杂查询")
    void testComplexQueryWithBackticks() {
        String sql = """
            SELECT
                `u`.`id`, `u`.`name`, `u`.`email`,
                IFNULL(`p`.`title`, 'No Title') as `profile_title`,
                CONCAT(`u`.`first_name`, ' ', `u`.`last_name`) as `full_name`,
                DATE_FORMAT(`u`.`created_at`, '%Y-%m-%d') as `created_date`
            FROM `users` `u`
            LEFT JOIN `user_profiles` `p` ON `u`.`id` = `p`.`user_id`
            WHERE `u`.`created_at` >= '2024-01-01'
            ORDER BY `u`.`name` ASC;
            """;

        System.out.println("Testing complex SQL with backticks: " + sql);

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement, "带反引号的复杂SQL应该能够成功解析");
        assertTrue(statement instanceof QueryStmt, "应该解析为QueryStmt类型");

        String damengSql = generator.generate(statement);
        assertNotNull(damengSql, "应该能够生成达梦SQL");
        System.out.println("Generated Dameng SQL: " + damengSql);

        // 验证MySQL函数转换
        assertTrue(damengSql.contains("NVL("), "IFNULL应该转换为NVL");
        assertTrue(damengSql.contains("CONCAT("), "CONCAT函数应该保持不变（达梦完全支持）");
        assertTrue(damengSql.contains("TO_CHAR("), "DATE_FORMAT应该转换为TO_CHAR");

        // 验证反引号转换
        assertFalse(damengSql.contains("`"), "不应该包含反引号");

        // 验证基本结构
        assertTrue(damengSql.contains("SELECT"), "应该包含SELECT");
        assertTrue(damengSql.contains("LEFT JOIN"), "应该包含LEFT JOIN");
        assertTrue(damengSql.contains("WHERE"), "应该包含WHERE");
        assertTrue(damengSql.contains("ORDER BY"), "应该包含ORDER BY");
        assertTrue(damengSql.endsWith(";"), "应该以分号结尾");
    }
}
