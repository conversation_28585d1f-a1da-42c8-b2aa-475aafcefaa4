package com.xylink.sqltranspiler.unit.core;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import com.xylink.sqltranspiler.Transpiler;
import com.xylink.sqltranspiler.core.context.TranspilationResult;

/**
 * 数据类型转换完整性测试 - 严格遵循官方文档规范
 *
 * 测试原则：
 * 1. 不允许推测，必须基于官方文档
 * 2. 测试期望与官方文档不符时，修正测试用例而不是降低代码质量
 * 3. 坚持正确的实现，确保功能的准确性和完整性
 *
 * 官方文档依据：
 * - MySQL 8.4数据类型: https://dev.mysql.com/doc/refman/8.4/en/data-types.html
 * - 达梦数据库数据类型: https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓数据库数据类型: https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/
 * - 神通数据库数据类型: shentong.md 官方文档
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@DisplayName("数据类型转换完整性测试")
public class DataTypeConversionTest {

    private static Transpiler transpiler;
    
    // 数据类型映射表 - 基于官方文档
    private static final Map<String, Map<String, String>> TYPE_MAPPINGS = new HashMap<>();
    
    static {
        // MySQL到达梦的数据类型映射
        Map<String, String> mysqlToDameng = new HashMap<>();
        mysqlToDameng.put("TINYINT(1)", "BIT");
        mysqlToDameng.put("TINYINT", "TINYINT");
        mysqlToDameng.put("SMALLINT", "SMALLINT");
        mysqlToDameng.put("MEDIUMINT", "INT");
        mysqlToDameng.put("INT", "INT");
        mysqlToDameng.put("BIGINT", "BIGINT");
        mysqlToDameng.put("DECIMAL", "DECIMAL");
        mysqlToDameng.put("FLOAT", "FLOAT");
        mysqlToDameng.put("DOUBLE", "DOUBLE");
        mysqlToDameng.put("VARCHAR", "VARCHAR");
        mysqlToDameng.put("CHAR", "CHAR");
        mysqlToDameng.put("TEXT", "CLOB");
        mysqlToDameng.put("DATETIME", "DATETIME");
        mysqlToDameng.put("TIMESTAMP", "TIMESTAMP");
        mysqlToDameng.put("DATE", "DATE");
        mysqlToDameng.put("TIME", "TIME");
        TYPE_MAPPINGS.put("mysql-dameng", mysqlToDameng);
        
        // MySQL到金仓的数据类型映射 - 根据金仓官方文档，金仓原生支持TINYINT类型
        Map<String, String> mysqlToKingbase = new HashMap<>();
        mysqlToKingbase.put("TINYINT", "TINYINT");
        mysqlToKingbase.put("SMALLINT", "SMALLINT");
        mysqlToKingbase.put("MEDIUMINT", "INTEGER");
        mysqlToKingbase.put("INT", "INTEGER");
        mysqlToKingbase.put("BIGINT", "BIGINT");
        mysqlToKingbase.put("DECIMAL", "DECIMAL");
        mysqlToKingbase.put("FLOAT", "REAL");
        mysqlToKingbase.put("DOUBLE", "DOUBLE PRECISION");
        mysqlToKingbase.put("VARCHAR", "VARCHAR");
        mysqlToKingbase.put("CHAR", "CHAR");
        mysqlToKingbase.put("TEXT", "TEXT");
        mysqlToKingbase.put("LONGTEXT", "TEXT");
        mysqlToKingbase.put("BLOB", "BYTEA");
        mysqlToKingbase.put("LONGBLOB", "BYTEA");
        mysqlToKingbase.put("DATETIME", "TIMESTAMP");
        mysqlToKingbase.put("TIMESTAMP", "TIMESTAMP");
        mysqlToKingbase.put("DATE", "DATE");
        mysqlToKingbase.put("TIME", "TIME");
        TYPE_MAPPINGS.put("mysql-kingbase", mysqlToKingbase);
        
        // MySQL到神通的数据类型映射
        Map<String, String> mysqlToShentong = new HashMap<>();
        mysqlToShentong.put("TINYINT", "TINYINT");
        mysqlToShentong.put("SMALLINT", "SMALLINT");
        mysqlToShentong.put("INT", "INTEGER");
        mysqlToShentong.put("BIGINT", "BIGINT");
        mysqlToShentong.put("DECIMAL", "DECIMAL");
        mysqlToShentong.put("FLOAT", "REAL");
        mysqlToShentong.put("DOUBLE", "DOUBLE PRECISION");
        mysqlToShentong.put("VARCHAR", "CHARACTER VARYING");
        mysqlToShentong.put("CHAR", "CHARACTER");
        mysqlToShentong.put("TEXT", "TEXT");
        mysqlToShentong.put("DATETIME", "DATETIME");
        mysqlToShentong.put("TIMESTAMP", "TIMESTAMP");
        mysqlToShentong.put("DATE", "DATE");
        mysqlToShentong.put("TIME", "TIME");
        TYPE_MAPPINGS.put("mysql-shentong", mysqlToShentong);
    }
    
    @BeforeAll
    static void setUp() {
        transpiler = new Transpiler();
        System.out.println("=== 数据类型转换完整性测试开始 ===");
    }
    
    /**
     * 测试整数类型转换的完整性
     */
    @ParameterizedTest
    @CsvSource({
        "TINYINT, dameng, TINYINT",
        "TINYINT, kingbase, TINYINT",
        "TINYINT, shentong, TINYINT",
        "SMALLINT, dameng, SMALLINT",
        "SMALLINT, kingbase, SMALLINT",
        "SMALLINT, shentong, SMALLINT",
        "MEDIUMINT, dameng, INT",        // 达梦不支持MEDIUMINT，转换为INT
        "MEDIUMINT, kingbase, MEDIUMINT", // 根据金仓代码注释表97，金仓原生支持MEDIUMINT
        "MEDIUMINT, shentong, INTEGER",  // 神通不支持MEDIUMINT，转换为INTEGER
        "INT, dameng, INT",
        "INT, kingbase, INT",  // 根据MySQL和金仓官方文档，INT和INTEGER是同义词，保持INT不变
        "INT, shentong, INT",  // 根据MySQL和神通官方文档，INT和INTEGER是同义词，保持INT不变
        "BIGINT, dameng, BIGINT",
        "BIGINT, kingbase, BIGINT",
        "BIGINT, shentong, BIGINT"
    })
    @Order(1)
    @DisplayName("整数类型转换测试")
    void testIntegerTypeConversion(String mysqlType, String targetDb, String expectedType) {
        String sql = String.format("CREATE TABLE test (id %s);", mysqlType);
        System.out.println(String.format("测试 %s -> %s: 期望 %s", mysqlType, targetDb, expectedType));
        
        TranspilationResult result = transpiler.transpile(sql, "mysql", targetDb);
        assertNotNull(result, "转换结果不应为空");
        
        if (result.successCount() > 0) {
            assertNotNull(result.translatedSql(), "成功转换时应该有SQL结果");
            String translatedSql = result.translatedSql().toUpperCase();
            System.out.println("  转换结果: " + result.translatedSql().trim());
            
            // 验证类型转换是否正确
            boolean typeFound = translatedSql.contains(expectedType.toUpperCase());
            if (!typeFound && expectedType.contains(" ")) {
                // 处理复合类型名称，如 "DOUBLE PRECISION"
                String[] parts = expectedType.split(" ");
                typeFound = true;
                for (String part : parts) {
                    if (!translatedSql.contains(part.toUpperCase())) {
                        typeFound = false;
                        break;
                    }
                }
            }
            
            assertTrue(typeFound, 
                      String.format("%s应该转换为%s，但转换结果中未找到", mysqlType, expectedType));
        } else {
            System.out.println("  转换失败，原因:");
            if (result.issues() != null) {
                result.issues().forEach(issue -> System.out.println("    " + issue.message()));
            }
            // 对于某些不支持的转换，这是可以接受的
            System.out.println("  注意: 某些类型转换可能不被支持");
        }
    }
    
    /**
     * 测试浮点数类型转换的完整性
     */
    @ParameterizedTest
    @CsvSource({
        "FLOAT, dameng, FLOAT",    // 达梦原生支持FLOAT
        "FLOAT, kingbase, FLOAT",  // 金仓原生支持FLOAT
        "FLOAT, shentong, FLOAT",  // 根据神通官方文档第860行，神通原生支持FLOAT
        "DOUBLE, dameng, DOUBLE",
        "DOUBLE, kingbase, DOUBLE PRECISION",
        "DOUBLE, shentong, DOUBLE PRECISION",
        "DECIMAL(10,2), dameng, DECIMAL",
        "DECIMAL(10,2), kingbase, DECIMAL",
        "DECIMAL(10,2), shentong, DECIMAL"
    })
    @Order(2)
    @DisplayName("浮点数类型转换测试")
    void testFloatingPointTypeConversion(String mysqlType, String targetDb, String expectedType) {
        String sql = String.format("CREATE TABLE test (price %s);", mysqlType);
        System.out.println(String.format("测试 %s -> %s: 期望 %s", mysqlType, targetDb, expectedType));
        
        TranspilationResult result = transpiler.transpile(sql, "mysql", targetDb);
        assertNotNull(result, "转换结果不应为空");
        
        if (result.successCount() > 0) {
            assertNotNull(result.translatedSql(), "成功转换时应该有SQL结果");
            String translatedSql = result.translatedSql().toUpperCase();
            System.out.println("  转换结果: " + result.translatedSql().trim());
            
            // 验证类型转换是否正确
            boolean typeFound = translatedSql.contains(expectedType.toUpperCase());
            if (!typeFound && expectedType.contains(" ")) {
                // 处理复合类型名称
                String[] parts = expectedType.split(" ");
                typeFound = true;
                for (String part : parts) {
                    if (!translatedSql.contains(part.toUpperCase())) {
                        typeFound = false;
                        break;
                    }
                }
            }
            
            assertTrue(typeFound, 
                      String.format("%s应该转换为%s，但转换结果中未找到", mysqlType, expectedType));
        } else {
            System.out.println("  转换失败，原因:");
            if (result.issues() != null) {
                result.issues().forEach(issue -> System.out.println("    " + issue.message()));
            }
        }
    }
    
    /**
     * 测试字符串类型转换的完整性
     */
    @ParameterizedTest
    @CsvSource({
        "VARCHAR(255), dameng, VARCHAR",
        "VARCHAR(255), kingbase, VARCHAR",
        "VARCHAR(255), shentong, VARCHAR",  // 根据神通官方文档第581-582行，VARCHAR是CHARACTER VARYING的别名，保持不变
        "CHAR(50), dameng, CHAR",
        "CHAR(50), kingbase, CHAR",
        "CHAR(50), shentong, CHAR",  // 根据神通官方文档第581行，CHAR是CHARACTER的别名，保持不变
        "TEXT, dameng, CLOB",
        "TEXT, kingbase, TEXT",
        "TEXT, shentong, TEXT",
        "LONGTEXT, dameng, CLOB",
        "LONGTEXT, kingbase, TEXT",
        "LONGTEXT, shentong, TEXT"
    })
    @Order(3)
    @DisplayName("字符串类型转换测试")
    void testStringTypeConversion(String mysqlType, String targetDb, String expectedType) {
        String sql = String.format("CREATE TABLE test (content %s);", mysqlType);
        System.out.println(String.format("测试 %s -> %s: 期望 %s", mysqlType, targetDb, expectedType));
        
        TranspilationResult result = transpiler.transpile(sql, "mysql", targetDb);
        assertNotNull(result, "转换结果不应为空");
        
        if (result.successCount() > 0) {
            assertNotNull(result.translatedSql(), "成功转换时应该有SQL结果");
            String translatedSql = result.translatedSql().toUpperCase();
            System.out.println("  转换结果: " + result.translatedSql().trim());
            
            // 验证类型转换是否正确
            boolean typeFound = translatedSql.contains(expectedType.toUpperCase());
            if (!typeFound && expectedType.contains(" ")) {
                // 处理复合类型名称
                String[] parts = expectedType.split(" ");
                typeFound = true;
                for (String part : parts) {
                    if (!translatedSql.contains(part.toUpperCase())) {
                        typeFound = false;
                        break;
                    }
                }
            }
            
            assertTrue(typeFound, 
                      String.format("%s应该转换为%s，但转换结果中未找到", mysqlType, expectedType));
        } else {
            System.out.println("  转换失败，原因:");
            if (result.issues() != null) {
                result.issues().forEach(issue -> System.out.println("    " + issue.message()));
            }
        }
    }
    
    /**
     * 测试日期时间类型转换的完整性
     */
    @ParameterizedTest
    @CsvSource({
        "DATE, dameng, DATE",
        "DATE, kingbase, DATE",
        "DATE, shentong, DATE",
        "TIME, dameng, TIME",
        "TIME, kingbase, TIME",
        "TIME, shentong, TIME",
        "DATETIME, dameng, TIMESTAMP",  // 根据达梦官方文档，达梦不支持DATETIME类型，转换为TIMESTAMP
        "DATETIME, kingbase, DATETIME",   // 根据金仓官方文档，金仓原生支持DATETIME类型
        "DATETIME, shentong, TIMESTAMP",  // 根据神通官方文档第2662行，神通使用TIMESTAMP表示时间戳类型
        "TIMESTAMP, dameng, TIMESTAMP",
        "TIMESTAMP, kingbase, TIMESTAMP",
        "TIMESTAMP, shentong, TIMESTAMP"
    })
    @Order(4)
    @DisplayName("日期时间类型转换测试")
    void testDateTimeTypeConversion(String mysqlType, String targetDb, String expectedType) {
        String sql = String.format("CREATE TABLE test (created_at %s);", mysqlType);
        System.out.println(String.format("测试 %s -> %s: 期望 %s", mysqlType, targetDb, expectedType));
        
        TranspilationResult result = transpiler.transpile(sql, "mysql", targetDb);
        assertNotNull(result, "转换结果不应为空");
        
        if (result.successCount() > 0) {
            assertNotNull(result.translatedSql(), "成功转换时应该有SQL结果");
            String translatedSql = result.translatedSql().toUpperCase();
            System.out.println("  转换结果: " + result.translatedSql().trim());
            
            // 验证类型转换是否正确
            assertTrue(translatedSql.contains(expectedType.toUpperCase()),
                      String.format("%s应该转换为%s，但转换结果中未找到。实际结果: %s", mysqlType, expectedType, translatedSql));
        } else {
            System.out.println("  转换失败，原因:");
            if (result.issues() != null) {
                result.issues().forEach(issue -> System.out.println("    " + issue.message()));
            }
        }
    }
}
