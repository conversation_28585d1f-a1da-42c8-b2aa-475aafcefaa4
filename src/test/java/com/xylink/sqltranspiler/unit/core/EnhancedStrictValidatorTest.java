package com.xylink.sqltranspiler.unit.core;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.validation.StrictSqlValidator;
import com.xylink.sqltranspiler.core.validation.StrictValidationResult;

/**
 * 增强的严格SQL验证器测试
 * 测试基于官方文档的增强验证功能：
 * - 保留字验证
 * - 数据类型长度限制验证
 * - 跨数据库兼容性验证
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
class EnhancedStrictValidatorTest {
    
    private StrictSqlValidator validator;
    
    @BeforeEach
    void setUp() {
        validator = new StrictSqlValidator();
    }
    
    @Test
    @DisplayName("MySQL保留字验证")
    void testMysqlReservedWords() {
        String sql = """
            CREATE TABLE test_reserved (
                `order` int,
                `group` varchar(50),
                `select` text,
                normal_column varchar(100)
            );
            """;
        
        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");
        
        // 应该检测到保留字警告
        assertTrue(result.hasWarnings(), "应该检测到保留字警告");
        
        // 检查是否有保留字相关的警告
        boolean hasReservedWordWarning = result.getWarnings().stream()
                .anyMatch(warning -> warning.getMessage().contains("保留字"));
        
        if (hasReservedWordWarning) {
            assertTrue(result.hasSuggestions(), "应该提供保留字处理建议");
        }
    }
    
    @Test
    @DisplayName("VARCHAR长度限制验证")
    void testVarcharLengthValidation() {
        String sql = """
            CREATE TABLE test_varchar_length (
                id int,
                short_text varchar(100),
                long_text varchar(50000)
            );
            """;
        
        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");
        
        // 检查是否有长度限制相关的问题
        if (result.hasErrors()) {
            boolean hasLengthError = result.getErrors().stream()
                    .anyMatch(error -> error.getMessage().contains("长度") && 
                             error.getMessage().contains("超过"));
            
            if (hasLengthError) {
                assertTrue(result.hasSuggestions(), "应该提供长度调整建议");
            }
        }
    }
    
    @Test
    @DisplayName("DECIMAL精度验证")
    void testDecimalPrecisionValidation() {
        String sql = """
            CREATE TABLE test_decimal_precision (
                id int,
                normal_decimal decimal(10,2),
                high_precision decimal(50,10)
            );
            """;
        
        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");
        
        // 检查是否有精度限制相关的问题
        if (result.hasErrors()) {
            boolean hasPrecisionError = result.getErrors().stream()
                    .anyMatch(error -> error.getMessage().contains("精度") && 
                             error.getMessage().contains("超过"));
            
            if (hasPrecisionError) {
                assertTrue(result.hasSuggestions(), "应该提供精度调整建议");
            }
        }
    }
    
    @Test
    @DisplayName("跨数据库类型转换建议")
    void testCrossDatabaseTypeConversion() {
        String sql = """
            CREATE TABLE test_cross_db (
                id int auto_increment primary key,
                name varchar(100),
                data json,
                created_at timestamp default current_timestamp
            );
            """;
        
        // 测试MySQL到达梦的转换
        StrictValidationResult mysqlToDameng = validator.validate(sql, "mysql", "dameng");
        assertTrue(mysqlToDameng.hasSuggestions(), "MySQL到达梦应该有转换建议");
        
        // 测试MySQL到金仓的转换
        StrictValidationResult mysqlToKingbase = validator.validate(sql, "mysql", "kingbase");
        assertTrue(mysqlToKingbase.hasSuggestions(), "MySQL到金仓应该有转换建议");
        
        // 测试MySQL到神通的转换
        StrictValidationResult mysqlToShentong = validator.validate(sql, "mysql", "shentong");
        assertTrue(mysqlToShentong.hasSuggestions(), "MySQL到神通应该有转换建议");
    }
    
    @Test
    @DisplayName("复杂SQL综合验证")
    void testComplexSqlValidation() {
        String sql = """
            CREATE TABLE complex_test (
                `order` bigint(20) auto_increment primary key,
                `user` varchar(10000) default test_value,
                `group` decimal(50,20) not null,
                update_time datetime default now() on update now(),
                invalid_auto varchar(50) auto_increment,
                `select` text
            );
            """;
        
        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");
        
        // 应该检测到多种问题
        assertTrue(result.hasWarnings() || result.hasErrors(), "复杂SQL应该检测到问题");
        
        // 应该有详细的修复建议
        assertTrue(result.hasSuggestions(), "应该提供详细的修复建议");
        
        // 验证报告应该包含所有问题类型
        String detailedReport = result.generateDetailedReport();
        assertFalse(detailedReport.isEmpty(), "详细报告不应为空");
        
        // 控制台输出应该格式化良好
        String consoleOutput = result.getConsoleOutput();
        assertFalse(consoleOutput.isEmpty(), "控制台输出不应为空");
    }
    
    @Test
    @DisplayName("官方文档规范遵循验证")
    void testOfficialDocumentationCompliance() {
        // 测试严格按照MySQL 8.4官方文档的验证
        String mysqlSql = """
            CREATE TABLE mysql_compliance_test (
                id int auto_increment primary key,
                count_field bigint default '0',
                name varchar(100) default test_value,
                created_at timestamp default current_timestamp,
                updated_at timestamp on update now()
            );
            """;
        
        StrictValidationResult result = validator.validate(mysqlSql, "mysql", "dameng");
        
        // 应该检测到MySQL官方文档规范问题
        assertTrue(result.hasWarnings() || result.hasErrors(), "应该检测到MySQL规范问题");
        
        // 错误和警告消息应该引用官方文档
        boolean hasOfficialDocReference = 
            result.getErrors().stream().anyMatch(e -> e.getMessage().contains("MySQL 8.4官方文档")) ||
            result.getWarnings().stream().anyMatch(w -> w.getMessage().contains("MySQL 8.4官方文档"));
        
        if (result.hasErrors() || result.hasWarnings()) {
            assertTrue(hasOfficialDocReference, "错误或警告消息应该引用官方文档");
        }
        
        // 修复建议应该基于官方文档
        boolean hasSuggestionWithDocReference = result.getSuggestions().stream()
                .anyMatch(s -> s.getMessage().contains("官方文档") || 
                         s.getMessage().contains("官方规范"));
        
        if (result.hasSuggestions()) {
            assertTrue(hasSuggestionWithDocReference, "修复建议应该基于官方文档");
        }
    }
    
    @Test
    @DisplayName("多数据库官方文档规范验证")
    void testMultiDatabaseOfficialCompliance() {
        String sql = """
            CREATE TABLE multi_db_test (
                id int auto_increment primary key,
                name varchar(100)
            );
            """;

        // 验证不同目标数据库的官方文档规范
        String[] targetDatabases = {"dameng", "kingbase", "shentong"};

        for (String targetDb : targetDatabases) {
            StrictValidationResult result = validator.validate(sql, "mysql", targetDb);

            // 验证器应该能够处理不同的目标数据库
            assertNotNull(result, String.format("MySQL到%s的验证结果不应为null", targetDb));

            // 如果有AUTO_INCREMENT，应该有相应的转换建议
            if (result.hasSuggestions()) {
                boolean hasAutoIncrementSuggestion = result.getSuggestions().stream()
                        .anyMatch(s -> s.getMessage().contains("AUTO_INCREMENT") ||
                                 s.getMessage().contains("IDENTITY") ||
                                 s.getMessage().contains("SERIAL"));

                assertTrue(hasAutoIncrementSuggestion,
                    String.format("MySQL到%s应该有AUTO_INCREMENT相关的转换建议", targetDb));
            }
        }
    }
    
    @Test
    @DisplayName("验证统计信息和性能")
    void testValidationStatisticsAndPerformance() {
        String sql = """
            CREATE TABLE performance_test (
                id int auto_increment primary key,
                name varchar(100),
                data text,
                created_at timestamp default current_timestamp
            );
            """;
        
        long startTime = System.currentTimeMillis();
        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");
        long endTime = System.currentTimeMillis();
        
        // 验证应该在合理时间内完成
        long validationTime = endTime - startTime;
        assertTrue(validationTime < 1000, "验证应该在1秒内完成");
        
        // 验证时间应该被记录
        assertTrue(result.getValidationTime() != null, "应该记录验证时间");
        
        // 可以添加统计信息
        result.addStatistic("验证耗时(ms)", validationTime);
        result.addStatistic("验证表数", 1);
        result.addStatistic("验证列数", 4);
        
        // 统计信息应该被正确记录
        assertTrue(result.getStatistics().containsKey("验证耗时(ms)"), "应该包含验证耗时统计");
        assertTrue(result.getStatistics().containsKey("验证表数"), "应该包含验证表数统计");
        assertTrue(result.getStatistics().containsKey("验证列数"), "应该包含验证列数统计");
    }
}
