package com.xylink.sqltranspiler.unit.core.ast;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import com.xylink.sqltranspiler.core.ast.dml.DeleteTable;
import com.xylink.sqltranspiler.core.ast.dml.InsertTable;
import com.xylink.sqltranspiler.core.ast.dml.QueryStmt;
import com.xylink.sqltranspiler.core.ast.dml.UpdateTable;
import com.xylink.sqltranspiler.core.ast.table.ColumnRel;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;

/**
 * AST节点验证测试
 * 基于官方文档验证AST节点的正确性和完整性
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/index.html
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
@DisplayName("AST节点验证测试")
public class ASTNodeValidationTest {
    
    private static final Logger logger = LoggerFactory.getLogger(ASTNodeValidationTest.class);
    
    @BeforeEach
    void setUp() {
        logger.info("开始执行AST节点验证测试");
    }
    
    /**
     * 测试CreateTable AST节点的完整性
     * 基于MySQL官方文档CREATE TABLE语法规范
     */
    @Test
    @DisplayName("CreateTable AST节点完整性验证")
    void testCreateTableASTNodeIntegrity() throws Exception {
        logger.info("开始执行CreateTable AST节点完整性验证");
        
        String createTableSql = """
            CREATE TABLE users (
                id INTEGER AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                email VARCHAR(255) UNIQUE,
                age INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            );
            """;
        
        Statement statement = MySqlHelper.parseStatement(createTableSql);
        
        // 验证基本AST节点类型
        assertNotNull(statement, "AST节点不应为null");
        assertTrue(statement instanceof CreateTable, "应该解析为CreateTable类型");
        
        CreateTable createTable = (CreateTable) statement;
        
        // 验证表标识符（基于MySQL官方文档标识符规范）
        assertNotNull(createTable.getTableId(), "表标识符不应为null");
        assertEquals("users", createTable.getTableId().getTableName(), "表名应该正确解析");
        
        // 验证列定义（基于MySQL官方文档列定义规范）
        assertNotNull(createTable.getColumnRels(), "列定义不应为null");
        assertFalse(createTable.getColumnRels().isEmpty(), "应该包含列定义");
        assertEquals(6, createTable.getColumnRels().size(), "应该解析出6个列");
        
        // 验证每个列的AST节点完整性
        for (ColumnRel columnRel : createTable.getColumnRels()) {
            assertNotNull(columnRel, "列定义不应为null");
            assertNotNull(columnRel.getColumnName(), "列名不应为null");
            assertNotNull(columnRel.getTypeName(), "数据类型不应为null");

            logger.debug("列: {} - 类型: {}",
                columnRel.getColumnName(),
                columnRel.getTypeName());
        }
        
        logger.info("CreateTable AST节点完整性验证通过");
    }
    
    /**
     * 测试QueryStmt AST节点的完整性
     * 基于MySQL官方文档SELECT语法规范
     */
    @Test
    @DisplayName("QueryStmt AST节点完整性验证")
    void testQueryStmtASTNodeIntegrity() throws Exception {
        logger.info("开始执行QueryStmt AST节点完整性验证");
        
        String selectSql = """
            SELECT u.id, u.name, u.email, COUNT(o.id) as order_count
            FROM users u
            LEFT JOIN orders o ON u.id = o.user_id
            WHERE u.age > 18 AND u.created_at > '2023-01-01'
            GROUP BY u.id, u.name, u.email
            HAVING COUNT(o.id) > 0
            ORDER BY u.created_at DESC
            LIMIT 10 OFFSET 20;
            """;
        
        Statement statement = MySqlHelper.parseStatement(selectSql);
        
        // 验证基本AST节点类型
        assertNotNull(statement, "AST节点不应为null");
        assertTrue(statement instanceof QueryStmt, "应该解析为QueryStmt类型");
        
        QueryStmt queryStmt = (QueryStmt) statement;
        
        // 验证输入表（基于MySQL官方文档FROM子句规范）
        assertNotNull(queryStmt.getInputTables(), "输入表不应为null");
        assertFalse(queryStmt.getInputTables().isEmpty(), "应该包含输入表");
        
        // 验证原始SQL保持
        assertNotNull(queryStmt.getSql(), "原始SQL不应为null");
        assertTrue(queryStmt.getSql().contains("SELECT"), "原始SQL应包含SELECT");
        assertTrue(queryStmt.getSql().contains("FROM"), "原始SQL应包含FROM");
        assertTrue(queryStmt.getSql().contains("WHERE"), "原始SQL应包含WHERE");
        assertTrue(queryStmt.getSql().contains("GROUP BY"), "原始SQL应包含GROUP BY");
        assertTrue(queryStmt.getSql().contains("HAVING"), "原始SQL应包含HAVING");
        assertTrue(queryStmt.getSql().contains("ORDER BY"), "原始SQL应包含ORDER BY");
        assertTrue(queryStmt.getSql().contains("LIMIT"), "原始SQL应包含LIMIT");
        
        logger.info("QueryStmt AST节点完整性验证通过");
    }
    
    /**
     * 测试InsertTable AST节点的完整性
     * 基于MySQL官方文档INSERT语法规范
     */
    @Test
    @DisplayName("InsertTable AST节点完整性验证")
    void testInsertTableASTNodeIntegrity() throws Exception {
        logger.info("开始执行InsertTable AST节点完整性验证");
        
        String insertSql = """
            INSERT INTO users (name, email, age, created_at)
            VALUES 
                ('John Doe', '<EMAIL>', 25, NOW()),
                ('Jane Smith', '<EMAIL>', 30, NOW());
            """;
        
        Statement statement = MySqlHelper.parseStatement(insertSql);
        
        // 验证基本AST节点类型
        assertNotNull(statement, "AST节点不应为null");
        assertTrue(statement instanceof InsertTable, "应该解析为InsertTable类型");
        
        InsertTable insertTable = (InsertTable) statement;
        
        // 验证表标识符（基于MySQL官方文档INSERT语法）
        assertNotNull(insertTable.getTableId(), "表标识符不应为null");
        assertEquals("users", insertTable.getTableId().getTableName(), "表名应该正确解析");
        
        // 验证列列表（基于MySQL官方文档INSERT列规范）
        assertNotNull(insertTable.getColumns(), "列列表不应为null");
        assertFalse(insertTable.getColumns().isEmpty(), "应该包含列列表");
        assertEquals(4, insertTable.getColumns().size(), "应该解析出4个列");

        // 验证值列表（基于MySQL官方文档VALUES子句规范）
        assertNotNull(insertTable.getValuesClause(), "VALUES子句不应为null");
        assertNotNull(insertTable.getValuesClause().getRows(), "值列表不应为null");
        assertFalse(insertTable.getValuesClause().getRows().isEmpty(), "应该包含值列表");
        assertEquals(2, insertTable.getValuesClause().getRows().size(), "应该解析出2行数据");
        
        logger.info("InsertTable AST节点完整性验证通过");
    }
    
    /**
     * 测试UpdateTable AST节点的完整性
     * 基于MySQL官方文档UPDATE语法规范
     */
    @Test
    @DisplayName("UpdateTable AST节点完整性验证")
    void testUpdateTableASTNodeIntegrity() throws Exception {
        logger.info("开始执行UpdateTable AST节点完整性验证");
        
        String updateSql = """
            UPDATE users 
            SET name = 'Updated Name', 
                email = '<EMAIL>',
                updated_at = NOW()
            WHERE id = 1 AND age > 18;
            """;
        
        Statement statement = MySqlHelper.parseStatement(updateSql);
        
        // 验证基本AST节点类型
        assertNotNull(statement, "AST节点不应为null");
        assertTrue(statement instanceof UpdateTable, "应该解析为UpdateTable类型");
        
        UpdateTable updateTable = (UpdateTable) statement;
        
        // 验证表标识符（基于MySQL官方文档UPDATE语法）
        assertNotNull(updateTable.getTableId(), "表标识符不应为null");
        assertEquals("users", updateTable.getTableId().getTableName(), "表名应该正确解析");
        
        // 验证SET子句（基于MySQL官方文档SET子句规范）
        assertNotNull(updateTable.getSetClauses(), "SET列表不应为null");
        assertFalse(updateTable.getSetClauses().isEmpty(), "应该包含SET列表");
        assertEquals(3, updateTable.getSetClauses().size(), "应该解析出3个SET项");
        
        logger.info("UpdateTable AST节点完整性验证通过");
    }
    
    /**
     * 测试DeleteTable AST节点的完整性
     * 基于MySQL官方文档DELETE语法规范
     */
    @Test
    @DisplayName("DeleteTable AST节点完整性验证")
    void testDeleteTableASTNodeIntegrity() throws Exception {
        logger.info("开始执行DeleteTable AST节点完整性验证");
        
        String deleteSql = """
            DELETE FROM users 
            WHERE age < 18 OR created_at < '2020-01-01';
            """;
        
        Statement statement = MySqlHelper.parseStatement(deleteSql);
        
        // 验证基本AST节点类型
        assertNotNull(statement, "AST节点不应为null");
        assertTrue(statement instanceof DeleteTable, "应该解析为DeleteTable类型");
        
        DeleteTable deleteTable = (DeleteTable) statement;
        
        // 验证表标识符（基于MySQL官方文档DELETE语法）
        assertNotNull(deleteTable.getTableId(), "表标识符不应为null");
        assertEquals("users", deleteTable.getTableId().getTableName(), "表名应该正确解析");
        
        logger.info("DeleteTable AST节点完整性验证通过");
    }
    
    /**
     * 测试AST节点的边界情况处理
     * 验证解析器对异常情况的处理能力
     */
    @Test
    @DisplayName("AST节点边界情况处理验证")
    void testASTNodeBoundaryConditions() throws Exception {
        logger.info("开始执行AST节点边界情况处理验证");
        
        // 测试空表名处理
        String[] boundaryCases = {
            "CREATE TABLE (id INT);", // 缺少表名
            "SELECT * FROM;", // 缺少表名
            "INSERT INTO VALUES (1);", // 缺少表名
            "UPDATE SET id = 1;", // 缺少表名
            "DELETE FROM WHERE id = 1;" // 缺少表名
        };
        
        for (String sql : boundaryCases) {
            logger.debug("测试边界情况SQL: {}", sql);
            
            try {
                Statement statement = MySqlHelper.parseStatement(sql);
                
                // 如果解析成功，验证是否为fallback语句
                if (statement != null) {
                    logger.debug("边界情况解析结果: {}", statement.getClass().getSimpleName());
                    
                    // 验证fallback语句的基本属性
                    assertNotNull(statement.getStatementType(), "语句类型不应为null");
                    assertNotNull(statement.getSql(), "原始SQL不应为null");
                }
                
            } catch (Exception e) {
                // 预期的异常处理
                logger.debug("边界情况异常处理: {}", e.getMessage());
                assertNotNull(e.getMessage(), "异常消息不应为null");
            }
        }
        
        logger.info("AST节点边界情况处理验证通过");
    }
}
