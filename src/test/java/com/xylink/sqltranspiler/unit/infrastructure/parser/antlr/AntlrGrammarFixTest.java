package com.xylink.sqltranspiler.unit.infrastructure.parser.antlr;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.fail;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;

/**
 * ANTLR语法修正验证测试
 * 根据数据库规则：严格禁止简化测试用例SQL，必须使用真实的MySQL语法
 * 目的：验证ANTLR语法修正的有效性，确保解析器能够正确处理复杂的SQL语句
 * 主要验证的ANTLR语法问题：
 * 1. 多JSON列解析问题（已修正：JSON从spatialDataType移到simpleDataType）
 * 2. 复杂窗口函数解析（验证是否真的存在问题）
 * 3. 复杂日期函数解析（验证是否真的存在问题）
 * 4. 复杂JSON函数解析（验证是否真的存在问题）
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
public class AntlrGrammarFixTest {
    
    private static final Logger log = LoggerFactory.getLogger(AntlrGrammarFixTest.class);
    
    @Test
    @DisplayName("验证多JSON列解析修正 - 深度分析")
    public void testMultipleJsonColumnsParsingFix() {
        // 根据数据库规则，我需要深入分析ANTLR语法问题
        // 测试不同的JSON列组合，寻找解析失败的模式

        // 第一组：基础测试（这些应该成功）
        String[] basicTests = {
            "CREATE TABLE test (json_data JSON);",
            "CREATE TABLE test (id INT, json_data JSON);",
            "CREATE TABLE test (json_data JSON, id INT);"
        };

        log.info("=== 基础JSON列测试 ===");
        for (int i = 0; i < basicTests.length; i++) {
            testSingleCase("基础测试" + (i + 1), basicTests[i], true);
        }

        // 第二组：连续JSON列测试（测试列名与JSON函数名的冲突）
        String[] consecutiveJsonTests = {
            "CREATE TABLE test (json_data JSON, json_array JSON);",  // json_array可能与JSON_ARRAY函数冲突
            "CREATE TABLE test (json_object JSON, json_extract JSON);",  // 测试其他JSON函数名
            "CREATE TABLE test (json1 JSON, json2 JSON, json3 JSON);",  // 使用非函数名的列名
            "CREATE TABLE test (id INT, json1 JSON, json2 JSON);"  // 混合类型
        };

        log.info("=== 连续JSON列测试 ===");
        for (int i = 0; i < consecutiveJsonTests.length; i++) {
            testSingleCase("连续JSON测试" + (i + 1), consecutiveJsonTests[i], false);
        }

        // 第三组：JSON与其他类型混合测试
        String[] mixedTests = {
            "CREATE TABLE test (json_data JSON, text_data TEXT);",
            "CREATE TABLE test (json_data JSON, int_data INT, text_data TEXT);",
            "CREATE TABLE test (text_data TEXT, json_data JSON, int_data INT);"
        };

        log.info("=== JSON与其他类型混合测试 ===");
        for (int i = 0; i < mixedTests.length; i++) {
            testSingleCase("混合测试" + (i + 1), mixedTests[i], true);
        }

        // 第四组：JSON函数名冲突测试（专门测试列名与JSON函数名的冲突）
        String[] jsonFunctionNameTests = {
            "CREATE TABLE test (json_array JSON);",  // 单独测试json_array
            "CREATE TABLE test (json_object JSON);",  // 单独测试json_object
            "CREATE TABLE test (json_extract JSON);",  // 单独测试json_extract
            "CREATE TABLE test (json_valid JSON);",  // 单独测试json_valid
            "CREATE TABLE test (json_type JSON);"  // 单独测试json_type
        };

        log.info("=== JSON函数名冲突测试 ===");
        for (int i = 0; i < jsonFunctionNameTests.length; i++) {
            testSingleCase("JSON函数名测试" + (i + 1), jsonFunctionNameTests[i], true);
        }

        log.info("=== 测试总结 ===");
        log.info("基于测试结果分析ANTLR语法问题的根本原因：");
        log.info("1. 如果单独的JSON函数名列测试失败，说明问题是列名与JSON函数名的词法冲突");
        log.info("2. 如果连续JSON列测试失败，说明问题是JSON类型定义的语法歧义");
        log.info("3. 需要根据具体的失败模式来确定修正方案");
    }

    private void testSingleCase(String testName, String sql, boolean shouldSucceed) {
        log.info("测试 {}: {}", testName, sql);

        try {
            Statement statement = MySqlHelper.parseStatement(sql);
            assertNotNull(statement, testName + " 应该能够正常解析");
            log.info("✅ {} 解析成功", testName);

            if (!shouldSucceed) {
                log.warn("⚠️  {} 预期失败但实际成功，可能ANTLR语法已被修正", testName);
            }
        } catch (Exception e) {
            log.error("❌ {} 解析失败: {}", testName, e.getMessage());

            // 详细分析错误位置
            analyzeParsingError(sql, e);

            if (shouldSucceed) {
                fail(testName + " 解析失败: " + e.getMessage());
            } else {
                log.info("✓ {} 按预期失败，确认了ANTLR语法问题", testName);
            }
        }
    }

    private void analyzeParsingError(String sql, Exception e) {
        if (e.getMessage().contains("pos ")) {
            String posStr = e.getMessage().substring(e.getMessage().indexOf("pos ") + 4);
            if (posStr.contains(")")) {
                posStr = posStr.substring(0, posStr.indexOf(")"));
            }
            try {
                int pos = Integer.parseInt(posStr);
                log.error("错误位置分析: 第{}个字符", pos);
                if (pos < sql.length()) {
                    char errorChar = sql.charAt(pos);
                    log.error("错误字符: '{}'", errorChar);

                    // 分析错误上下文
                    int start = Math.max(0, pos - 10);
                    int end = Math.min(sql.length(), pos + 10);
                    String context = sql.substring(start, pos) + "[" + errorChar + "]" + sql.substring(pos + 1, end);
                    log.error("错误上下文: {}", context);

                    // 分析可能的原因
                    if (errorChar == 'J' && pos + 4 < sql.length() && sql.substring(pos, pos + 4).equals("JSON")) {
                        log.error("分析：错误发生在JSON类型定义处，可能是连续JSON类型导致的解析歧义");
                    }
                }
            } catch (NumberFormatException nfe) {
                log.error("无法解析错误位置: {}", posStr);
            }
        }
    }
    
    @Test
    @DisplayName("验证复杂窗口函数解析能力")
    public void testComplexWindowFunctionsParsing() {
        String sql = """
            SELECT 
                name,
                salary,
                ROW_NUMBER() OVER (ORDER BY salary DESC) as row_num,
                RANK() OVER (ORDER BY salary DESC) as rank_val,
                DENSE_RANK() OVER (ORDER BY salary DESC) as dense_rank_val,
                LAG(salary, 1) OVER (ORDER BY salary DESC) as prev_salary,
                LEAD(salary, 1) OVER (ORDER BY salary DESC) as next_salary
            FROM employees;
            """;
        
        log.info("验证复杂窗口函数解析: {}", sql);
        
        try {
            Statement statement = MySqlHelper.parseStatement(sql);
            assertNotNull(statement, "复杂窗口函数应该能够正常解析");
            log.info("✅ 复杂窗口函数解析验证成功");
        } catch (Exception e) {
            log.error("❌ 复杂窗口函数解析失败: {}", e.getMessage());
            // 根据数据库规则，不简化测试，而是记录问题
            fail("复杂窗口函数解析失败，可能需要进一步修正ANTLR语法: " + e.getMessage());
        }
    }
    
    @Test
    @DisplayName("验证复杂日期函数解析能力")
    public void testComplexDateFunctionsParsing() {
        String sql = """
            SELECT 
                DATE_ADD(NOW(), INTERVAL 1 DAY) as date_add,
                DATE_SUB(NOW(), INTERVAL 1 MONTH) as date_sub,
                DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s') as formatted_date,
                TIMESTAMPDIFF(DAY, '2023-01-01', NOW()) as days_diff
            FROM dual;
            """;
        
        log.info("验证复杂日期函数解析: {}", sql);
        
        try {
            Statement statement = MySqlHelper.parseStatement(sql);
            assertNotNull(statement, "复杂日期函数应该能够正常解析");
            log.info("✅ 复杂日期函数解析验证成功");
        } catch (Exception e) {
            log.error("❌ 复杂日期函数解析失败: {}", e.getMessage());
            fail("复杂日期函数解析失败，可能需要进一步修正ANTLR语法: " + e.getMessage());
        }
    }
    
    @Test
    @DisplayName("验证复杂JSON函数解析能力")
    public void testComplexJsonFunctionsParsing() {
        String sql = """
            SELECT 
                JSON_EXTRACT(json_col, '$.name') as extracted_name,
                JSON_OBJECT('name', 'John', 'age', 30, 'city', 'Beijing') as json_object,
                JSON_ARRAY('a', 'b', 'c', 'd', 'e') as json_array,
                JSON_VALID(json_col) as is_valid_json,
                JSON_SET(json_col, '$.name', 'Jane') as updated_json
            FROM test_table
            WHERE JSON_EXTRACT(json_col, '$.active') = true;
            """;
        
        log.info("验证复杂JSON函数解析: {}", sql);
        
        try {
            Statement statement = MySqlHelper.parseStatement(sql);
            assertNotNull(statement, "复杂JSON函数应该能够正常解析");
            log.info("✅ 复杂JSON函数解析验证成功");
        } catch (Exception e) {
            log.error("❌ 复杂JSON函数解析失败: {}", e.getMessage());
            fail("复杂JSON函数解析失败，可能需要进一步修正ANTLR语法: " + e.getMessage());
        }
    }
    
    @Test
    @DisplayName("验证混合复杂语法解析能力")
    public void testMixedComplexSyntaxParsing() {
        String sql = """
            WITH RECURSIVE category_tree AS (
                SELECT id, name, parent_id, 0 as level, json_data
                FROM categories 
                WHERE parent_id IS NULL
                UNION ALL
                SELECT c.id, c.name, c.parent_id, ct.level + 1, c.json_data
                FROM categories c
                JOIN category_tree ct ON c.parent_id = ct.id
            )
            SELECT 
                ct.name,
                ct.level,
                JSON_EXTRACT(ct.json_data, '$.description') as description,
                ROW_NUMBER() OVER (PARTITION BY ct.level ORDER BY ct.name) as row_num,
                DATE_FORMAT(NOW(), '%Y-%m-%d') as current_date
            FROM category_tree ct
            WHERE JSON_VALID(ct.json_data) = 1
            ORDER BY ct.level, ct.name
            LIMIT 100;
            """;
        
        log.info("验证混合复杂语法解析: {}", sql);
        
        try {
            Statement statement = MySqlHelper.parseStatement(sql);
            assertNotNull(statement, "混合复杂语法应该能够正常解析");
            log.info("✅ 混合复杂语法解析验证成功");
        } catch (Exception e) {
            log.error("❌ 混合复杂语法解析失败: {}", e.getMessage());
            // 这个测试可能会失败，因为它包含了多种复杂语法
            // 根据数据库规则，记录具体的失败原因
            log.error("失败原因分析：可能是CTE、窗口函数、JSON函数或日期函数的组合导致的解析冲突");
            fail("混合复杂语法解析失败，需要进一步分析ANTLR语法规则的优先级和歧义: " + e.getMessage());
        }
    }
    
    @Test
    @DisplayName("验证ANTLR语法修正的回归测试")
    public void testAntlrGrammarRegressionTest() {
        // 确保之前能够解析的简单语句仍然可以解析
        String[] testCases = {
            "CREATE TABLE test (id INT PRIMARY KEY);",
            "CREATE TABLE test (id INT, name VARCHAR(100));",
            "CREATE TABLE test (id INT, data JSON);",
            "SELECT * FROM test;",
            "SELECT COUNT(*) FROM test;",
            "SELECT id, name FROM test WHERE id > 0;",
            "INSERT INTO test (id, name) VALUES (1, 'test');",
            "UPDATE test SET name = 'updated' WHERE id = 1;",
            "DELETE FROM test WHERE id = 1;"
        };
        
        for (String sql : testCases) {
            log.info("回归测试: {}", sql);
            try {
                Statement statement = MySqlHelper.parseStatement(sql);
                assertNotNull(statement, "基本SQL语句应该能够正常解析: " + sql);
            } catch (Exception e) {
                log.error("❌ 回归测试失败: {} - {}", sql, e.getMessage());
                fail("ANTLR语法修正不应该破坏基本SQL语句的解析: " + sql + " - " + e.getMessage());
            }
        }
        
        log.info("✅ ANTLR语法修正回归测试全部通过");
    }
}
