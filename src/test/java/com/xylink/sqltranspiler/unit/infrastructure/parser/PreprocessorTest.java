package com.xylink.sqltranspiler.unit.infrastructure.parser;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.infrastructure.parser.PreprocessingResult;
import com.xylink.sqltranspiler.infrastructure.parser.Preprocessor;

/**
 * 预处理器测试
 * 测试SQL预处理功能，包括跨行列定义规范化等
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
class PreprocessorTest {

    @Test
    void testNormalizeMultiLineColumnDefinitions() {
        String sql = """
            CREATE TABLE test_table (
                id bigint auto_increment primary key,
                number_prefix bigint not null
                    unique comment 'subtype 生成前缀',
                name varchar(100) not null
            );
            """;

        PreprocessingResult result = Preprocessor.preprocess(sql);
        String processedSql = result.cleanedSql();

        // 跨行的列定义应该被合并为单行（注意预处理器会添加反引号）
        assertThat(processedSql).contains("`number_prefix` bigint not null unique comment 'subtype 生成前缀',");
        // 不应该包含跨行的格式
        assertThat(processedSql).doesNotContain("not null\n                    unique");

        // 其他列定义应该保持不变（预处理器可能会添加反引号）
        assertThat(processedSql).contains("id bigint auto_increment primary key");
        assertThat(processedSql).contains("name varchar(100) not null");
    }

    @Test
    void testNormalizeMultiLineColumnDefinitionsWithBackticks() {
        String sql = """
            CREATE TABLE `test_table` (
                `id` bigint auto_increment primary key,
                `number_prefix` bigint not null
                    unique comment 'test comment',
                `status` int default 1
                    comment 'status field'
            );
            """;

        PreprocessingResult result = Preprocessor.preprocess(sql);
        String processedSql = result.cleanedSql();

        // 跨行的列定义应该被合并为单行
        assertThat(processedSql).contains("`number_prefix` bigint not null unique comment 'test comment',");
        assertThat(processedSql).contains("`status` int default 1 comment 'status field'");

        // 不应该包含跨行的格式
        assertThat(processedSql).doesNotContain("not null\n                    unique");
        assertThat(processedSql).doesNotContain("default 1\n                    comment");
    }

    @Test
    void testNormalizeRealWorldExample() {
        // 来自实际失败案例的SQL
        String sql = """
            create table IF NOT EXISTS ainemo.subtype_number_type_mapping
            (
            `id`            bigint
            primary key auto_increment comment '表主键',
            `number_prefix` bigint                              not null
            unique comment 'subtype 生成前缀',
            `number_type`   integer                             not null comment '生成资源策略type',
            `create_time`   timestamp default CURRENT_TIMESTAMP not null,
            `update_time`   timestamp default CURRENT_TIMESTAMP not null
            );
            """;

        PreprocessingResult result = Preprocessor.preprocess(sql);
        String processedSql = result.cleanedSql();

        // 跨行的列定义应该被合并为单行，多余空格被清理
        assertThat(processedSql).contains("`number_prefix` bigint not null unique comment 'subtype 生成前缀',");

        // 不应该包含跨行的格式
        assertThat(processedSql).doesNotContain("not null\nunique comment");

        // 其他列定义应该保持正确，多余空格被清理
        assertThat(processedSql).contains("`id` bigint primary key auto_increment comment '表主键',");
        assertThat(processedSql).contains("`number_type` integer not null comment '生成资源策略type',");
    }

    @Test
    void testNormalizeDoesNotAffectNormalStatements() {
        String sql = """
            CREATE TABLE normal_table (
                id bigint auto_increment primary key,
                name varchar(100) not null,
                status int default 1
            );

            INSERT INTO normal_table (name, status) VALUES ('test', 1);
            """;

        PreprocessingResult result = Preprocessor.preprocess(sql);
        String processedSql = result.cleanedSql();

        // 正常的语句应该保持不变（注意预处理器会添加反引号）
        assertThat(processedSql).contains("id bigint auto_increment primary key,");
        assertThat(processedSql).contains("`name` varchar(100) not null,");
        assertThat(processedSql).contains("status int default 1");
        assertThat(processedSql).contains("INSERT INTO `normal_table`");
    }

    @Test
    void testNormalizeOnlyAffectsCreateTableStatements() {
        String sql = """
            SELECT column1,
                column2,
                column3
            FROM table1;

            CREATE TABLE test_table (
                id bigint not null
                    primary key,
                name varchar(100)
            );
            """;

        PreprocessingResult result = Preprocessor.preprocess(sql);
        String processedSql = result.cleanedSql();

        // SELECT语句的跨行应该保持不变
        assertThat(processedSql).contains("SELECT column1,\n");

        // 只有CREATE TABLE中的跨行应该被规范化
        assertThat(processedSql).contains("id bigint not null primary key,");
    }
}
