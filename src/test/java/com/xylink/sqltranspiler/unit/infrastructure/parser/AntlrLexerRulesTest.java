package com.xylink.sqltranspiler.unit.infrastructure.parser;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.ArrayList;
import java.util.List;

import org.antlr.v4.runtime.CharStreams;
import org.antlr.v4.runtime.Token;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.infrastructure.parser.antlr.UpperCaseCharStream;
import com.xylink.sqltranspiler.infrastructure.parser.generated.MySqlLexer;

/**
 * 测试ANTLR词法规则的修复
 * 这个测试类验证了我们修复的词法规则冲突问题：
 * - BQUOTA_STRING 不再被 STRING_LITERAL 匹配
 * - 反引号标识符被正确识别为 REVERSE_QUOTE_ID
 * - 字符串字面量和反引号标识符能正确区分
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
@DisplayName("ANTLR词法规则修复测试")
public class AntlrLexerRulesTest {

    @Test
    @DisplayName("测试反引号标识符被识别为REVERSE_QUOTE_ID")
    void testBacktickIdentifierTokenType() {
        String sql = "`test_table`";
        
        List<TokenInfo> tokens = tokenize(sql);
        
        assertEquals(1, tokens.size());
        TokenInfo token = tokens.get(0);
        assertEquals("`test_table`", token.text);
        assertEquals("REVERSE_QUOTE_ID", token.typeName);
    }

    @Test
    @DisplayName("测试字符串字面量不包含反引号")
    void testStringLiteralExcludesBackticks() {
        String sql = "'single_quote' \"double_quote\"";
        
        List<TokenInfo> tokens = tokenize(sql);
        
        // 应该有3个token: 'single_quote', 空格, "double_quote"
        assertTrue(tokens.size() >= 2);
        
        // 查找字符串字面量token
        List<TokenInfo> stringTokens = tokens.stream()
            .filter(t -> "STRING_LITERAL".equals(t.typeName))
            .toList();
        
        assertEquals(2, stringTokens.size());
        assertEquals("'single_quote'", stringTokens.get(0).text);
        assertEquals("\"double_quote\"", stringTokens.get(1).text);
    }

    @Test
    @DisplayName("测试混合使用反引号和字符串字面量")
    void testMixedBackticksAndStringLiterals() {
        String sql = "`table_name` 'string_value' \"another_string\"";
        
        List<TokenInfo> tokens = tokenize(sql);
        
        // 查找不同类型的token
        List<TokenInfo> backquoteTokens = tokens.stream()
            .filter(t -> "REVERSE_QUOTE_ID".equals(t.typeName))
            .toList();
        
        List<TokenInfo> stringTokens = tokens.stream()
            .filter(t -> "STRING_LITERAL".equals(t.typeName))
            .toList();
        
        assertEquals(1, backquoteTokens.size());
        assertEquals("`table_name`", backquoteTokens.get(0).text);
        
        assertEquals(2, stringTokens.size());
        assertEquals("'string_value'", stringTokens.get(0).text);
        assertEquals("\"another_string\"", stringTokens.get(1).text);
    }

    @Test
    @DisplayName("测试CREATE TABLE语句中的token类型")
    void testCreateTableTokenTypes() {
        String sql = "CREATE TABLE `users` (`name` VARCHAR(100))";
        
        List<TokenInfo> tokens = tokenize(sql);
        
        // 验证关键token的类型
        TokenInfo createToken = findToken(tokens, "CREATE");
        assertEquals("'CREATE'", createToken.typeName);
        
        TokenInfo tableToken = findToken(tokens, "TABLE");
        assertEquals("'TABLE'", tableToken.typeName);
        
        TokenInfo usersToken = findToken(tokens, "`users`");
        assertEquals("REVERSE_QUOTE_ID", usersToken.typeName);
        
        TokenInfo nameToken = findToken(tokens, "`name`");
        assertEquals("REVERSE_QUOTE_ID", nameToken.typeName);
        
        TokenInfo varcharToken = findToken(tokens, "VARCHAR");
        assertEquals("'VARCHAR'", varcharToken.typeName);
    }

    @Test
    @DisplayName("测试INSERT语句中的token类型")
    void testInsertStatementTokenTypes() {
        String sql = "INSERT INTO `users` VALUES ('John', '<EMAIL>')";
        
        List<TokenInfo> tokens = tokenize(sql);
        
        // 验证关键token的类型
        TokenInfo insertToken = findToken(tokens, "INSERT");
        assertEquals("'INSERT'", insertToken.typeName);
        
        TokenInfo intoToken = findToken(tokens, "INTO");
        assertEquals("'INTO'", intoToken.typeName);
        
        TokenInfo usersToken = findToken(tokens, "`users`");
        assertEquals("REVERSE_QUOTE_ID", usersToken.typeName);
        
        TokenInfo valuesToken = findToken(tokens, "VALUES");
        assertEquals("'VALUES'", valuesToken.typeName);
        
        // 验证字符串值
        List<TokenInfo> stringTokens = tokens.stream()
            .filter(t -> "STRING_LITERAL".equals(t.typeName))
            .toList();
        
        assertEquals(2, stringTokens.size());
        assertEquals("'John'", stringTokens.get(0).text);
        assertEquals("'<EMAIL>'", stringTokens.get(1).text);
    }

    @Test
    @DisplayName("测试MySQL关键字作为反引号标识符")
    void testKeywordsAsBacktickIdentifiers() {
        String sql = "`name` `status` `type` `class` `group` `user`";
        
        List<TokenInfo> tokens = tokenize(sql);
        
        List<TokenInfo> backquoteTokens = tokens.stream()
            .filter(t -> "REVERSE_QUOTE_ID".equals(t.typeName))
            .toList();
        
        assertEquals(6, backquoteTokens.size());
        assertEquals("`name`", backquoteTokens.get(0).text);
        assertEquals("`status`", backquoteTokens.get(1).text);
        assertEquals("`type`", backquoteTokens.get(2).text);
        assertEquals("`class`", backquoteTokens.get(3).text);
        assertEquals("`group`", backquoteTokens.get(4).text);
        assertEquals("`user`", backquoteTokens.get(5).text);
    }

    @Test
    @DisplayName("测试复杂SQL语句的token分析")
    void testComplexSqlTokenAnalysis() {
        String sql = "CREATE TABLE `order_details` (" +
                     "`order_id` INT, " +
                     "`name` VARCHAR(100) DEFAULT 'Unknown', " +
                     "`status` ENUM('active', 'inactive')" +
                     ")";
        
        List<TokenInfo> tokens = tokenize(sql);
        
        // 验证反引号标识符
        List<TokenInfo> backquoteTokens = tokens.stream()
            .filter(t -> "REVERSE_QUOTE_ID".equals(t.typeName))
            .toList();
        
        assertEquals(4, backquoteTokens.size());
        assertEquals("`order_details`", backquoteTokens.get(0).text);
        assertEquals("`order_id`", backquoteTokens.get(1).text);
        assertEquals("`name`", backquoteTokens.get(2).text);
        assertEquals("`status`", backquoteTokens.get(3).text);
        
        // 验证字符串字面量
        List<TokenInfo> stringTokens = tokens.stream()
            .filter(t -> "STRING_LITERAL".equals(t.typeName))
            .toList();
        
        assertEquals(3, stringTokens.size());
        assertEquals("'Unknown'", stringTokens.get(0).text);
        assertEquals("'active'", stringTokens.get(1).text);
        assertEquals("'inactive'", stringTokens.get(2).text);
    }

    // 辅助方法
    private List<TokenInfo> tokenize(String sql) {
        UpperCaseCharStream charStream = new UpperCaseCharStream(CharStreams.fromString(sql));
        MySqlLexer lexer = new MySqlLexer(charStream);
        
        List<TokenInfo> tokens = new ArrayList<>();
        Token token;
        while ((token = lexer.nextToken()).getType() != Token.EOF) {
            if (token.getChannel() == Token.DEFAULT_CHANNEL) { // 忽略空格和注释
                String typeName = lexer.getVocabulary().getDisplayName(token.getType());
                tokens.add(new TokenInfo(token.getText(), typeName, token.getType()));
            }
        }
        
        return tokens;
    }
    
    private TokenInfo findToken(List<TokenInfo> tokens, String text) {
        return tokens.stream()
            .filter(t -> text.equals(t.text))
            .findFirst()
            .orElseThrow(() -> new AssertionError("Token not found: " + text));
    }
    
    // 内部类用于存储token信息
    private static class TokenInfo {
        final String text;
        final String typeName;
        final int type;
        
        TokenInfo(String text, String typeName, int type) {
            this.text = text;
            this.typeName = typeName;
            this.type = type;
        }
        
        @Override
        public String toString() {
            return String.format("Token{text='%s', type=%s(%d)}", text, typeName, type);
        }
    }
}
