package com.xylink.sqltranspiler.unit.infrastructure.parser.error;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.infrastructure.parser.error.EnhancedErrorAnalyzer;
import com.xylink.sqltranspiler.infrastructure.parser.error.ErrorPatternRegistry;
import com.xylink.sqltranspiler.infrastructure.parser.error.SqlErrorInfo;
import com.xylink.sqltranspiler.infrastructure.parser.error.SqlErrorType;

/**
 * 全面的错误检测测试
 * 验证各种常见SQL错误的检测和处理能力
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
@DisplayName("全面的错误检测测试")
public class ComprehensiveErrorDetectionTest {
    
    private static final Logger log = LoggerFactory.getLogger(ComprehensiveErrorDetectionTest.class);
    
    @Test
    @DisplayName("测试MySQL特有语法检测")
    public void testMySqlSpecificSyntaxDetection() {
        String[] mysqlSpecificSqls = {
            // LIMIT语法
            "SELECT * FROM users LIMIT 10, 20",
            
            // ENGINE子句
            "CREATE TABLE test (id INT) ENGINE=InnoDB",
            
            // CHARACTER SET子句
            "CREATE TABLE test (id INT) DEFAULT CHARSET=utf8mb4",
            
            // AUTO_INCREMENT
            "CREATE TABLE test (id INT AUTO_INCREMENT PRIMARY KEY)",
            
            // UNSIGNED类型
            "CREATE TABLE test (id INT UNSIGNED, count BIGINT UNSIGNED)",
            
            // 反引号标识符
            "SELECT `user_id`, `user_name` FROM `user_table`",
            
            // COMMENT语法
            "CREATE TABLE test (id INT COMMENT 'Primary key')",
            
            // SET语句
            "SET FOREIGN_KEY_CHECKS = 0",
            "SET SQL_MODE = 'STRICT_TRANS_TABLES'",
            "SET NAMES utf8mb4",
            
            // USE语句
            "USE database_name",
            
            // LOCK TABLES
            "LOCK TABLES users READ",
            "UNLOCK TABLES",
            
            // DELIMITER（存储过程）
            "DELIMITER $$"
        };
        
        for (String sql : mysqlSpecificSqls) {
            boolean hasIssues = ErrorPatternRegistry.hasKnownSyntaxIssues(sql);
            log.info("MySQL特有语法检测 - SQL: {} | 检测到问题: {}", sql, hasIssues);
            
            // 分析错误信息
            SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(sql, "test error", 1, 1);
            assertNotNull(errorInfo, "错误信息不应为空");
            log.info("  错误类型: {}", errorInfo.getErrorType());
            log.info("  友好信息: {}", errorInfo.getFriendlyMessage());
        }
    }
    
    @Test
    @DisplayName("测试数据类型兼容性检测")
    public void testDataTypeCompatibilityDetection() {
        String[] dataTypeSqls = {
            // MySQL特有数据类型
            "CREATE TABLE test (id MEDIUMINT, content LONGTEXT)",
            "CREATE TABLE test (status ENUM('active', 'inactive'))",
            "CREATE TABLE test (tags SET('tag1', 'tag2', 'tag3'))",
            
            // VARCHAR长度问题
            "CREATE TABLE test (description VARCHAR(65535))",
            "CREATE TABLE test (content VARCHAR(32768))",
            
            // UNSIGNED类型
            "CREATE TABLE test (count INT UNSIGNED, amount DECIMAL(10,2) UNSIGNED)"
        };
        
        for (String sql : dataTypeSqls) {
            SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(sql, "test error", 1, 1);
            assertNotNull(errorInfo, "错误信息不应为空");
            log.info("数据类型兼容性 - SQL: {}", sql);
            log.info("  错误类型: {}", errorInfo.getErrorType());
            log.info("  友好信息: {}", errorInfo.getFriendlyMessage());
        }
    }
    
    @Test
    @DisplayName("测试函数兼容性检测")
    public void testFunctionCompatibilityDetection() {
        String[] functionSqls = {
            // MySQL特有函数
            "SELECT GROUP_CONCAT(name) FROM users",
            "SELECT IFNULL(name, 'Unknown') FROM users",
            "SELECT DATE_FORMAT(created_at, '%Y-%m-%d') FROM users",
            "SELECT STR_TO_DATE('2023-01-01', '%Y-%m-%d')",
            "SELECT UNIX_TIMESTAMP(NOW())",
            
            // 窗口函数
            "SELECT ROW_NUMBER() OVER (ORDER BY id) FROM users",
            "SELECT RANK() OVER (PARTITION BY department ORDER BY salary) FROM employees",
            
            // CTE
            "WITH user_stats AS (SELECT COUNT(*) as cnt FROM users) SELECT * FROM user_stats"
        };
        
        for (String sql : functionSqls) {
            SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(sql, "test error", 1, 1);
            assertNotNull(errorInfo, "错误信息不应为空");
            log.info("函数兼容性 - SQL: {}", sql);
            log.info("  错误类型: {}", errorInfo.getErrorType());
            log.info("  友好信息: {}", errorInfo.getFriendlyMessage());
        }
    }
    
    @Test
    @DisplayName("测试自动修复功能")
    public void testAutoFixCapabilities() {
        String[] autoFixableSqls = {
            // 可自动修复的语法
            "ALTER TABLE test ADD COLUMN update_time DATETIME ON UPDATE NOW()",
            "CREATE TABLE test (created_at TIMESTAMP DEFAULT NOW())",
            "SELECT * FROM users LIMIT 10, 20",
            "CREATE TABLE test (id INT UNSIGNED, name VARCHAR(255))",
            "SELECT `user_id`, `user_name` FROM `user_table`"
        };
        
        for (String sql : autoFixableSqls) {
            boolean hasIssues = ErrorPatternRegistry.hasKnownSyntaxIssues(sql);
            if (hasIssues) {
                String fixedSql = ErrorPatternRegistry.autoFixKnownIssues(sql);
                log.info("自动修复测试:");
                log.info("  原始SQL: {}", sql);
                log.info("  修复后SQL: {}", fixedSql);
                assertFalse(sql.equals(fixedSql), "修复后的SQL应该与原始SQL不同");
            }
        }
    }
    
    @Test
    @DisplayName("测试错误模式的全面覆盖")
    public void testComprehensiveErrorCoverage() {
        // 测试各种错误场景
        String[] testCases = {
            // 语法错误
            "CREATE TABLE test (id INT PRIMARY KEY, name VARCHAR(255)",  // 缺少括号
            "SELECT * FROM users WHERE name = 'unclosed string",         // 未闭合字符串
            
            // MySQL特有语法
            "CREATE TABLE test (id INT AUTO_INCREMENT) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4",
            "SELECT * FROM users LIMIT 10, 20",
            "SET FOREIGN_KEY_CHECKS = 0",
            
            // 数据类型问题
            "CREATE TABLE test (id MEDIUMINT UNSIGNED, content LONGTEXT)",
            "CREATE TABLE test (status ENUM('active', 'inactive'))",
            
            // 函数兼容性
            "SELECT GROUP_CONCAT(name SEPARATOR ',') FROM users",
            "SELECT IFNULL(description, 'No description') FROM products",
            
            // 高级SQL特性
            "WITH RECURSIVE tree AS (SELECT id FROM categories) SELECT * FROM tree",
            "SELECT ROW_NUMBER() OVER (ORDER BY created_at) FROM orders"
        };
        
        int detectedIssues = 0;
        int autoFixableIssues = 0;
        
        for (String sql : testCases) {
            SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(sql, "test error", 1, 1);
            
            if (errorInfo.getErrorType() != SqlErrorType.GENERIC_SYNTAX_ERROR) {
                detectedIssues++;
                log.info("检测到问题 - SQL: {}", sql);
                log.info("  错误类型: {}", errorInfo.getErrorType());
                log.info("  友好信息: {}", errorInfo.getFriendlyMessage());
                
                if (errorInfo.isAutoFixable()) {
                    autoFixableIssues++;
                    log.info("  可自动修复: {}", errorInfo.getSuggestedFix());
                }
            }
        }
        
        log.info("错误检测统计:");
        log.info("  总测试用例: {}", testCases.length);
        log.info("  检测到问题: {}", detectedIssues);
        log.info("  可自动修复: {}", autoFixableIssues);
        
        assertTrue(detectedIssues > 0, "应该检测到一些问题");
        assertTrue(autoFixableIssues > 0, "应该有一些可自动修复的问题");
    }
    
    @Test
    @DisplayName("测试错误模式的精确性")
    public void testErrorPatternPrecision() {
        // 测试不应该被误报的正常SQL
        String[] validSqls = {
            "SELECT id, name FROM users WHERE active = 1",
            "CREATE TABLE users (id INT PRIMARY KEY, name VARCHAR(255) NOT NULL)",
            "INSERT INTO users (name, email) VALUES ('John', '<EMAIL>')",
            "UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = 1",
            "DELETE FROM users WHERE created_at < '2020-01-01'"
        };
        
        for (String sql : validSqls) {
            SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(sql, "test error", 1, 1);
            
            // 这些SQL应该被识别为通用语法错误（因为我们传入了"test error"）
            // 但不应该匹配任何特定的错误模式
            log.info("正常SQL测试 - SQL: {}", sql);
            log.info("  错误类型: {}", errorInfo.getErrorType());
        }
    }
}
