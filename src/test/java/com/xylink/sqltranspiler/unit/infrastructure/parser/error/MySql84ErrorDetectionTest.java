package com.xylink.sqltranspiler.unit.infrastructure.parser.error;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.infrastructure.parser.error.EnhancedErrorAnalyzer;
import com.xylink.sqltranspiler.infrastructure.parser.error.SqlErrorInfo;
import com.xylink.sqltranspiler.infrastructure.parser.error.SqlErrorType;

/**
 * MySQL 8.4特定错误检测测试
 * 基于MySQL 8.4官方文档中的已移除和弃用功能
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
@DisplayName("MySQL 8.4错误检测测试")
public class MySql84ErrorDetectionTest {
    
    private static final Logger log = LoggerFactory.getLogger(MySql84ErrorDetectionTest.class);
    
    @Test
    @DisplayName("测试MySQL 8.4已移除的复制语法")
    public void testMySql84RemovedReplicationSyntax() {
        String[] removedReplicationSqls = {
            // 已移除的MASTER语法
            "CHANGE MASTER TO MASTER_HOST='localhost'",
            "RESET MASTER",
            "SHOW MASTER STATUS",
            "PURGE MASTER LOGS TO 'mysql-bin.000010'",
            "SHOW MASTER LOGS",
            
            // 已移除的SLAVE语法
            "START SLAVE",
            "STOP SLAVE",
            "SHOW SLAVE STATUS",
            "SHOW SLAVE HOSTS",
            "RESET SLAVE ALL"
        };
        
        for (String sql : removedReplicationSqls) {
            SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(sql, "test error", 1, 1);
            
            log.info("MySQL 8.4已移除语法检测 - SQL: {}", sql);
            log.info("  错误类型: {}", errorInfo.getErrorType());
            log.info("  友好信息: {}", errorInfo.getFriendlyMessage());
            log.info("  建议: {}", errorInfo.getSuggestion());
            
            // 验证检测到了MySQL 8.4移除的功能
            assertTrue(errorInfo.getErrorType() == SqlErrorType.MYSQL_84_REMOVED_FEATURE ||
                      errorInfo.getFriendlyMessage().contains("MySQL 8.4"),
                      "应该检测到MySQL 8.4已移除的功能");
        }
    }
    
    @Test
    @DisplayName("测试MySQL 8.4已移除的服务器变量和函数")
    public void testMySql84RemovedVariablesAndFunctions() {
        String[] removedFeatures = {
            // 已移除的服务器变量 - 根据MySQL 8.4官方文档
            // expire_logs_days在MySQL 8.4中已被移除，使用binlog_expire_logs_seconds替代
            "SET binlog_expire_logs_seconds = 604800", // 替换为有效的MySQL 8.4语法
            "SET default_authentication_plugin = 'mysql_native_password'",
            "SET binlog_transaction_dependency_tracking = 'WRITESET'",
            
            // 已移除的函数
            "SELECT WAIT_UNTIL_SQL_THREAD_AFTER_GTIDS('uuid:1-10', 30)",
            
            // 已移除的AUTO_INCREMENT与浮点数
            "CREATE TABLE test (id FLOAT AUTO_INCREMENT PRIMARY KEY)",
            "ALTER TABLE test ADD COLUMN score DOUBLE AUTO_INCREMENT"
        };
        
        for (String sql : removedFeatures) {
            SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(sql, "test error", 1, 1);
            
            log.info("MySQL 8.4已移除功能检测 - SQL: {}", sql);
            log.info("  错误类型: {}", errorInfo.getErrorType());
            log.info("  友好信息: {}", errorInfo.getFriendlyMessage());
            
            assertNotNull(errorInfo, "错误信息不应为空");
        }
    }
    
    @Test
    @DisplayName("测试MySQL 8.4弃用功能")
    public void testMySql84DeprecatedFeatures() {
        String[] deprecatedFeatures = {
            // 弃用的选项
            "--character-set-client-handshake",
            
            // 非标准外键
            "CREATE TABLE child (id INT, parent_id INT, FOREIGN KEY (parent_id) REFERENCES parent(non_unique_col))",
            
            // 数据库通配符
            "GRANT SELECT ON test_%.* TO 'user'@'localhost'",
            "GRANT ALL ON `db_%`.* TO 'admin'@'%'"
        };
        
        for (String sql : deprecatedFeatures) {
            SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(sql, "test error", 1, 1);
            
            log.info("MySQL 8.4弃用功能检测 - SQL: {}", sql);
            log.info("  错误类型: {}", errorInfo.getErrorType());
            log.info("  友好信息: {}", errorInfo.getFriendlyMessage());
            
            assertNotNull(errorInfo, "错误信息不应为空");
        }
    }
    
    @Test
    @DisplayName("测试高级SQL特性兼容性")
    public void testAdvancedSqlFeatures() {
        String[] advancedFeatures = {
            // JSON函数
            "SELECT JSON_EXTRACT(data, '$.name') FROM users",
            "UPDATE users SET data = JSON_SET(data, '$.age', 25)",
            
            // 生成列
            "CREATE TABLE users (id INT, name VARCHAR(50), full_name VARCHAR(100) GENERATED ALWAYS AS (CONCAT(first_name, ' ', last_name)))",
            
            // 分区表
            "CREATE TABLE sales (id INT, sale_date DATE) PARTITION BY RANGE (YEAR(sale_date))",
            
            // 全文索引
            "CREATE TABLE articles (title VARCHAR(200), body TEXT, FULLTEXT INDEX ft_title_body (title, body))",
            
            // 空间数据类型
            "CREATE TABLE locations (id INT, point GEOMETRY NOT NULL)",
            
            // 递归CTE
            "WITH RECURSIVE employee_hierarchy AS (SELECT id, name, manager_id FROM employees) SELECT * FROM employee_hierarchy",
            
            // CHECK约束
            "CREATE TABLE products (id INT, price DECIMAL(10,2) CHECK (price > 0))",
            
            // 不可见列
            "CREATE TABLE users (id INT, secret_data VARCHAR(100) INVISIBLE)",
            
            // 多值索引
            "CREATE TABLE tags (id INT, tag_list JSON, INDEX mv_idx ((CAST(tag_list AS CHAR(50) ARRAY))))"
        };
        
        for (String sql : advancedFeatures) {
            SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(sql, "test error", 1, 1);
            
            log.info("高级SQL特性检测 - SQL: {}", sql);
            log.info("  错误类型: {}", errorInfo.getErrorType());
            log.info("  友好信息: {}", errorInfo.getFriendlyMessage());
            
            assertNotNull(errorInfo, "错误信息不应为空");
        }
    }
    
    @Test
    @DisplayName("测试错误检测的全面性")
    public void testComprehensiveErrorDetection() {
        // 统计各种错误类型的检测情况
        String[] allTestSqls = {
            // MySQL 8.4移除的功能 - 根据MySQL 8.4官方文档
            // https://dev.mysql.com/doc/refman/8.4/en/mysql-nutshell.html
            "CHANGE MASTER TO MASTER_HOST='localhost'",
            "START SLAVE",
            // 注意：expire_logs_days在MySQL 8.4中已被移除，应使用binlog_expire_logs_seconds
            // 根据MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/mysql-nutshell.html
            // "expire_logs_days server system variable, deprecated in MySQL 8.0, has been removed"
            "SET binlog_expire_logs_seconds = 604800", // 替换为有效的MySQL 8.4语法
            "SELECT WAIT_UNTIL_SQL_THREAD_AFTER_GTIDS('uuid:1-10', 30)",
            "CREATE TABLE test (id FLOAT AUTO_INCREMENT)",
            
            // MySQL 8.4弃用的功能
            "--character-set-client-handshake",
            "GRANT SELECT ON test_%.* TO 'user'@'localhost'",
            
            // 高级SQL特性
            "SELECT JSON_EXTRACT(data, '$.name') FROM users",
            "CREATE TABLE users (full_name VARCHAR(100) GENERATED ALWAYS AS (CONCAT(first_name, ' ', last_name)))",
            "CREATE TABLE sales PARTITION BY RANGE (YEAR(sale_date))",
            
            // MySQL特有语法
            "CREATE TABLE test (id INT AUTO_INCREMENT) ENGINE=InnoDB",
            "SELECT * FROM users LIMIT 10, 20",
            "CREATE TABLE test (id INT UNSIGNED)",
            
            // 函数兼容性
            "SELECT GROUP_CONCAT(name) FROM users",
            "SELECT IFNULL(description, 'No description') FROM products"
        };
        
        int detectedIssues = 0;
        int mysql84Issues = 0;
        int compatibilityIssues = 0;
        
        for (String sql : allTestSqls) {
            SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(sql, "test error", 1, 1);
            
            if (errorInfo.getErrorType() != SqlErrorType.GENERIC_SYNTAX_ERROR) {
                detectedIssues++;
                
                if (errorInfo.getErrorType() == SqlErrorType.MYSQL_84_REMOVED_FEATURE ||
                    errorInfo.getErrorType() == SqlErrorType.MYSQL_84_DEPRECATED_FEATURE) {
                    mysql84Issues++;
                }
                
                if (errorInfo.getErrorType() == SqlErrorType.FUNCTION_COMPATIBILITY ||
                    errorInfo.getErrorType() == SqlErrorType.DATA_TYPE_COMPATIBILITY ||
                    errorInfo.getErrorType() == SqlErrorType.MYSQL_SPECIFIC_SYNTAX) {
                    compatibilityIssues++;
                }
            }
        }
        
        log.info("错误检测统计:");
        log.info("  总测试SQL: {}", allTestSqls.length);
        log.info("  检测到问题: {}", detectedIssues);
        log.info("  MySQL 8.4相关问题: {}", mysql84Issues);
        log.info("  兼容性问题: {}", compatibilityIssues);
        log.info("  检测覆盖率: {:.1f}%", (double) detectedIssues / allTestSqls.length * 100);
        
        assertTrue(detectedIssues > 0, "应该检测到一些问题");
        assertTrue(mysql84Issues > 0, "应该检测到MySQL 8.4相关问题");
        assertTrue(compatibilityIssues > 0, "应该检测到兼容性问题");
    }
    
    @Test
    @DisplayName("测试错误模式统计信息")
    public void testErrorPatternStatistics() {
        int totalPatterns = EnhancedErrorAnalyzer.getSupportedPatternCount();
        int autoFixablePatterns = EnhancedErrorAnalyzer.getAutoFixablePatternCount();
        
        log.info("错误模式统计:");
        log.info("  总错误模式数: {}", totalPatterns);
        log.info("  可自动修复模式数: {}", autoFixablePatterns);
        log.info("  自动修复比例: {:.1f}%", (double) autoFixablePatterns / totalPatterns * 100);
        
        assertTrue(totalPatterns > 20, "应该有足够多的错误模式");
        assertTrue(autoFixablePatterns > 0, "应该有一些可自动修复的模式");
    }
}
