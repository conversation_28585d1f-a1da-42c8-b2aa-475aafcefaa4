package com.xylink.sqltranspiler.unit.infrastructure.logging;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.Transpiler;
import com.xylink.sqltranspiler.core.context.TranspilationIssue;
import com.xylink.sqltranspiler.core.context.TranspilationResult;

/**
 * 测试警告处理逻辑，确保警告不会被错误地计入失败数量
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
class WarningHandlingTest {

    private Transpiler transpiler;

    @BeforeEach
    void setUp() {
        transpiler = new Transpiler();
    }

    @Test
    void testEmptyLinesGenerateWarningsNotFailures() {
        // 包含空行的SQL
        String sql = """
            CREATE TABLE test (id INT AUTO_INCREMENT PRIMARY KEY);
            
            INSERT INTO test VALUES (1);
            
            SELECT * FROM test;
            """;
        
        TranspilationResult result = transpiler.transpile(sql, "mysql", "shentong", true);
        
        // 验证转换成功
        assertThat(result.successCount()).isGreaterThan(0);
        assertThat(result.translatedSql()).isNotEmpty();
        
        // 关键测试：即使有空行，失败数量应该为0
        assertThat(result.failureCount()).isEqualTo(0);
        
        // 检查是否有警告级别的问题（可能有，也可能没有，取决于预处理）
        long warningCount = result.issues().stream()
            .filter(issue -> issue.level() == TranspilationIssue.IssueLevel.WARN)
            .count();
        
        // 不应该有错误级别的问题
        long errorCount = result.issues().stream()
            .filter(issue -> issue.level() == TranspilationIssue.IssueLevel.ERROR)
            .count();
        assertThat(errorCount).isEqualTo(0);
        
        // 验证生成的SQL包含预期内容
        assertThat(result.translatedSql()).contains("CREATE TABLE");
        assertThat(result.translatedSql()).contains("INSERT INTO");
        assertThat(result.translatedSql()).contains("SELECT");
    }

    @Test
    void testOnlyEmptyLinesDoNotCauseFailure() {
        // 只包含空行和注释的SQL
        String sql = """
            
            -- 这是一个注释
            
            """;
        
        TranspilationResult result = transpiler.transpile(sql, "mysql", "shentong", true);
        
        // 即使没有实际的SQL语句，也不应该报告失败
        assertThat(result.failureCount()).isEqualTo(0);
        
        // 成功数量可能为0（因为没有实际语句）
        assertThat(result.successCount()).isGreaterThanOrEqualTo(0);
    }

    @Test
    void testMixedContentWithWarnings() {
        // 混合内容：有效SQL + 空行 + 注释
        String sql = """
            CREATE DATABASE test_db CHARACTER SET utf8mb4;
            
            -- 用户表
            CREATE TABLE users (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                email VARCHAR(255) UNIQUE
            );
            
            INSERT INTO users (name, email) VALUES ('张三', '<EMAIL>');
            """;
        
        TranspilationResult result = transpiler.transpile(sql, "mysql", "shentong", true);
        
        // 验证转换成功
        assertThat(result.successCount()).isGreaterThan(0);
        assertThat(result.failureCount()).isEqualTo(0);
        
        // 验证生成的SQL包含神通数据库特定的转换
        assertThat(result.translatedSql()).contains("CHARACTER SET UTF8");
        assertThat(result.translatedSql()).contains("AUTO_INCREMENT");
        assertThat(result.translatedSql()).contains("\"users\"");
    }
}
