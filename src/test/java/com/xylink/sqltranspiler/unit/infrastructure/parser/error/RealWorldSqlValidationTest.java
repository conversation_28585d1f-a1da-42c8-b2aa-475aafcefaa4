package com.xylink.sqltranspiler.unit.infrastructure.parser.error;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.Transpiler;
import com.xylink.sqltranspiler.core.context.TranspilationResult;

import lombok.extern.slf4j.Slf4j;

/**
 * 真实SQL语句验证测试
 * 测试项目能否正确识别用户提供的实际SQL语句中的MySQL语法问题
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
@Slf4j
@DisplayName("真实SQL语句验证测试")
public class RealWorldSqlValidationTest {

    @Test
    @DisplayName("测试用户提供的问题SQL语句")
    public void testUserProvidedSql() {
        // 用户提供的实际SQL语句
        String sql = "INSERT INTO ainemo.t_en_role_res (id, role_id, res_id, create_time) " +
                    "SELECT lower(md5(random()::text || clock_timestamp()::text)), t.role_id, " +
                    "'95958fe34aa32087022583c4bcfaf95c', NOW() " +
                    "FROM ainemo.t_en_role_res t " +
                    "WHERE t.res_id = 'ff8080815ac59dd5015ac63d1b7e0058' " +
                    "AND t.role_id NOT IN ( 'ff8080815ac59dd5015ac63d1b7e002c', 'ff8080815ac59dd5015ac63d1b7e002e' );";
        
        log.info("测试用户提供的SQL语句:");
        log.info("SQL: {}", sql);
        
        // 使用Transpiler进行转换，这会触发语法验证
        Transpiler transpiler = new Transpiler();
        TranspilationResult result = transpiler.transpile(sql, "mysql", "dameng");
        
        log.info("转换结果:");
        log.info("成功数量: {}", result.successCount());
        log.info("失败数量: {}", result.failureCount());
        log.info("问题数量: {}", result.issues().size());
        
        // 检查是否检测到了语法问题
        assertTrue(result.issues().size() > 0, "应该检测到语法问题");
        
        // 输出所有检测到的问题
        result.issues().forEach(issue -> {
            log.info("检测到问题:");
            log.info("  级别: {}", issue.level());
            log.info("  代码: {}", issue.issueCode());
            log.info("  消息: {}", issue.message());
            log.info("  行号: {}", issue.line());
            log.info("  列号: {}", issue.column());
        });
        
        // 检查是否有语法错误相关的问题
        boolean hasSyntaxError = result.issues().stream()
            .anyMatch(issue -> issue.message().contains("语法错误") || 
                              issue.message().contains("random") ||
                              issue.message().contains("clock_timestamp") ||
                              issue.message().contains("::"));
        
        assertTrue(hasSyntaxError, "应该检测到与非MySQL语法相关的错误");
        
        log.info("✓ 成功检测到用户SQL中的非MySQL语法问题");
    }

    @Test
    @DisplayName("测试各种非MySQL语法的检测")
    public void testVariousNonMySqlSyntax() {
        String[] testCases = {
            // PostgreSQL函数
            "SELECT random() FROM test",
            "SELECT clock_timestamp() FROM test", 
            
            // PostgreSQL类型转换
            "SELECT id::text FROM test",
            "SELECT created_at::timestamp FROM test",
            
            // 组合语法
            "SELECT random()::text FROM test",
            "SELECT clock_timestamp()::varchar FROM test"
        };
        
        Transpiler transpiler = new Transpiler();
        
        for (String testSql : testCases) {
            log.info("测试SQL: {}", testSql);
            
            TranspilationResult result = transpiler.transpile(testSql, "mysql", "dameng");
            
            log.info("  问题数量: {}", result.issues().size());
            
            if (result.issues().size() > 0) {
                result.issues().forEach(issue -> {
                    if (issue.message().contains("语法错误") || 
                        issue.message().contains("random") ||
                        issue.message().contains("clock_timestamp") ||
                        issue.message().contains("::")) {
                        log.info("  ✓ 检测到非MySQL语法: {}", issue.message());
                    }
                });
            } else {
                log.warn("  ✗ 未检测到语法问题");
            }
        }
    }

    @Test
    @DisplayName("测试有效MySQL语法不会被误报")
    public void testValidMySqlSyntaxNotReported() {
        // 标准的MySQL语法
        String validSql = "INSERT INTO test (id, name, created_at) " +
                         "SELECT CONCAT(MD5(RAND()), UUID()), 'test', NOW() " +
                         "FROM dual;";
        
        log.info("测试有效的MySQL语法: {}", validSql);
        
        Transpiler transpiler = new Transpiler();
        TranspilationResult result = transpiler.transpile(validSql, "mysql", "dameng");
        
        log.info("转换结果:");
        log.info("成功数量: {}", result.successCount());
        log.info("失败数量: {}", result.failureCount());
        log.info("问题数量: {}", result.issues().size());
        
        // 检查是否有语法错误（应该没有）
        boolean hasSyntaxError = result.issues().stream()
            .anyMatch(issue -> issue.message().contains("语法错误") && 
                              (issue.message().contains("random") ||
                               issue.message().contains("clock_timestamp") ||
                               issue.message().contains("::")));
        
        assertFalse(hasSyntaxError, "有效的MySQL语法不应该被报告为语法错误");
        
        log.info("✓ 有效MySQL语法测试通过");
    }
}
