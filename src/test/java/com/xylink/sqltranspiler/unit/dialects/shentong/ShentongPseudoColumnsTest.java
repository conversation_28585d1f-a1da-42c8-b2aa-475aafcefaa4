package com.xylink.sqltranspiler.unit.dialects.shentong;

import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.shared.base.BaseShentongConversionTest;

/**
 * 神通数据库伪列支持测试
 * 基于神通数据库官方文档 st.md 第2.6节伪列规范
 * 神通数据库支持的伪列：
 * - ROWID: 在整个数据库中唯一标识了一行数据
 * - SYSATTR_ROWVERSION: 标识表中一行的版本
 * - ROWNUM: 为每一条记录分配一个递增唯一的整数值
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保方言功能符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试方言特有功能
 *
 * <AUTHOR>
 */
public class ShentongPseudoColumnsTest extends BaseShentongConversionTest {

    /**
     * 测试ROWID伪列支持
     * 根据文档：ROWID在整个数据库中唯一标识了一行数据，通过它可以直接定位数据的物理存储位置
     */
    @Test
    public void testRowidPseudoColumn() throws Exception {
        String mysqlSql = """
            SELECT ROWID, name, email 
            FROM users 
            WHERE ROWID = 'AAAEAbAAEAAAAAgAAA';
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证ROWID伪列支持
        assertTrue(shentongSql.contains("ROWID"), "应支持ROWID伪列");
        assertTrue(shentongSql.contains("SELECT"), "应支持SELECT语句");
        assertTrue(shentongSql.contains("WHERE"), "应支持WHERE条件");
    }

    /**
     * 测试SYSATTR_ROWVERSION伪列支持
     * 根据文档：SYSATTR_ROWVERSION标识表中一行的版本，如果行被更新了，该SYSATTR_ROWVERSION就会发生变化
     */
    @Test
    public void testSysattrRowversionPseudoColumn() throws Exception {
        String mysqlSql = """
            SELECT SYSATTR_ROWVERSION, id, name 
            FROM users 
            WHERE id = 1;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证SYSATTR_ROWVERSION伪列支持
        assertTrue(shentongSql.contains("SYSATTR_ROWVERSION"), "应支持SYSATTR_ROWVERSION伪列");
        assertTrue(shentongSql.contains("SELECT"), "应支持SELECT语句");
    }

    /**
     * 测试ROWNUM伪列基本功能
     * 根据文档：ROWNUM为每一条记录分配一个递增唯一的整数值，第一条记录的ROWNUM是1，第二条是2
     */
    @Test
    public void testRownumBasicFunctionality() throws Exception {
        String mysqlSql = """
            SELECT name, email
            FROM users
            ORDER BY name
            LIMIT 4;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);

        // 验证ROWNUM伪列支持
        assertTrue(shentongSql.contains("ROWNUM"), "应支持ROWNUM伪列");
        assertTrue(shentongSql.contains("WHERE"), "应支持WHERE条件");
        assertTrue(shentongSql.contains("ORDER BY"), "应支持ORDER BY");
    }

    /**
     * 测试ROWNUM分页查询
     * 根据文档示例：使用ROWNUM实现分页显示
     */
    @Test
    public void testRownumPagination() throws Exception {
        String mysqlSql = """
            SELECT * FROM users
            ORDER BY name DESC
            LIMIT 3 OFFSET 0;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证ROWNUM分页支持
        assertTrue(shentongSql.contains("ROWNUM"), "应支持ROWNUM伪列");
        assertTrue(shentongSql.contains("ROW_NUM"), "应支持ROWNUM别名");
        assertTrue(shentongSql.contains("BETWEEN"), "应支持BETWEEN条件");
        assertTrue(shentongSql.contains("ORDER BY"), "应支持ORDER BY");
    }

    /**
     * 测试MySQL LIMIT转换为ROWNUM
     * 将MySQL的LIMIT语法转换为神通的ROWNUM方式
     */
    @Test
    public void testLimitToRownumConversion() throws Exception {
        String mysqlSql = """
            SELECT name, email 
            FROM users 
            ORDER BY name 
            LIMIT 10 OFFSET 20;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        // 调试输出
        System.out.println("Original MySQL SQL: " + mysqlSql);
        System.out.println("Converted Shentong SQL: " + shentongSql);

        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);

        // 验证LIMIT转换为ROWNUM
        // 神通数据库应该将LIMIT OFFSET转换为ROWNUM分页查询
        assertTrue(shentongSql.contains("ROWNUM") || shentongSql.contains("ROW_NUMBER"),
                   "LIMIT应转换为ROWNUM或ROW_NUMBER分页");
        assertTrue(shentongSql.contains("ORDER BY"), "应保持ORDER BY");
    }

    /**
     * 测试ROWNUM的限制条件
     * 根据文档：ROWNUM = n或ROWNUM >= n（n>1）时，查询条件是恒假的
     */
    @Test
    public void testRownumLimitations() throws Exception {
        String mysqlSql = """
            SELECT name
            FROM users
            LIMIT 1;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证ROWNUM = 1的特殊情况
        assertTrue(shentongSql.contains("ROWNUM"), "应支持ROWNUM伪列");
        assertTrue(shentongSql.contains("= 1"), "应支持ROWNUM = 1条件");
    }

    /**
     * 测试Oracle ROWNUM在GROUP BY中的语法被MySQL强制校验正确拒绝
     * 验证Oracle特有语法被正确检测和拒绝
     */
    @Test
    public void testOracleRownumInGroupBySyntaxRejection() throws Exception {
        String oracleSql = """
            SELECT COUNT(*)
            FROM (
                SELECT name
                FROM users
                GROUP BY name, ROWNUM
            ) AS subquery;
            """;

        String result = convertMySqlToShentong(oracleSql);

        // 验证Oracle语法被正确拒绝 - 根据MySQL 8.4官方文档，ROWNUM不是MySQL语法
        assertTrue(result.isEmpty(), "Oracle ROWNUM在GROUP BY中的语法应该被MySQL强制校验拒绝，转换结果应为空");
    }

    /**
     * 测试MySQL标准GROUP BY语法的转换
     * 使用正确的MySQL语法进行GROUP BY测试
     */
    @Test
    public void testMySqlStandardGroupBySyntax() throws Exception {
        String mysqlSql = """
            SELECT COUNT(*)
            FROM (
                SELECT name
                FROM users
                GROUP BY name
            ) AS subquery;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);

        // 验证MySQL标准GROUP BY语法转换
        assertTrue(shentongSql.contains("GROUP BY"), "应支持GROUP BY");
        assertTrue(shentongSql.contains("COUNT(*)"), "应支持COUNT(*)");
        assertTrue(shentongSql.contains("subquery"), "应支持子查询别名");
        assertTrue(shentongSql.contains("SELECT"), "应支持SELECT语句");
        assertTrue(shentongSql.contains("FROM"), "应支持FROM子句");
    }

    /**
     * 测试Oracle伪列语法被MySQL强制校验正确拒绝
     * 验证Oracle特有伪列被正确检测和拒绝
     */
    @Test
    public void testOraclePseudoColumnSyntaxRejection() throws Exception {
        // 测试Oracle伪列（应该被拒绝）
        String oracleSql = """
            SELECT ROWID, ROWNUM, SYSATTR_ROWVERSION, name
            FROM users;
            """;

        String result = convertMySqlToShentong(oracleSql);

        // 验证Oracle语法被正确拒绝 - 根据MySQL 8.4官方文档，ROWID、ROWNUM等不是MySQL语法
        assertTrue(result.isEmpty(), "Oracle伪列语法应该被MySQL强制校验拒绝，转换结果应为空");
    }

    /**
     * 测试MySQL标准查询语法的转换
     * 使用正确的MySQL语法进行查询测试
     */
    @Test
    public void testMySqlStandardQuerySyntax() throws Exception {
        // 测试MySQL标准查询（应该成功）
        String mysqlSql = """
            SELECT id, name, created_at
            FROM users
            ORDER BY id
            LIMIT 10;
            """;

        String shentongSelectSql = convertMySqlToShentong(mysqlSql);

        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSelectSql);

        // 验证MySQL标准查询语法被正确转换
        assertTrue(shentongSelectSql.contains("SELECT"), "应支持SELECT语句");
        assertTrue(shentongSelectSql.contains("FROM"), "应支持FROM子句");
        assertTrue(shentongSelectSql.contains("ORDER BY"), "应支持ORDER BY子句");
        assertTrue(shentongSelectSql.contains("LIMIT") || shentongSelectSql.contains("ROWNUM"), "LIMIT应被转换为ROWNUM或保持");
    }

    /**
     * 测试伪列在复杂查询中的使用
     * 包括子查询、连接等复杂场景
     */
    @Test
    public void testPseudoColumnsInComplexQueries() throws Exception {
        String mysqlSql = """
            SELECT u.name, u.email
            FROM users u
            WHERE EXISTS (
                SELECT 1
                FROM orders o
                WHERE o.user_id = u.id
                LIMIT 1
            )
            ORDER BY u.name
            LIMIT 10;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证复杂查询支持（注意：此SQL不包含伪列，只是复杂查询）
        assertTrue(shentongSql.contains("SELECT"), "应支持SELECT查询");
        assertTrue(shentongSql.contains("FROM"), "应支持FROM子句");
        assertTrue(shentongSql.contains("EXISTS"), "应支持EXISTS子查询");
    }
}
