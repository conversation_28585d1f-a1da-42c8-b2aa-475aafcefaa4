package com.xylink.sqltranspiler.unit.dialects.kingbase;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import com.xylink.sqltranspiler.shared.base.BaseKingbaseTest;

/**
 * 金仓数据库约束测试
 * 基于金仓官方文档：
 * - 金仓数据库约束：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/ddl/constraints.html
 * - PostgreSQL兼容性：https://help.kingbase.com.cn/v8/development/develop-transfer/kes-vs-mysql/index.html
 * - MySQL迁移最佳实践：https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html
 * 测试原则：
 * 1. 严格遵循金仓官方文档规范
 * 2. 验证MySQL约束到金仓的正确转换
 * 3. 确保PostgreSQL兼容性语法的正确性
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保方言功能符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试方言特有功能
 *
 * <AUTHOR>
 */
@DisplayName("金仓数据库约束测试")
public class KingbaseConstraintTest extends BaseKingbaseTest {

    private KingbaseGenerator generator;

    @BeforeEach
    protected void setUp() {
        super.setUp();
        generator = new KingbaseGenerator();
    }

    @Test
    @DisplayName("测试主键约束转换")
    void testPrimaryKeyConstraint() {
        // 基于MySQL 8.4官方文档的主键约束语法
        // https://dev.mysql.com/doc/refman/8.4/en/create-table.html
        String mysqlSql = """
            CREATE TABLE users (
                id INT NOT NULL AUTO_INCREMENT,
                username VARCHAR(50) NOT NULL,
                email VARCHAR(100) NOT NULL,
                PRIMARY KEY (id),
                CONSTRAINT uk_username UNIQUE (username),
                CONSTRAINT uk_email UNIQUE (email)
            );
            """;

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("金仓主键约束转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档验证转换结果
        assertKingbaseConversionRequirements(kingbaseSql);
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"users\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留PRIMARY KEY约束");
        assertTrue(kingbaseSql.contains("UNIQUE"), "应保留UNIQUE约束");
        assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("IDENTITY"), 
                  "应转换AUTO_INCREMENT为SERIAL或IDENTITY");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
        assertFalse(kingbaseSql.contains("AUTO_INCREMENT"), "不应包含AUTO_INCREMENT");
    }

    @Test
    @DisplayName("测试外键约束转换")
    void testForeignKeyConstraint() {
        String mysqlSql = """
            CREATE TABLE orders (
                id INT NOT NULL AUTO_INCREMENT,
                user_id INT NOT NULL,
                product_id INT NOT NULL,
                order_date DATE NOT NULL,
                PRIMARY KEY (id),
                CONSTRAINT fk_orders_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE ON UPDATE RESTRICT,
                CONSTRAINT fk_orders_product FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE SET NULL
            );
            """;

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("金仓外键约束转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档验证转换结果
        assertKingbaseConversionRequirements(kingbaseSql);
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"orders\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("FOREIGN KEY"), "应保留FOREIGN KEY约束");
        assertTrue(kingbaseSql.contains("REFERENCES"), "应保留REFERENCES");
        assertTrue(kingbaseSql.contains("ON DELETE"), "应保留ON DELETE");
        assertTrue(kingbaseSql.contains("ON UPDATE"), "应保留ON UPDATE");
        assertTrue(kingbaseSql.contains("CASCADE"), "应保留CASCADE");
        assertTrue(kingbaseSql.contains("\"users\""), "引用表名应使用双引号");
        assertTrue(kingbaseSql.contains("\"products\""), "引用表名应使用双引号");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试检查约束转换")
    void testCheckConstraint() {
        String mysqlSql = """
            CREATE TABLE products (
                id INT NOT NULL AUTO_INCREMENT,
                name VARCHAR(100) NOT NULL,
                price DECIMAL(10,2) NOT NULL,
                category_id INT NOT NULL,
                status ENUM('active', 'inactive', 'discontinued') DEFAULT 'active',
                PRIMARY KEY (id),
                CONSTRAINT chk_price_positive CHECK (price > 0),
                CONSTRAINT chk_category_valid CHECK (category_id > 0),
                CONSTRAINT chk_name_length CHECK (LENGTH(name) >= 3)
            );
            """;

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("金仓检查约束转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档验证转换结果
        assertKingbaseConversionRequirements(kingbaseSql);
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"products\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("CHECK"), "应保留CHECK约束");
        assertTrue(kingbaseSql.contains("DECIMAL(10,2)"), "应保留DECIMAL类型");
        // ENUM类型在金仓中可能转换为CHECK约束或其他类型
        assertTrue(kingbaseSql.contains("CHECK") || kingbaseSql.contains("VARCHAR") || 
                  kingbaseSql.contains("-- ENUM"), "应处理ENUM类型");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试NOT NULL约束转换")
    void testNotNullConstraint() {
        String mysqlSql = """
            CREATE TABLE employees (
                id INT NOT NULL AUTO_INCREMENT,
                first_name VARCHAR(50) NOT NULL,
                last_name VARCHAR(50) NOT NULL,
                email VARCHAR(100) NOT NULL,
                phone VARCHAR(20),
                hire_date DATE NOT NULL,
                salary DECIMAL(10,2) NOT NULL DEFAULT 0.00,
                PRIMARY KEY (id)
            );
            """;

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("金仓NOT NULL约束转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档验证转换结果
        assertKingbaseConversionRequirements(kingbaseSql);
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"employees\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(kingbaseSql.contains("DEFAULT"), "应保留DEFAULT值");
        assertTrue(kingbaseSql.contains("VARCHAR(50)"), "应保留VARCHAR类型");
        assertTrue(kingbaseSql.contains("DECIMAL(10,2)"), "应保留DECIMAL类型");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试复合约束转换")
    void testCompositeConstraints() {
        String mysqlSql = """
            CREATE TABLE user_roles (
                user_id INT NOT NULL,
                role_id INT NOT NULL,
                assigned_date DATE NOT NULL DEFAULT (CURRENT_DATE),
                assigned_by INT NOT NULL,
                is_active BOOLEAN NOT NULL DEFAULT TRUE,
                PRIMARY KEY (user_id, role_id),
                CONSTRAINT fk_user_roles_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                CONSTRAINT fk_user_roles_role FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
                CONSTRAINT fk_user_roles_assigned_by FOREIGN KEY (assigned_by) REFERENCES users(id),
                CONSTRAINT uk_user_role_date UNIQUE (user_id, role_id, assigned_date),
                CONSTRAINT chk_assigned_date_valid CHECK (assigned_date <= CURRENT_DATE)
            );
            """;

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("金仓复合约束转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档验证转换结果
        assertKingbaseConversionRequirements(kingbaseSql);
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"user_roles\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留复合主键");
        assertTrue(kingbaseSql.contains("FOREIGN KEY"), "应保留外键约束");
        assertTrue(kingbaseSql.contains("UNIQUE"), "应保留唯一约束");
        assertTrue(kingbaseSql.contains("CHECK"), "应保留检查约束");
        assertTrue(kingbaseSql.contains("BOOLEAN") || kingbaseSql.contains("BOOL"), 
                  "应保留或转换BOOLEAN类型");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试约束命名规范")
    void testConstraintNaming() {
        String mysqlSql = """
            CREATE TABLE test_constraints (
                id INT NOT NULL AUTO_INCREMENT,
                code VARCHAR(10) NOT NULL,
                name VARCHAR(100) NOT NULL,
                parent_id INT,
                status INT NOT NULL DEFAULT 1,
                PRIMARY KEY (id),
                UNIQUE KEY uk_test_code (code),
                UNIQUE KEY uk_test_name (name),
                FOREIGN KEY fk_test_parent (parent_id) REFERENCES test_constraints(id),
                CHECK (status IN (0, 1, 2))
            );
            """;

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("金仓约束命名转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档验证转换结果
        assertKingbaseConversionRequirements(kingbaseSql);
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"test_constraints\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("UNIQUE"), "应保留唯一约束");
        assertTrue(kingbaseSql.contains("FOREIGN KEY"), "应保留外键");
        assertTrue(kingbaseSql.contains("CHECK"), "应保留检查约束");
        // 约束名称应该被保留或适当转换
        assertTrue(kingbaseSql.contains("uk_test") || kingbaseSql.contains("fk_test") || 
                  kingbaseSql.contains("CONSTRAINT"), "应保留约束名称");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    /**
     * 解析CREATE TABLE语句的辅助方法
     */
    protected CreateTable parseCreateTable(String sql) {
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement, "SQL解析不应失败");
        assertTrue(statement instanceof CreateTable, "应解析为CreateTable");
        return (CreateTable) statement;
    }
}
