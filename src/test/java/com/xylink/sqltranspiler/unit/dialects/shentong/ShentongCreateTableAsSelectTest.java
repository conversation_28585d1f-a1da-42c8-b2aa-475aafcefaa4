package com.xylink.sqltranspiler.unit.dialects.shentong;

import com.xylink.sqltranspiler.core.dialects.shentong.ShentongGenerator;

import com.xylink.sqltranspiler.core.ast.TableId;
import com.xylink.sqltranspiler.core.ast.create.CreateTableAsSelect;
import com.xylink.sqltranspiler.core.ast.dml.QueryStmt;
import com.xylink.sqltranspiler.core.ast.table.ColumnRel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 神通数据库CREATE TABLE AS SELECT功能测试
 * 基于神通官方文档的测试驱动开发
 * 参考神通数据库官方文档：神通数据库支持PostgreSQL兼容的CREATE TABLE AS SELECT语句
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保方言功能符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试方言特有功能
 *
 * <AUTHOR>
 */
public class ShentongCreateTableAsSelectTest {

    private ShentongGenerator generator;

    @BeforeEach
    public void setUp() {
        generator = new ShentongGenerator();
    }

    @Test
    @DisplayName("测试基本CREATE TABLE AS SELECT语句")
    public void testBasicCreateTableAsSelect() {
        TableId tableId = new TableId("employee_backup");
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql("SELECT * FROM employees");
        
        CreateTableAsSelect ctas = new CreateTableAsSelect(tableId, queryStmt, null, false, null);
        String result = generator.generate(ctas);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"employee_backup\""));
        assertTrue(result.contains("AS"));
        assertTrue(result.contains("SELECT * FROM employees"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试CREATE TABLE IF NOT EXISTS AS SELECT语句")
    public void testCreateTableIfNotExistsAsSelect() {
        TableId tableId = new TableId("safe_backup");
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql("SELECT id, name FROM employees WHERE active = true");
        
        CreateTableAsSelect ctas = new CreateTableAsSelect(tableId, queryStmt, null, true, null);
        String result = generator.generate(ctas);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("IF NOT EXISTS"));
        assertTrue(result.contains("\"safe_backup\""));
        assertTrue(result.contains("AS"));
        assertTrue(result.contains("SELECT id, name FROM employees WHERE active = true"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试带列定义的CREATE TABLE AS SELECT语句")
    public void testCreateTableAsSelectWithColumns() {
        TableId tableId = new TableId("employee_summary");
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql("SELECT id, name, salary FROM employees");
        
        List<ColumnRel> columnRels = Arrays.asList(
            new ColumnRel("emp_id", null, null),
            new ColumnRel("emp_name", null, null),
            new ColumnRel("emp_salary", null, null)
        );
        
        CreateTableAsSelect ctas = new CreateTableAsSelect(tableId, queryStmt, null, false, null);
        ctas.setColumnRels(columnRels);
        String result = generator.generate(ctas);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"employee_summary\""));
        assertTrue(result.contains("(\"emp_id\", \"emp_name\", \"emp_salary\")"));
        assertTrue(result.contains("AS"));
        assertTrue(result.contains("SELECT id, name, salary FROM employees"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试空查询的CREATE TABLE AS SELECT语句 - PostgreSQL兼容")
    public void testCreateTableAsSelectEmptyQuery() {
        TableId tableId = new TableId("empty_table");
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(""); // 空查询
        
        CreateTableAsSelect ctas = new CreateTableAsSelect(tableId, queryStmt, null, false, null);
        String result = generator.generate(ctas);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"empty_table\""));
        assertTrue(result.contains("AS"));
        assertTrue(result.contains("SELECT 1 WHERE FALSE")); // PostgreSQL兼容的空表创建
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试神通数据库PostgreSQL兼容特性")
    public void testShentongPostgreSQLCompatibility() {
        // 神通数据库支持PostgreSQL兼容的CREATE TABLE AS SELECT语法
        TableId tableId = new TableId("postgresql_table");
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql("SELECT id, name, created_at FROM users WHERE created_at > NOW() - INTERVAL '1 month'");
        
        CreateTableAsSelect ctas = new CreateTableAsSelect(tableId, queryStmt, null, false, null);
        String result = generator.generate(ctas);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"postgresql_table\""));
        assertTrue(result.contains("AS"));
        assertTrue(result.contains("SELECT id, name, created_at"));
        assertTrue(result.contains("INTERVAL"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试神通数据库PLOSCAR语言兼容的CTAS")
    public void testShentongPLOSCARCompatibility() {
        // 神通数据库支持PLOSCAR语言和PostgreSQL兼容语法
        TableId tableId = new TableId("ploscar_table");
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql("SELECT product_id, SUM(quantity) as total_qty FROM sales GROUP BY product_id");
        
        CreateTableAsSelect ctas = new CreateTableAsSelect(tableId, queryStmt, null, false, null);
        String result = generator.generate(ctas);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"ploscar_table\""));
        assertTrue(result.contains("AS"));
        assertTrue(result.contains("SELECT product_id, SUM(quantity)"));
        assertTrue(result.contains("GROUP BY"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试带反引号的CREATE TABLE AS SELECT语句")
    public void testCreateTableAsSelectWithBackticks() {
        TableId tableId = new TableId("user_backup");
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql("SELECT `id`, `name`, `email` FROM `users` WHERE `status` = 'active'");
        
        CreateTableAsSelect ctas = new CreateTableAsSelect(tableId, queryStmt, null, false, null);
        String result = generator.generate(ctas);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"user_backup\""));
        assertTrue(result.contains("AS"));
        assertTrue(result.contains("SELECT \"id\", \"name\", \"email\" FROM \"users\""));
        assertFalse(result.contains("`")); // 确保反引号被转换为双引号
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试神通数据库官方文档示例")
    public void testShentongOfficialExample() {
        // 根据神通数据库官方文档的CREATE TABLE AS SELECT示例
        TableId tableId = new TableId("analytics_summary");
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql("SELECT department, COUNT(*) as employee_count, AVG(salary) as avg_salary " +
                         "FROM employees WHERE hire_date >= '2023-01-01' " +
                         "GROUP BY department HAVING COUNT(*) > 5");
        
        CreateTableAsSelect ctas = new CreateTableAsSelect(tableId, queryStmt, null, false, null);
        String result = generator.generate(ctas);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"analytics_summary\""));
        assertTrue(result.contains("AS"));
        assertTrue(result.contains("SELECT department, COUNT(*)"));
        assertTrue(result.contains("GROUP BY"));
        assertTrue(result.contains("HAVING"));
        assertTrue(result.endsWith(";"));
        
        // 验证生成的SQL格式正确
        assertTrue(result.startsWith("CREATE TABLE"));
        assertTrue(result.contains(" AS "));
    }
}
