package com.xylink.sqltranspiler.unit.dialects.shentong;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.shared.base.BaseShentongConversionTest;

/**
 * 神通数据库字符集支持测试
 * 基于神通数据库官方文档第2.1节字符集规范
 * 神通数据库字符集包括：
 * - 英文大小写字母
 * - ASCII码为128-255的字符
 * 参考文档：神通数据库SQL参考手册 第2.1节
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保方言功能符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试方言特有功能
 *
 * <AUTHOR>
 */
@DisplayName("神通数据库字符集测试")
public class ShentongCharacterSetTest extends BaseShentongConversionTest {

    /**
     * 测试基本ASCII字符支持
     * 根据文档：神通数据库支持英文大小写字母
     */
    @Test
    @DisplayName("测试基本ASCII字符支持")
    public void testBasicAsciiCharacters() throws Exception {
        String mysqlSql = """
            CREATE TABLE ascii_test (
                lowercase_col VARCHAR(50) DEFAULT 'abcdefghijklmnopqrstuvwxyz',
                uppercase_col VARCHAR(50) DEFAULT 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
                numbers_col VARCHAR(20) DEFAULT '0123456789'
            );
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);

        // 根据神通官方文档，SQL语句必须以分号结尾
        assertTrue(shentongSql.trim().endsWith(";"),
                   "根据神通官方文档第144行，SQL语句必须以分号结尾: " + shentongSql);
        
        // 验证字符集支持
        assertTrue(shentongSql.contains("CREATE TABLE"), "应支持CREATE TABLE语句");
        assertTrue(shentongSql.contains("\"ascii_test\""), "表名应正确转换");
        assertTrue(shentongSql.contains("abcdefghijklmnopqrstuvwxyz"), "应支持小写字母");
        assertTrue(shentongSql.contains("ABCDEFGHIJKLMNOPQRSTUVWXYZ"), "应支持大写字母");
        assertTrue(shentongSql.contains("0123456789"), "应支持数字字符");
    }

    /**
     * 测试扩展ASCII字符支持（128-255）
     * 根据文档：神通数据库的字符集包括ASCII码为128-255的字符
     */
    @Test
    @DisplayName("测试扩展ASCII字符支持（128-255）")
    public void testExtendedAsciiCharacters() throws Exception {
        // 构造包含扩展ASCII字符的SQL
        String mysqlSql = """
            CREATE TABLE extended_ascii_test (
                id INT PRIMARY KEY,
                extended_char_col VARCHAR(100) DEFAULT 'àáâãäåæçèéêë',
                symbol_col VARCHAR(50) DEFAULT '©®™±×÷'
            );
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证扩展ASCII字符支持
        assertTrue(shentongSql.contains("CREATE TABLE"), "应支持CREATE TABLE语句");
        assertTrue(shentongSql.contains("\"extended_ascii_test\""), "表名应正确转换");
        assertTrue(shentongSql.contains("àáâãäåæçèéêë"), "应支持扩展ASCII字符");
        assertTrue(shentongSql.contains("©®™±×÷"), "应支持特殊符号字符");
    }

    /**
     * 测试字符集在标识符中的使用
     * 根据文档：标识符可以包含神通数据库字符集中的字符
     */
    @Test
    @DisplayName("测试字符集在标识符中的使用")
    public void testCharacterSetInIdentifiers() throws Exception {
        String mysqlSql = """
            CREATE TABLE `测试表` (
                `用户名` VARCHAR(100),
                `邮箱地址` VARCHAR(200),
                `创建时间` TIMESTAMP
            );
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证中文字符在标识符中的支持
        assertTrue(shentongSql.contains("CREATE TABLE"), "应支持CREATE TABLE语句");
        assertTrue(shentongSql.contains("\"测试表\""), "应支持中文表名");
        assertTrue(shentongSql.contains("\"用户名\""), "应支持中文列名");
        assertTrue(shentongSql.contains("\"邮箱地址\""), "应支持中文列名");
        assertTrue(shentongSql.contains("\"创建时间\""), "应支持中文列名");
    }

    /**
     * 测试字符集在字符串常量中的使用
     * 验证神通数据库对各种字符编码的支持
     */
    @Test
    @DisplayName("测试字符集在字符串常量中的使用")
    public void testCharacterSetInStringLiterals() throws Exception {
        String mysqlSql = """
            INSERT INTO user_info (name, description, notes) VALUES 
            ('张三', '这是一个测试用户', 'Special chars: àáâãäåæçèéêë'),
            ('李四', '包含特殊符号：©®™±×÷', 'Mixed: ABC123中文测试');
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证字符串常量中的字符集支持
        assertTrue(shentongSql.contains("INSERT INTO"), "应支持INSERT语句");
        assertTrue(shentongSql.contains("张三"), "应支持中文字符串");
        assertTrue(shentongSql.contains("李四"), "应支持中文字符串");
        assertTrue(shentongSql.contains("这是一个测试用户"), "应支持中文描述");
        assertTrue(shentongSql.contains("àáâãäåæçèéêë"), "应支持扩展ASCII字符");
        assertTrue(shentongSql.contains("©®™±×÷"), "应支持特殊符号");
        assertTrue(shentongSql.contains("ABC123中文测试"), "应支持混合字符集");
    }

    /**
     * 测试字符集边界情况
     * 验证字符集处理的边界条件
     */
    @Test
    @DisplayName("测试字符集边界情况")
    public void testCharacterSetBoundaryConditions() throws Exception {
        String mysqlSql = """
            CREATE TABLE boundary_test (
                empty_string VARCHAR(10) DEFAULT '',
                single_char VARCHAR(1) DEFAULT 'A',
                max_ascii VARCHAR(10) DEFAULT 'ÿ',
                mixed_boundary VARCHAR(100) DEFAULT 'Start中文End'
            );
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证边界情况处理
        assertTrue(shentongSql.contains("CREATE TABLE"), "应支持CREATE TABLE语句");
        assertTrue(shentongSql.contains("\"boundary_test\""), "表名应正确转换");
        assertTrue(shentongSql.contains("DEFAULT ''"), "应支持空字符串");
        assertTrue(shentongSql.contains("DEFAULT 'A'"), "应支持单字符");
        assertTrue(shentongSql.contains("ÿ"), "应支持最大ASCII字符");
        assertTrue(shentongSql.contains("Start中文End"), "应支持混合字符边界");
    }

    /**
     * 测试字符集编码一致性
     * 确保转换过程中字符编码保持一致
     */
    @Test
    @DisplayName("测试字符集编码一致性")
    public void testCharacterSetEncodingConsistency() throws Exception {
        String mysqlSql = """
            SELECT 
                '中文测试' as chinese_text,
                'English Test' as english_text,
                'àáâãäåæçèéêë' as extended_ascii,
                '©®™±×÷' as symbols,
                'Mixed: 中文English123©' as mixed_text
            FROM dual;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证字符编码一致性
        assertTrue(shentongSql.contains("SELECT"), "应支持SELECT语句");
        assertTrue(shentongSql.contains("中文测试"), "中文字符应保持不变");
        assertTrue(shentongSql.contains("English Test"), "英文字符应保持不变");
        assertTrue(shentongSql.contains("àáâãäåæçèéêë"), "扩展ASCII字符应保持不变");
        assertTrue(shentongSql.contains("©®™±×÷"), "特殊符号应保持不变");
        assertTrue(shentongSql.contains("Mixed: 中文English123©"), "混合字符应保持不变");
        
        // 验证FROM dual的转换（神通数据库可能需要特殊处理）
        assertTrue(shentongSql.contains("FROM") || shentongSql.contains("from"), "应包含FROM子句");
    }

    /**
     * 测试字符集在注释中的使用
     * 根据文档：注释可以包含任意字符
     */
    @Test
    @DisplayName("测试字符集在注释中的使用")
    public void testCharacterSetInComments() throws Exception {
        String mysqlSql = """
            CREATE TABLE comment_test (
                id INT PRIMARY KEY COMMENT '主键标识符',
                name VARCHAR(100) COMMENT '用户姓名：支持中文、English、àáâãäåæçèéêë',
                status INT COMMENT '状态码：©®™±×÷'
            );
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证注释中的字符集支持
        assertTrue(shentongSql.contains("CREATE TABLE"), "应支持CREATE TABLE语句");
        assertTrue(shentongSql.contains("\"comment_test\""), "表名应正确转换");
        
        // 注意：神通数据库的注释处理可能与MySQL不同，需要根据实际实现调整
        // 这里主要验证字符集在注释中不会导致转换失败
        assertFalse(shentongSql.isEmpty(), "转换结果不应为空");
        assertTrue(shentongSql.contains("PRIMARY KEY"), "应包含主键约束");
    }
}
