package com.xylink.sqltranspiler.unit.dialects;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.dialects.DamengDialect;
import com.xylink.sqltranspiler.core.dialects.SqlDialect;

/**
 * SQL方言抽象测试
 * 验证借鉴Apache Calcite设计思想的方言抽象是否正常工作
 *
 * 测试原则：严格基于各数据库官方文档进行方言功能验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证方言功能的正确性
 * 2. 确保数据类型映射符合官方文档规范
 * 3. 验证分页、自增、函数映射等核心功能基于官方文档
 * 4. 测试标识符引用规则符合各数据库官方规范
 *
 * <AUTHOR>
 */
public class SqlDialectTest {
    
    @Test
    public void testDamengDialectBasicFunctionality() {
        SqlDialect dialect = new DamengDialect();
        
        // 测试基本信息
        assertEquals("DM", dialect.getName());
        assertEquals("DM Database", dialect.getDatabaseProduct());
        
        // 测试标识符引用
        assertEquals("normal_table", dialect.quoteIdentifier("normal_table"));
        assertEquals("\"SELECT\"", dialect.quoteIdentifier("SELECT")); // 保留字需要引用
        
        // 测试字符串引用
        assertEquals("'test'", dialect.quoteLiteral("test"));
        assertEquals("NULL", dialect.quoteLiteral(null));
        
        // 测试数据类型映射
        assertEquals("CLOB", dialect.mapDataType("TEXT", null, null, null));
        assertEquals("VARCHAR(255)", dialect.mapDataType("VARCHAR", 255, null, null));
        assertEquals("DECIMAL(10,2)", dialect.mapDataType("DECIMAL", null, 10, 2));
        
        // 测试分页支持 - 根据达梦官方文档，达梦数据库原生支持LIMIT语法
        assertTrue(dialect.supportsLimit()); // 达梦原生支持LIMIT
        assertTrue(dialect.supportsOffset()); // 达梦原生支持OFFSET
        // 根据达梦官方文档，达梦数据库原生支持LIMIT语法
        assertEquals("LIMIT 10", dialect.formatPagination(10, null));
        assertEquals("LIMIT 10 OFFSET 5", dialect.formatPagination(10, 5));
        
        // 测试自增支持
        assertTrue(dialect.supportsAutoIncrement());
        assertEquals("IDENTITY(1,1)", dialect.getAutoIncrementSyntax());
        
        // 测试函数映射
        assertEquals("SYSDATE", dialect.mapFunction("NOW"));
        assertEquals("CONCAT(a, b)", dialect.mapFunction("CONCAT", "a", "b"));
        assertEquals("LENGTH(str)", dialect.mapFunction("LENGTH", "str"));
        
        // 测试约束支持
        assertTrue(dialect.supportsForeignKey());
        assertTrue(dialect.supportsCheckConstraint());
        assertEquals("PRIMARY KEY (id)", dialect.formatPrimaryKey("id"));
        assertEquals("UNIQUE (email, phone)", dialect.formatUniqueConstraint("email", "phone"));
        
        // 测试默认方法
        assertEquals("SYSDATE", dialect.getCurrentTimestampFunction());
        assertEquals("TRUNC(SYSDATE)", dialect.getCurrentDateFunction());
        assertEquals("||", dialect.getConcatOperator());
        assertFalse(dialect.isCaseSensitive());
    }
    
    @Test
    public void testDataTypeMappingEdgeCases() {
        SqlDialect dialect = new DamengDialect();
        
        // 测试null输入
        assertEquals("VARCHAR(255)", dialect.mapDataType(null, null, null, null));
        
        // 测试未知类型
        assertEquals("UNKNOWN_TYPE", dialect.mapDataType("UNKNOWN_TYPE", null, null, null));
        
        // 测试大小写不敏感
        assertEquals("CLOB", dialect.mapDataType("text", null, null, null));
        assertEquals("CLOB", dialect.mapDataType("TEXT", null, null, null));
        assertEquals("CLOB", dialect.mapDataType("Text", null, null, null));
    }
    
    @Test
    public void testFunctionMappingEdgeCases() {
        SqlDialect dialect = new DamengDialect();
        
        // 测试null输入
        assertEquals("", dialect.mapFunction(null));
        
        // 测试无参数函数
        assertEquals("SYSDATE", dialect.mapFunction("NOW"));
        
        // 测试多参数函数
        assertEquals("CONCAT(a, b, c)", dialect.mapFunction("CONCAT", "a", "b", "c"));
        
        // 测试未映射的函数
        assertEquals("CUSTOM_FUNC(arg1)", dialect.mapFunction("CUSTOM_FUNC", "arg1"));
        
        // 测试函数支持检查
        assertTrue(dialect.supportsFunction("NOW"));
        assertTrue(dialect.supportsFunction("CONCAT"));
        assertFalse(dialect.supportsFunction("UNKNOWN_FUNC"));
    }
    
    @Test
    public void testIdentifierQuotingRules() {
        SqlDialect dialect = new DamengDialect();
        
        // 普通标识符不需要引用
        assertFalse(dialect.requiresQuoting("normal_table"));
        assertFalse(dialect.requiresQuoting("user_id"));
        assertFalse(dialect.requiresQuoting("table123"));
        
        // 保留字需要引用
        assertTrue(dialect.requiresQuoting("SELECT"));
        assertTrue(dialect.requiresQuoting("FROM"));
        assertTrue(dialect.requiresQuoting("WHERE"));
        
        // 特殊字符需要引用
        assertTrue(dialect.requiresQuoting("table-name"));
        assertTrue(dialect.requiresQuoting("table name"));
        assertTrue(dialect.requiresQuoting("table.name"));
        
        // 以数字开头需要引用
        assertTrue(dialect.requiresQuoting("123table"));
        
        // 空字符串和null不需要引用
        assertFalse(dialect.requiresQuoting(""));
        assertFalse(dialect.requiresQuoting(null));
    }
    
    @Test
    public void testPaginationFormatting() {
        SqlDialect dialect = new DamengDialect();

        // 根据达梦官方文档，达梦数据库原生支持LIMIT语法
        // 参考：https://eco.dameng.com/community/training/fb9112374fe523839a412dd30104f615
        // 达梦官方文档明确显示：分页查询支持LIMIT语法，兼容两者语法

        // 只有LIMIT
        assertEquals("LIMIT 10", dialect.formatPagination(10, null));
        assertEquals("LIMIT 100", dialect.formatPagination(100, null));

        // LIMIT + OFFSET
        assertEquals("LIMIT 10 OFFSET 10", dialect.formatPagination(10, 10));
        assertEquals("LIMIT 50 OFFSET 50", dialect.formatPagination(50, 50));

        // 边界情况
        assertEquals("", dialect.formatPagination(null, null));
        assertEquals("", dialect.formatPagination(null, 10));
        assertEquals("LIMIT 0", dialect.formatPagination(0, null));
        // 根据达梦官方文档，当offset为0时，标准做法是省略OFFSET子句
        // 参考：https://blog.csdn.net/sinat_32856657/article/details/127674659
        assertEquals("LIMIT 0", dialect.formatPagination(0, 0)); // offset=0时省略OFFSET子句
    }
    
    @Test
    public void testConstraintFormatting() {
        SqlDialect dialect = new DamengDialect();
        
        // 主键约束
        assertEquals("PRIMARY KEY (id)", dialect.formatPrimaryKey("id"));
        assertEquals("PRIMARY KEY (user_id, tenant_id)", dialect.formatPrimaryKey("user_id", "tenant_id"));
        assertEquals("", dialect.formatPrimaryKey()); // 空参数
        
        // 唯一约束
        assertEquals("UNIQUE (email)", dialect.formatUniqueConstraint("email"));
        assertEquals("UNIQUE (email, phone)", dialect.formatUniqueConstraint("email", "phone"));
        assertEquals("", dialect.formatUniqueConstraint()); // 空参数
    }
}
