package com.xylink.sqltranspiler.unit.dialects.dameng;

import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 达梦数据库二进制数据测试
 * 根据达梦官方文档，测试MySQL二进制数据类型到达梦的转换
 * 参考：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保方言功能符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试方言特有功能
 *
 * <AUTHOR>
 */
@DisplayName("达梦数据库二进制数据测试")
public class DamengBinaryDataTest {

    private DamengGenerator generator;

    @BeforeEach
    void setUp() {
        generator = new DamengGenerator();
    }

    private CreateTable parseCreateTable(String sql) {
        return (CreateTable) MySqlHelper.parseStatement(sql);
    }

    @Test
    @DisplayName("测试基本二进制数据类型转换")
    void testBasicBinaryDataTypes() {
        String mysqlSql = "CREATE TABLE binary_basic (" +
                "id INT PRIMARY KEY, " +
                "binary_data BINARY(16), " +
                "varbinary_data VARBINARY(255), " +
                "bit_data BIT(8)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("基本二进制数据类型转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证二进制数据类型转换
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("binary_basic"), "应保留表名");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("BINARY(16)"), "应保留BINARY类型");
        assertTrue(damengSql.contains("VARBINARY(255)"), "应保留VARBINARY类型");
        assertTrue(damengSql.contains("BIT(8)"), "应保留BIT类型");
    }

    @Test
    @DisplayName("测试BLOB数据类型转换")
    void testBlobDataTypes() {
        String mysqlSql = "CREATE TABLE blob_types (" +
                "id INT PRIMARY KEY, " +
                "tiny_blob TINYBLOB, " +
                "blob_data BLOB, " +
                "medium_blob MEDIUMBLOB, " +
                "long_blob LONGBLOB" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("BLOB数据类型转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证BLOB类型转换
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("blob_types"), "应保留表名");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("BLOB"), "应转换或保留BLOB类型");
        
        // 验证各种BLOB类型的处理
        assertTrue(damengSql.contains("tiny_blob"), "应保留tiny_blob列名");
        assertTrue(damengSql.contains("blob_data"), "应保留blob_data列名");
        assertTrue(damengSql.contains("medium_blob"), "应保留medium_blob列名");
        assertTrue(damengSql.contains("long_blob"), "应保留long_blob列名");
    }

    @Test
    @DisplayName("测试二进制数据与约束的组合")
    void testBinaryDataWithConstraints() {
        String mysqlSql = "CREATE TABLE binary_constraints (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "file_hash BINARY(32) NOT NULL UNIQUE, " +
                "file_data LONGBLOB, " +
                "file_size BIGINT NOT NULL, " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("二进制数据与约束组合转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证二进制数据与约束的组合
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("binary_constraints"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("BINARY(32)"), "应保留BINARY类型");
        assertTrue(damengSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(damengSql.contains("UNIQUE"), "应保留UNIQUE约束");
        assertTrue(damengSql.contains("BLOB"), "应转换或保留BLOB类型");
        assertTrue(damengSql.contains("BIGINT"), "应保留BIGINT类型");
        assertTrue(damengSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(damengSql.contains("DEFAULT"), "应保留默认值");
    }

    @Test
    @DisplayName("测试二进制数据与索引的组合")
    void testBinaryDataWithIndexes() {
        String mysqlSql = "CREATE TABLE binary_indexes (" +
                "id INT PRIMARY KEY, " +
                "checksum BINARY(16) NOT NULL, " +
                "content MEDIUMBLOB, " +
                "metadata VARBINARY(1024), " +
                "INDEX idx_checksum (checksum)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("二进制数据与索引组合转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证二进制数据与索引的组合
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("binary_indexes"), "应保留表名");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("BINARY(16)"), "应保留BINARY类型");
        assertTrue(damengSql.contains("BLOB"), "应转换或保留BLOB类型");
        assertTrue(damengSql.contains("VARBINARY(1024)"), "应保留VARBINARY类型");
        assertTrue(damengSql.contains("NOT NULL"), "应保留NOT NULL约束");
        
        // 验证索引处理（达梦可能在单独的语句中处理索引）
        assertTrue(damengSql.contains("idx_checksum") || damengSql.contains("checksum"), 
                  "应处理checksum索引");
    }

    @Test
    @DisplayName("测试大型二进制数据处理")
    void testLargeBinaryData() {
        String mysqlSql = "CREATE TABLE large_binary (" +
                "id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
                "document LONGBLOB, " +
                "thumbnail MEDIUMBLOB, " +
                "signature VARBINARY(512), " +
                "document_size BIGINT, " +
                "mime_type VARCHAR(100)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("大型二进制数据处理结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证大型二进制数据处理
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("large_binary"), "应保留表名");
        assertTrue(damengSql.contains("BIGINT"), "应保留BIGINT类型");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("BLOB"), "应转换或保留BLOB类型");
        assertTrue(damengSql.contains("VARBINARY(512)"), "应保留VARBINARY类型");
        assertTrue(damengSql.contains("document"), "应保留document列名");
        assertTrue(damengSql.contains("thumbnail"), "应保留thumbnail列名");
        assertTrue(damengSql.contains("signature"), "应保留signature列名");
        assertTrue(damengSql.contains("document_size"), "应保留document_size列名");
        assertTrue(damengSql.contains("mime_type"), "应保留mime_type列名");
    }

    @Test
    @DisplayName("测试二进制数据的默认值处理")
    void testBinaryDataDefaultValues() {
        String mysqlSql = "CREATE TABLE binary_defaults (" +
                "id INT PRIMARY KEY, " +
                "status_flag BIT(1) DEFAULT b'0', " +
                "empty_binary BINARY(8) DEFAULT 0x0000000000000000, " +
                "config_data VARBINARY(256)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("二进制数据默认值处理结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证二进制数据默认值处理
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("binary_defaults"), "应保留表名");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("BIT(1)"), "应保留BIT类型");
        assertTrue(damengSql.contains("BINARY(8)"), "应保留BINARY类型");
        assertTrue(damengSql.contains("VARBINARY(256)"), "应保留VARBINARY类型");
        assertTrue(damengSql.contains("status_flag"), "应保留status_flag列名");
        assertTrue(damengSql.contains("empty_binary"), "应保留empty_binary列名");
        assertTrue(damengSql.contains("config_data"), "应保留config_data列名");
        
        // 验证默认值处理（达梦可能有不同的二进制字面量语法）
        assertTrue(damengSql.contains("DEFAULT") || damengSql.contains("status_flag"), 
                  "应处理默认值");
    }

    @Test
    @DisplayName("测试二进制数据在分区表中的应用")
    void testBinaryDataInPartitionedTable() {
        String mysqlSql = "CREATE TABLE binary_partitioned (" +
                "id BIGINT AUTO_INCREMENT, " +
                "partition_key DATE NOT NULL, " +
                "file_data LONGBLOB, " +
                "file_hash BINARY(32), " +
                "PRIMARY KEY (id, partition_key)" +
                ") PARTITION BY RANGE (YEAR(partition_key)) (" +
                "PARTITION p2020 VALUES LESS THAN (2021), " +
                "PARTITION p2021 VALUES LESS THAN (2022), " +
                "PARTITION p2022 VALUES LESS THAN (2023)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("分区表中二进制数据应用结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证分区表中的二进制数据应用
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("binary_partitioned"), "应保留表名");
        assertTrue(damengSql.contains("BIGINT"), "应保留BIGINT类型");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("DATE"), "应保留DATE类型");
        assertTrue(damengSql.contains("BLOB"), "应转换或保留BLOB类型");
        assertTrue(damengSql.contains("BINARY(32)"), "应保留BINARY类型");
        assertTrue(damengSql.contains("partition_key"), "应保留分区键");
        
        // 验证分区处理（达梦支持分区表）
        assertTrue(damengSql.contains("PARTITION") || damengSql.contains("binary_partitioned"), 
                  "应处理分区定义");
    }

    @Test
    @DisplayName("测试达梦特有的二进制数据特性")
    void testDamengSpecificBinaryFeatures() {
        String mysqlSql = "CREATE TABLE dameng_binary_features (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "raw_data RAW(2000), " +
                "long_raw LONG RAW, " +
                "blob_data BLOB, " +
                "clob_data CLOB" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("达梦特有二进制数据特性:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证达梦特有的二进制数据特性
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("dameng_binary_features"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        
        // 验证达梦特有类型处理
        assertTrue(damengSql.contains("raw_data"), "应保留raw_data列名");
        assertTrue(damengSql.contains("long_raw"), "应保留long_raw列名");
        assertTrue(damengSql.contains("blob_data"), "应保留blob_data列名");
        assertTrue(damengSql.contains("clob_data"), "应保留clob_data列名");
        assertTrue(damengSql.contains("BLOB") || damengSql.contains("RAW"), 
                  "应处理二进制数据类型");
        assertTrue(damengSql.contains("CLOB"), "应保留CLOB类型");
    }

    @Test
    @DisplayName("测试二进制数据的完整性约束")
    void testBinaryDataIntegrityConstraints() {
        String mysqlSql = "CREATE TABLE binary_integrity (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "file_content LONGBLOB NOT NULL, " +
                "checksum BINARY(32) NOT NULL UNIQUE, " +
                "file_size BIGINT NOT NULL CHECK (file_size > 0), " +
                "upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "CONSTRAINT chk_content_size CHECK (LENGTH(file_content) = file_size)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("二进制数据完整性约束结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证二进制数据的完整性约束
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("binary_integrity"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("BLOB"), "应转换或保留BLOB类型");
        assertTrue(damengSql.contains("BINARY(32)"), "应保留BINARY类型");
        assertTrue(damengSql.contains("BIGINT"), "应保留BIGINT类型");
        assertTrue(damengSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(damengSql.contains("UNIQUE"), "应保留UNIQUE约束");
        assertTrue(damengSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(damengSql.contains("DEFAULT"), "应保留默认值");
        
        // 验证约束处理（达梦可能在单独的语句中处理复杂约束）
        assertTrue(damengSql.contains("CHECK") || damengSql.contains("file_size"), 
                  "应处理CHECK约束");
    }
}
