package com.xylink.sqltranspiler.unit.dialects;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator;
import com.xylink.sqltranspiler.core.dialects.shentong.ShentongGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SHOW语句转换测试
 * 根据各数据库官方文档验证SHOW语句的处理：
 * - MySQL: https://dev.mysql.com/doc/refman/8.4/en/show.html
 * - 达梦: 支持SHOW语句 (https://eco.dameng.com/community/post/20250427113255V8NEZD65AWKFN11N64)
 * - 金仓: 支持SHOW语句 (https://help.kingbase.com.cn/v8.6.8.14/PDF/KingbaseES常见问题手册.pdf)
 * - 神通: 支持SHOW语句，部分转换为系统视图查询
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保方言功能符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试方言特有功能
 *
 * <AUTHOR>
 */
@DisplayName("SHOW语句转换测试")
public class ShowStatementConversionTest {

    private static final Logger log = LoggerFactory.getLogger(ShowStatementConversionTest.class);

    private DamengGenerator damengGenerator;
    private KingbaseGenerator kingbaseGenerator;
    private ShentongGenerator shentongGenerator;

    @BeforeEach
    void setUp() {
        damengGenerator = new DamengGenerator();
        kingbaseGenerator = new KingbaseGenerator();
        shentongGenerator = new ShentongGenerator();
    }

    @Test
    @DisplayName("测试SHOW TABLES语句转换")
    void testShowTablesConversion() {
        String sql = "SHOW TABLES;";
        Statement statement = MySqlHelper.parseStatement(sql);
        
        String damengSql = damengGenerator.generate(statement);
        String kingbaseSql = kingbaseGenerator.generate(statement);
        String shentongSql = shentongGenerator.generate(statement);
        
        log.info("MySQL SQL: {}", sql);
        log.info("达梦转换结果: {}", damengSql);
        log.info("金仓转换结果: {}", kingbaseSql);
        log.info("神通转换结果: {}", shentongSql);
        
        // 验证转换结果
        assertNotNull(damengSql, "达梦转换结果不应为空");
        assertNotNull(kingbaseSql, "金仓转换结果不应为空");
        assertNotNull(shentongSql, "神通转换结果不应为空");
        
        // 达梦现在支持SHOW语句，应该直接转换
        assertTrue(damengSql.contains("SHOW TABLES"),
                  "达梦应该支持SHOW语句");
        
        // 金仓现在支持SHOW语句，应该直接转换
        assertTrue(kingbaseSql.contains("SHOW TABLES"),
                  "金仓应该支持SHOW语句");
        
        // 神通数据库：根据官方文档，部分SHOW语句保持原样，部分转换为SELECT查询
        assertTrue(shentongSql.contains("SHOW") || shentongSql.contains("SELECT"),
                  "神通应该保持SHOW语句或转换为SELECT查询");
    }

    @Test
    @DisplayName("测试SHOW DATABASES语句转换")
    void testShowDatabasesConversion() {
        String sql = "SHOW DATABASES;";
        Statement statement = MySqlHelper.parseStatement(sql);
        
        String damengSql = damengGenerator.generate(statement);
        String kingbaseSql = kingbaseGenerator.generate(statement);
        String shentongSql = shentongGenerator.generate(statement);
        
        log.info("MySQL SQL: {}", sql);
        log.info("达梦转换结果: {}", damengSql);
        log.info("金仓转换结果: {}", kingbaseSql);
        log.info("神通转换结果: {}", shentongSql);
        
        // 验证转换结果
        assertNotNull(damengSql, "达梦转换结果不应为空");
        assertNotNull(kingbaseSql, "金仓转换结果不应为空");
        assertNotNull(shentongSql, "神通转换结果不应为空");
        
        // 达梦现在支持SHOW语句，应该直接转换
        assertTrue(damengSql.contains("SHOW DATABASES"),
                  "达梦应该支持SHOW语句");
        assertTrue(kingbaseSql.contains("SHOW DATABASES"),
                  "金仓应该支持SHOW语句");
        assertTrue(shentongSql.contains("SHOW") || shentongSql.contains("SELECT"),
                  "神通应该保持SHOW语句或转换为SELECT查询");
    }

    @Test
    @DisplayName("测试SHOW COLUMNS语句转换")
    void testShowColumnsConversion() {
        String sql = "SHOW COLUMNS FROM users;";
        Statement statement = MySqlHelper.parseStatement(sql);
        
        String damengSql = damengGenerator.generate(statement);
        String kingbaseSql = kingbaseGenerator.generate(statement);
        String shentongSql = shentongGenerator.generate(statement);
        
        log.info("MySQL SQL: {}", sql);
        log.info("达梦转换结果: {}", damengSql);
        log.info("金仓转换结果: {}", kingbaseSql);
        log.info("神通转换结果: {}", shentongSql);
        
        // 验证转换结果包含表名
        assertTrue(damengSql.contains("users") || damengSql.contains("USERS"), 
                  "达梦转换结果应该包含表名");
        assertTrue(kingbaseSql.contains("users") || kingbaseSql.contains("USERS"), 
                  "金仓转换结果应该包含表名");
        assertTrue(shentongSql.contains("users") || shentongSql.contains("USERS"), 
                  "神通转换结果应该包含表名");
    }

    @Test
    @DisplayName("测试SHOW CREATE TABLE语句转换")
    void testShowCreateTableConversion() {
        String sql = "SHOW CREATE TABLE users;";
        Statement statement = MySqlHelper.parseStatement(sql);
        
        String damengSql = damengGenerator.generate(statement);
        String kingbaseSql = kingbaseGenerator.generate(statement);
        String shentongSql = shentongGenerator.generate(statement);
        
        log.info("MySQL SQL: {}", sql);
        log.info("达梦转换结果: {}", damengSql);
        log.info("金仓转换结果: {}", kingbaseSql);
        log.info("神通转换结果: {}", shentongSql);
        
        // 验证转换结果
        assertNotNull(damengSql, "达梦转换结果不应为空");
        assertNotNull(kingbaseSql, "金仓转换结果不应为空");
        assertNotNull(shentongSql, "神通转换结果不应为空");
        
        // 所有数据库都应该提供等效的查询或说明
        assertTrue(damengSql.contains("users") || damengSql.contains("USERS"), 
                  "达梦转换结果应该包含表名");
        assertTrue(kingbaseSql.contains("users") || kingbaseSql.contains("USERS"), 
                  "金仓转换结果应该包含表名");
        assertTrue(shentongSql.contains("users") || shentongSql.contains("USERS"), 
                  "神通转换结果应该包含表名");
    }

    @Test
    @DisplayName("测试SHOW INDEX语句转换")
    void testShowIndexConversion() {
        String sql = "SHOW INDEX FROM users;";
        Statement statement = MySqlHelper.parseStatement(sql);
        
        String damengSql = damengGenerator.generate(statement);
        String kingbaseSql = kingbaseGenerator.generate(statement);
        String shentongSql = shentongGenerator.generate(statement);
        
        log.info("MySQL SQL: {}", sql);
        log.info("达梦转换结果: {}", damengSql);
        log.info("金仓转换结果: {}", kingbaseSql);
        log.info("神通转换结果: {}", shentongSql);
        
        // 验证转换结果包含表名
        assertTrue(damengSql.contains("users") || damengSql.contains("USERS"), 
                  "达梦转换结果应该包含表名");
        assertTrue(kingbaseSql.contains("users") || kingbaseSql.contains("USERS"), 
                  "金仓转换结果应该包含表名");
        assertTrue(shentongSql.contains("users") || shentongSql.contains("USERS"), 
                  "神通转换结果应该包含表名");
    }

    @Test
    @DisplayName("验证SHOW语句转换的官方文档合规性")
    void testOfficialDocumentationCompliance() {
        String[] showSqls = {
            "SHOW TABLES;",
            "SHOW DATABASES;",
            "SHOW COLUMNS FROM users;",
            "SHOW INDEX FROM users;"
        };

        for (String sql : showSqls) {
            Statement statement = MySqlHelper.parseStatement(sql);
            
            // 达梦数据库：根据官方文档，支持SHOW语句（可能转换为等效查询）
            String damengSql = damengGenerator.generate(statement);
            assertTrue(damengSql.contains("SHOW") || damengSql.contains("SELECT"),
                      "达梦应该支持SHOW语句或转换为等效查询（符合官方文档）");

            // 金仓数据库：根据官方文档，支持SHOW语句（可能转换为等效查询）
            String kingbaseSql = kingbaseGenerator.generate(statement);
            assertTrue(kingbaseSql.contains("SHOW") || kingbaseSql.contains("SELECT"),
                      "金仓应该支持SHOW语句或转换为等效查询（符合官方文档）");
            
            // 神通数据库：根据官方文档，部分SHOW语句保持原样，部分转换为SELECT查询
            String shentongSql = shentongGenerator.generate(statement);
            assertTrue(shentongSql.contains("SHOW") || shentongSql.contains("SELECT"),
                      "神通应该保持SHOW语句或转换为SELECT查询（符合官方文档）");
        }
        
        log.info("✅ 所有数据库的SHOW语句处理都符合官方文档规范");
    }
}
