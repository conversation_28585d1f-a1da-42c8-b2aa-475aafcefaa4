package com.xylink.sqltranspiler.unit.dialects.shentong;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.shared.base.BaseShentongConversionTest;

/**
 * 神通数据库官方标准验证测试
 * 基于神通数据库官方文档验证转换结果是否符合神通数据库标准
 * 神通数据库基本支持SQL92的入门级和过渡级标准，并联系实际应用进行了大量扩展
 * 参考文档：神通数据库SQL参考手册
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保方言功能符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试方言特有功能
 *
 * <AUTHOR>
 */
@DisplayName("神通数据库官方标准验证测试")
public class ShentongOfficialStandardTest extends BaseShentongConversionTest {

    @Test
    @DisplayName("验证神通数据库字符集支持")
    void testShentongCharacterSetSupport() throws Exception {
        System.out.println("=== 神通数据库字符集支持验证 ===\n");
        
        String mysqlSql = "CREATE DATABASE testdb DEFAULT CHARSET=utf8mb4;";
        
        System.out.println("原始MySQL SQL:");
        System.out.println(mysqlSql);
        System.out.println();
        
        String shentongSql = convertMySqlToShentong(mysqlSql);
        System.out.println("神通转换结果:");
        System.out.println(shentongSql);
        
        // 验证字符集转换符合神通标准
        assertTrue(shentongSql.contains("CHARACTER SET UTF8"), "应使用神通支持的UTF8字符集");
        assertFalse(shentongSql.contains("utf8mb4"), "不应包含MySQL特有的utf8mb4");
        
        System.out.println("✅ 字符集转换符合神通标准");
        System.out.println();
    }

    @Test
    @DisplayName("验证神通数据库标识符规则")
    void testShentongIdentifierRules() throws Exception {
        System.out.println("=== 神通数据库标识符规则验证 ===\n");
        
        String mysqlSql = """
            CREATE TABLE `test_table` (
                `id` INT AUTO_INCREMENT PRIMARY KEY,
                `select` VARCHAR(50),
                `order` INT,
                `SYS_test` VARCHAR(100)
            );
            """;
        
        System.out.println("原始MySQL SQL:");
        System.out.println(mysqlSql);
        System.out.println();
        
        String shentongSql = convertMySqlToShentong(mysqlSql);
        System.out.println("神通转换结果:");
        System.out.println(shentongSql);
        
        // 验证标识符规则
        assertTrue(shentongSql.contains("\"test_table\""), "表名应使用双引号");
        assertTrue(shentongSql.contains("\"id\""), "列名应使用双引号");
        assertTrue(shentongSql.contains("\"select\""), "保留字应使用双引号");
        assertTrue(shentongSql.contains("\"order\""), "保留字应使用双引号");
        
        // 验证不包含反引号
        assertFalse(shentongSql.contains("`"), "不应包含MySQL的反引号");
        
        // 警告：SYS_开头的标识符可能与系统对象冲突
        if (shentongSql.contains("SYS_")) {
            System.out.println("⚠️  警告：SYS_开头的标识符可能与神通系统对象冲突");
        }
        
        System.out.println("✅ 标识符规则符合神通标准");
        System.out.println();
    }

    @Test
    @DisplayName("验证神通数据库数据类型支持")
    void testShentongDataTypeSupport() throws Exception {
        System.out.println("=== 神通数据库数据类型支持验证 ===\n");
        
        String mysqlSql = """
            CREATE TABLE data_types_test (
                tiny_col TINYINT,
                small_col SMALLINT,
                medium_col MEDIUMINT,
                int_col INT,
                big_col BIGINT,
                decimal_col DECIMAL(10,2),
                float_col FLOAT,
                double_col DOUBLE,
                char_col CHAR(10),
                varchar_col VARCHAR(255),
                text_col TEXT,
                date_col DATE,
                time_col TIME,
                datetime_col DATETIME,
                timestamp_col TIMESTAMP,
                year_col YEAR,
                binary_col BINARY(16),
                varbinary_col VARBINARY(255),
                blob_col BLOB
            );
            """;
        
        System.out.println("原始MySQL SQL:");
        System.out.println(mysqlSql);
        System.out.println();
        
        String shentongSql = convertMySqlToShentong(mysqlSql);
        System.out.println("神通转换结果:");
        System.out.println(shentongSql);
        
        // 验证数据类型转换符合神通标准
        assertTrue(shentongSql.contains("TINYINT"), "神通支持TINYINT");
        assertTrue(shentongSql.contains("SMALLINT"), "神通支持SMALLINT");
        assertTrue(shentongSql.contains("INT"), "MEDIUMINT应转换为INT");
        assertTrue(shentongSql.contains("BIGINT"), "神通支持BIGINT");
        assertTrue(shentongSql.contains("DECIMAL(10,2)"), "神通支持DECIMAL");
        assertTrue(shentongSql.contains("FLOAT"), "神通支持FLOAT");
        assertTrue(shentongSql.contains("DOUBLE"), "神通支持DOUBLE");
        assertTrue(shentongSql.contains("CHAR(10)"), "神通支持CHAR");
        assertTrue(shentongSql.contains("VARCHAR(255)"), "神通支持VARCHAR");
        assertTrue(shentongSql.contains("TEXT"), "神通支持TEXT");
        assertTrue(shentongSql.contains("DATE"), "神通支持DATE");
        assertTrue(shentongSql.contains("TIME"), "神通支持TIME");
        assertTrue(shentongSql.contains("TIMESTAMP"), "神通支持TIMESTAMP");
        assertTrue(shentongSql.contains("BINARY(16)"), "神通支持BINARY");
        assertTrue(shentongSql.contains("VARBINARY(255)"), "神通支持VARBINARY");
        assertTrue(shentongSql.contains("BLOB"), "神通支持BLOB");
        
        // 验证不支持的类型被正确转换
        assertFalse(shentongSql.contains("MEDIUMINT"), "MEDIUMINT应被转换");
        assertFalse(shentongSql.contains("YEAR"), "YEAR应被转换为SMALLINT");
        
        System.out.println("✅ 数据类型转换符合神通标准");
        System.out.println();
    }

    @Test
    @DisplayName("验证神通数据库AUTO_INCREMENT支持")
    void testShentongAutoIncrementSupport() throws Exception {
        System.out.println("=== 神通数据库AUTO_INCREMENT支持验证 ===\n");
        
        String mysqlSql = """
            CREATE TABLE auto_increment_test (
                id INT AUTO_INCREMENT PRIMARY KEY,
                big_id BIGINT AUTO_INCREMENT UNIQUE,
                float_id FLOAT AUTO_INCREMENT
            ) AUTO_INCREMENT = 100;
            """;
        
        System.out.println("原始MySQL SQL:");
        System.out.println(mysqlSql);
        System.out.println();
        
        String shentongSql = convertMySqlToShentong(mysqlSql);
        System.out.println("神通转换结果:");
        System.out.println(shentongSql);
        
        // 验证AUTO_INCREMENT支持
        assertTrue(shentongSql.contains("AUTO_INCREMENT"), "神通应支持AUTO_INCREMENT");
        assertTrue(shentongSql.contains("PRIMARY KEY"), "应支持主键约束");
        assertTrue(shentongSql.contains("UNIQUE"), "应支持唯一约束");
        
        // 验证支持的数据类型
        assertTrue(shentongSql.contains("INT AUTO_INCREMENT"), "INT类型应支持AUTO_INCREMENT");
        assertTrue(shentongSql.contains("BIGINT AUTO_INCREMENT"), "BIGINT类型应支持AUTO_INCREMENT");
        assertTrue(shentongSql.contains("FLOAT AUTO_INCREMENT"), "FLOAT类型应支持AUTO_INCREMENT");
        
        System.out.println("✅ AUTO_INCREMENT支持符合神通标准");
        System.out.println();
    }

    @Test
    @DisplayName("验证神通数据库序列类型支持")
    void testShentongSerialTypeSupport() throws Exception {
        System.out.println("=== 神通数据库序列类型支持验证 ===\n");
        
        String mysqlSql = """
            CREATE TABLE serial_test (
                id INT AUTO_INCREMENT PRIMARY KEY,
                big_id BIGINT AUTO_INCREMENT UNIQUE,
                name VARCHAR(100)
            );
            """;
        
        System.out.println("原始MySQL SQL:");
        System.out.println(mysqlSql);
        System.out.println();
        
        String shentongSql = convertMySqlToShentong(mysqlSql);
        System.out.println("神通转换结果:");
        System.out.println(shentongSql);
        
        // 验证序列类型支持 - 根据神通官方文档，神通完全支持AUTO_INCREMENT语法
        assertTrue(shentongSql.contains("AUTO_INCREMENT") || shentongSql.contains("SERIAL"),
                   "神通应支持AUTO_INCREMENT语法或转换为SERIAL类型");
        assertTrue(shentongSql.contains("AUTO_INCREMENT") || shentongSql.contains("BIGSERIAL"),
                   "神通应支持BIGINT AUTO_INCREMENT语法或转换为BIGSERIAL类型");
        
        System.out.println("✅ 序列类型支持符合神通标准");
        System.out.println();
    }

    @Test
    @DisplayName("验证神通数据库精度支持")
    void testShentongPrecisionSupport() throws Exception {
        System.out.println("=== 神通数据库精度支持验证 ===\n");
        
        String mysqlSql = """
            CREATE TABLE precision_test (
                max_precision NUMERIC(1000,500),
                normal_precision NUMERIC(18,2),
                small_precision NUMERIC(5,2)
            );
            """;
        
        System.out.println("原始MySQL SQL:");
        System.out.println(mysqlSql);
        System.out.println();
        
        String shentongSql = convertMySqlToShentong(mysqlSql);
        System.out.println("神通转换结果:");
        System.out.println(shentongSql);
        
        // 验证精度支持
        assertTrue(shentongSql.contains("NUMERIC(1000,500)"), "神通应支持最大精度1000");
        assertTrue(shentongSql.contains("NUMERIC(18,2)"), "神通应支持默认精度18");
        assertTrue(shentongSql.contains("NUMERIC(5,2)"), "神通应支持小精度");
        
        System.out.println("✅ 精度支持符合神通标准");
        System.out.println();
    }

    @Test
    @DisplayName("验证神通数据库函数支持")
    void testShentongFunctionSupport() throws Exception {
        System.out.println("=== 神通数据库函数支持验证 ===\n");
        
        String mysqlSql = """
            SELECT 
                IFNULL(name, 'Unknown') as display_name,
                CONCAT(first_name, ' ', last_name) as full_name,
                CHAR_LENGTH(description) as desc_length,
                CURRENT_DATE as today,
                CURRENT_TIME as now_time,
                CURRENT_TIMESTAMP as now_timestamp,
                LAST_INSERT_ID() as last_id
            FROM users;
            """;
        
        System.out.println("原始MySQL SQL:");
        System.out.println(mysqlSql);
        System.out.println();
        
        String shentongSql = convertMySqlToShentong(mysqlSql);
        System.out.println("神通转换结果:");
        System.out.println(shentongSql);
        
        // 验证函数支持
        assertTrue(shentongSql.contains("IFNULL"), "神通应支持IFNULL函数");
        assertTrue(shentongSql.contains("CONCAT"), "神通应支持CONCAT函数");
        assertTrue(shentongSql.contains("CHAR_LENGTH"), "神通应支持CHAR_LENGTH函数");
        assertTrue(shentongSql.contains("CURRENT_DATE"), "神通应支持CURRENT_DATE");
        assertTrue(shentongSql.contains("CURRENT_TIME"), "神通应支持CURRENT_TIME");
        assertTrue(shentongSql.contains("CURRENT_TIMESTAMP"), "神通应支持CURRENT_TIMESTAMP");
        assertTrue(shentongSql.contains("LAST_INSERT_ID"), "神通应支持LAST_INSERT_ID函数");
        
        System.out.println("✅ 函数支持符合神通标准");
        System.out.println();
    }

    @Test
    @DisplayName("验证神通数据库伪列支持")
    void testShentongPseudoColumnSupport() throws Exception {
        System.out.println("=== 神通数据库伪列支持验证 ===\n");
        
        String mysqlSql = """
            SELECT
                name,
                email,
                @row_number := @row_number + 1 AS row_num
            FROM users, (SELECT @row_number := 0) AS r
            ORDER BY name
            LIMIT 10;
            """;

        System.out.println("原始MySQL SQL:");
        System.out.println(mysqlSql);
        System.out.println();

        String shentongSql = convertMySqlToShentong(mysqlSql);
        System.out.println("神通转换结果:");
        System.out.println(shentongSql);

        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);

        // 验证伪列支持 - 神通数据库支持ROWNUM、ROWID等伪列，但不会自动添加
        // 这里主要验证转换过程没有出错，伪列功能在运行时可用
        assertTrue(shentongSql.contains("SELECT"), "应包含SELECT语句");
        assertTrue(shentongSql.contains("FROM"), "应包含FROM子句");
        
        System.out.println("✅ 伪列相关转换符合神通标准");
        System.out.println();
    }

    @Test
    @DisplayName("验证神通数据库分页查询支持")
    void testShentongPaginationSupport() throws Exception {
        System.out.println("=== 神通数据库分页查询支持验证 ===\n");
        
        String mysqlSql = """
            SELECT * FROM users
            ORDER BY created_at DESC
            LIMIT 10 OFFSET 20;
            """;
        
        System.out.println("原始MySQL SQL:");
        System.out.println(mysqlSql);
        System.out.println();
        
        String shentongSql = convertMySqlToShentong(mysqlSql);
        System.out.println("神通转换结果:");
        System.out.println(shentongSql);
        
        // 验证分页查询支持
        assertTrue(shentongSql.contains("ROWNUM"), "应支持ROWNUM分页");
        assertTrue(shentongSql.contains("BETWEEN"), "应支持BETWEEN操作符");
        assertTrue(shentongSql.contains("ORDER BY"), "应支持ORDER BY");
        
        System.out.println("✅ 分页查询支持符合神通标准");
        System.out.println();
    }

    @Test
    @DisplayName("验证神通数据库注释语法支持")
    void testShentongCommentSyntaxSupport() throws Exception {
        System.out.println("=== 神通数据库注释语法支持验证 ===\n");
        
        String mysqlSql = """
            -- 这是单行注释
            CREATE TABLE comment_test (
                id INT AUTO_INCREMENT PRIMARY KEY, -- 主键ID
                /* 多行注释
                   用户姓名字段 */
                name VARCHAR(100) NOT NULL
            );
            """;
        
        System.out.println("原始MySQL SQL:");
        System.out.println(mysqlSql);
        System.out.println();
        
        String shentongSql = convertMySqlToShentong(mysqlSql);
        System.out.println("神通转换结果:");
        System.out.println(shentongSql);
        
        // 验证注释语法支持
        assertTrue(shentongSql.contains("--") || shentongSql.contains("/*"),
                   "应支持注释语法");
        
        System.out.println("✅ 注释语法支持符合神通标准");
        System.out.println();
    }

    @Test
    @DisplayName("验证神通数据库综合标准符合性")
    void testShentongOverallStandardCompliance() throws Exception {
        System.out.println("=== 神通数据库综合标准符合性验证 ===\n");
        
        String mysqlSql = """
            CREATE TABLE comprehensive_test (
                id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
                name VARCHAR(100) NOT NULL COMMENT '姓名',
                email VARCHAR(255) UNIQUE COMMENT '邮箱',
                age TINYINT DEFAULT 0 COMMENT '年龄',
                salary DECIMAL(10,2) COMMENT '薪资',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='综合测试表';
            """;
        
        System.out.println("原始MySQL SQL:");
        System.out.println(mysqlSql);
        System.out.println();
        
        String shentongSql = convertMySqlToShentong(mysqlSql);
        System.out.println("神通转换结果:");
        System.out.println(shentongSql);
        
        // 验证综合标准符合性
        assertBasicConversionRequirements(shentongSql);
        assertShentongStandardCompliance(shentongSql);
        
        // 验证关键转换
        assertTrue(shentongSql.contains("AUTO_INCREMENT"), "应支持AUTO_INCREMENT");
        assertTrue(shentongSql.contains("PRIMARY KEY"), "应支持PRIMARY KEY");
        assertTrue(shentongSql.contains("NOT NULL"), "应支持NOT NULL");
        assertTrue(shentongSql.contains("UNIQUE"), "应支持UNIQUE");
        assertTrue(shentongSql.contains("DEFAULT"), "应支持DEFAULT");
        assertTrue(shentongSql.contains("CURRENT_TIMESTAMP"), "应支持CURRENT_TIMESTAMP");
        
        // 验证不支持的语法被移除或转换
        assertFalse(shentongSql.contains("ENGINE=InnoDB"), "应移除ENGINE子句");
        assertTrue(shentongSql.contains("CHARACTER SET UTF8"), "应转换字符集");
        
        System.out.println("✅ 综合标准符合性验证通过");
        System.out.println();
    }
}
