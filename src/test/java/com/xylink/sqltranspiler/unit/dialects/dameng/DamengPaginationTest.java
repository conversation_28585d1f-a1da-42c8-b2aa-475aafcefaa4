package com.xylink.sqltranspiler.unit.dialects.dameng;

import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.dml.QueryStmt;

/**
 * 达梦数据库分页查询支持测试
 * 基于达梦官方文档LIMIT语法的测试驱动开发
 * 根据达梦官方文档，达梦数据库原生支持LIMIT语法，无需转换为ROWNUM
 * 参考达梦数据库官方文档：https://eco.dameng.com/document/dm/zh-cn/pm/check-phrases.html#4-10-LIMIT限定条件
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保方言功能符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试方言特有功能
 *
 * <AUTHOR>
 */
public class DamengPaginationTest {

    private DamengGenerator generator;

    @BeforeEach
    public void setUp() {
        generator = new DamengGenerator();
    }

    @Test
    @DisplayName("测试LIMIT语法保留 - 达梦官方文档标准")
    public void testLimitToRownum() {
        String sql = "SELECT * FROM test_table ORDER BY id LIMIT 5";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(sql);
        String result = generator.generate(queryStmt);

        // 添加调试输出
        System.out.println("=== 达梦数据库LIMIT语法测试 ===");
        System.out.println("原始SQL: " + sql);
        System.out.println("转换结果: " + result);
        System.out.println("===============================");

        assertTrue(result.contains("SELECT *") && result.contains("FROM"));
        assertTrue(result.contains("test_table"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("ORDER BY id"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("LIMIT 5")); // 达梦原生支持LIMIT语法
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试LIMIT OFFSET语法保留 - 达梦官方文档标准")
    public void testLimitOffsetToRownum() {
        String sql = "SELECT * FROM test_table ORDER BY id LIMIT 3 OFFSET 2";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(sql);
        String result = generator.generate(queryStmt);

        // 添加调试输出
        System.out.println("=== 达梦数据库LIMIT OFFSET语法测试 ===");
        System.out.println("原始SQL: " + sql);
        System.out.println("转换结果: " + result);
        System.out.println("包含LIMIT 3: " + result.contains("LIMIT 3"));
        System.out.println("包含OFFSET 2: " + result.contains("OFFSET 2"));
        System.out.println("===============================");

        assertTrue(result.contains("SELECT *") && result.contains("FROM"));
        assertTrue(result.contains("test_table"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("ORDER BY id"));  // 根据达梦官方文档，普通标识符不需要双引号
        // 检查LIMIT和OFFSET是否都存在（忽略格式化的换行）
        assertTrue(result.contains("LIMIT 3") && result.contains("OFFSET 2")); // 达梦原生支持LIMIT OFFSET语法
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试复杂查询的分页语法保留 - 达梦官方文档标准")
    public void testComplexQueryPagination() {
        String sql = "SELECT `id`, `name`, `email` FROM `users` WHERE `status` = 'active' ORDER BY `created_date` DESC LIMIT 10 OFFSET 20";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(sql);
        String result = generator.generate(queryStmt);

        // 根据达梦官方文档修正后，普通标识符不需要双引号
        assertTrue(result.contains("SELECT id, name, email"));
        assertTrue(result.contains("FROM users"));
        assertTrue(result.contains("WHERE status = 'active'"));
        assertTrue(result.contains("ORDER BY created_date DESC"));
        // 检查LIMIT和OFFSET是否都存在（忽略格式化的换行）
        assertTrue(result.contains("LIMIT 10") && result.contains("OFFSET 20")); // 达梦原生支持LIMIT OFFSET语法
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试达梦数据库官方文档示例")
    public void testDamengOfficialExample() {
        // 根据达梦官方文档的分页显示示例
        String sql = "SELECT * FROM tab3 ORDER BY a DESC LIMIT 3 OFFSET 0";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(sql);
        String result = generator.generate(queryStmt);

        assertTrue(result.contains("SELECT *") && result.contains("FROM tab3"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("ORDER BY a DESC"));  // 根据达梦官方文档，普通标识符不需要双引号
        // 检查LIMIT和OFFSET是否都存在（忽略格式化的换行）
        assertTrue(result.contains("LIMIT 3") && result.contains("OFFSET 0")); // 达梦原生支持LIMIT OFFSET语法
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试没有LIMIT的查询不被转换")
    public void testQueryWithoutLimitNotConverted() {
        String sql = "SELECT * FROM test_table ORDER BY id";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(sql);
        String result = generator.generate(queryStmt);

        // 没有LIMIT的查询应该不被转换为ROWNUM结构
        assertFalse(result.contains("ROWNUM"));
        assertFalse(result.contains("ROW_NUM"));
        // 检查关键组件是否存在（忽略格式化的换行）
        assertTrue(result.contains("SELECT *") && result.contains("FROM test_table"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("ORDER BY id"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.endsWith(";"));
    }
}
