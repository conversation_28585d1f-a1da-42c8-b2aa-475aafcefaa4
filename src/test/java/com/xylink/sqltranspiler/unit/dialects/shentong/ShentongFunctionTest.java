package com.xylink.sqltranspiler.unit.dialects.shentong;

import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.shared.base.BaseShentongConversionTest;

/**
 * 神通数据库函数转换测试
 * 基于神通数据库官方文档 shentong.md 的函数规范
 * 根据文档第6015-18803行：神通数据库完全支持函数功能
 * 包括：
 * - CREATE FUNCTION：创建函数
 * - DROP FUNCTION：删除函数
 * - 数学函数：ABS、CEIL、FLOOR、ROUND、SQRT等
 * - 字符函数：CONCAT、SUBSTR、LENGTH、UPPER、LOWER等
 * - 时间函数：CURRENT_DATE、CURRENT_TIME、CURRENT_TIMESTAMP等
 * - 聚集函数：COUNT、SUM、AVG、MAX、MIN等
 * - 分析函数：ROW_NUMBER、RANK、DENSE_RANK等
 * - 系统函数：USER、DATABASE等
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保函数转换符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试复杂函数组合的转换正确性
 *
 * <AUTHOR>
 */
public class ShentongFunctionTest extends BaseShentongConversionTest {

    /**
     * 测试数学函数支持
     * 根据文档第6026-9566行：神通数据库完全支持数学函数
     */
    @Test
    public void testMathFunctions() throws Exception {
        String mysqlSql = """
            SELECT 
                ABS(-123) AS abs_value,
                CEIL(4.3) AS ceil_value,
                FLOOR(4.7) AS floor_value,
                ROUND(123.456, 2) AS round_value,
                SQRT(16) AS sqrt_value,
                POWER(2, 3) AS power_value,
                MOD(10, 3) AS mod_value,
                PI() AS pi_value,
                RAND() AS random_value,
                SIGN(-5) AS sign_value
            FROM dual;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        // 验证官方文档合规性
        assertOfficialDocumentCompliance(shentongSql);

        // 验证数学函数支持（基于官方文档第6026-9566行）
        assertTrue(shentongSql.contains("ABS"), "应支持ABS函数");
        assertTrue(shentongSql.contains("CEIL"), "应支持CEIL函数");
        assertTrue(shentongSql.contains("FLOOR"), "应支持FLOOR函数");
        assertTrue(shentongSql.contains("ROUND"), "应支持ROUND函数");
        assertTrue(shentongSql.contains("SQRT"), "应支持SQRT函数");
        assertTrue(shentongSql.contains("POWER") || shentongSql.contains("POW"),
                   "应支持POWER函数");
        assertTrue(shentongSql.contains("MOD"), "应支持MOD函数");
        assertTrue(shentongSql.contains("PI"), "应支持PI函数");
        assertTrue(shentongSql.contains("RAND"), "应支持RAND函数");
        assertTrue(shentongSql.contains("SIGN"), "应支持SIGN函数");
    }

    /**
     * 测试字符函数支持
     * 根据文档第9569-15141行：神通数据库完全支持字符函数
     */
    @Test
    public void testStringFunctions() throws Exception {
        String mysqlSql = """
            SELECT 
                CONCAT('Hello', ' ', 'World') AS concat_result,
                SUBSTR('Hello World', 1, 5) AS substr_result,
                LENGTH('Hello World') AS length_result,
                UPPER('hello world') AS upper_result,
                LOWER('HELLO WORLD') AS lower_result,
                TRIM('  Hello World  ') AS trim_result,
                LTRIM('  Hello World') AS ltrim_result,
                RTRIM('Hello World  ') AS rtrim_result,
                REPLACE('Hello World', 'World', 'Universe') AS replace_result,
                INSTR('Hello World', 'World') AS instr_result
            FROM dual;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证字符函数支持
        assertTrue(shentongSql.contains("CONCAT"), "应支持CONCAT函数");
        assertTrue(shentongSql.contains("SUBSTR"), "应支持SUBSTR函数");
        assertTrue(shentongSql.contains("LENGTH"), "应支持LENGTH函数");
        assertTrue(shentongSql.contains("UPPER"), "应支持UPPER函数");
        assertTrue(shentongSql.contains("LOWER"), "应支持LOWER函数");
        assertTrue(shentongSql.contains("TRIM"), "应支持TRIM函数");
        assertTrue(shentongSql.contains("LTRIM"), "应支持LTRIM函数");
        assertTrue(shentongSql.contains("RTRIM"), "应支持RTRIM函数");
        assertTrue(shentongSql.contains("REPLACE"), "应支持REPLACE函数");
        assertTrue(shentongSql.contains("INSTR"), "应支持INSTR函数");
    }

    /**
     * 测试时间函数支持
     * 根据文档第15141-18049行：神通数据库完全支持时间函数
     */
    @Test
    public void testDateTimeFunctions() throws Exception {
        String mysqlSql = """
            SELECT 
                CURRENT_DATE AS current_date,
                CURRENT_TIME AS current_time,
                CURRENT_TIMESTAMP AS current_timestamp,
                NOW() AS now_result,
                YEAR(CURRENT_DATE) AS year_result,
                MONTH(CURRENT_DATE) AS month_result,
                DAY(CURRENT_DATE) AS day_result,
                HOUR(CURRENT_TIME) AS hour_result,
                MINUTE(CURRENT_TIME) AS minute_result,
                SECOND(CURRENT_TIME) AS second_result,
                DATE_ADD(CURRENT_DATE, INTERVAL 1 DAY) AS date_add_result,
                DATEDIFF('2023-12-31', '2023-01-01') AS datediff_result
            FROM dual;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证时间函数支持
        assertTrue(shentongSql.contains("CURRENT_DATE"), "应支持CURRENT_DATE函数");
        assertTrue(shentongSql.contains("CURRENT_TIME"), "应支持CURRENT_TIME函数");
        assertTrue(shentongSql.contains("CURRENT_TIMESTAMP"), "应支持CURRENT_TIMESTAMP函数");
        assertTrue(shentongSql.contains("NOW") || shentongSql.contains("CURRENT_TIMESTAMP"), 
                   "应支持NOW函数或转换为CURRENT_TIMESTAMP");
        assertTrue(shentongSql.contains("YEAR"), "应支持YEAR函数");
        assertTrue(shentongSql.contains("MONTH"), "应支持MONTH函数");
        assertTrue(shentongSql.contains("DAY"), "应支持DAY函数");
        assertTrue(shentongSql.contains("HOUR"), "应支持HOUR函数");
        assertTrue(shentongSql.contains("MINUTE"), "应支持MINUTE函数");
        assertTrue(shentongSql.contains("SECOND"), "应支持SECOND函数");
    }

    /**
     * 测试聚集函数支持
     * 根据文档第18049-18803行：神通数据库完全支持聚集函数
     */
    @Test
    public void testAggregateFunctions() throws Exception {
        String mysqlSql = """
            CREATE TABLE sales (
                id INT AUTO_INCREMENT PRIMARY KEY,
                product_name VARCHAR(100),
                category VARCHAR(50),
                price DECIMAL(10,2),
                quantity INT,
                sale_date DATE
            );
            
            INSERT INTO sales (product_name, category, price, quantity, sale_date) VALUES
            ('Product A', 'Electronics', 100.00, 5, '2023-01-01'),
            ('Product B', 'Electronics', 150.00, 3, '2023-01-02'),
            ('Product C', 'Books', 25.00, 10, '2023-01-03'),
            ('Product D', 'Books', 30.00, 8, '2023-01-04');
            
            SELECT 
                category,
                COUNT(*) AS total_products,
                SUM(price * quantity) AS total_revenue,
                AVG(price) AS avg_price,
                MAX(price) AS max_price,
                MIN(price) AS min_price,
                COUNT(DISTINCT product_name) AS unique_products
            FROM sales
            GROUP BY category
            ORDER BY total_revenue DESC;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证聚集函数支持
        assertTrue(shentongSql.contains("COUNT"), "应支持COUNT函数");
        assertTrue(shentongSql.contains("SUM"), "应支持SUM函数");
        assertTrue(shentongSql.contains("AVG"), "应支持AVG函数");
        assertTrue(shentongSql.contains("MAX"), "应支持MAX函数");
        assertTrue(shentongSql.contains("MIN"), "应支持MIN函数");
        assertTrue(shentongSql.contains("DISTINCT"), "应支持DISTINCT关键字");
        assertTrue(shentongSql.contains("GROUP BY"), "应支持GROUP BY子句");
        assertTrue(shentongSql.contains("ORDER BY"), "应支持ORDER BY子句");
    }

    /**
     * 测试Oracle DECODE函数被MySQL强制校验正确拒绝
     * 验证Oracle特有函数被正确检测和拒绝
     */
    @Test
    public void testOracleDecodeFunctionSyntaxRejection() throws Exception {
        String oracleSql = """
            SELECT
                id,
                name,
                CASE
                    WHEN age < 18 THEN 'Minor'
                    WHEN age >= 18 AND age < 65 THEN 'Adult'
                    ELSE 'Senior'
                END AS age_category,
                IF(active = 1, 'Active', 'Inactive') AS status,
                IFNULL(email, 'No Email') AS email_display,
                COALESCE(phone, mobile, 'No Contact') AS contact_info,
                DECODE(gender, 'M', 'Male', 'F', 'Female', 'Unknown') AS gender_display
            FROM users;
            """;

        String result = convertMySqlToShentong(oracleSql);

        // 验证Oracle语法被正确拒绝 - 根据MySQL 8.4官方文档，DECODE不是MySQL函数
        assertTrue(result.isEmpty(), "Oracle DECODE函数应该被MySQL强制校验拒绝，转换结果应为空");
    }

    /**
     * 测试MySQL标准条件函数的转换
     * 使用正确的MySQL语法进行条件函数测试 - 双重测试策略第二部分
     */
    @Test
    public void testValidMySqlConditionalFunctions() throws Exception {
        String mysqlSql = """
            SELECT
                id,
                name,
                CASE
                    WHEN age < 18 THEN 'Minor'
                    WHEN age >= 18 AND age < 65 THEN 'Adult'
                    ELSE 'Senior'
                END AS age_category,
                IF(active = 1, 'Active', 'Inactive') AS status,
                IFNULL(email, 'No Email') AS email_display,
                COALESCE(phone, mobile, 'No Contact') AS contact_info,
                CASE gender
                    WHEN 'M' THEN 'Male'
                    WHEN 'F' THEN 'Female'
                    ELSE 'Unknown'
                END AS gender_display
            FROM users;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);

        // 验证MySQL标准条件函数转换
        // 根据神通数据库官方文档，神通支持CASE WHEN语句但不直接支持MySQL的IF()函数
        // 因此转换器将IF()函数转换为等价的CASE WHEN语句是正确的行为
        assertTrue(shentongSql.contains("CASE"), "应支持CASE语句");
        assertTrue(shentongSql.contains("IFNULL"), "应支持IFNULL函数");
        assertTrue(shentongSql.contains("COALESCE"), "应支持COALESCE函数");
        assertTrue(shentongSql.contains("SELECT"), "应支持SELECT语句");
        assertTrue(shentongSql.contains("FROM users"), "应保持表名");

        // 验证IF()函数被正确转换为CASE WHEN语句
        // MySQL: IF(active = 1, 'Active', 'Inactive')
        // 应转换为: CASE WHEN active = 1 THEN 'Active' ELSE 'Inactive' END
        assertTrue(shentongSql.contains("WHEN") && shentongSql.contains("THEN") && shentongSql.contains("ELSE"),
                   "IF函数应被转换为CASE WHEN语句");
    }

    /**
     * 测试Oracle类型转换函数被MySQL强制校验正确拒绝
     * 验证Oracle特有函数被正确检测和拒绝
     */
    @Test
    public void testOracleConversionFunctionSyntaxRejection() throws Exception {
        String oracleSql = """
            SELECT
                CAST('123' AS INT) AS cast_to_int,
                CAST(123.456 AS CHAR(10)) AS cast_to_char,
                CONVERT('2023-01-01', DATE) AS convert_to_date,
                TO_NUMBER('123.45') AS to_number_result,
                TO_CHAR(123.45) AS to_char_result,
                TO_DATE('2023-01-01', 'YYYY-MM-DD') AS to_date_result
            FROM dual;
            """;

        String result = convertMySqlToShentong(oracleSql);

        // 验证Oracle语法被正确拒绝 - 根据MySQL 8.4官方文档，TO_NUMBER、TO_CHAR、TO_DATE不是MySQL函数
        assertTrue(result.isEmpty(), "Oracle类型转换函数应该被MySQL强制校验拒绝，转换结果应为空");
    }

    /**
     * 测试MySQL标准类型转换函数的转换
     * 使用正确的MySQL语法进行类型转换测试
     */
    @Test
    public void testMySqlStandardConversionFunctions() throws Exception {
        String mysqlSql = """
            SELECT
                CAST('123' AS SIGNED) AS cast_to_int,
                CAST(123.456 AS CHAR(10)) AS cast_to_char,
                CAST('2023-01-01' AS DATE) AS cast_to_date,
                CONVERT('123.45', DECIMAL(10,2)) AS convert_to_decimal,
                DATE_FORMAT(NOW(), '%Y-%m-%d') AS format_date,
                STR_TO_DATE('2023-01-01', '%Y-%m-%d') AS str_to_date_result
            FROM dual;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);

        // 验证MySQL标准类型转换函数转换
        assertTrue(shentongSql.contains("CAST"), "应支持CAST函数");
        assertTrue(shentongSql.contains("CONVERT"), "应支持CONVERT函数");
        assertTrue(shentongSql.contains("DATE_FORMAT") || shentongSql.contains("TO_CHAR"), "DATE_FORMAT应被支持或转换");
        assertTrue(shentongSql.contains("STR_TO_DATE") || shentongSql.contains("TO_DATE"), "STR_TO_DATE应被支持或转换");
        assertTrue(shentongSql.contains("SELECT"), "应支持SELECT语句");
        assertTrue(shentongSql.contains("FROM dual"), "应保持FROM dual");
    }

    /**
     * 测试非MySQL序列函数被正确拒绝
     * 验证MySQL强制语法校验工作正常 - 双重测试策略第一部分
     */
    @Test
    public void testInvalidSequenceFunctionRejection() throws Exception {
        String postgresqlSql = """
            SELECT
                NEXTVAL('user_seq') AS next_id,
                CURRVAL('user_seq') AS current_id,
                SETVAL('user_seq', 1000) AS set_value
            FROM dual;
            """;

        String result = convertMySqlToShentong(postgresqlSql);

        // 验证PostgreSQL序列函数被正确拒绝 - 根据MySQL 8.4官方文档，NEXTVAL/CURRVAL/SETVAL不是MySQL函数
        assertTrue(result.isEmpty(), "PostgreSQL序列函数应该被MySQL强制校验拒绝，转换结果应为空");
    }

    /**
     * 测试MySQL标准的AUTO_INCREMENT序列功能
     * 使用正确的MySQL语法进行序列测试 - 双重测试策略第二部分
     */
    @Test
    public void testValidMySqlAutoIncrementSequence() throws Exception {
        String mysqlSql = """
            CREATE TABLE user_sequence (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100)
            );

            INSERT INTO user_sequence (name) VALUES ('User1'), ('User2');

            SELECT LAST_INSERT_ID() AS last_id, id, name FROM user_sequence;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);

        // 验证MySQL AUTO_INCREMENT序列功能支持
        assertTrue(shentongSql.contains("AUTO_INCREMENT") || shentongSql.contains("SERIAL"), "应支持AUTO_INCREMENT或转换为SERIAL");
        assertTrue(shentongSql.contains("LAST_INSERT_ID"), "应支持LAST_INSERT_ID函数");
        assertTrue(shentongSql.contains("user_sequence"), "应保持表名称");
    }

    /**
     * 测试系统函数支持
     * 根据文档第82151行：系统函数 (System Functions)
     */
    @Test
    public void testSystemFunctions() throws Exception {
        String mysqlSql = """
            SELECT 
                USER() AS current_user,
                DATABASE() AS current_database,
                VERSION() AS database_version,
                CONNECTION_ID() AS connection_id,
                LAST_INSERT_ID() AS last_insert_id
            FROM dual;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证系统函数支持
        assertTrue(shentongSql.contains("USER"), "应支持USER函数");
        assertTrue(shentongSql.contains("DATABASE"), "应支持DATABASE函数");
        assertTrue(shentongSql.contains("VERSION"), "应支持VERSION函数");
        assertTrue(shentongSql.contains("CONNECTION_ID"), "应支持CONNECTION_ID函数");
        assertTrue(shentongSql.contains("LAST_INSERT_ID"), "应支持LAST_INSERT_ID函数");
    }

    /**
     * 测试自定义函数创建
     * 根据文档：CREATE FUNCTION支持 - 双重测试策略第二部分
     */
    @Test
    public void testValidMySqlCreateFunction() throws Exception {
        String mysqlSql = """
            CREATE FUNCTION calculate_discount(
                original_price DECIMAL(10,2),
                discount_rate DECIMAL(5,2)
            ) RETURNS DECIMAL(10,2)
            READS SQL DATA
            DETERMINISTIC
            BEGIN
                DECLARE discounted_price DECIMAL(10,2);
                SET discounted_price = original_price * (1 - discount_rate / 100);
                RETURN discounted_price;
            END;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证CREATE FUNCTION支持
        assertTrue(shentongSql.contains("CREATE FUNCTION"), "应支持CREATE FUNCTION语句");
        assertTrue(shentongSql.contains("calculate_discount"), "应保持函数名称");
        assertTrue(shentongSql.contains("RETURNS"), "应支持RETURNS子句");
        assertTrue(shentongSql.contains("BEGIN") && shentongSql.contains("END"), 
                   "应支持函数体");
        assertTrue(shentongSql.contains("DECLARE"), "应支持DECLARE变量声明");
        assertTrue(shentongSql.contains("RETURN"), "应支持RETURN语句");
        assertTrue(shentongSql.contains("READS SQL DATA") || shentongSql.contains("DETERMINISTIC"), 
                   "应支持函数特性");
    }

    /**
     * 测试复杂函数嵌套
     * 根据文档：函数可以嵌套使用 - 双重测试策略第二部分
     */
    @Test
    public void testValidMySqlComplexFunctionNesting() throws Exception {
        String mysqlSql = """
            SELECT
                id,
                UPPER(CONCAT(
                    SUBSTRING(first_name, 1, 1),
                    '. ',
                    SUBSTRING(last_name, 1, 10)
                )) AS display_name,
                ROUND(
                    AVG(CASE
                        WHEN score >= 90 THEN score * 1.1
                        WHEN score >= 80 THEN score * 1.05
                        ELSE score
                    END), 2
                ) AS adjusted_avg_score,
                COUNT(DISTINCT YEAR(created_date)) AS active_years,
                COALESCE(
                    NULLIF(TRIM(email), ''),
                    CONCAT(username, '@company.com')
                ) AS contact_email
            FROM student_records
            WHERE EXTRACT(YEAR FROM created_date) >= YEAR(CURRENT_DATE) - 5
            GROUP BY id, first_name, last_name, username, email
            HAVING COUNT(*) > 1
            ORDER BY adjusted_avg_score DESC;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证复杂函数嵌套支持
        assertTrue(shentongSql.contains("UPPER"), "应支持UPPER函数");
        assertTrue(shentongSql.contains("CONCAT"), "应支持CONCAT函数");
        assertTrue(shentongSql.contains("SUBSTR"), "应支持SUBSTR函数");
        assertTrue(shentongSql.contains("ROUND"), "应支持ROUND函数");
        assertTrue(shentongSql.contains("AVG"), "应支持AVG函数");
        assertTrue(shentongSql.contains("CASE"), "应支持CASE表达式");
        assertTrue(shentongSql.contains("COUNT"), "应支持COUNT函数");
        assertTrue(shentongSql.contains("DISTINCT"), "应支持DISTINCT关键字");
        assertTrue(shentongSql.contains("YEAR"), "应支持YEAR函数");
        assertTrue(shentongSql.contains("COALESCE"), "应支持COALESCE函数");
        assertTrue(shentongSql.contains("NULLIF"), "应支持NULLIF函数");
        assertTrue(shentongSql.contains("TRIM"), "应支持TRIM函数");
        assertTrue(shentongSql.contains("EXTRACT"), "应支持EXTRACT函数");
        assertTrue(shentongSql.contains("CURRENT_DATE"), "应支持CURRENT_DATE函数");
    }

    /**
     * 测试窗口函数支持
     * 根据文档第18804行：分析函数
     */
    @Test
    public void testWindowFunctions() throws Exception {
        String mysqlSql = """
            SELECT 
                id,
                name,
                department,
                salary,
                ROW_NUMBER() OVER (PARTITION BY department ORDER BY salary DESC) AS row_num,
                RANK() OVER (PARTITION BY department ORDER BY salary DESC) AS rank_num,
                DENSE_RANK() OVER (PARTITION BY department ORDER BY salary DESC) AS dense_rank_num,
                LAG(salary, 1) OVER (PARTITION BY department ORDER BY salary DESC) AS prev_salary,
                LEAD(salary, 1) OVER (PARTITION BY department ORDER BY salary DESC) AS next_salary,
                SUM(salary) OVER (PARTITION BY department) AS dept_total_salary,
                AVG(salary) OVER (PARTITION BY department) AS dept_avg_salary
            FROM employees
            ORDER BY department, salary DESC;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证窗口函数支持
        assertTrue(shentongSql.contains("ROW_NUMBER"), "应支持ROW_NUMBER函数");
        assertTrue(shentongSql.contains("RANK"), "应支持RANK函数");
        assertTrue(shentongSql.contains("DENSE_RANK"), "应支持DENSE_RANK函数");
        assertTrue(shentongSql.contains("LAG"), "应支持LAG函数");
        assertTrue(shentongSql.contains("LEAD"), "应支持LEAD函数");
        assertTrue(shentongSql.contains("OVER"), "应支持OVER子句");
        assertTrue(shentongSql.contains("PARTITION BY"), "应支持PARTITION BY子句");
        assertTrue(shentongSql.contains("ORDER BY"), "应支持ORDER BY子句");
    }

    /**
     * 测试Oracle函数错误处理语法被MySQL强制校验正确拒绝
     * 验证Oracle特有函数被正确检测和拒绝
     */
    @Test
    public void testOracleFunctionErrorHandlingSyntaxRejection() throws Exception {
        String oracleSql = """
            SELECT
                -- 正常函数调用
                ABS(-123) AS normal_function,

                -- Oracle特有函数
                SQRT(-1) AS sqrt_negative,
                SUBSTR('Hello', 10, 5) AS substr_out_of_range,
                CAST('invalid' AS INT) AS invalid_cast,
                TO_DATE('invalid-date', 'YYYY-MM-DD') AS invalid_date
            FROM dual;
            """;

        String result = convertMySqlToShentong(oracleSql);

        // 验证Oracle语法被正确拒绝 - 根据MySQL 8.4官方文档，TO_DATE不是MySQL函数
        assertTrue(result.isEmpty(), "Oracle函数语法应该被MySQL强制校验拒绝，转换结果应为空");
    }

    /**
     * 测试MySQL标准函数错误处理
     * 验证MySQL标准函数的错误情况能够被正确处理
     */
    @Test
    public void testMySqlStandardFunctionErrorHandling() throws Exception {
        String mysqlSql = """
            SELECT
                -- 正常函数调用
                ABS(-123) AS normal_function,

                -- 可能的错误情况（在实际执行时可能失败，但转换应该成功）
                SQRT(-1) AS sqrt_negative,
                SUBSTRING('Hello', 10, 5) AS substr_out_of_range,
                CAST('invalid' AS SIGNED) AS invalid_cast,
                STR_TO_DATE('invalid-date', '%Y-%m-%d') AS invalid_date
            FROM dual;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);

        // 验证MySQL标准函数错误处理转换
        assertTrue(shentongSql.contains("ABS"), "应支持ABS函数");
        assertTrue(shentongSql.contains("SQRT"), "应支持SQRT函数");
        assertTrue(shentongSql.contains("SUBSTRING") || shentongSql.contains("SUBSTR"), "SUBSTRING应被支持或转换");
        assertTrue(shentongSql.contains("CAST"), "应支持CAST函数");
        assertTrue(shentongSql.contains("STR_TO_DATE") || shentongSql.contains("TO_DATE"), "STR_TO_DATE应被支持或转换");
        assertTrue(shentongSql.contains("SELECT"), "应支持SELECT语句");
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证错误处理
        assertTrue(shentongSql.contains("ABS"), "应正确转换ABS函数");
        assertTrue(shentongSql.contains("SQRT"), "应正确转换SQRT函数");
        assertTrue(shentongSql.contains("SUBSTR"), "应正确转换SUBSTR函数");
        assertTrue(shentongSql.contains("CAST"), "应正确转换CAST函数");
        assertTrue(shentongSql.contains("TO_DATE"), "应正确转换TO_DATE函数");
        
        // 注意：实际的函数参数验证是在数据库执行时进行的，转换器只负责语法转换
    }
}
