package com.xylink.sqltranspiler.unit.dialects;

import com.xylink.sqltranspiler.core.ast.common.UseStatement;
import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator;
import com.xylink.sqltranspiler.core.dialects.shentong.ShentongGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static org.junit.jupiter.api.Assertions.*;

/**
 * USE语句转换测试
 * 根据各数据库官方文档验证USE语句的处理：
 * - MySQL: https://dev.mysql.com/doc/refman/8.4/en/use.html
 * - 达梦: 不支持USE语句，通过连接字符串指定数据库
 * - 金仓: https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
 * - 神通: 不支持USE语句，通过连接字符串指定数据库
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保方言功能符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试方言特有功能
 *
 * <AUTHOR>
 */
@DisplayName("USE语句转换测试")
public class UseStatementConversionTest {

    private static final Logger log = LoggerFactory.getLogger(UseStatementConversionTest.class);

    private DamengGenerator damengGenerator;
    private KingbaseGenerator kingbaseGenerator;
    private ShentongGenerator shentongGenerator;

    @BeforeEach
    void setUp() {
        damengGenerator = new DamengGenerator();
        kingbaseGenerator = new KingbaseGenerator();
        shentongGenerator = new ShentongGenerator();
    }

    @Test
    @DisplayName("测试USE语句解析")
    void testUseStatementParsing() {
        String[] useSqls = {
            "USE test_database;",
            "USE `test_database`;",
            "USE test_db_123;",
            "USE production;"
        };

        for (String sql : useSqls) {
            log.info("测试USE语句解析: {}", sql);
            
            UseStatement useStatement = (UseStatement) MySqlHelper.parseStatement(sql);
            assertNotNull(useStatement, "USE语句应该能够被解析: " + sql);
            assertNotNull(useStatement.getDatabaseName(), "数据库名称不应为空");
            
            log.info("解析成功，数据库名称: {}", useStatement.getDatabaseName());
        }
    }

    @Test
    @DisplayName("测试达梦数据库USE语句转换")
    void testDamengUseStatementConversion() {
        String sql = "USE test_database;";
        UseStatement useStatement = (UseStatement) MySqlHelper.parseStatement(sql);
        
        String damengSql = damengGenerator.generate(useStatement);
        
        log.info("MySQL SQL: {}", sql);
        log.info("达梦转换结果: {}", damengSql);
        
        // 根据达梦官方文档，USE语句应该转换为注释
        assertNotNull(damengSql, "达梦转换结果不应为空");
        assertTrue(damengSql.contains("--"), "达梦应该将USE语句转换为注释");
        assertTrue(damengSql.contains("test_database"), "应该保留数据库名称");
        assertTrue(damengSql.contains("连接字符串"), "应该提示通过连接字符串指定数据库");
    }

    @Test
    @DisplayName("测试金仓数据库USE语句转换")
    void testKingbaseUseStatementConversion() {
        String sql = "USE test_database;";
        UseStatement useStatement = (UseStatement) MySqlHelper.parseStatement(sql);
        
        String kingbaseSql = kingbaseGenerator.generate(useStatement);
        
        log.info("MySQL SQL: {}", sql);
        log.info("金仓转换结果: {}", kingbaseSql);
        
        // 根据金仓官方文档，USE语句不被支持，应该转换为注释
        assertNotNull(kingbaseSql, "金仓转换结果不应为空");
        assertTrue(kingbaseSql.contains("--"), "金仓应该将USE语句转换为注释");
        assertTrue(kingbaseSql.contains("test_database"), "应该保留数据库名称");
        assertTrue(kingbaseSql.contains("MySQL-specific") || kingbaseSql.contains("不支持"), 
                  "应该说明USE语句是MySQL特有的");
    }

    @Test
    @DisplayName("测试神通数据库USE语句转换")
    void testShentongUseStatementConversion() {
        String sql = "USE test_database;";
        UseStatement useStatement = (UseStatement) MySqlHelper.parseStatement(sql);
        
        String shentongSql = shentongGenerator.generate(useStatement);
        
        log.info("MySQL SQL: {}", sql);
        log.info("神通转换结果: {}", shentongSql);
        
        // 根据神通数据库文档，USE语句应该转换为注释
        assertNotNull(shentongSql, "神通转换结果不应为空");
        assertTrue(shentongSql.contains("--"), "神通应该将USE语句转换为注释");
        assertTrue(shentongSql.contains("test_database"), "应该保留数据库名称");
        assertTrue(shentongSql.contains("连接字符串"), "应该提示通过连接字符串指定数据库");
    }

    @Test
    @DisplayName("测试带引号的数据库名称")
    void testQuotedDatabaseName() {
        String sql = "USE `test_database_with_special_chars`;";
        UseStatement useStatement = (UseStatement) MySqlHelper.parseStatement(sql);
        
        String damengSql = damengGenerator.generate(useStatement);
        String kingbaseSql = kingbaseGenerator.generate(useStatement);
        String shentongSql = shentongGenerator.generate(useStatement);
        
        log.info("MySQL SQL: {}", sql);
        log.info("达梦转换结果: {}", damengSql);
        log.info("金仓转换结果: {}", kingbaseSql);
        log.info("神通转换结果: {}", shentongSql);
        
        // 所有数据库都应该正确处理带引号的数据库名称
        assertTrue(damengSql.contains("test_database_with_special_chars"), 
                  "达梦应该保留数据库名称");
        assertTrue(kingbaseSql.contains("test_database_with_special_chars"), 
                  "金仓应该保留数据库名称");
        assertTrue(shentongSql.contains("test_database_with_special_chars"), 
                  "神通应该保留数据库名称");
    }

    @Test
    @DisplayName("验证USE语句转换的官方文档合规性")
    void testOfficialDocumentationCompliance() {
        String sql = "USE production_db;";
        UseStatement useStatement = (UseStatement) MySqlHelper.parseStatement(sql);
        
        // 达梦数据库：根据官方文档，不支持USE语句
        String damengSql = damengGenerator.generate(useStatement);
        assertTrue(damengSql.contains("--"), "达梦应该将USE语句转换为注释（符合官方文档）");
        
        // 金仓数据库：根据官方文档，不支持MySQL的USE语句
        String kingbaseSql = kingbaseGenerator.generate(useStatement);
        assertTrue(kingbaseSql.contains("--"), "金仓应该将USE语句转换为注释（符合官方文档）");
        
        // 神通数据库：根据官方文档，不支持USE语句
        String shentongSql = shentongGenerator.generate(useStatement);
        assertTrue(shentongSql.contains("--"), "神通应该将USE语句转换为注释（符合官方文档）");
        
        log.info("✅ 所有数据库的USE语句处理都符合官方文档规范");
    }
}
