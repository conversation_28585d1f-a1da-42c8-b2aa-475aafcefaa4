package com.xylink.sqltranspiler.unit.dialects.shentong;

import com.xylink.sqltranspiler.core.dialects.shentong.ShentongGenerator;

import com.xylink.sqltranspiler.core.ast.table.TruncateTable;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 神通数据库TRUNCATE TABLE功能测试
 * 基于神通官方文档的测试驱动开发
 * 参考神通数据库官方文档：TRUNCATE TABLE语句用于快速删除表中的所有数据
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保方言功能符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试方言特有功能
 *
 * <AUTHOR>
 */
public class ShentongTruncateTableTest {

    private ShentongGenerator generator;

    @BeforeEach
    public void setUp() {
        generator = new ShentongGenerator();
    }

    private TruncateTable parseTruncateTable(String sql) {
        return (TruncateTable) MySqlHelper.parseStatement(sql);
    }

    @Test
    @DisplayName("测试基本TRUNCATE TABLE语句")
    public void testBasicTruncateTable() {
        String sql = "TRUNCATE TABLE employees;";
        TruncateTable truncateTable = parseTruncateTable(sql);
        String result = generator.generate(truncateTable);
        
        assertTrue(result.contains("TRUNCATE TABLE"));
        assertTrue(result.contains("\"employees\""));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试带模式名的TRUNCATE TABLE语句")
    public void testTruncateTableWithSchema() {
        String sql = "TRUNCATE TABLE hr.employees;";
        TruncateTable truncateTable = parseTruncateTable(sql);
        String result = generator.generate(truncateTable);
        
        assertTrue(result.contains("TRUNCATE TABLE"));
        assertTrue(result.contains("\"hr\".\"employees\""));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试带反引号的TRUNCATE TABLE语句")
    public void testTruncateTableWithBackticks() {
        String sql = "TRUNCATE TABLE `user_data`;";
        TruncateTable truncateTable = parseTruncateTable(sql);
        String result = generator.generate(truncateTable);
        
        assertTrue(result.contains("TRUNCATE TABLE"));
        assertTrue(result.contains("\"user_data\""));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试复杂表名的TRUNCATE TABLE语句")
    public void testTruncateTableComplexName() {
        String sql = "TRUNCATE TABLE `order-details`;";
        TruncateTable truncateTable = parseTruncateTable(sql);
        String result = generator.generate(truncateTable);
        
        assertTrue(result.contains("TRUNCATE TABLE"));
        assertTrue(result.contains("\"order-details\""));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试大写TRUNCATE TABLE语句")
    public void testUppercaseTruncateTable() {
        String sql = "TRUNCATE TABLE EMPLOYEES;";
        TruncateTable truncateTable = parseTruncateTable(sql);
        String result = generator.generate(truncateTable);
        
        assertTrue(result.contains("TRUNCATE TABLE"));
        assertTrue(result.contains("\"EMPLOYEES\""));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试混合大小写TRUNCATE TABLE语句")
    public void testMixedCaseTruncateTable() {
        String sql = "truncate table Employee_Data;";
        TruncateTable truncateTable = parseTruncateTable(sql);
        String result = generator.generate(truncateTable);
        
        assertTrue(result.contains("TRUNCATE TABLE"));
        assertTrue(result.contains("\"Employee_Data\""));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试神通数据库官方文档示例")
    public void testShentongOfficialExample() {
        // 根据神通官方文档，TRUNCATE TABLE用于快速删除表中的所有数据
        String sql = "TRUNCATE TABLE sales_data;";
        TruncateTable truncateTable = parseTruncateTable(sql);
        String result = generator.generate(truncateTable);
        
        assertTrue(result.contains("TRUNCATE TABLE"));
        assertTrue(result.contains("\"sales_data\""));
        assertTrue(result.endsWith(";"));
        
        // 验证生成的SQL格式正确
        assertEquals("TRUNCATE TABLE \"sales_data\";", result);
    }

    @Test
    @DisplayName("测试神通数据库PostgreSQL兼容特性")
    public void testShentongPostgreSQLCompatibility() {
        // 根据神通官方文档，神通数据库支持PostgreSQL兼容的TRUNCATE TABLE语法
        String sql = "TRUNCATE TABLE postgresql_table;";
        TruncateTable truncateTable = parseTruncateTable(sql);
        String result = generator.generate(truncateTable);
        
        assertTrue(result.contains("TRUNCATE TABLE"));
        assertTrue(result.contains("\"postgresql_table\""));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试多个TRUNCATE TABLE语句的转换一致性")
    public void testMultipleTruncateTableConsistency() {
        String[] sqls = {
            "TRUNCATE TABLE table1;",
            "TRUNCATE TABLE table2;",
            "TRUNCATE TABLE table3;"
        };
        
        for (String sql : sqls) {
            TruncateTable truncateTable = parseTruncateTable(sql);
            String result = generator.generate(truncateTable);
            
            assertTrue(result.contains("TRUNCATE TABLE"));
            assertTrue(result.endsWith(";"));
            assertFalse(result.contains("`")); // 确保反引号被转换为双引号
        }
    }

    @Test
    @DisplayName("测试TRUNCATE TABLE语句的性能特性")
    public void testTruncateTablePerformance() {
        // 根据神通官方文档，TRUNCATE TABLE比DELETE更快
        String sql = "TRUNCATE TABLE large_table;";
        TruncateTable truncateTable = parseTruncateTable(sql);
        String result = generator.generate(truncateTable);
        
        // 验证生成的语句保持TRUNCATE的语义，而不是转换为DELETE
        assertTrue(result.contains("TRUNCATE TABLE"));
        assertFalse(result.contains("DELETE"));
        assertTrue(result.contains("\"large_table\""));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试神通数据库PLOSCAR语言兼容的TRUNCATE TABLE")
    public void testShentongPLOSCARCompatibility() {
        // 神通数据库支持PLOSCAR语言和PostgreSQL兼容语法
        String sql = "TRUNCATE TABLE ploscar_table;";
        TruncateTable truncateTable = parseTruncateTable(sql);
        String result = generator.generate(truncateTable);
        
        assertTrue(result.contains("TRUNCATE TABLE"));
        assertTrue(result.contains("\"ploscar_table\""));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试神通数据库事务处理中的TRUNCATE TABLE")
    public void testShentongTransactionTruncate() {
        // 神通数据库在事务中支持TRUNCATE TABLE
        String sql = "TRUNCATE TABLE transaction_log;";
        TruncateTable truncateTable = parseTruncateTable(sql);
        String result = generator.generate(truncateTable);
        
        assertTrue(result.contains("TRUNCATE TABLE"));
        assertTrue(result.contains("\"transaction_log\""));
        assertTrue(result.endsWith(";"));
        
        // 验证生成的SQL格式正确
        assertEquals("TRUNCATE TABLE \"transaction_log\";", result);
    }
}
