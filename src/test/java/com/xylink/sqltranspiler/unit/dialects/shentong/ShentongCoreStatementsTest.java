package com.xylink.sqltranspiler.unit.dialects.shentong;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import com.xylink.sqltranspiler.shared.base.BaseShentongConversionTest;

/**
 * 神通数据库核心SQL语句支持测试
 * 基于神通数据库官方文档 st.md 的SQL语句规范
 * 测试覆盖：
 * - INSERT语句的各种形式
 * - UPDATE语句的各种形式
 * - DELETE语句的各种形式
 * - SELECT语句的复杂查询
 * - 事务控制语句
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保方言功能符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试方言特有功能
 *
 * <AUTHOR>
 */
public class ShentongCoreStatementsTest extends BaseShentongConversionTest {

    /**
     * 测试INSERT语句的基本功能
     * 根据文档：神通数据库完全支持标准INSERT语法
     */
    @Test
    public void testBasicInsertStatements() throws Exception {
        String mysqlSql = """
            INSERT INTO users (name, email, age) VALUES ('张三', '<EMAIL>', 25);
            INSERT INTO users VALUES (1, '李四', '<EMAIL>', 30);
            INSERT INTO users (name, email) VALUES 
                ('王五', '<EMAIL>'),
                ('赵六', '<EMAIL>');
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证INSERT语句支持
        assertTrue(shentongSql.contains("INSERT INTO"), "应支持INSERT INTO语法");
        assertTrue(shentongSql.contains("VALUES"), "应支持VALUES子句");
        assertTrue(shentongSql.contains("'张三'"), "应支持中文字符串");
        assertTrue(shentongSql.contains("'<EMAIL>'"), "应支持邮箱格式");
    }

    /**
     * 测试INSERT语句与自增列的配合
     * 根据文档：神通数据库支持AUTO_INCREMENT和SERIAL类型
     */
    @Test
    public void testInsertWithAutoIncrement() throws Exception {
        String mysqlSql = """
            CREATE TABLE test_auto (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(50)
            );
            INSERT INTO test_auto (name) VALUES ('测试1');
            INSERT INTO test_auto VALUES (10, '测试2');
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证自增列支持
        assertTrue(shentongSql.contains("AUTO_INCREMENT") || shentongSql.contains("SERIAL"),
                   "应支持自增列类型");
        assertTrue(shentongSql.contains("PRIMARY KEY"), "应支持主键约束");
    }

    /**
     * 测试UPDATE语句的各种形式
     * 根据文档：神通数据库支持标准UPDATE语法，包括WHERE条件
     */
    @Test
    public void testUpdateStatements() throws Exception {
        String mysqlSql = """
            UPDATE users SET name = '新名字' WHERE id = 1;
            UPDATE users SET name = '批量更新', email = '<EMAIL>' 
            WHERE age > 25 AND age < 50;
            UPDATE users SET age = age + 1 WHERE name LIKE '张%';
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证UPDATE语句支持
        assertTrue(shentongSql.contains("UPDATE"), "应支持UPDATE语句");
        assertTrue(shentongSql.contains("SET"), "应支持SET子句");
        assertTrue(shentongSql.contains("WHERE"), "应支持WHERE条件");
        assertTrue(shentongSql.contains("LIKE"), "应支持LIKE操作符");
        assertTrue(shentongSql.contains("AND"), "应支持AND逻辑操作符");
    }

    /**
     * 测试DELETE语句的各种形式
     * 根据文档：神通数据库支持标准DELETE语法
     */
    @Test
    public void testDeleteStatements() throws Exception {
        String mysqlSql = """
            DELETE FROM users WHERE id = 1;
            DELETE FROM users WHERE age < 18 OR age > 65;
            DELETE FROM users WHERE name IS NULL;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证DELETE语句支持
        assertTrue(shentongSql.contains("DELETE FROM"), "应支持DELETE FROM语法");
        assertTrue(shentongSql.contains("WHERE"), "应支持WHERE条件");
        assertTrue(shentongSql.contains("OR"), "应支持OR逻辑操作符");
        assertTrue(shentongSql.contains("IS NULL"), "应支持IS NULL判断");
    }

    /**
     * 测试复杂SELECT查询
     * 根据文档：神通数据库支持复杂的SELECT查询，包括JOIN、子查询等
     */
    @Test
    public void testComplexSelectQueries() throws Exception {
        String mysqlSql = """
            SELECT u.name, u.email, p.title 
            FROM users u 
            LEFT JOIN profiles p ON u.id = p.user_id 
            WHERE u.age BETWEEN 20 AND 40 
            ORDER BY u.name ASC, u.age DESC 
            LIMIT 10;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证复杂查询支持
        assertTrue(shentongSql.contains("LEFT JOIN"), "应支持LEFT JOIN");
        assertTrue(shentongSql.contains("ON"), "应支持JOIN条件");
        assertTrue(shentongSql.contains("BETWEEN"), "应支持BETWEEN操作符");
        assertTrue(shentongSql.contains("ORDER BY"), "应支持ORDER BY");
        assertTrue(shentongSql.contains("ASC") && shentongSql.contains("DESC"), 
                   "应支持排序方向");
        // LIMIT应该被转换为ROWNUM分页
        assertTrue(shentongSql.contains("ROWNUM") || shentongSql.contains("ROW_NUMBER"), 
                   "LIMIT应转换为ROWNUM分页");
    }

    /**
     * 测试子查询支持
     * 根据文档：神通数据库支持各种形式的子查询
     */
    @Test
    public void testSubqueries() throws Exception {
        String mysqlSql = """
            SELECT name, email 
            FROM users 
            WHERE id IN (
                SELECT user_id 
                FROM orders 
                WHERE order_date > '2023-01-01'
            ) 
            AND age > (
                SELECT AVG(age) 
                FROM users 
                WHERE status = 'active'
            );
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证子查询支持
        assertTrue(shentongSql.contains("IN"), "应支持IN子查询");
        assertTrue(shentongSql.contains("SELECT") && 
                   shentongSql.indexOf("SELECT") != shentongSql.lastIndexOf("SELECT"), 
                   "应支持多个SELECT（子查询）");
        assertTrue(shentongSql.contains("AVG"), "应支持聚合函数");
    }

    /**
     * 测试聚合函数和GROUP BY
     * 根据文档：神通数据库支持标准聚合函数
     */
    @Test
    public void testAggregationAndGroupBy() throws Exception {
        String mysqlSql = """
            SELECT department, 
                   COUNT(*) as employee_count,
                   AVG(salary) as avg_salary,
                   MAX(salary) as max_salary,
                   MIN(salary) as min_salary,
                   SUM(salary) as total_salary
            FROM employees 
            GROUP BY department 
            HAVING COUNT(*) > 5 
            ORDER BY avg_salary DESC;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证聚合函数支持
        assertTrue(shentongSql.contains("COUNT(*)"), "应支持COUNT聚合函数");
        assertTrue(shentongSql.contains("AVG"), "应支持AVG聚合函数");
        assertTrue(shentongSql.contains("MAX"), "应支持MAX聚合函数");
        assertTrue(shentongSql.contains("MIN"), "应支持MIN聚合函数");
        assertTrue(shentongSql.contains("SUM"), "应支持SUM聚合函数");
        assertTrue(shentongSql.contains("GROUP BY"), "应支持GROUP BY");
        assertTrue(shentongSql.contains("HAVING"), "应支持HAVING子句");
    }

    /**
     * 测试UNION查询
     * 根据文档：神通数据库支持UNION操作
     */
    @Test
    public void testUnionQueries() throws Exception {
        String mysqlSql = """
            SELECT name, email FROM active_users 
            UNION 
            SELECT name, email FROM inactive_users 
            ORDER BY name;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证UNION支持
        assertTrue(shentongSql.contains("UNION"), "应支持UNION操作");
        assertTrue(shentongSql.contains("ORDER BY"), "应支持UNION后的ORDER BY");
    }

    /**
     * 测试EXISTS和NOT EXISTS
     * 根据文档：神通数据库支持EXISTS子查询
     */
    @Test
    public void testExistsQueries() throws Exception {
        String mysqlSql = """
            SELECT u.name, u.email 
            FROM users u 
            WHERE EXISTS (
                SELECT 1 
                FROM orders o 
                WHERE o.user_id = u.id 
                AND o.status = 'completed'
            ) 
            AND NOT EXISTS (
                SELECT 1 
                FROM complaints c 
                WHERE c.user_id = u.id
            );
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证EXISTS支持
        assertTrue(shentongSql.contains("EXISTS"), "应支持EXISTS子查询");
        assertTrue(shentongSql.contains("NOT EXISTS"), "应支持NOT EXISTS子查询");
    }

    /**
     * 测试CASE WHEN表达式
     * 根据文档：神通数据库支持CASE WHEN条件表达式
     */
    @Test
    public void testCaseWhenExpressions() throws Exception {
        String mysqlSql = """
            SELECT name, 
                   age,
                   CASE 
                       WHEN age < 18 THEN '未成年'
                       WHEN age >= 18 AND age < 60 THEN '成年人'
                       ELSE '老年人'
                   END as age_group,
                   CASE status 
                       WHEN 'active' THEN '活跃'
                       WHEN 'inactive' THEN '非活跃'
                       ELSE '未知'
                   END as status_desc
            FROM users;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证CASE WHEN支持
        assertTrue(shentongSql.contains("CASE"), "应支持CASE表达式");
        assertTrue(shentongSql.contains("WHEN"), "应支持WHEN子句");
        assertTrue(shentongSql.contains("THEN"), "应支持THEN子句");
        assertTrue(shentongSql.contains("ELSE"), "应支持ELSE子句");
        assertTrue(shentongSql.contains("END"), "应支持END关键字");
    }
}
