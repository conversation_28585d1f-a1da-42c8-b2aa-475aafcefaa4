package com.xylink.sqltranspiler.unit.dialects.shentong;

import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.shared.base.BaseShentongConversionTest;

/**
 * 神通数据库ALTER TABLE支持测试
 * 基于神通数据库官方文档 st.md 的ALTER TABLE规范
 * 根据文档第27252-29003行：神通数据库完全支持ALTER TABLE功能
 * 包括：
 * - ADD COLUMN：添加列
 * - DROP COLUMN：删除列
 * - ALTER COLUMN：修改列
 * - MODIFY COLUMN：修改列（Oracle兼容语法）
 * - ADD CONSTRAINT：添加约束
 * - DROP CONSTRAINT：删除约束
 * - RENAME COLUMN：重命名列
 * - RENAME TO：重命名表
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保方言功能符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试方言特有功能
 *
 * <AUTHOR>
 */
public class ShentongAlterTableSupportTest extends BaseShentongConversionTest {

    /**
     * 测试ADD COLUMN支持
     * 根据文档第28054行：支持ADD COLUMN操作
     */
    @Test
    public void testAddColumn() throws Exception {
        String mysqlSql = """
            CREATE TABLE users (
                id INT PRIMARY KEY,
                username VARCHAR(50) NOT NULL
            );
            
            ALTER TABLE users ADD COLUMN email VARCHAR(100);
            ALTER TABLE users ADD COLUMN phone VARCHAR(20) DEFAULT '';
            ALTER TABLE users ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
            ALTER TABLE users ADD COLUMN status VARCHAR(20) DEFAULT 'active' NOT NULL;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证ADD COLUMN支持
        assertTrue(shentongSql.contains("CREATE TABLE"), "应支持CREATE TABLE");
        assertTrue(shentongSql.contains("ALTER TABLE"), "应支持ALTER TABLE");
        assertTrue(shentongSql.contains("ADD COLUMN") || shentongSql.contains("ADD"), 
                   "应支持ADD COLUMN操作");
        assertTrue(shentongSql.contains("email"), "应保持新列名");
        assertTrue(shentongSql.contains("phone"), "应保持新列名");
        assertTrue(shentongSql.contains("created_at"), "应保持新列名");
        assertTrue(shentongSql.contains("status"), "应保持新列名");
        assertTrue(shentongSql.contains("DEFAULT"), "应支持DEFAULT值");
        assertTrue(shentongSql.contains("NOT NULL"), "应支持NOT NULL约束");
        assertTrue(shentongSql.contains("CURRENT_TIMESTAMP"), "应支持函数默认值");
    }

    /**
     * 测试DROP COLUMN支持
     * 根据文档第28054行：支持DROP COLUMN操作
     */
    @Test
    public void testDropColumn() throws Exception {
        String mysqlSql = """
            CREATE TABLE test_table (
                id INT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                old_field1 VARCHAR(50),
                old_field2 INT,
                email VARCHAR(100),
                phone VARCHAR(20)
            );
            
            ALTER TABLE test_table DROP COLUMN old_field1;
            ALTER TABLE test_table DROP COLUMN old_field2;
            ALTER TABLE test_table DROP old_field3;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证DROP COLUMN支持
        assertTrue(shentongSql.contains("CREATE TABLE"), "应支持CREATE TABLE");
        assertTrue(shentongSql.contains("ALTER TABLE"), "应支持ALTER TABLE");
        assertTrue(shentongSql.contains("DROP COLUMN") || shentongSql.contains("DROP"), 
                   "应支持DROP COLUMN操作");
        assertTrue(shentongSql.contains("test_table"), "应保持表名");
        // 注意：删除的列名在结果中可能不会出现，这是正常的
    }

    /**
     * 测试ALTER COLUMN支持
     * 根据文档第27456行：ALTER COLUMN TYPE改变表中一个字段的类型
     */
    @Test
    public void testAlterColumn() throws Exception {
        String mysqlSql = """
            CREATE TABLE products (
                id INT PRIMARY KEY,
                name VARCHAR(50) NOT NULL,
                price DECIMAL(8,2),
                description TEXT,
                status VARCHAR(10)
            );
            
            ALTER TABLE products MODIFY COLUMN name VARCHAR(100) NOT NULL;
            ALTER TABLE products MODIFY COLUMN price DECIMAL(12,2);
            ALTER TABLE products ALTER COLUMN status SET DEFAULT 'active';
            ALTER TABLE products ALTER COLUMN description DROP DEFAULT;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        // 输出转换结果用于调试
        System.out.println("转换结果: " + shentongSql);

        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证ALTER COLUMN支持
        assertTrue(shentongSql.contains("CREATE TABLE"), "应支持CREATE TABLE");
        assertTrue(shentongSql.contains("ALTER TABLE"), "应支持ALTER TABLE");
        assertTrue(shentongSql.contains("ALTER COLUMN") || shentongSql.contains("ALTER"), 
                   "应支持ALTER COLUMN操作");
        assertTrue(shentongSql.contains("products"), "应保持表名");
        assertTrue(shentongSql.contains("name"), "应保持列名");
        assertTrue(shentongSql.contains("price"), "应保持列名");
        assertTrue(shentongSql.contains("status"), "应保持列名");
        assertTrue(shentongSql.contains("description"), "应保持列名");
    }

    /**
     * 测试MODIFY COLUMN支持
     * 根据文档第27474行：MODIFY为兼容Oracle的用法
     */
    @Test
    public void testModifyColumn() throws Exception {
        String mysqlSql = """
            CREATE TABLE employees (
                id INT PRIMARY KEY,
                name VARCHAR(50) NOT NULL,
                salary DECIMAL(8,2),
                department VARCHAR(30)
            );
            
            ALTER TABLE employees MODIFY COLUMN name VARCHAR(100) NOT NULL;
            ALTER TABLE employees MODIFY COLUMN salary DECIMAL(12,2) DEFAULT 0;
            ALTER TABLE employees MODIFY department VARCHAR(50);
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证MODIFY COLUMN支持
        assertTrue(shentongSql.contains("CREATE TABLE"), "应支持CREATE TABLE");
        assertTrue(shentongSql.contains("ALTER TABLE"), "应支持ALTER TABLE");
        assertTrue(shentongSql.contains("MODIFY") || shentongSql.contains("ALTER"), 
                   "应支持MODIFY COLUMN操作");
        assertTrue(shentongSql.contains("employees"), "应保持表名");
        assertTrue(shentongSql.contains("name"), "应保持列名");
        assertTrue(shentongSql.contains("salary"), "应保持列名");
        assertTrue(shentongSql.contains("department"), "应保持列名");
        assertTrue(shentongSql.contains("NOT NULL"), "应支持NOT NULL约束");
        assertTrue(shentongSql.contains("DEFAULT"), "应支持DEFAULT值");
    }

    /**
     * 测试ADD CONSTRAINT支持
     * 根据文档第28055行：支持ADD CONSTRAINT操作
     */
    @Test
    public void testAddConstraint() throws Exception {
        String mysqlSql = """
            CREATE TABLE orders (
                id INT PRIMARY KEY,
                customer_id INT,
                product_id INT,
                quantity INT,
                unit_price DECIMAL(10,2),
                order_date DATE
            );
            
            ALTER TABLE orders ADD CONSTRAINT fk_customer FOREIGN KEY (customer_id) REFERENCES customers(id);
            ALTER TABLE orders ADD CONSTRAINT chk_quantity CHECK (quantity > 0);
            ALTER TABLE orders ADD CONSTRAINT chk_price CHECK (unit_price >= 0);
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证ADD CONSTRAINT支持
        assertTrue(shentongSql.contains("CREATE TABLE"), "应支持CREATE TABLE");
        assertTrue(shentongSql.contains("ALTER TABLE"), "应支持ALTER TABLE");
        assertTrue(shentongSql.contains("ADD CONSTRAINT") || shentongSql.contains("CONSTRAINT"),
                   "应支持ADD CONSTRAINT操作");
        assertTrue(shentongSql.contains("FOREIGN KEY"), "应支持外键约束");
        assertTrue(shentongSql.contains("CHECK"), "应支持检查约束");
        assertTrue(shentongSql.contains("REFERENCES"), "应支持引用关系");
        assertTrue(shentongSql.contains("orders"), "应保持表名");
        assertTrue(shentongSql.contains("fk_customer"), "应保持约束名称");
        assertTrue(shentongSql.contains("chk_quantity"), "应保持约束名称");
        assertTrue(shentongSql.contains("chk_price"), "应保持约束名称");
    }

    /**
     * 测试DROP CONSTRAINT支持
     * 根据文档第28060行：支持DROP CONSTRAINT操作
     */
    @Test
    public void testDropConstraint() throws Exception {
        String mysqlSql = """
            CREATE TABLE test_constraints (
                id INT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                age INT,
                email VARCHAR(100),
                CONSTRAINT chk_age CHECK (age >= 0),
                CONSTRAINT uk_email UNIQUE (email)
            );
            
            ALTER TABLE test_constraints DROP CONSTRAINT chk_age;
            ALTER TABLE test_constraints DROP CONSTRAINT uk_email;
            ALTER TABLE test_constraints DROP PRIMARY KEY;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证DROP CONSTRAINT支持
        assertTrue(shentongSql.contains("CREATE TABLE"), "应支持CREATE TABLE");
        assertTrue(shentongSql.contains("ALTER TABLE"), "应支持ALTER TABLE");
        assertTrue(shentongSql.contains("DROP CONSTRAINT") || shentongSql.contains("DROP"), 
                   "应支持DROP CONSTRAINT操作");
        assertTrue(shentongSql.contains("test_constraints"), "应保持表名");
        // 注意：删除的约束在结果中可能不会出现，这是正常的
    }

    /**
     * 测试RENAME操作支持
     * 根据文档第28058行：支持RENAME COLUMN和RENAME TO操作
     */
    @Test
    public void testRenameOperations() throws Exception {
        String mysqlSql = """
            CREATE TABLE old_table_name (
                id INT PRIMARY KEY,
                old_column_name VARCHAR(100) NOT NULL,
                another_column INT
            );
            
            ALTER TABLE old_table_name RENAME COLUMN old_column_name TO new_column_name;
            ALTER TABLE old_table_name RENAME TO new_table_name;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证RENAME操作支持
        assertTrue(shentongSql.contains("CREATE TABLE"), "应支持CREATE TABLE");
        assertTrue(shentongSql.contains("ALTER TABLE"), "应支持ALTER TABLE");
        assertTrue(shentongSql.contains("RENAME") || shentongSql.contains("old_table_name"), 
                   "应支持RENAME操作");
        // 注意：重命名操作的具体语法可能因数据库而异
    }

    /**
     * 测试复杂ALTER TABLE操作组合
     * 根据文档：ALTER TABLE支持多种操作的组合
     */
    @Test
    public void testComplexAlterTableOperations() throws Exception {
        String mysqlSql = """
            CREATE TABLE comprehensive_table (
                id INT PRIMARY KEY,
                name VARCHAR(50) NOT NULL,
                old_field VARCHAR(30),
                price DECIMAL(8,2)
            );
            
            -- 添加新列
            ALTER TABLE comprehensive_table ADD COLUMN email VARCHAR(100) UNIQUE;
            ALTER TABLE comprehensive_table ADD COLUMN phone VARCHAR(20);
            ALTER TABLE comprehensive_table ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
            
            -- 修改现有列（MySQL标准语法）
            ALTER TABLE comprehensive_table MODIFY COLUMN name VARCHAR(100) NOT NULL;
            ALTER TABLE comprehensive_table MODIFY COLUMN price DECIMAL(12,2);
            
            -- 删除不需要的列
            ALTER TABLE comprehensive_table DROP COLUMN old_field;
            
            -- 添加约束
            ALTER TABLE comprehensive_table ADD CONSTRAINT chk_price CHECK (price >= 0);
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证复杂ALTER TABLE操作支持
        assertTrue(shentongSql.contains("CREATE TABLE"), "应支持CREATE TABLE");
        assertTrue(shentongSql.contains("ALTER TABLE"), "应支持ALTER TABLE");
        assertTrue(shentongSql.contains("ADD") || shentongSql.contains("ADD COLUMN"), 
                   "应支持ADD COLUMN操作");
        assertTrue(shentongSql.contains("MODIFY") || shentongSql.contains("ALTER"), 
                   "应支持MODIFY/ALTER COLUMN操作");
        assertTrue(shentongSql.contains("DROP") || shentongSql.contains("DROP COLUMN"), 
                   "应支持DROP COLUMN操作");
        assertTrue(shentongSql.contains("CONSTRAINT") || shentongSql.contains("CHECK"), 
                   "应支持约束操作");
        assertTrue(shentongSql.contains("comprehensive_table"), "应保持表名");
        assertTrue(shentongSql.contains("email"), "应保持新列名");
        assertTrue(shentongSql.contains("phone"), "应保持新列名");
        assertTrue(shentongSql.contains("created_at"), "应保持新列名");
    }

    /**
     * 测试PostgreSQL ALTER COLUMN TYPE语法被MySQL强制校验正确拒绝
     * 验证PostgreSQL特有语法被正确检测和拒绝
     */
    @Test
    public void testPostgreSqlAlterColumnTypeSyntaxRejection() throws Exception {
        String postgresqlSql = """
            CREATE TABLE auto_increment_test (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                value INT
            );

            -- 添加新的自增列（在删除现有自增列后）
            ALTER TABLE auto_increment_test DROP COLUMN id;
            ALTER TABLE auto_increment_test ADD COLUMN new_id BIGINT AUTO_INCREMENT PRIMARY KEY FIRST;

            -- 修改自增列类型（PostgreSQL语法）
            ALTER TABLE auto_increment_test ALTER COLUMN new_id TYPE BIGINT AUTO_INCREMENT;
            """;

        String result = convertMySqlToShentong(postgresqlSql);

        // 验证PostgreSQL语法被正确拒绝 - 根据MySQL 8.4官方文档，ALTER COLUMN TYPE不是MySQL语法
        assertTrue(result.isEmpty(), "PostgreSQL ALTER COLUMN TYPE语法应该被MySQL强制校验拒绝，转换结果应为空");
    }

    /**
     * 测试MySQL标准ALTER TABLE操作的转换
     * 使用正确的MySQL语法进行ALTER TABLE测试
     */
    @Test
    public void testMySqlStandardAlterTableOperations() throws Exception {
        String mysqlSql = """
            CREATE TABLE auto_increment_test (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                value INT
            );

            -- 添加新的自增列（在删除现有自增列后）
            ALTER TABLE auto_increment_test DROP COLUMN id;
            ALTER TABLE auto_increment_test ADD COLUMN new_id BIGINT AUTO_INCREMENT PRIMARY KEY FIRST;

            -- 修改自增列类型（MySQL语法）
            ALTER TABLE auto_increment_test MODIFY COLUMN new_id BIGINT AUTO_INCREMENT;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);

        // 验证MySQL标准ALTER TABLE操作
        assertTrue(shentongSql.contains("CREATE TABLE"), "应支持CREATE TABLE");
        assertTrue(shentongSql.contains("ALTER TABLE"), "应支持ALTER TABLE");
        assertTrue(shentongSql.contains("AUTO_INCREMENT"), "应支持AUTO_INCREMENT关键字");
        assertTrue(shentongSql.contains("PRIMARY KEY"), "应支持PRIMARY KEY约束");
        assertTrue(shentongSql.contains("auto_increment_test"), "应保持表名");
        assertTrue(shentongSql.contains("DROP COLUMN"), "应支持DROP COLUMN操作");
        assertTrue(shentongSql.contains("ADD COLUMN"), "应支持ADD COLUMN操作");
        assertTrue(shentongSql.contains("MODIFY") || shentongSql.contains("ALTER COLUMN"),
                   "MySQL MODIFY COLUMN语法应该被正确处理");
    }

    /**
     * 测试ALTER TABLE错误处理
     * 验证ALTER TABLE相关的错误情况能够被正确处理
     */
    @Test
    public void testAlterTableErrorHandling() throws Exception {
        String mysqlSql = """
            CREATE TABLE error_test_table (
                id INT PRIMARY KEY,
                name VARCHAR(100) NOT NULL
            );
            
            -- 可能的错误情况（在实际执行时可能失败，但转换应该成功）
            ALTER TABLE error_test_table ADD COLUMN name VARCHAR(50); -- 重复列名
            ALTER TABLE error_test_table DROP COLUMN nonexistent_column; -- 不存在的列
            ALTER TABLE nonexistent_table ADD COLUMN new_col INT; -- 不存在的表
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证错误处理
        assertTrue(shentongSql.contains("CREATE TABLE"), "应正确转换CREATE TABLE");
        assertTrue(shentongSql.contains("ALTER TABLE"), "应正确转换ALTER TABLE");
        assertTrue(shentongSql.contains("error_test_table"), "应保持表名");
        
        // 注意：实际的表结构冲突检查是在数据库执行时进行的，转换器只负责语法转换
    }
}
