package com.xylink.sqltranspiler.unit.dialects.kingbase;

import com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator;

import com.xylink.sqltranspiler.core.ast.TableId;
import com.xylink.sqltranspiler.core.ast.alter.AlterSequence;
import com.xylink.sqltranspiler.core.ast.create.CreateSequence;
import com.xylink.sqltranspiler.core.ast.drop.DropSequence;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 金仓数据库序列功能测试
 * 基于金仓官方文档的测试驱动开发
 * 参考：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/SQL_Statements_3.html#create-sequence
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保方言功能符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试方言特有功能
 *
 * <AUTHOR>
 */
public class KingbaseSequenceTest {

    private KingbaseGenerator generator;

    @BeforeEach
    public void setUp() {
        generator = new KingbaseGenerator();
    }

    @Test
    @DisplayName("测试基本CREATE SEQUENCE语句 - PostgreSQL兼容")
    public void testBasicCreateSequence() {
        TableId sequenceId = new TableId("test_seq");
        CreateSequence createSequence = new CreateSequence(sequenceId);
        String result = generator.generate(createSequence);
        
        assertTrue(result.contains("CREATE SEQUENCE"));
        assertTrue(result.contains("\"test_seq\""));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试带数据类型的CREATE SEQUENCE语句 - PostgreSQL兼容")
    public void testCreateSequenceWithDataType() {
        TableId sequenceId = new TableId("typed_seq");
        CreateSequence createSequence = new CreateSequence(sequenceId, 1L, 1L, 1L, 999999L, 20L, false, false, "BIGINT");
        String result = generator.generate(createSequence);
        
        assertTrue(result.contains("CREATE SEQUENCE"));
        assertTrue(result.contains("\"typed_seq\""));
        assertTrue(result.contains("AS BIGINT"));
        assertTrue(result.contains("START WITH 1"));
        assertTrue(result.contains("INCREMENT BY 1"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试ALTER SEQUENCE语句 - PostgreSQL兼容")
    public void testAlterSequence() {
        TableId sequenceId = new TableId("test_seq");
        AlterSequence alterSequence = new AlterSequence(sequenceId, 100L, 2L, 1L, 9999L, 50L, true, "table.column");
        String result = generator.generate(alterSequence);
        
        assertTrue(result.contains("ALTER SEQUENCE"));
        assertTrue(result.contains("\"test_seq\""));
        assertTrue(result.contains("RESTART WITH 100"));
        assertTrue(result.contains("INCREMENT BY 2"));
        assertTrue(result.contains("CYCLE"));
        assertTrue(result.contains("OWNED BY table.column"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试DROP SEQUENCE CASCADE语句 - PostgreSQL兼容")
    public void testDropSequenceCascade() {
        TableId sequenceId = new TableId("cascade_seq");
        List<TableId> sequenceIds = Arrays.asList(sequenceId);
        DropSequence dropSequence = new DropSequence(sequenceIds, true, true, false);
        String result = generator.generate(dropSequence);
        
        assertTrue(result.contains("DROP SEQUENCE"));
        assertTrue(result.contains("IF EXISTS"));
        assertTrue(result.contains("\"cascade_seq\""));
        assertTrue(result.contains("CASCADE"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试金仓数据库官方文档示例")
    public void testKingbaseOfficialExample() {
        // 根据金仓官方文档的PostgreSQL兼容序列示例
        TableId sequenceId = new TableId("serial_seq");
        CreateSequence createSequence = new CreateSequence(sequenceId, 1L, 1L, 1L, 9223372036854775807L, 1L, false, false, "BIGINT");
        String result = generator.generate(createSequence);
        
        assertTrue(result.contains("CREATE SEQUENCE"));
        assertTrue(result.contains("\"serial_seq\""));
        assertTrue(result.contains("AS BIGINT"));
        assertTrue(result.contains("START WITH 1"));
        assertTrue(result.contains("INCREMENT BY 1"));
        assertTrue(result.contains("MAXVALUE 9223372036854775807"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试金仓数据库MySQL兼容特性")
    public void testKingbaseMySQLCompatibility() {
        // 金仓数据库支持MySQL兼容的序列功能
        TableId sequenceId = new TableId("mysql_compat_seq");
        CreateSequence createSequence = new CreateSequence(sequenceId, 1000L, 1L);
        String result = generator.generate(createSequence);
        
        assertTrue(result.contains("CREATE SEQUENCE"));
        assertTrue(result.contains("\"mysql_compat_seq\""));
        assertTrue(result.contains("START WITH 1000"));
        assertTrue(result.contains("INCREMENT BY 1"));
        assertTrue(result.endsWith(";"));
    }
}
