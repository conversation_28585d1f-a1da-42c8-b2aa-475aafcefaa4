package com.xylink.sqltranspiler.unit.dialects.dameng;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.TableId;
import com.xylink.sqltranspiler.core.ast.create.CreateTableAsSelect;
import com.xylink.sqltranspiler.core.ast.dml.QueryStmt;
import com.xylink.sqltranspiler.core.ast.table.ColumnRel;
import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;

/**
 * 达梦数据库CREATE TABLE AS SELECT功能测试
 * 基于达梦官方文档的测试驱动开发
 * 参考：https://eco.dameng.com/document/dm/zh-cn/pm/definition-statement.html
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保方言功能符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试方言特有功能
 *
 * <AUTHOR>
 */
public class DamengCreateTableAsSelectTest {

    private DamengGenerator generator;

    @BeforeEach
    public void setUp() {
        generator = new DamengGenerator();
    }

    @Test
    @DisplayName("测试基本CREATE TABLE AS SELECT语句")
    public void testBasicCreateTableAsSelect() {
        TableId tableId = new TableId("employee_backup");
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql("SELECT * FROM employees");
        
        CreateTableAsSelect ctas = new CreateTableAsSelect(tableId, queryStmt, null, false, null);
        String result = generator.generate(ctas);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("employee_backup"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("AS"));
        assertTrue(result.contains("SELECT * FROM employees"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试CREATE TABLE IF NOT EXISTS AS SELECT语句")
    public void testCreateTableIfNotExistsAsSelect() {
        TableId tableId = new TableId("safe_backup");
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql("SELECT id, name FROM employees WHERE active = 1");
        
        CreateTableAsSelect ctas = new CreateTableAsSelect(tableId, queryStmt, null, true, null);
        String result = generator.generate(ctas);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("IF NOT EXISTS"));
        assertTrue(result.contains("safe_backup"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("AS"));
        assertTrue(result.contains("SELECT id, name FROM employees WHERE active = 1"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试带列定义的CREATE TABLE AS SELECT语句")
    public void testCreateTableAsSelectWithColumns() {
        TableId tableId = new TableId("employee_summary");
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql("SELECT id, name, salary FROM employees");
        
        List<ColumnRel> columnRels = Arrays.asList(
            new ColumnRel("emp_id", null, null),
            new ColumnRel("emp_name", null, null),
            new ColumnRel("emp_salary", null, null)
        );
        
        CreateTableAsSelect ctas = new CreateTableAsSelect(tableId, queryStmt, null, false, null);
        ctas.setColumnRels(columnRels);
        String result = generator.generate(ctas);

        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("employee_summary"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("(emp_id, emp_name, emp_salary)"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("AS"));
        assertTrue(result.contains("SELECT id, name, salary FROM employees"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试带模式名的CREATE TABLE AS SELECT语句")
    public void testCreateTableAsSelectWithSchema() {
        TableId tableId = new TableId("hr", "employee_backup");
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql("SELECT * FROM hr.employees");
        
        CreateTableAsSelect ctas = new CreateTableAsSelect(tableId, queryStmt, null, false, null);
        String result = generator.generate(ctas);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("hr.employee_backup"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("AS"));
        assertTrue(result.contains("SELECT * FROM hr.employees"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试复杂查询的CREATE TABLE AS SELECT语句")
    public void testCreateTableAsSelectComplexQuery() {
        TableId tableId = new TableId("department_stats");
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql("SELECT d.name as dept_name, COUNT(e.id) as emp_count, AVG(e.salary) as avg_salary " +
                         "FROM departments d LEFT JOIN employees e ON d.id = e.department_id " +
                         "GROUP BY d.id, d.name ORDER BY emp_count DESC");
        
        CreateTableAsSelect ctas = new CreateTableAsSelect(tableId, queryStmt, null, false, null);
        String result = generator.generate(ctas);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("department_stats"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("AS"));
        assertTrue(result.contains("SELECT d.name as dept_name"));
        assertTrue(result.contains("LEFT JOIN"));
        assertTrue(result.contains("GROUP BY"));
        assertTrue(result.contains("ORDER BY"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试带反引号的CREATE TABLE AS SELECT语句")
    public void testCreateTableAsSelectWithBackticks() {
        TableId tableId = new TableId("user_backup");
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql("SELECT `id`, `name`, `email` FROM `users` WHERE `status` = 'active'");
        
        CreateTableAsSelect ctas = new CreateTableAsSelect(tableId, queryStmt, null, false, null);
        String result = generator.generate(ctas);

        // 调试输出
        System.out.println("反引号测试实际输出: " + result);

        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("user_backup"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("AS"));
        assertTrue(result.contains("SELECT id, name, email FROM users"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertFalse(result.contains("`")); // 确保反引号被正确处理
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试空查询的CREATE TABLE AS SELECT语句")
    public void testCreateTableAsSelectEmptyQuery() {
        TableId tableId = new TableId("empty_table");
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(""); // 空查询
        
        CreateTableAsSelect ctas = new CreateTableAsSelect(tableId, queryStmt, null, false, null);
        String result = generator.generate(ctas);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("empty_table"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("AS"));
        assertTrue(result.contains("SELECT * FROM dual")); // 达梦默认查询
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试null查询的CREATE TABLE AS SELECT语句")
    public void testCreateTableAsSelectNullQuery() {
        TableId tableId = new TableId("null_table");
        
        CreateTableAsSelect ctas = new CreateTableAsSelect(tableId, null, null, false, null);
        String result = generator.generate(ctas);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("null_table"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("AS"));
        assertTrue(result.contains("SELECT * FROM dual")); // 达梦默认查询
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试达梦数据库官方文档示例")
    public void testDamengOfficialExample() {
        // 根据达梦官方文档的CREATE TABLE AS SELECT示例
        TableId tableId = new TableId("sales_summary");
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql("SELECT product_id, SUM(quantity) as total_qty, SUM(amount) as total_amount " +
                         "FROM sales WHERE sale_date >= '2024-01-01' " +
                         "GROUP BY product_id HAVING SUM(amount) > 10000");
        
        CreateTableAsSelect ctas = new CreateTableAsSelect(tableId, queryStmt, null, false, null);
        String result = generator.generate(ctas);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("sales_summary"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("AS"));
        assertTrue(result.contains("SELECT product_id, SUM(quantity)"));
        assertTrue(result.contains("GROUP BY"));
        assertTrue(result.contains("HAVING"));
        assertTrue(result.endsWith(";"));
        
        // 验证生成的SQL格式正确，符合达梦数据库标准
        assertTrue(result.startsWith("CREATE TABLE"));
        assertTrue(result.contains(" AS "));
    }

    @Test
    @DisplayName("测试达梦数据库Oracle兼容的CTAS语法")
    public void testDamengOracleCompatibility() {
        // 达梦数据库支持Oracle兼容的CREATE TABLE AS SELECT语法
        TableId tableId = new TableId("oracle_style_table");
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql("SELECT ROWNUM as rn, e.* FROM employees e WHERE ROWNUM <= 100");
        
        CreateTableAsSelect ctas = new CreateTableAsSelect(tableId, queryStmt, null, false, null);
        String result = generator.generate(ctas);

        // 调试输出
        System.out.println("Oracle兼容性测试实际输出: " + result);

        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("oracle_style_table"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("AS"));
        // 根据达梦官方文档，ROWNUM是内置伪列，不需要双引号
        assertTrue(result.contains("SELECT ROWNUM"));
        assertTrue(result.endsWith(";"));
    }
}
