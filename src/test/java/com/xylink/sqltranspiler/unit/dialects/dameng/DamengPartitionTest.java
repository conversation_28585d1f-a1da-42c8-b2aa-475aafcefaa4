package com.xylink.sqltranspiler.unit.dialects.dameng;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import com.xylink.sqltranspiler.shared.base.BaseDamengTest;

/**
 * 达梦数据库分区表测试
 * 基于达梦官方文档：
 * - 达梦数据库分区表：https://eco.dameng.com/document/dm/zh-cn/sql-dev/dmpl-sql-ddl.html#3.1.1
 * - 达梦分区类型：RANGE、LIST、HASH分区
 * - 达梦分区语法：支持MySQL兼容的分区语法
 * 测试原则：
 * 1. 严格遵循达梦官方文档规范
 * 2. 验证MySQL分区表到达梦的正确转换
 * 3. 确保分区键和分区定义的正确性
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保方言功能符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试方言特有功能
 *
 * <AUTHOR>
 */
@DisplayName("达梦数据库分区表测试")
public class DamengPartitionTest extends BaseDamengTest {

    private DamengGenerator generator;

    @BeforeEach
    protected void setUp() {
        super.setUp();
        generator = new DamengGenerator();
    }

    @Test
    @DisplayName("测试RANGE分区表转换")
    void testRangePartitionConversion() {
        // 基于MySQL 8.4官方文档的RANGE分区语法
        // https://dev.mysql.com/doc/refman/8.4/en/partitioning-range.html
        String mysqlSql = """
            CREATE TABLE sales_range (
                id INT NOT NULL AUTO_INCREMENT,
                sale_date DATE NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                region VARCHAR(50),
                PRIMARY KEY (id, sale_date)
            )
            PARTITION BY RANGE (YEAR(sale_date)) (
                PARTITION p2020 VALUES LESS THAN (2021),
                PARTITION p2021 VALUES LESS THAN (2022),
                PARTITION p2022 VALUES LESS THAN (2023),
                PARTITION p_max VALUES LESS THAN MAXVALUE
            );
            """;

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("达梦RANGE分区转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档验证转换结果
        assertDamengConversionRequirements(damengSql);
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("sales_range"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("DATE"), "应保留DATE类型");
        assertTrue(damengSql.contains("DECIMAL(10,2)"), "应保留DECIMAL类型");
        
        // 验证分区处理（达梦支持RANGE分区）
        assertTrue(damengSql.contains("PARTITION") || damengSql.contains("-- 分区"), 
                  "应处理RANGE分区定义");
    }

    @Test
    @DisplayName("测试LIST分区表转换")
    void testListPartitionConversion() {
        // 基于MySQL 8.4官方文档的LIST分区语法
        // https://dev.mysql.com/doc/refman/8.4/en/partitioning-list.html
        String mysqlSql = """
            CREATE TABLE sales_list (
                id INT NOT NULL AUTO_INCREMENT,
                region_id INT NOT NULL,
                sale_amount DECIMAL(10,2),
                PRIMARY KEY (id, region_id)
            )
            PARTITION BY LIST (region_id) (
                PARTITION p_north VALUES IN (1, 2, 3),
                PARTITION p_south VALUES IN (4, 5, 6),
                PARTITION p_east VALUES IN (7, 8, 9),
                PARTITION p_west VALUES IN (10, 11, 12)
            );
            """;

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("达梦LIST分区转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档验证转换结果
        assertDamengConversionRequirements(damengSql);
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("sales_list"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        
        // 验证分区处理（达梦支持LIST分区）
        assertTrue(damengSql.contains("PARTITION") || damengSql.contains("-- 分区"), 
                  "应处理LIST分区定义");
    }

    @Test
    @DisplayName("测试HASH分区表转换")
    void testHashPartitionConversion() {
        // 基于MySQL 8.4官方文档的HASH分区语法
        // https://dev.mysql.com/doc/refman/8.4/en/partitioning-hash.html
        String mysqlSql = """
            CREATE TABLE sales_hash (
                id INT NOT NULL AUTO_INCREMENT,
                customer_id INT NOT NULL,
                order_date DATE,
                total_amount DECIMAL(12,2),
                PRIMARY KEY (id, customer_id)
            )
            PARTITION BY HASH (customer_id)
            PARTITIONS 4;
            """;

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("达梦HASH分区转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档验证转换结果
        assertDamengConversionRequirements(damengSql);
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("sales_hash"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        
        // 验证分区处理（达梦支持HASH分区）
        assertTrue(damengSql.contains("PARTITION") || damengSql.contains("-- 分区"), 
                  "应处理HASH分区定义");
    }

    @Test
    @DisplayName("测试子分区表转换")
    void testSubPartitionConversion() {
        // 基于MySQL 8.4官方文档的子分区语法
        String mysqlSql = """
            CREATE TABLE sales_subpart (
                id INT NOT NULL AUTO_INCREMENT,
                sale_date DATE NOT NULL,
                region_id INT NOT NULL,
                amount DECIMAL(10,2),
                PRIMARY KEY (id, sale_date, region_id)
            )
            PARTITION BY RANGE (YEAR(sale_date))
            SUBPARTITION BY HASH (region_id)
            SUBPARTITIONS 2 (
                PARTITION p2022 VALUES LESS THAN (2023),
                PARTITION p2023 VALUES LESS THAN (2024)
            );
            """;

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("达梦子分区转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档验证转换结果
        assertDamengConversionRequirements(damengSql);
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("sales_subpart"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        
        // 验证子分区处理
        assertTrue(damengSql.contains("PARTITION") || damengSql.contains("-- 分区"), 
                  "应处理子分区定义");
    }

    @Test
    @DisplayName("测试分区表管理语句")
    void testPartitionManagementStatements() {
        // 测试分区管理相关的ALTER TABLE语句
        String[] managementSqls = {
            "ALTER TABLE sales_range ADD PARTITION (PARTITION p2024 VALUES LESS THAN (2025));",
            "ALTER TABLE sales_range DROP PARTITION p2020;",
            "ALTER TABLE sales_hash COALESCE PARTITION 2;",
            "ALTER TABLE sales_list REORGANIZE PARTITION p_north INTO (PARTITION p_north1 VALUES IN (1,2), PARTITION p_north2 VALUES IN (3));"
        };

        for (String sql : managementSqls) {
            System.out.println("测试分区管理语句: " + sql);
            
            Statement statement = MySqlHelper.parseStatement(sql);
            assertNotNull(statement, "分区管理语句应能解析");
            
            String damengSql = generator.generate(statement);
            assertNotNull(damengSql, "达梦转换结果不应为null");
            
            System.out.println("达梦转换结果: " + damengSql);
            assertTrue(damengSql.contains("ALTER TABLE") || damengSql.contains("-- 分区管理"), 
                      "应处理分区管理语句");
        }
    }

    /**
     * 解析CREATE TABLE语句的辅助方法
     */
    protected CreateTable parseCreateTable(String sql) {
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement, "SQL解析不应失败");
        assertTrue(statement instanceof CreateTable, "应解析为CreateTable");
        return (CreateTable) statement;
    }
}
