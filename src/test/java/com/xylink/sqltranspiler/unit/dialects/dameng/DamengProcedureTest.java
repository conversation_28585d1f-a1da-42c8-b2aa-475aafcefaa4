package com.xylink.sqltranspiler.unit.dialects.dameng;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.create.CreateProcedure;
import com.xylink.sqltranspiler.core.ast.drop.DropProcedure;
import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;

/**
 * 达梦数据库存储过程功能测试
 * 基于达梦官方文档的测试驱动开发
 * 参考：https://eco.dameng.com/document/dm/zh-cn/sql-dev/practice-pro.html
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保方言功能符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试方言特有功能
 *
 * <AUTHOR>
 */
public class DamengProcedureTest {

    private DamengGenerator generator;

    @BeforeEach
    public void setUp() {
        generator = new DamengGenerator();
    }

    private CreateProcedure parseCreateProcedure(String sql) {
        return (CreateProcedure) MySqlHelper.parseStatement(sql);
    }

    private DropProcedure parseDropProcedure(String sql) {
        return (DropProcedure) MySqlHelper.parseStatement(sql);
    }

    @Test
    @DisplayName("测试基本CREATE PROCEDURE语句")
    public void testBasicCreateProcedure() {
        String sql = "CREATE PROCEDURE update_employee_salary(IN emp_id INT, IN new_salary DECIMAL(10,2)) " +
                    "BEGIN " +
                    "UPDATE employees SET salary = new_salary WHERE id = emp_id; " +
                    "END;";
        CreateProcedure createProcedure = parseCreateProcedure(sql);
        String result = generator.generate(createProcedure);
        
        assertTrue(result.contains("CREATE PROCEDURE"));
        assertTrue(result.contains("update_employee_salary"));
        assertTrue(result.contains("IN emp_id INT"));
        assertTrue(result.contains("IN new_salary DECIMAL(10,2)"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试CREATE OR REPLACE PROCEDURE语句 - 达梦Oracle兼容")
    public void testCreateOrReplaceProcedure() {
        String sql = "CREATE PROCEDURE calculate_department_bonus(dept_id INT) " +
                    "BEGIN " +
                    "UPDATE employees SET bonus = salary * 0.1 WHERE department_id = dept_id; " +
                    "COMMIT; " +
                    "END;";
        CreateProcedure createProcedure = parseCreateProcedure(sql);
        String result = generator.generate(createProcedure);

        assertTrue(result.contains("CREATE PROCEDURE"));
        assertTrue(result.contains("calculate_department_bonus"));
        assertTrue(result.contains("BEGIN"));
        assertTrue(result.contains("END"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试带OUT参数的存储过程")
    public void testProcedureWithOutParameters() {
        String sql = "CREATE PROCEDURE get_employee_info(IN emp_id INT, OUT emp_name VARCHAR(100), OUT emp_salary DECIMAL(10,2)) " +
                    "BEGIN " +
                    "SELECT name, salary INTO emp_name, emp_salary FROM employees WHERE id = emp_id; " +
                    "END;";
        CreateProcedure createProcedure = parseCreateProcedure(sql);
        String result = generator.generate(createProcedure);
        
        assertTrue(result.contains("CREATE PROCEDURE"));
        assertTrue(result.contains("get_employee_info"));
        assertTrue(result.contains("IN emp_id INT"));
        assertTrue(result.contains("OUT emp_name VARCHAR(100)"));
        assertTrue(result.contains("OUT emp_salary DECIMAL(10,2)"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试复杂的CREATE PROCEDURE语句")
    public void testComplexCreateProcedure() {
        String sql = "CREATE PROCEDURE process_monthly_payroll(IN month_year VARCHAR(7)) " +
                    "BEGIN " +
                    "DECLARE emp_id INT; " +
                    "DECLARE emp_salary DECIMAL(10,2); " +
                    "DECLARE total_payroll DECIMAL(15,2) DEFAULT 0; " +
                    "DECLARE done INT DEFAULT FALSE; " +
                    "DECLARE emp_cursor CURSOR FOR SELECT id, salary FROM employees WHERE status = 'ACTIVE'; " +
                    "DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE; " +
                    "OPEN emp_cursor; " +
                    "read_loop: LOOP " +
                    "FETCH emp_cursor INTO emp_id, emp_salary; " +
                    "IF done THEN LEAVE read_loop; END IF; " +
                    "INSERT INTO payroll (employee_id, month_year, amount) VALUES (emp_id, month_year, emp_salary); " +
                    "SET total_payroll = total_payroll + emp_salary; " +
                    "END LOOP; " +
                    "CLOSE emp_cursor; " +
                    "INSERT INTO payroll_summary (month_year, total_amount) VALUES (month_year, total_payroll); " +
                    "COMMIT; " +
                    "END;";
        CreateProcedure createProcedure = parseCreateProcedure(sql);
        String result = generator.generate(createProcedure);

        assertTrue(result.contains("CREATE PROCEDURE"));
        assertTrue(result.contains("process_monthly_payroll"));
        assertTrue(result.contains("DECLARE"));
        assertTrue(result.contains("CURSOR"));
        assertTrue(result.contains("BEGIN"));
        assertTrue(result.contains("END"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试基本DROP PROCEDURE语句")
    public void testBasicDropProcedure() {
        String sql = "DROP PROCEDURE update_employee_salary;";
        DropProcedure dropProcedure = parseDropProcedure(sql);
        String result = generator.generate(dropProcedure);
        
        assertTrue(result.contains("DROP PROCEDURE"));
        assertTrue(result.contains("update_employee_salary"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试DROP PROCEDURE IF EXISTS语句")
    public void testDropProcedureIfExists() {
        String sql = "DROP PROCEDURE IF EXISTS calculate_department_bonus;";
        DropProcedure dropProcedure = parseDropProcedure(sql);
        String result = generator.generate(dropProcedure);
        
        assertTrue(result.contains("DROP PROCEDURE"));
        assertTrue(result.contains("IF EXISTS"));
        assertTrue(result.contains("calculate_department_bonus"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试达梦数据库Oracle兼容的存储过程语法")
    public void testDamengOracleCompatibility() {
        // 根据达梦官方文档，达梦数据库支持Oracle兼容的存储过程语法
        // 使用MySQL兼容语法进行测试，生成器会转换为达梦格式
        String sql = "CREATE PROCEDURE archive_old_records(cutoff_date DATE) " +
                    "BEGIN " +
                    "DECLARE records_count INT DEFAULT 0; " +
                    "SELECT COUNT(*) INTO records_count FROM transactions WHERE created_date < cutoff_date; " +
                    "INSERT INTO archived_transactions SELECT * FROM transactions WHERE created_date < cutoff_date; " +
                    "DELETE FROM transactions WHERE created_date < cutoff_date; " +
                    "COMMIT; " +
                    "END;";
        CreateProcedure createProcedure = parseCreateProcedure(sql);
        String result = generator.generate(createProcedure);

        assertTrue(result.contains("CREATE PROCEDURE"));
        assertTrue(result.contains("archive_old_records"));
        assertTrue(result.contains("DATE"));
        assertTrue(result.contains("BEGIN"));
        assertTrue(result.contains("END"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试达梦数据库存储过程的反引号转换")
    public void testDamengProcedureBacktickConversion() {
        String sql = "CREATE PROCEDURE `update_user_status`(IN `user_id` INT, IN `new_status` VARCHAR(20)) " +
                    "BEGIN " +
                    "UPDATE `users` SET `status` = `new_status`, `updated_at` = NOW() WHERE `id` = `user_id`; " +
                    "END;";
        CreateProcedure createProcedure = parseCreateProcedure(sql);
        String result = generator.generate(createProcedure);

        assertTrue(result.contains("CREATE PROCEDURE"));
        assertTrue(result.contains("\"update_user_status\""));
        assertTrue(result.contains("\"user_id\""));
        assertTrue(result.contains("\"new_status\""));
        assertTrue(result.contains("\"users\""));
        assertFalse(result.contains("`")); // 确保反引号被转换为双引号
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试达梦数据库异常处理存储过程")
    public void testDamengProcedureExceptionHandling() {
        String sql = "CREATE PROCEDURE safe_transfer(from_account INT, to_account INT, amount DECIMAL(10,2)) " +
                    "BEGIN " +
                    "DECLARE from_balance DECIMAL(10,2) DEFAULT 0; " +
                    "DECLARE EXIT HANDLER FOR SQLEXCEPTION " +
                    "BEGIN " +
                    "ROLLBACK; " +
                    "RESIGNAL; " +
                    "END; " +
                    "START TRANSACTION; " +
                    "SELECT balance INTO from_balance FROM accounts WHERE id = from_account; " +
                    "IF from_balance < amount THEN " +
                    "SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Insufficient funds'; " +
                    "END IF; " +
                    "UPDATE accounts SET balance = balance - amount WHERE id = from_account; " +
                    "UPDATE accounts SET balance = balance + amount WHERE id = to_account; " +
                    "COMMIT; " +
                    "END;";
        CreateProcedure createProcedure = parseCreateProcedure(sql);
        String result = generator.generate(createProcedure);

        assertTrue(result.contains("CREATE PROCEDURE"));
        assertTrue(result.contains("safe_transfer"));
        assertTrue(result.contains("HANDLER"));
        assertTrue(result.contains("ROLLBACK"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试达梦数据库官方文档示例")
    public void testDamengOfficialExample() {
        // 根据达梦官方文档的存储过程示例
        // 使用MySQL兼容语法进行测试，生成器会转换为达梦格式
        String sql = "CREATE PROCEDURE update_employee_grade() " +
                    "BEGIN " +
                    "UPDATE employees SET grade = " +
                    "CASE " +
                    "WHEN salary >= 10000 THEN 'A' " +
                    "WHEN salary >= 7000 THEN 'B' " +
                    "WHEN salary >= 5000 THEN 'C' " +
                    "ELSE 'D' " +
                    "END; " +
                    "COMMIT; " +
                    "END;";
        CreateProcedure createProcedure = parseCreateProcedure(sql);
        String result = generator.generate(createProcedure);

        assertTrue(result.contains("CREATE PROCEDURE"));
        assertTrue(result.contains("update_employee_grade"));
        assertTrue(result.contains("CASE"));
        assertTrue(result.contains("WHEN"));
        assertTrue(result.contains("COMMIT"));
        assertTrue(result.endsWith(";"));
    }
}
