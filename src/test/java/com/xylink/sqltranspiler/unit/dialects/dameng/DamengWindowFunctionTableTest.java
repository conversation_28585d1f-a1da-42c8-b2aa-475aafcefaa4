package com.xylink.sqltranspiler.unit.dialects.dameng;

import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 达梦数据库窗口函数表结构测试
 * 根据达梦官方文档，测试MySQL窗口函数相关表结构到达梦的转换
 * 参考：https://eco.dameng.com/document/dm/zh-cn/sql-dev/dmpl-sql-datatype.html
 * 测试覆盖：
 * - 窗口函数支持的表结构
 * - 分析函数的数据类型要求
 * - 排序和分组相关的字段定义
 * - 聚合窗口函数的数据存储
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保函数转换符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试复杂函数组合的转换正确性
 *
 * <AUTHOR>
 */
@DisplayName("达梦数据库窗口函数表结构测试")
public class DamengWindowFunctionTableTest {

    private DamengGenerator generator;

    @BeforeEach
    void setUp() {
        generator = new DamengGenerator();
    }

    private CreateTable parseCreateTable(String sql) {
        return (CreateTable) MySqlHelper.parseStatement(sql);
    }

    @Test
    @DisplayName("测试基本窗口函数支持的表结构")
    void testBasicWindowFunctionTableStructure() {
        String mysqlSql = "CREATE TABLE window_function_data (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "department_id INT NOT NULL, " +
                "employee_id INT NOT NULL, " +
                "salary DECIMAL(10,2) NOT NULL, " +
                "bonus DECIMAL(8,2), " +
                "hire_date DATE NOT NULL, " +
                "performance_score DECIMAL(5,2), " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "INDEX idx_dept_salary (department_id, salary), " +
                "INDEX idx_employee_date (employee_id, hire_date)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("基本窗口函数支持的表结构转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证窗口函数支持的表结构
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("window_function_data"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY") || damengSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为IDENTITY或保留");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("department_id"), "应保留部门ID列");
        assertTrue(damengSql.contains("employee_id"), "应保留员工ID列");
        assertTrue(damengSql.contains("salary"), "应保留薪资列");
        assertTrue(damengSql.contains("bonus"), "应保留奖金列");
        assertTrue(damengSql.contains("hire_date"), "应保留入职日期列");
        assertTrue(damengSql.contains("performance_score"), "应保留绩效分数列");
        assertTrue(damengSql.contains("created_at"), "应保留创建时间列");
        assertTrue(damengSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(damengSql.contains("DECIMAL(10,2)"), "应保留薪资DECIMAL类型");
        assertTrue(damengSql.contains("DECIMAL(8,2)"), "应保留奖金DECIMAL类型");
        assertTrue(damengSql.contains("DECIMAL(5,2)"), "应保留绩效DECIMAL类型");
        assertTrue(damengSql.contains("DATE"), "应保留DATE类型");
        assertTrue(damengSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(damengSql.contains("DEFAULT"), "应保留默认值");
        
        // 验证窗口函数相关索引处理
        assertTrue(damengSql.contains("idx_dept_salary") || damengSql.contains("department_id"), 
                  "应处理部门薪资索引");
        assertTrue(damengSql.contains("idx_employee_date") || damengSql.contains("employee_id"), 
                  "应处理员工日期索引");
        assertFalse(damengSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试排名函数支持的表结构")
    void testRankingFunctionTableStructure() {
        String mysqlSql = "CREATE TABLE ranking_function_data (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "category_id INT NOT NULL, " +
                "subcategory_id INT, " +
                "product_name VARCHAR(200) NOT NULL, " +
                "sales_amount DECIMAL(15,2) NOT NULL, " +
                "sales_quantity INT NOT NULL, " +
                "sales_date DATE NOT NULL, " +
                "region_id INT, " +
                "rank_value INT, " +
                "dense_rank_value INT, " +
                "row_number_value INT, " +
                "ntile_value INT, " +
                "INDEX idx_category_sales (category_id, sales_amount DESC), " +
                "INDEX idx_date_region (sales_date, region_id)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("排名函数支持的表结构转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证排名函数支持的表结构
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("ranking_function_data"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY") || damengSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为IDENTITY或保留");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("category_id"), "应保留分类ID列");
        assertTrue(damengSql.contains("subcategory_id"), "应保留子分类ID列");
        assertTrue(damengSql.contains("product_name"), "应保留产品名称列");
        assertTrue(damengSql.contains("sales_amount"), "应保留销售金额列");
        assertTrue(damengSql.contains("sales_quantity"), "应保留销售数量列");
        assertTrue(damengSql.contains("sales_date"), "应保留销售日期列");
        assertTrue(damengSql.contains("region_id"), "应保留区域ID列");
        assertTrue(damengSql.contains("rank_value"), "应保留排名值列");
        assertTrue(damengSql.contains("dense_rank_value"), "应保留密集排名值列");
        assertTrue(damengSql.contains("row_number_value"), "应保留行号值列");
        assertTrue(damengSql.contains("ntile_value"), "应保留分组值列");
        assertTrue(damengSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(damengSql.contains("VARCHAR(200)"), "应保留VARCHAR类型");
        assertTrue(damengSql.contains("DECIMAL(15,2)"), "应保留销售金额DECIMAL类型");
        assertTrue(damengSql.contains("DATE"), "应保留DATE类型");
        
        // 验证排名函数相关索引处理
        assertTrue(damengSql.contains("idx_category_sales") || damengSql.contains("category_id"), 
                  "应处理分类销售索引");
        assertTrue(damengSql.contains("idx_date_region") || damengSql.contains("sales_date"), 
                  "应处理日期区域索引");
        assertFalse(damengSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试聚合窗口函数支持的表结构")
    void testAggregateWindowFunctionTableStructure() {
        String mysqlSql = "CREATE TABLE aggregate_window_data (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "group_id INT NOT NULL, " +
                "sequence_number INT NOT NULL, " +
                "value DECIMAL(12,4) NOT NULL, " +
                "running_sum DECIMAL(20,4), " +
                "running_avg DECIMAL(15,6), " +
                "running_count INT, " +
                "running_min DECIMAL(12,4), " +
                "running_max DECIMAL(12,4), " +
                "moving_avg_3 DECIMAL(15,6), " +
                "moving_sum_5 DECIMAL(20,4), " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "INDEX idx_group_seq (group_id, sequence_number)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("聚合窗口函数支持的表结构转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证聚合窗口函数支持的表结构
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("aggregate_window_data"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY") || damengSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为IDENTITY或保留");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("group_id"), "应保留分组ID列");
        assertTrue(damengSql.contains("sequence_number"), "应保留序列号列");
        assertTrue(damengSql.contains("value"), "应保留值列");
        assertTrue(damengSql.contains("running_sum"), "应保留累计和列");
        assertTrue(damengSql.contains("running_avg"), "应保留累计平均值列");
        assertTrue(damengSql.contains("running_count"), "应保留累计计数列");
        assertTrue(damengSql.contains("running_min"), "应保留累计最小值列");
        assertTrue(damengSql.contains("running_max"), "应保留累计最大值列");
        assertTrue(damengSql.contains("moving_avg_3"), "应保留移动平均值列");
        assertTrue(damengSql.contains("moving_sum_5"), "应保留移动和列");
        assertTrue(damengSql.contains("created_at"), "应保留创建时间列");
        assertTrue(damengSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(damengSql.contains("DECIMAL(12,4)"), "应保留基础值DECIMAL类型");
        assertTrue(damengSql.contains("DECIMAL(20,4)"), "应保留累计和DECIMAL类型");
        assertTrue(damengSql.contains("DECIMAL(15,6)"), "应保留平均值DECIMAL类型");
        assertTrue(damengSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(damengSql.contains("DEFAULT"), "应保留默认值");
        
        // 验证聚合窗口函数相关索引处理
        assertTrue(damengSql.contains("idx_group_seq") || damengSql.contains("group_id"), 
                  "应处理分组序列索引");
        assertFalse(damengSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试分析函数支持的表结构")
    void testAnalyticFunctionTableStructure() {
        String mysqlSql = "CREATE TABLE analytic_function_data (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "partition_key VARCHAR(50) NOT NULL, " +
                "order_key INT NOT NULL, " +
                "measure_value DECIMAL(18,6) NOT NULL, " +
                "lag_value DECIMAL(18,6), " +
                "lead_value DECIMAL(18,6), " +
                "first_value DECIMAL(18,6), " +
                "last_value DECIMAL(18,6), " +
                "nth_value DECIMAL(18,6), " +
                "percent_rank DECIMAL(10,8), " +
                "cume_dist DECIMAL(10,8), " +
                "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, " +
                "INDEX idx_partition_order (partition_key, order_key)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("分析函数支持的表结构转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证分析函数支持的表结构
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("analytic_function_data"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY") || damengSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为IDENTITY或保留");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("partition_key"), "应保留分区键列");
        assertTrue(damengSql.contains("order_key"), "应保留排序键列");
        assertTrue(damengSql.contains("measure_value"), "应保留度量值列");
        assertTrue(damengSql.contains("lag_value"), "应保留滞后值列");
        assertTrue(damengSql.contains("lead_value"), "应保留领先值列");
        assertTrue(damengSql.contains("first_value"), "应保留首值列");
        assertTrue(damengSql.contains("last_value"), "应保留末值列");
        assertTrue(damengSql.contains("nth_value"), "应保留第N值列");
        assertTrue(damengSql.contains("percent_rank"), "应保留百分比排名列");
        assertTrue(damengSql.contains("cume_dist"), "应保留累积分布列");
        assertTrue(damengSql.contains("updated_at"), "应保留更新时间列");
        assertTrue(damengSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(damengSql.contains("VARCHAR(50)"), "应保留VARCHAR类型");
        assertTrue(damengSql.contains("DECIMAL(18,6)"), "应保留度量值DECIMAL类型");
        assertTrue(damengSql.contains("DECIMAL(10,8)"), "应保留百分比DECIMAL类型");
        assertTrue(damengSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(damengSql.contains("DEFAULT"), "应保留默认值");
        
        // 验证分析函数相关索引处理
        assertTrue(damengSql.contains("idx_partition_order") || damengSql.contains("partition_key"), 
                  "应处理分区排序索引");
        assertFalse(damengSql.contains("`"), "不应包含反引号");
    }
}
