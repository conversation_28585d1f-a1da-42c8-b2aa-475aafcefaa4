package com.xylink.sqltranspiler.unit.dialects.kingbase;

import com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 金仓数据库字符集和编码转换测试
 * 根据金仓官方文档，测试MySQL字符集到金仓字符集的转换
 * 参考：https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保方言功能符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试方言特有功能
 *
 * <AUTHOR>
 */
@DisplayName("金仓数据库字符集和编码转换测试")
public class KingbaseCharsetAndEncodingTest {

    private KingbaseGenerator generator;

    @BeforeEach
    void setUp() {
        generator = new KingbaseGenerator();
    }

    private CreateTable parseCreateTable(String sql) {
        return (CreateTable) MySqlHelper.parseStatement(sql);
    }

    @Test
    @DisplayName("测试MySQL字符集转换为金仓字符集")
    void testMySqlCharsetToKingbaseCharset() {
        String mysqlSql = "CREATE TABLE test_charset (" +
                "id INT PRIMARY KEY, " +
                "name VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci, " +
                "description TEXT CHARACTER SET latin1" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("字符集转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证基本转换
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"test_charset\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("\"name\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"description\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("VARCHAR(100)"), "应保留VARCHAR类型");
        assertTrue(kingbaseSql.contains("TEXT"), "应保留TEXT类型");

        // 验证反引号移除
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试表级别字符集转换")
    void testTableCharsetConversion() {
        String mysqlSql = "CREATE TABLE test_db_charset (" +
                "id INT PRIMARY KEY, " +
                "name VARCHAR(100)" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("表级字符集转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证基本转换
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"test_db_charset\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("\"name\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("VARCHAR(100)"), "应保留VARCHAR类型");

        // 验证反引号移除
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试多列字符集转换")
    void testMultiColumnCharsetConversion() {
        String mysqlSql = "CREATE TABLE charset_table (" +
                "id INT PRIMARY KEY, " +
                "utf8_col VARCHAR(100) CHARACTER SET utf8, " +
                "utf8mb4_col VARCHAR(100) CHARACTER SET utf8mb4" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("多列字符集转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证基本转换
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"charset_table\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("\"utf8_col\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"utf8mb4_col\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("VARCHAR(100)"), "应保留VARCHAR类型");

        // 验证反引号移除
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试多种字符集混合转换")
    void testMixedCharsetConversion() {
        String mysqlSql = "CREATE TABLE mixed_charset (" +
                "id INT PRIMARY KEY, " +
                "utf8_col VARCHAR(100) CHARACTER SET utf8, " +
                "utf8mb4_col VARCHAR(100) CHARACTER SET utf8mb4, " +
                "latin1_col VARCHAR(100) CHARACTER SET latin1, " +
                "ascii_col VARCHAR(100) CHARACTER SET ascii, " +
                "binary_col VARBINARY(100)" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("混合字符集转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证基本转换
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"mixed_charset\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("\"utf8_col\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"utf8mb4_col\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"latin1_col\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"ascii_col\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"binary_col\""), "列名应使用双引号");

        // 验证数据类型保留
        assertTrue(kingbaseSql.contains("VARCHAR(100)"), "应保留VARCHAR类型");
        assertTrue(kingbaseSql.contains("VARBINARY(100)"), "应保留VARBINARY类型");

        // 验证反引号移除
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试字符集相关表结构转换")
    void testCharsetTableStructureConversion() {
        String mysqlSql = "CREATE TABLE charset_functions (" +
                "id INT PRIMARY KEY, " +
                "name VARCHAR(100) CHARACTER SET utf8mb4, " +
                "description TEXT CHARACTER SET latin1" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("字符集表结构转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证基本转换
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"charset_functions\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("\"name\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"description\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("VARCHAR(100)"), "应保留VARCHAR类型");
        assertTrue(kingbaseSql.contains("TEXT"), "应保留TEXT类型");

        // 验证反引号移除
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试中文字符集处理")
    void testChineseCharsetHandling() {
        String mysqlSql = "CREATE TABLE chinese_test (" +
                "id INT PRIMARY KEY, " +
                "chinese_name VARCHAR(100) CHARACTER SET utf8mb4 COMMENT '中文姓名', " +
                "english_name VARCHAR(100) CHARACTER SET latin1 COMMENT 'English Name'" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='中文测试表';";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("中文字符集处理结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证基本转换
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"chinese_test\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("\"chinese_name\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"english_name\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("VARCHAR(100)"), "应保留VARCHAR类型");

        // 验证反引号移除
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }
}
