package com.xylink.sqltranspiler.unit.dialects.dameng;

import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import com.xylink.sqltranspiler.shared.base.BaseDamengTest;

/**
 * 达梦数据库数据类型转换测试
 * 基于达梦官方文档的测试驱动开发
 * 参考：https://eco.dameng.com/document/dm/zh-cn/pm/data-type.html
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保数据类型映射符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试数据类型边界情况
 *
 * <AUTHOR>
 */
public class DamengDataTypeTest extends BaseDamengTest {

    @Test
    @DisplayName("测试整数类型转换 - 达梦官方文档标准")
    public void testIntegerTypeConversion() {
        String sql = "CREATE TABLE test_integers (" +
                    "tiny_col TINYINT, " +
                    "small_col SMALLINT, " +
                    "medium_col MEDIUMINT, " +
                    "int_col INT, " +
                    "big_col BIGINT" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);

        assertTrue(result.contains("CREATE TABLE"));
        // 根据达梦官方文档，普通标识符不需要双引号
        // 参考：https://eco.dameng.com/document/dm/zh-cn/pm/sql-appendix
        assertTrue(result.contains("test_integers")); // 普通表名无需双引号
        assertTrue(result.contains("tiny_col TINYINT")); // 根据达梦官方文档第3.2节，TINYINT原生支持，保持不变
        assertTrue(result.contains("small_col SMALLINT"));
        assertTrue(result.contains("medium_col INT")); // MEDIUMINT转换为INT
        assertTrue(result.contains("int_col INT"));
        assertTrue(result.contains("big_col BIGINT"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试布尔类型转换 - TINYINT(1)转BIT")
    public void testBooleanTypeConversion() {
        String sql = "CREATE TABLE test_boolean (" +
                    "is_active TINYINT(1), " +
                    "is_deleted BOOLEAN" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);

        assertTrue(result.contains("CREATE TABLE"));
        // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("test_boolean")); // 普通表名无需双引号
        assertTrue(result.contains("is_active BIT")); // TINYINT(1)转换为BIT，普通列名无需双引号
        assertTrue(result.contains("is_deleted BIT")); // BOOLEAN转换为BIT
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试保留关键字标识符引用 - 达梦官方文档规范")
    public void testReservedKeywordQuoting() {
        // 测试保留关键字表名和列名
        String sql = "CREATE TABLE `order` (" +
                    "`table` VARCHAR(50), " +
                    "`index` INT, " +
                    "`user` VARCHAR(100), " +
                    "normal_column VARCHAR(50)" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);

        assertTrue(result.contains("CREATE TABLE"));
        // 根据达梦官方文档，保留关键字需要双引号
        assertTrue(result.contains("\"order\"")); // order是保留关键字，需要双引号
        assertTrue(result.contains("\"table\" VARCHAR")); // table是保留关键字，需要双引号
        assertTrue(result.contains("\"index\" INT")); // index是保留关键字，需要双引号
        assertTrue(result.contains("\"user\" VARCHAR")); // user是保留关键字，需要双引号
        // 普通列名不需要双引号
        assertTrue(result.contains("normal_column VARCHAR")); // 普通列名无需双引号
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试字符串类型转换")
    public void testStringTypeConversion() {
        String sql = "CREATE TABLE test_strings (" +
                    "char_col CHAR(10), " +
                    "varchar_col VARCHAR(255), " +
                    "tiny_text TINYTEXT, " +
                    "text_col TEXT, " +
                    "medium_text MEDIUMTEXT, " +
                    "long_text LONGTEXT" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);

        assertTrue(result.contains("CREATE TABLE"));
        // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("test_strings"));
        assertTrue(result.contains("char_col CHAR(10)"));
        assertTrue(result.contains("varchar_col VARCHAR(255)"));
        assertTrue(result.contains("tiny_text CLOB")); // TINYTEXT转换为CLOB，基于达梦官方DTS文档
        assertTrue(result.contains("text_col CLOB")); // TEXT转换为CLOB
        assertTrue(result.contains("medium_text CLOB")); // MEDIUMTEXT转换为CLOB
        assertTrue(result.contains("long_text CLOB")); // LONGTEXT转换为CLOB
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试二进制类型转换")
    public void testBinaryTypeConversion() {
        String sql = "CREATE TABLE test_binary (" +
                    "binary_col BINARY(16), " +
                    "varbinary_col VARBINARY(255), " +
                    "tiny_blob TINYBLOB, " +
                    "blob_col BLOB, " +
                    "medium_blob MEDIUMBLOB, " +
                    "long_blob LONGBLOB" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);

        assertTrue(result.contains("CREATE TABLE"));
        // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("test_binary"));
        assertTrue(result.contains("binary_col BINARY(16)"));
        assertTrue(result.contains("varbinary_col VARBINARY(255)"));
        assertTrue(result.contains("tiny_blob BLOB")); // TINYBLOB转换为BLOB
        assertTrue(result.contains("blob_col BLOB"));
        assertTrue(result.contains("medium_blob BLOB")); // MEDIUMBLOB转换为BLOB
        assertTrue(result.contains("long_blob BLOB")); // LONGBLOB转换为BLOB
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试数值类型转换")
    public void testNumericTypeConversion() {
        String sql = "CREATE TABLE test_numeric (" +
                    "decimal_col DECIMAL(10,2), " +
                    "numeric_col NUMERIC(15,4), " +
                    "float_col FLOAT, " +
                    "double_col DOUBLE, " +
                    "real_col REAL" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);

        assertTrue(result.contains("CREATE TABLE"));
        // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("test_numeric"));
        assertTrue(result.contains("decimal_col DECIMAL(10,2)"));
        assertTrue(result.contains("numeric_col NUMERIC(15,4)"));
        assertTrue(result.contains("float_col FLOAT"));
        assertTrue(result.contains("double_col DOUBLE"));
        assertTrue(result.contains("real_col REAL"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试日期时间类型转换")
    public void testDateTimeTypeConversion() {
        String sql = "CREATE TABLE test_datetime (" +
                    "date_col DATE, " +
                    "time_col TIME, " +
                    "datetime_col DATETIME, " +
                    "timestamp_col TIMESTAMP, " +
                    "year_col YEAR" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);

        // 添加调试输出
        System.out.println("日期时间类型转换测试实际输出: " + result);

        assertTrue(result.contains("CREATE TABLE"));
        // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("test_datetime"));
        assertTrue(result.contains("date_col DATE"));
        assertTrue(result.contains("time_col TIME"));
        // 根据达梦官方文档，达梦数据库不支持DATETIME类型，DATETIME被正确映射为TIMESTAMP
        // 参考：https://eco.dameng.com/document/dm/zh-cn/sql-dev/dmpl-sql-datatype.html#3-4-日期时间数据类型
        assertTrue(result.contains("datetime_col TIMESTAMP")); // DATETIME转换为TIMESTAMP
        assertTrue(result.contains("timestamp_col TIMESTAMP"));
        assertTrue(result.contains("year_col INT")); // YEAR转换为INT
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试特殊类型转换 - ENUM和SET")
    public void testSpecialTypeConversion() {
        String sql = "CREATE TABLE test_special (" +
                    "enum_col ENUM('small', 'medium', 'large'), " +
                    "set_col SET('read', 'write', 'execute'), " +
                    "json_col JSON" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);

        // 添加调试输出
        System.out.println("特殊类型转换测试实际输出: " + result);

        assertTrue(result.contains("CREATE TABLE"));
        // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("test_special"));
        assertTrue(result.contains("enum_col VARCHAR")); // ENUM转换为VARCHAR
        assertTrue(result.contains("set_col VARCHAR")); // SET转换为VARCHAR
        // 根据达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/pm/json.html
        // 达梦数据库完全支持JSON数据类型，应该保持JSON而不是转换为CLOB
        assertTrue(result.contains("json_col JSON")); // JSON保持为JSON（达梦原生支持）
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试VARCHAR长度限制处理")
    public void testVarcharLengthLimit() {
        String sql = "CREATE TABLE test_varchar_limit (" +
                    "normal_varchar VARCHAR(255), " +
                    "large_varchar VARCHAR(50000)" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);

        // 添加调试输出
        System.out.println("VARCHAR长度限制测试实际输出: " + result);

        assertTrue(result.contains("CREATE TABLE"));
        // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("test_varchar_limit"));
        assertTrue(result.contains("normal_varchar VARCHAR(255)"));
        assertTrue(result.contains("large_varchar CLOB")); // 超过32767转换为CLOB
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试AUTO_INCREMENT转换为IDENTITY")
    public void testAutoIncrementConversion() {
        String sql = "CREATE TABLE test_auto_increment (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY, " +
                    "name VARCHAR(100)" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);

        assertTrue(result.contains("CREATE TABLE"));
        // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("test_auto_increment"));
        assertTrue(result.contains("id INT IDENTITY(1,1)")); // AUTO_INCREMENT转换为IDENTITY
        assertTrue(result.contains("PRIMARY KEY"));
        assertTrue(result.contains("name VARCHAR(100)"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试默认值转换")
    public void testDefaultValueConversion() {
        String sql = "CREATE TABLE test_defaults (" +
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                    "updated_at DATETIME DEFAULT NOW(), " +
                    "status VARCHAR(20) DEFAULT 'active'" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);

        // 添加调试输出
        System.out.println("默认值转换测试实际输出: " + result);

        assertTrue(result.contains("CREATE TABLE"));
        // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("test_defaults"));
        assertTrue(result.contains("created_at TIMESTAMP"));
        // 根据达梦官方文档，DATETIME被转换为TIMESTAMP
        assertTrue(result.contains("updated_at TIMESTAMP"));
        assertTrue(result.contains("status VARCHAR(20)"));
        assertTrue(result.contains("DEFAULT"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试达梦数据库官方文档示例")
    public void testDamengOfficialExample() {
        // 根据达梦官方文档的数据类型映射示例
        String sql = "CREATE TABLE employee_info (" +
                    "emp_id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
                    "emp_name VARCHAR(100) NOT NULL, " +
                    "is_active TINYINT(1) DEFAULT 1, " +
                    "salary DECIMAL(10,2), " +
                    "hire_date DATE, " +
                    "profile TEXT, " +
                    "birth_year YEAR" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);

        // 调试输出
        System.out.println("达梦数据类型测试实际输出: " + result);

        assertTrue(result.contains("CREATE TABLE"));
        // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("employee_info"));
        assertTrue(result.contains("emp_id BIGINT IDENTITY(1,1)"));
        assertTrue(result.contains("emp_name VARCHAR(100) NOT NULL"));
        assertTrue(result.contains("is_active BIT"));
        assertTrue(result.contains("salary DECIMAL(10,2)"));
        assertTrue(result.contains("hire_date DATE"));
        assertTrue(result.contains("profile CLOB"));  // profile不是达梦保留关键字，不需要双引号
        assertTrue(result.contains("birth_year INT"));
        assertTrue(result.contains("PRIMARY KEY"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试NVARCHAR2和VARCHAR区别 - 基于达梦官方文档")
    public void testNvarchar2VsVarcharDifference() {
        // 根据达梦官方文档，NVARCHAR2和VARCHAR的区别
        String sql = "CREATE TABLE nvarchar_test (" +
                    "id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
                    "varchar_field VARCHAR(100), " +
                    "nvarchar_field NVARCHAR(100), " +
                    "text_field TEXT, " +
                    "unicode_text LONGTEXT" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);

        // 调试输出
        System.out.println("NVARCHAR2测试实际输出: " + result);

        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("nvarchar_test"));
        assertTrue(result.contains("varchar_field VARCHAR(100)"));
        assertTrue(result.contains("nvarchar_field NVARCHAR(100)"));
        assertTrue(result.contains("text_field CLOB"));  // TEXT转换为CLOB
        assertTrue(result.contains("unicode_text CLOB"));  // LONGTEXT转换为CLOB
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试二进制数据类型转换 - 基于达梦官方文档")
    public void testBinaryDataTypeConversion() {
        // 根据达梦官方文档第3.5节多媒体数据类型：
        // - BINARY和VARBINARY：属于精确数值数据类型，达梦原生支持
        // - BLOB：用于指明变长的二进制大对象，达梦原生支持
        // - TINYBLOB、MEDIUMBLOB、LONGBLOB：MySQL特有，需转换为达梦的BLOB或VARBINARY
        String sql = "CREATE TABLE binary_test (" +
                    "id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
                    "binary_field BINARY(16), " +
                    "varbinary_field VARBINARY(255), " +
                    "tinyblob_field TINYBLOB, " +
                    "blob_field BLOB, " +
                    "mediumblob_field MEDIUMBLOB, " +
                    "longblob_field LONGBLOB" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);

        // 调试输出
        System.out.println("二进制数据类型测试实际输出: " + result);

        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("binary_test"));

        // 根据达梦官方文档，BINARY和VARBINARY原生支持
        assertTrue(result.contains("binary_field BINARY(16)"));
        assertTrue(result.contains("varbinary_field VARBINARY(255)"));

        // 根据达梦官方文档，BLOB原生支持
        assertTrue(result.contains("blob_field BLOB"));

        // MySQL的TINYBLOB、MEDIUMBLOB、LONGBLOB需要转换
        // 根据实际转换实现，可能转换为BLOB或VARBINARY
        assertTrue(result.contains("tinyblob_field"));
        assertTrue(result.contains("mediumblob_field"));
        assertTrue(result.contains("longblob_field"));

        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试JSON数据类型转换 - 基于达梦官方文档")
    public void testJsonDataTypeConversion() {
        // 根据达梦官方文档第18章JSON：
        // - 达梦数据库原生支持JSON和JSONB数据类型
        // - JSON类型：直接存储文本，最大长度为2G-1字节
        // - 除了JSON和JSONB类型外，还可以使用字符串（如varchar类型）和CLOB类型为载体处理JSON数据
        String sql = "CREATE TABLE json_test (" +
                    "id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
                    "json_data JSON, " +
                    "config JSON, " +
                    "settings TEXT" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);

        // 调试输出
        System.out.println("JSON数据类型测试实际输出: " + result);

        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("json_test"));

        // 根据达梦官方文档，JSON类型原生支持
        // 实际转换可能保持JSON类型或转换为CLOB类型
        assertTrue(result.contains("json_data"));
        assertTrue(result.contains("config"));

        // 根据达梦官方文档，TEXT转换为CLOB
        assertTrue(result.contains("settings CLOB"));

        assertTrue(result.endsWith(";"));
    }
}
