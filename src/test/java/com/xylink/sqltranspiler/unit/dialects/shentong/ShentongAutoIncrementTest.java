package com.xylink.sqltranspiler.unit.dialects.shentong;

import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.shared.base.BaseShentongConversionTest;

/**
 * 神通数据库自增列类型测试
 * 基于神通数据库官方文档第2.9.4节自增列类型规范
 * 根据文档：
 * - 神通数据库可指定表的一列（列类型可以为INT BIGINT FLOAT）为自增列
 * - 可用于为新行生成唯一标识，并且性能优于序列和SERIAL
 * - 如果创建表时没有为AUTO_INCREMENT列指定初始值，神通数据库会从1开始自动分配序列号
 * - 如果初始建表时指定了自增列的值，就以该值作为自增列的起始值
 * - 神通仅支持将一个列的属性设置为自增列，且自增列必须有仅包括本列的PRIMARY KEY或UNIQUE约束
 * 参考文档：神通数据库SQL参考手册 第2.9.4节
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保方言功能符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试方言特有功能
 *
 * <AUTHOR>
 */
@DisplayName("神通数据库自增列类型测试")
public class ShentongAutoIncrementTest extends BaseShentongConversionTest {

    /**
     * 测试基本AUTO_INCREMENT功能
     * 根据文档：神通数据库可指定表的一列为自增列，从1开始自动分配序列号
     */
    @Test
    @DisplayName("测试基本AUTO_INCREMENT功能")
    public void testBasicAutoIncrement() throws Exception {
        String mysqlSql = """
            CREATE TABLE auto_increment_test (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100)
            );
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证AUTO_INCREMENT支持
        assertTrue(shentongSql.contains("CREATE TABLE"), "应支持CREATE TABLE语句");
        assertTrue(shentongSql.contains("\"auto_increment_test\""), "表名应正确转换");
        assertTrue(shentongSql.contains("AUTO_INCREMENT"), "应支持AUTO_INCREMENT关键字");
        assertTrue(shentongSql.contains("PRIMARY KEY"), "应支持PRIMARY KEY约束");
        assertTrue(shentongSql.contains("INT"), "应支持INT数据类型");
    }

    /**
     * 测试支持的自增列数据类型
     * 根据文档：列类型可以为INT BIGINT FLOAT
     */
    @Test
    @DisplayName("测试支持的自增列数据类型")
    public void testAutoIncrementDataTypes() throws Exception {
        String mysqlSql = """
            CREATE TABLE int_auto_test (
                id INT AUTO_INCREMENT PRIMARY KEY,
                data VARCHAR(50)
            );
            
            CREATE TABLE bigint_auto_test (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                data VARCHAR(50)
            );
            
            CREATE TABLE float_auto_test (
                id FLOAT AUTO_INCREMENT PRIMARY KEY,
                data VARCHAR(50)
            );
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证不同数据类型的AUTO_INCREMENT支持
        assertTrue(shentongSql.contains("INT AUTO_INCREMENT"), "应支持INT AUTO_INCREMENT");
        assertTrue(shentongSql.contains("BIGINT AUTO_INCREMENT"), "应支持BIGINT AUTO_INCREMENT");
        assertTrue(shentongSql.contains("FLOAT AUTO_INCREMENT"), "应支持FLOAT AUTO_INCREMENT");
        assertTrue(shentongSql.contains("PRIMARY KEY"), "应支持PRIMARY KEY约束");
    }

    /**
     * 测试AUTO_INCREMENT初始值设置
     * 根据文档：如果初始建表时指定了自增列的值，就以该值作为自增列的起始值
     */
    @Test
    @DisplayName("测试AUTO_INCREMENT初始值设置")
    public void testAutoIncrementInitialValue() throws Exception {
        String mysqlSql = """
            CREATE TABLE auto_increment_initial (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100)
            ) AUTO_INCREMENT = 100;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证AUTO_INCREMENT初始值设置
        assertTrue(shentongSql.contains("CREATE TABLE"), "应支持CREATE TABLE语句");
        assertTrue(shentongSql.contains("AUTO_INCREMENT"), "应支持AUTO_INCREMENT关键字");
        assertTrue(shentongSql.contains("100"), "应支持初始值设置");
        assertTrue(shentongSql.contains("PRIMARY KEY"), "应支持PRIMARY KEY约束");
    }

    /**
     * 测试AUTO_INCREMENT唯一性约束
     * 根据文档：自增列必须有仅包括本列的PRIMARY KEY或UNIQUE约束
     */
    @Test
    @DisplayName("测试AUTO_INCREMENT唯一性约束")
    public void testAutoIncrementUniqueConstraints() throws Exception {
        String mysqlSql = """
            CREATE TABLE auto_primary_key (
                id INT AUTO_INCREMENT PRIMARY KEY,
                data VARCHAR(50)
            );
            
            CREATE TABLE auto_unique_key (
                id INT AUTO_INCREMENT UNIQUE,
                data VARCHAR(50)
            );
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证AUTO_INCREMENT唯一性约束
        assertTrue(shentongSql.contains("AUTO_INCREMENT PRIMARY KEY"), "应支持AUTO_INCREMENT PRIMARY KEY");
        assertTrue(shentongSql.contains("AUTO_INCREMENT UNIQUE"), "应支持AUTO_INCREMENT UNIQUE");
        assertTrue(shentongSql.contains("\"auto_primary_key\""), "表名应正确转换");
        assertTrue(shentongSql.contains("\"auto_unique_key\""), "表名应正确转换");
    }

    /**
     * 测试INSERT语句中的AUTO_INCREMENT处理
     * 根据文档：在插入数据时，不指定自增列的值，数据库会自动从初值开始递增分配列值
     */
    @Test
    @DisplayName("测试INSERT语句中的AUTO_INCREMENT处理")
    public void testAutoIncrementInInsert() throws Exception {
        String mysqlSql = """
            INSERT INTO auto_test (name, email) VALUES 
            ('张三', '<EMAIL>'),
            ('李四', '<EMAIL>');
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证INSERT语句中的AUTO_INCREMENT处理
        assertTrue(shentongSql.contains("INSERT INTO"), "应支持INSERT语句");
        assertTrue(shentongSql.contains("\"auto_test\""), "表名应正确转换");
        assertTrue(shentongSql.contains("张三"), "应支持中文数据");
        assertTrue(shentongSql.contains("李四"), "应支持中文数据");
        assertTrue(shentongSql.contains("VALUES"), "应支持VALUES子句");
    }

    /**
     * 测试指定AUTO_INCREMENT列值的INSERT
     * 根据文档：支持INSERT语句中指定插入的自增列的值
     */
    @Test
    @DisplayName("测试指定AUTO_INCREMENT列值的INSERT")
    public void testInsertWithSpecificAutoIncrementValue() throws Exception {
        String mysqlSql = """
            INSERT INTO auto_test (id, name, email) VALUES 
            (1000, '指定ID用户', '<EMAIL>');
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证指定AUTO_INCREMENT值的INSERT
        assertTrue(shentongSql.contains("INSERT INTO"), "应支持INSERT语句");
        assertTrue(shentongSql.contains("\"auto_test\""), "表名应正确转换");
        assertTrue(shentongSql.contains("1000"), "应支持指定自增列值");
        assertTrue(shentongSql.contains("指定ID用户"), "应支持中文数据");
        assertTrue(shentongSql.contains("VALUES"), "应支持VALUES子句");
    }

    /**
     * 测试ALTER TABLE修改AUTO_INCREMENT属性
     * 根据文档：支持ALTER语句修改相关自增属性
     */
    @Test
    @DisplayName("测试ALTER TABLE修改AUTO_INCREMENT属性")
    public void testAlterTableAutoIncrement() throws Exception {
        String mysqlSql = """
            ALTER TABLE test_table AUTO_INCREMENT = 5000;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证ALTER TABLE AUTO_INCREMENT
        assertTrue(shentongSql.contains("ALTER TABLE"), "应支持ALTER TABLE语句");
        assertTrue(shentongSql.contains("\"test_table\""), "表名应正确转换");
        assertTrue(shentongSql.contains("AUTO_INCREMENT"), "应支持AUTO_INCREMENT关键字");
        assertTrue(shentongSql.contains("5000"), "应支持修改自增起始值");
    }

    /**
     * 测试PostgreSQL ALTER COLUMN TYPE语法被MySQL强制校验正确拒绝
     * 验证PostgreSQL特有语法被正确检测和拒绝
     */
    @Test
    @DisplayName("PostgreSQL ALTER COLUMN TYPE语法拒绝测试")
    public void testPostgreSqlAlterColumnTypeSyntaxRejection() throws Exception {
        String postgresqlSql = """
            ALTER TABLE test_table
            ALTER COLUMN id TYPE INT AUTO_INCREMENT;
            """;

        String result = convertMySqlToShentong(postgresqlSql);

        // 验证PostgreSQL语法被正确拒绝 - 根据MySQL 8.4官方文档，ALTER COLUMN TYPE不是MySQL语法
        assertTrue(result.isEmpty(), "PostgreSQL ALTER COLUMN TYPE语法应该被MySQL强制校验拒绝，转换结果应为空");
    }

    /**
     * 测试MySQL标准ALTER TABLE MODIFY COLUMN语法的转换
     * 使用正确的MySQL语法进行列修改测试
     * 根据MySQL 8.4官方文档，使用MODIFY COLUMN语法修改列定义
     */
    @Test
    @DisplayName("MySQL标准ALTER TABLE MODIFY COLUMN转换测试")
    public void testMySqlAlterTableModifyColumn() throws Exception {
        String mysqlSql = """
            ALTER TABLE test_table
            MODIFY COLUMN id INT AUTO_INCREMENT;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);

        // 验证MySQL标准语法被正确处理
        assertTrue(shentongSql.contains("ALTER TABLE"), "应支持ALTER TABLE语句");
        assertTrue(shentongSql.contains("\"test_table\""), "表名应正确转换");
        assertTrue(shentongSql.contains("MODIFY") || shentongSql.contains("ALTER COLUMN"),
                "MySQL MODIFY COLUMN语法应该被正确处理");
        assertTrue(shentongSql.contains("AUTO_INCREMENT"), "应支持AUTO_INCREMENT关键字");
    }

    /**
     * 测试列由自增列改为非自增列
     * 根据文档：列由自增列改为非自增列
     */
    @Test
    @DisplayName("测试列由自增列改为非自增列")
    public void testAlterColumnFromAutoIncrement() throws Exception {
        String mysqlSql = """
            ALTER TABLE test_table 
            MODIFY COLUMN id INT;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证列改为非自增列
        assertTrue(shentongSql.contains("ALTER TABLE"), "应支持ALTER TABLE语句");
        assertTrue(shentongSql.contains("\"test_table\""), "表名应正确转换");
        assertTrue(shentongSql.contains("MODIFY"), "应支持MODIFY COLUMN");
        assertTrue(shentongSql.contains("INT"), "应支持INT数据类型");
    }

    /**
     * 测试UPDATE语句更新自增列值
     * 根据文档：支持UPDATE语句中更新自增列的值，不过这不会影响下次分配的值
     */
    @Test
    @DisplayName("测试UPDATE语句更新自增列值")
    public void testUpdateAutoIncrementColumn() throws Exception {
        String mysqlSql = """
            UPDATE test_table 
            SET id = 9999, name = '更新的用户'
            WHERE id = 1;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证UPDATE自增列
        assertTrue(shentongSql.contains("UPDATE"), "应支持UPDATE语句");
        assertTrue(shentongSql.contains("\"test_table\""), "表名应正确转换");
        assertTrue(shentongSql.contains("SET"), "应支持SET子句");
        assertTrue(shentongSql.contains("9999"), "应支持更新自增列值");
        assertTrue(shentongSql.contains("更新的用户"), "应支持中文数据");
        assertTrue(shentongSql.contains("WHERE"), "应支持WHERE条件");
    }

    /**
     * 测试LAST_INSERT_ID函数
     * 根据文档：支持LAST_INSERT_ID函数来获取本会话上一次插入过的自增列值
     */
    @Test
    @DisplayName("测试LAST_INSERT_ID函数")
    public void testLastInsertIdFunction() throws Exception {
        String mysqlSql = """
            SELECT LAST_INSERT_ID() as last_id;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证LAST_INSERT_ID函数
        assertTrue(shentongSql.contains("SELECT"), "应支持SELECT语句");
        assertTrue(shentongSql.contains("LAST_INSERT_ID"), "应支持LAST_INSERT_ID函数");
        assertTrue(shentongSql.contains("last_id"), "应支持别名");
    }

    /**
     * 测试PostgreSQL SERIAL语法被MySQL强制校验正确拒绝
     * 验证SERIAL类型被正确检测和拒绝
     */
    @Test
    @DisplayName("PostgreSQL SERIAL语法拒绝测试")
    public void testPostgreSqlSerialSyntaxRejection() throws Exception {
        String postgresqlSql = """
            CREATE TABLE serial_table (
                id SERIAL PRIMARY KEY,
                data VARCHAR(100)
            );
            """;

        String result = convertMySqlToShentong(postgresqlSql);

        // 验证PostgreSQL语法被正确拒绝 - 根据MySQL 8.4官方文档，SERIAL不是MySQL语法
        assertTrue(result.isEmpty(), "PostgreSQL SERIAL语法应该被MySQL强制校验拒绝，转换结果应为空");
    }

    /**
     * 测试MySQL标准AUTO_INCREMENT语法的转换
     * 使用正确的MySQL语法进行自增列转换测试
     * 根据神通官方文档第947行：AUTO_INCREMENT性能优于序列和SERIAL
     */
    @Test
    @DisplayName("MySQL标准AUTO_INCREMENT转换测试")
    public void testMySqlAutoIncrementConversion() throws Exception {
        String mysqlSql = """
            CREATE TABLE auto_increment_table (
                id INT AUTO_INCREMENT PRIMARY KEY,
                data VARCHAR(100)
            );

            CREATE TABLE bigint_auto_table (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(50)
            );
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);

        // 验证AUTO_INCREMENT转换 - 根据MySQL 8.4官方文档，AUTO_INCREMENT是标准MySQL语法
        assertTrue(shentongSql.contains("AUTO_INCREMENT"), "根据神通官方文档，应支持AUTO_INCREMENT");
        assertTrue(shentongSql.contains("\"auto_increment_table\""), "表名应正确转换");
        assertTrue(shentongSql.contains("\"bigint_auto_table\""), "表名应正确转换");
        assertTrue(shentongSql.contains("PRIMARY KEY"), "应支持PRIMARY KEY约束");
    }
}
