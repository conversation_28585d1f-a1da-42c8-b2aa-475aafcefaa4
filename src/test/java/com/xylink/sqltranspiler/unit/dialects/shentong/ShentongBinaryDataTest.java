package com.xylink.sqltranspiler.unit.dialects.shentong;

import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.shared.base.BaseShentongConversionTest;

/**
 * 神通数据库二进制字符串类型测试
 * 基于神通数据库官方文档第2.9.5节二进制字符串类型规范
 * 根据文档：
 * - BINARY(n): 定长的二进制字串，最大长度为8000字节
 * - VARBINARY(n): 变长的二进制字串，最大长度为8000字节
 * - RAW(n): 变长的二进制字串（映射为VARBINARY(n)）
 * - 二进制数可以是0-9和A-F或a-f的字符组成
 * - 输出时二进制数以0x标识开头
 * - 输入时binary类型的数据可以接受四种形式
 * 参考文档：神通数据库SQL参考手册 第2.9.5节
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保方言功能符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试方言特有功能
 *
 * <AUTHOR>
 */
@DisplayName("神通数据库二进制数据类型测试")
public class ShentongBinaryDataTest extends BaseShentongConversionTest {

    /**
     * 测试BINARY(n)定长二进制类型
     * 根据文档：定长的二进制字串，最大长度为8000字节
     */
    @Test
    @DisplayName("测试BINARY(n)定长二进制类型")
    public void testBinaryFixedLength() throws Exception {
        String mysqlSql = """
            CREATE TABLE binary_test (
                id INT PRIMARY KEY,
                binary_col BINARY(16),
                binary_small BINARY(4),
                binary_large BINARY(1000)
            );
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证BINARY类型支持
        assertTrue(shentongSql.contains("CREATE TABLE"), "应支持CREATE TABLE语句");
        assertTrue(shentongSql.contains("\"binary_test\""), "表名应正确转换");
        assertTrue(shentongSql.contains("BINARY(16)"), "应支持BINARY(16)类型");
        assertTrue(shentongSql.contains("BINARY(4)"), "应支持BINARY(4)类型");
        assertTrue(shentongSql.contains("BINARY(1000)"), "应支持BINARY(1000)类型");
        assertTrue(shentongSql.contains("PRIMARY KEY"), "应支持PRIMARY KEY约束");
    }

    /**
     * 测试VARBINARY(n)变长二进制类型
     * 根据文档：变长的二进制字串，最大长度为8000字节
     */
    @Test
    @DisplayName("测试VARBINARY(n)变长二进制类型")
    public void testVarbinaryVariableLength() throws Exception {
        String mysqlSql = """
            CREATE TABLE varbinary_test (
                id INT PRIMARY KEY,
                varbinary_col VARBINARY(255),
                varbinary_small VARBINARY(10),
                varbinary_large VARBINARY(8000)
            );
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证VARBINARY类型支持
        assertTrue(shentongSql.contains("CREATE TABLE"), "应支持CREATE TABLE语句");
        assertTrue(shentongSql.contains("\"varbinary_test\""), "表名应正确转换");
        assertTrue(shentongSql.contains("VARBINARY(255)"), "应支持VARBINARY(255)类型");
        assertTrue(shentongSql.contains("VARBINARY(10)"), "应支持VARBINARY(10)类型");
        assertTrue(shentongSql.contains("VARBINARY(8000)"), "应支持VARBINARY(8000)类型");
        assertTrue(shentongSql.contains("PRIMARY KEY"), "应支持PRIMARY KEY约束");
    }

    /**
     * 测试Oracle RAW类型语法被MySQL强制校验正确拒绝
     * 验证Oracle特有数据类型被正确检测和拒绝
     */
    @Test
    @DisplayName("Oracle RAW类型语法拒绝测试")
    public void testOracleRawTypeSyntaxRejection() throws Exception {
        String oracleSql = """
            CREATE TABLE raw_test (
                id INT PRIMARY KEY,
                raw_col RAW(100),
                raw_small RAW(16),
                raw_large RAW(2000)
            );
            """;

        String result = convertMySqlToShentong(oracleSql);

        // 验证Oracle语法被正确拒绝 - 根据MySQL 8.4官方文档，RAW不是MySQL数据类型
        assertTrue(result.isEmpty(), "Oracle RAW类型语法应该被MySQL强制校验拒绝，转换结果应为空");
    }

    /**
     * 测试MySQL标准VARBINARY类型的转换
     * 使用正确的MySQL语法进行二进制数据类型转换测试
     */
    @Test
    @DisplayName("MySQL标准VARBINARY类型转换测试")
    public void testMySqlVarbinaryTypeMapping() throws Exception {
        String mysqlSql = """
            CREATE TABLE binary_test (
                id INT PRIMARY KEY,
                binary_col VARBINARY(100),
                binary_small VARBINARY(16),
                binary_large VARBINARY(2000)
            );
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);

        // 验证MySQL标准VARBINARY类型转换
        assertTrue(shentongSql.contains("CREATE TABLE"), "应支持CREATE TABLE语句");
        assertTrue(shentongSql.contains("binary_test") || shentongSql.contains("BINARY_TEST"), "应包含表名");
        // VARBINARY是MySQL标准类型，应该被正确处理
        assertTrue(shentongSql.contains("VARBINARY(100)"), "VARBINARY(100)应该被正确处理");
        assertTrue(shentongSql.contains("VARBINARY(16)"), "VARBINARY(16)应该被正确处理");
        assertTrue(shentongSql.contains("VARBINARY(2000)"), "VARBINARY(2000)应该被正确处理");
    }

    /**
     * 测试二进制数据的输入格式
     * 根据文档：输入时binary类型的数据可以接受四种形式
     */
    @Test
    @DisplayName("测试二进制数据的输入格式")
    public void testBinaryDataInputFormats() throws Exception {
        String mysqlSql = """
            INSERT INTO binary_data_test (id, binary_col) VALUES
            (1, 0xABCD),
            (2, 0x1234567890ABCDEF),
            (3, 0xFF00),
            (4, 0x00);
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证二进制数据输入格式
        assertTrue(shentongSql.contains("INSERT INTO"), "应支持INSERT语句");
        assertTrue(shentongSql.contains("\"binary_data_test\""), "表名应正确转换");
        assertTrue(shentongSql.contains("0xABCD"), "应支持0x前缀的十六进制格式");
        assertTrue(shentongSql.contains("0x1234567890ABCDEF"), "应支持长十六进制格式");
        assertTrue(shentongSql.contains("0xFF00"), "应支持大写十六进制格式");
        assertTrue(shentongSql.contains("0x00"), "应支持零值十六进制格式");
        assertTrue(shentongSql.contains("VALUES"), "应支持VALUES子句");
    }

    /**
     * 测试非标准二进制LIKE语法被MySQL强制校验正确拒绝
     * 验证非标准语法被正确检测和拒绝
     */
    @Test
    @DisplayName("非标准二进制LIKE语法拒绝测试")
    public void testNonStandardBinaryLikeSyntaxRejection() throws Exception {
        String nonStandardSql = """
            SELECT id, binary_col, varbinary_col
            FROM binary_table
            WHERE binary_col = 0xABCD
               OR varbinary_col LIKE 0x12%
               AND LENGTH(binary_col) > 4;
            """;

        String result = convertMySqlToShentong(nonStandardSql);

        // 验证非标准语法被正确拒绝 - 根据MySQL 8.4官方文档，LIKE不能直接与十六进制字面量和通配符组合
        assertTrue(result.isEmpty(), "非标准二进制LIKE语法应该被MySQL强制校验拒绝，转换结果应为空");
    }

    /**
     * 测试MySQL标准二进制数据查询和比较
     * 使用正确的MySQL语法进行二进制数据查询测试
     */
    @Test
    @DisplayName("MySQL标准二进制数据查询测试")
    public void testMySqlStandardBinaryDataQuery() throws Exception {
        String mysqlSql = """
            SELECT id, binary_col, varbinary_col
            FROM binary_table
            WHERE binary_col = 0xABCD
               OR HEX(varbinary_col) LIKE '12%'
               AND LENGTH(binary_col) > 4;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);

        // 验证MySQL标准二进制数据查询
        assertTrue(shentongSql.contains("SELECT"), "应支持SELECT语句");
        assertTrue(shentongSql.contains("binary_table") || shentongSql.contains("BINARY_TABLE"), "应包含表名");
        assertTrue(shentongSql.contains("WHERE"), "应支持WHERE条件");
        assertTrue(shentongSql.contains("0xABCD"), "应支持二进制数据比较");
        assertTrue(shentongSql.contains("HEX"), "应支持HEX函数");
        assertTrue(shentongSql.contains("LENGTH"), "应支持LENGTH函数");
        assertTrue(shentongSql.contains("OR"), "应支持OR逻辑运算");
        assertTrue(shentongSql.contains("AND"), "应支持AND逻辑运算");
    }

    /**
     * 测试二进制数据的函数操作
     * 验证二进制数据相关的函数支持
     */
    @Test
    @DisplayName("测试二进制数据的函数操作")
    public void testBinaryDataFunctions() throws Exception {
        String mysqlSql = """
            SELECT 
                LENGTH(binary_col) as binary_length,
                HEX(binary_col) as hex_value,
                UNHEX('41424344') as unhex_result,
                CONCAT(binary_col, 0xFF) as concat_result
            FROM binary_function_test;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证二进制数据函数
        assertTrue(shentongSql.contains("SELECT"), "应支持SELECT语句");
        assertTrue(shentongSql.contains("LENGTH("), "应支持LENGTH函数");
        assertTrue(shentongSql.contains("HEX("), "应支持HEX函数");
        assertTrue(shentongSql.contains("UNHEX("), "应支持UNHEX函数");
        assertTrue(shentongSql.contains("CONCAT("), "应支持CONCAT函数");
        assertTrue(shentongSql.contains("'41424344'"), "应支持十六进制字符串");
        assertTrue(shentongSql.contains("0xFF"), "应支持十六进制常量");
    }

    /**
     * 测试二进制数据的更新操作
     * 验证UPDATE语句中二进制数据的处理
     */
    @Test
    @DisplayName("测试二进制数据的更新操作")
    public void testBinaryDataUpdate() throws Exception {
        String mysqlSql = """
            UPDATE binary_update_test
            SET binary_col = 0x1234ABCD,
                varbinary_col = 0xDEADBEEF,
                raw_col = 0x00FF00FF
            WHERE id = 1;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证二进制数据更新
        assertTrue(shentongSql.contains("UPDATE"), "应支持UPDATE语句");
        assertTrue(shentongSql.contains("\"binary_update_test\""), "表名应正确转换");
        assertTrue(shentongSql.contains("SET"), "应支持SET子句");
        assertTrue(shentongSql.contains("0x1234ABCD"), "应支持二进制数据更新");
        assertTrue(shentongSql.contains("0xDEADBEEF"), "应支持二进制数据更新");
        assertTrue(shentongSql.contains("0x00FF00FF"), "应支持二进制数据更新");
        assertTrue(shentongSql.contains("WHERE"), "应支持WHERE条件");
    }

    /**
     * 测试二进制数据类型的约束
     * 验证二进制数据类型上的各种约束
     */
    @Test
    @DisplayName("测试二进制数据类型的约束")
    public void testBinaryDataConstraints() throws Exception {
        String mysqlSql = """
            CREATE TABLE binary_constraints_test (
                id INT PRIMARY KEY,
                binary_not_null BINARY(16) NOT NULL,
                varbinary_unique VARBINARY(32) UNIQUE,
                binary_default BINARY(8) DEFAULT 0x00000000,
                varbinary_check VARBINARY(16) CHECK (LENGTH(varbinary_check) >= 4)
            );
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证二进制数据约束
        assertTrue(shentongSql.contains("CREATE TABLE"), "应支持CREATE TABLE语句");
        assertTrue(shentongSql.contains("\"binary_constraints_test\""), "表名应正确转换");
        assertTrue(shentongSql.contains("NOT NULL"), "应支持NOT NULL约束");
        assertTrue(shentongSql.contains("UNIQUE"), "应支持UNIQUE约束");
        assertTrue(shentongSql.contains("DEFAULT 0x00000000"), "应支持DEFAULT约束");
        assertTrue(shentongSql.contains("CHECK"), "应支持CHECK约束");
        assertTrue(shentongSql.contains("PRIMARY KEY"), "应支持PRIMARY KEY约束");
    }

    /**
     * 测试二进制数据的索引支持
     * 验证二进制数据类型上的索引创建
     */
    @Test
    @DisplayName("测试二进制数据的索引支持")
    public void testBinaryDataIndexes() throws Exception {
        String mysqlSql = """
            CREATE TABLE binary_index_test (
                id INT PRIMARY KEY,
                binary_col BINARY(16),
                varbinary_col VARBINARY(255),
                INDEX idx_binary (binary_col),
                INDEX idx_varbinary (varbinary_col)
            );
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证二进制数据索引
        assertTrue(shentongSql.contains("CREATE TABLE"), "应支持CREATE TABLE语句");
        assertTrue(shentongSql.contains("\"binary_index_test\""), "表名应正确转换");
        assertTrue(shentongSql.contains("BINARY(16)"), "应支持BINARY类型");
        assertTrue(shentongSql.contains("VARBINARY(255)"), "应支持VARBINARY类型");
        assertTrue(shentongSql.contains("INDEX"), "应支持INDEX创建");
        assertTrue(shentongSql.contains("PRIMARY KEY"), "应支持PRIMARY KEY约束");
    }

    /**
     * 测试二进制数据的边界条件
     * 验证二进制数据类型的边界情况处理
     */
    @Test
    @DisplayName("测试二进制数据的边界条件")
    public void testBinaryDataBoundaryConditions() throws Exception {
        String mysqlSql = """
            CREATE TABLE binary_boundary_test (
                id INT PRIMARY KEY,
                binary_min BINARY(1),
                binary_max BINARY(8000),
                varbinary_min VARBINARY(1),
                varbinary_max VARBINARY(8000),
                binary_empty BINARY(4) DEFAULT 0x00000000
            );
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证二进制数据边界条件
        assertTrue(shentongSql.contains("CREATE TABLE"), "应支持CREATE TABLE语句");
        assertTrue(shentongSql.contains("\"binary_boundary_test\""), "表名应正确转换");
        assertTrue(shentongSql.contains("BINARY(1)"), "应支持最小长度BINARY(1)");
        assertTrue(shentongSql.contains("BINARY(8000)"), "应支持最大长度BINARY(8000)");
        assertTrue(shentongSql.contains("VARBINARY(1)"), "应支持最小长度VARBINARY(1)");
        assertTrue(shentongSql.contains("VARBINARY(8000)"), "应支持最大长度VARBINARY(8000)");
        assertTrue(shentongSql.contains("DEFAULT 0x00000000"), "应支持默认二进制值");
    }
}
