package com.xylink.sqltranspiler.unit.dialects.shentong;

import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.shared.base.BaseShentongConversionTest;

/**
 * 神通数据库官方文档特定特性测试
 * 基于神通数据库官方文档中的具体示例和特性进行验证
 * 确保转换结果完全符合神通数据库的官方标准
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保方言功能符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试方言特有功能
 *
 * <AUTHOR>
 */
@DisplayName("神通数据库官方文档特定特性测试")
public class ShentongDocumentSpecificTest extends BaseShentongConversionTest {

    @Test
    @DisplayName("验证神通数据库ROWNUM伪列特性")
    void testShentongRowNumPseudoColumn() throws Exception {
        System.out.println("=== 神通数据库ROWNUM伪列特性验证 ===\n");
        
        // 基于官方文档示例1：使用ROWNUM限制查询结果
        String mysqlSql = """
            CREATE VIEW view1 AS SELECT * FROM tab1 ORDER BY a;
            
            INSERT INTO tab1 VALUES ('200001', 1000);
            INSERT INTO tab1 VALUES ('200002', 1100);
            INSERT INTO tab1 VALUES ('200003', 1200);
            INSERT INTO tab1 VALUES ('200004', 1300);
            INSERT INTO tab1 VALUES ('200005', 1400);
            INSERT INTO tab1 VALUES ('200006', 1500);
            INSERT INTO tab1 VALUES ('200007', 1600);
            
            SELECT * FROM view1 ORDER BY a LIMIT 4;
            """;
        
        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        assertBasicConversionRequirements(shentongSql);
        
        // 验证ROWNUM伪列支持
        assertTrue(shentongSql.contains("ROWNUM"), "应支持ROWNUM伪列");
        assertTrue(shentongSql.contains("CREATE VIEW"), "应支持CREATE VIEW");
        assertTrue(shentongSql.contains("INSERT INTO"), "应支持INSERT INTO");
        assertTrue(shentongSql.contains("ORDER BY"), "应支持ORDER BY");
        
        System.out.println("原始MySQL SQL:");
        System.out.println(mysqlSql);
        System.out.println("\n神通转换结果:");
        System.out.println(shentongSql);
        System.out.println("✅ ROWNUM伪列特性验证通过\n");
    }

    @Test
    @DisplayName("验证神通数据库分页显示特性")
    void testShentongPaginationFeature() throws Exception {
        System.out.println("=== 神通数据库分页显示特性验证 ===\n");
        
        // 基于官方文档示例3：MySQL LIMIT转换为神通ROWNUM分页显示
        String mysqlSql = """
            SELECT * FROM tab3 ORDER BY a DESC LIMIT 3 OFFSET 0;

            SELECT * FROM tab3 ORDER BY a DESC LIMIT 2 OFFSET 3;
            """;
        
        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        assertBasicConversionRequirements(shentongSql);
        
        // 验证分页查询支持
        assertTrue(shentongSql.contains("ROWNUM"), "应支持ROWNUM");
        assertTrue(shentongSql.contains("ROW_NUM"), "应支持ROW_NUM别名");
        assertTrue(shentongSql.contains("BETWEEN"), "应支持BETWEEN操作符");
        assertTrue(shentongSql.contains("ORDER BY"), "应支持ORDER BY");
        
        System.out.println("原始MySQL SQL:");
        System.out.println(mysqlSql);
        System.out.println("\n神通转换结果:");
        System.out.println(shentongSql);
        System.out.println("✅ 分页显示特性验证通过\n");
    }

    @Test
    @DisplayName("验证神通数据库字符串类型特性")
    void testShentongStringTypeFeatures() throws Exception {
        System.out.println("=== 神通数据库字符串类型特性验证 ===\n");
        
        // 基于MySQL官方文档的字符串类型示例
        String mysqlSql = """
            CREATE TABLE string_test (
                char_col CHARACTER(4),
                varchar_col CHARACTER VARYING(5),
                text_col TEXT,
                name_col VARCHAR(50),
                special_char CHAR(1)
            );

            INSERT INTO string_test VALUES ('ok', 'good', 'long text', 'identifier', 'x');

            SELECT char_col, CHAR_LENGTH(char_col) FROM string_test;
            """;
        
        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        assertBasicConversionRequirements(shentongSql);
        
        // 验证字符串类型支持
        assertTrue(shentongSql.contains("CHARACTER(4)"), "应支持CHARACTER类型");
        assertTrue(shentongSql.contains("CHARACTER VARYING") || shentongSql.contains("VARCHAR"), 
                   "应支持CHARACTER VARYING类型");
        assertTrue(shentongSql.contains("TEXT"), "应支持TEXT类型");
        assertTrue(shentongSql.contains("VARCHAR"), "应支持VARCHAR类型");
        assertTrue(shentongSql.contains("CHAR"), "应支持CHAR类型");
        assertTrue(shentongSql.contains("CHAR_LENGTH"), "应支持CHAR_LENGTH函数");
        
        System.out.println("原始MySQL SQL:");
        System.out.println(mysqlSql);
        System.out.println("\n神通转换结果:");
        System.out.println(shentongSql);
        System.out.println("✅ 字符串类型特性验证通过\n");
    }

    @Test
    @DisplayName("验证神通数据库位串类型特性")
    void testShentongBitStringTypeFeatures() throws Exception {
        System.out.println("=== 神通数据库位串类型特性验证 ===\n");
        
        // 基于官方文档的位串类型示例
        // 注意：MySQL不支持BIT VARYING语法，使用BIT类型
        String mysqlSql = """
            CREATE TABLE bit_test (
                bit_col BIT(3),
                varbit_col BIT(16)
            );

            INSERT INTO bit_test VALUES (B'101', B'1010101010101010');
            INSERT INTO bit_test VALUES (B'10', B'11110000');

            SELECT bit_col FROM bit_test;
            """;
        
        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        assertBasicConversionRequirements(shentongSql);
        
        // 验证位串类型支持
        assertTrue(shentongSql.contains("BIT(3)"), "应支持BIT类型");
        assertTrue(shentongSql.contains("BIT(16)"), "应支持BIT类型");
        assertTrue(shentongSql.contains("B'"), "应支持位串常量");
        assertTrue(shentongSql.contains("SELECT"), "应支持SELECT查询");
        
        System.out.println("原始MySQL SQL:");
        System.out.println(mysqlSql);
        System.out.println("\n神通转换结果:");
        System.out.println(shentongSql);
        System.out.println("✅ 位串类型特性验证通过\n");
    }

    /**
     * 测试PostgreSQL SERIAL类型语法被MySQL强制校验正确拒绝
     * 验证PostgreSQL特有数据类型被正确检测和拒绝
     */
    @Test
    @DisplayName("PostgreSQL SERIAL类型语法拒绝测试")
    void testPostgreSqlSerialTypeSyntaxRejection() throws Exception {
        System.out.println("=== PostgreSQL SERIAL类型语法拒绝测试 ===\n");

        String postgresqlSql = """
            CREATE TABLE numeric_test (
                serial_col SERIAL,
                bigint_auto_col BIGINT AUTO_INCREMENT,
                tinyint_col TINYINT
            );
            """;

        String result = convertMySqlToShentong(postgresqlSql);

        // 验证PostgreSQL语法被正确拒绝 - 根据MySQL 8.4官方文档，SERIAL不是MySQL数据类型
        assertTrue(result.isEmpty(), "PostgreSQL SERIAL类型语法应该被MySQL强制校验拒绝，转换结果应为空");

        System.out.println("✅ PostgreSQL SERIAL类型语法被正确拒绝\n");
    }

    /**
     * 测试MySQL标准数值类型的转换
     * 使用正确的MySQL语法进行数值类型转换测试
     */
    @Test
    @DisplayName("MySQL标准数值类型转换测试")
    void testMySqlStandardNumericTypeFeatures() throws Exception {
        System.out.println("=== MySQL标准数值类型转换测试 ===\n");

        // 基于MySQL 8.4官方文档的标准数值类型
        String mysqlSql = """
            CREATE TABLE numeric_test (
                id_col INT AUTO_INCREMENT PRIMARY KEY,
                bigint_auto_col BIGINT AUTO_INCREMENT,
                tinyint_col TINYINT,
                smallint_col SMALLINT,
                int_col INTEGER,
                bigint_col BIGINT,
                decimal_col DECIMAL(10,2),
                numeric_col NUMERIC(15,5),
                real_col REAL,
                double_col DOUBLE PRECISION,
                float_col FLOAT(53)
            );

            INSERT INTO numeric_test (tinyint_col, decimal_col) VALUES (127, 999.99);
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        assertBasicConversionRequirements(shentongSql);

        // 验证MySQL标准数值类型转换
        assertTrue(shentongSql.contains("AUTO_INCREMENT"), "应支持AUTO_INCREMENT");
        assertTrue(shentongSql.contains("BIGINT"), "应支持BIGINT类型");
        assertTrue(shentongSql.contains("TINYINT"), "应支持TINYINT类型");
        assertTrue(shentongSql.contains("SMALLINT"), "应支持SMALLINT类型");
        assertTrue(shentongSql.contains("INTEGER") || shentongSql.contains("INT"), "应支持INTEGER类型");
        assertTrue(shentongSql.contains("DECIMAL(10,2)"), "应支持DECIMAL类型");
        assertTrue(shentongSql.contains("NUMERIC"), "应支持NUMERIC类型");
        assertTrue(shentongSql.contains("REAL"), "应支持REAL类型");
        assertTrue(shentongSql.contains("DOUBLE PRECISION"), "应支持DOUBLE PRECISION类型");
        assertTrue(shentongSql.contains("FLOAT"), "应支持FLOAT类型");

        System.out.println("原始MySQL SQL:");
        System.out.println(mysqlSql);
        System.out.println("\n神通转换结果:");
        System.out.println(shentongSql);
        System.out.println("✅ MySQL标准数值类型转换成功\n");
    }

    @Test
    @DisplayName("验证神通数据库自增列特性")
    void testShentongAutoIncrementFeatures() throws Exception {
        System.out.println("=== 神通数据库自增列特性验证 ===\n");
        
        // 基于官方文档示例1：使用自增列类型
        String mysqlSql = """
            CREATE TABLE auto_test (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(10)
            );
            
            INSERT INTO auto_test (name) VALUES ('test1');
            INSERT INTO auto_test (name) VALUES ('test2');
            
            ALTER TABLE auto_test AUTO_INCREMENT = 6;
            
            INSERT INTO auto_test (name) VALUES ('test3');
            INSERT INTO auto_test VALUES (10, 'test4');
            
            UPDATE auto_test SET id = 15 WHERE name = 'test3';
            
            SELECT LAST_INSERT_ID();
            """;
        
        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        assertBasicConversionRequirements(shentongSql);
        
        // 验证自增列特性
        assertTrue(shentongSql.contains("AUTO_INCREMENT"), "应支持AUTO_INCREMENT");
        assertTrue(shentongSql.contains("PRIMARY KEY"), "应支持PRIMARY KEY");
        assertTrue(shentongSql.contains("ALTER TABLE"), "应支持ALTER TABLE");
        assertTrue(shentongSql.contains("LAST_INSERT_ID"), "应支持LAST_INSERT_ID函数");
        
        System.out.println("原始MySQL SQL:");
        System.out.println(mysqlSql);
        System.out.println("\n神通转换结果:");
        System.out.println(shentongSql);
        System.out.println("✅ 自增列特性验证通过\n");
    }

    @Test
    @DisplayName("验证神通数据库二进制字符串类型特性")
    void testShentongBinaryStringTypeFeatures() throws Exception {
        System.out.println("=== 神通数据库二进制字符串类型特性验证 ===\n");
        
        // 基于官方文档的二进制字符串类型示例
        // 注意：RAW类型是Oracle/神通特有的，MySQL使用VARBINARY
        String mysqlSql = """
            CREATE TABLE binary_test (
                binary_col BINARY(8),
                varbinary_col VARBINARY(255),
                blob_col BLOB
            );

            INSERT INTO binary_test VALUES (0xf5, 0x03ed, 0x123456);
            INSERT INTO binary_test VALUES ('f5', '03ed', '123456');
            """;
        
        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        assertBasicConversionRequirements(shentongSql);
        
        // 验证二进制字符串类型支持
        assertTrue(shentongSql.contains("BINARY(8)"), "应支持BINARY类型");
        assertTrue(shentongSql.contains("VARBINARY(255)"), "应支持VARBINARY类型");
        // BLOB类型应该被支持
        assertTrue(shentongSql.contains("BLOB"), "应支持BLOB类型");
        
        System.out.println("原始MySQL SQL:");
        System.out.println(mysqlSql);
        System.out.println("\n神通转换结果:");
        System.out.println(shentongSql);
        System.out.println("✅ 二进制字符串类型特性验证通过\n");
    }

    /**
     * 测试PostgreSQL类型转换语法被MySQL强制校验正确拒绝
     * 验证PostgreSQL特有语法被正确检测和拒绝
     */
    @Test
    @DisplayName("PostgreSQL类型转换语法拒绝测试")
    void testPostgreSqlTypeCastSyntaxRejection() throws Exception {
        System.out.println("=== PostgreSQL类型转换语法拒绝测试 ===\n");

        String postgresqlSql = """
            CREATE TABLE datetime_test (
                date_col DATE,
                time_col TIME(6),
                timestamp_col TIMESTAMP
            );

            INSERT INTO datetime_test VALUES ('1999-12-31', '04:05:06.789', '1999-12-31 12:13:14');

            SELECT CURRENT_DATE;
            SELECT CURRENT_TIME;
            SELECT CURRENT_TIMESTAMP;

            SELECT 'infinity'::DATE;
            SELECT 'epoch'::DATE;
            SELECT 'now'::DATE;
            """;

        String result = convertMySqlToShentong(postgresqlSql);

        // 验证PostgreSQL语法被正确拒绝 - 根据MySQL 8.4官方文档，::类型转换不是MySQL语法
        assertTrue(result.isEmpty(), "PostgreSQL类型转换语法应该被MySQL强制校验拒绝，转换结果应为空");

        System.out.println("✅ PostgreSQL类型转换语法被正确拒绝\n");
    }

    /**
     * 测试MySQL标准日期时间类型的转换
     * 使用正确的MySQL语法进行日期时间类型转换测试
     */
    @Test
    @DisplayName("MySQL标准日期时间类型转换测试")
    void testMySqlStandardDateTimeTypeFeatures() throws Exception {
        System.out.println("=== MySQL标准日期时间类型转换测试 ===\n");

        // 基于MySQL 8.4官方文档的标准日期时间类型
        String mysqlSql = """
            CREATE TABLE datetime_test (
                date_col DATE,
                time_col TIME(6),
                timestamp_col TIMESTAMP,
                datetime_col DATETIME,
                year_col YEAR
            );

            INSERT INTO datetime_test VALUES ('1999-12-31', '04:05:06.789', '1999-12-31 12:13:14', '1999-12-31 12:13:14', 1999);

            SELECT CURRENT_DATE;
            SELECT CURRENT_TIME;
            SELECT CURRENT_TIMESTAMP;

            SELECT CAST('1999-12-31' AS DATE);
            SELECT CONVERT('04:05:06', TIME);
            SELECT NOW();
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        assertBasicConversionRequirements(shentongSql);

        // 验证MySQL标准日期时间类型转换
        assertTrue(shentongSql.contains("DATE"), "应支持DATE类型");
        assertTrue(shentongSql.contains("TIME"), "应支持TIME类型");
        // 验证转换成功，不强制要求特定类型名称，因为类型转换是正常的
        assertTrue(!shentongSql.trim().isEmpty(), "转换应该成功");
        assertTrue(shentongSql.contains("CREATE TABLE"), "应包含CREATE TABLE语句");
        assertTrue(shentongSql.contains("INSERT"), "应包含INSERT语句");
        assertTrue(shentongSql.contains("CURRENT_DATE"), "应支持CURRENT_DATE");
        assertTrue(shentongSql.contains("CURRENT_TIME"), "应支持CURRENT_TIME");
        assertTrue(shentongSql.contains("CURRENT_TIMESTAMP"), "应支持CURRENT_TIMESTAMP");
        assertTrue(shentongSql.contains("CAST") || shentongSql.contains("CONVERT"), "应支持CAST/CONVERT函数");

        System.out.println("原始MySQL SQL:");
        System.out.println(mysqlSql);
        System.out.println("\n神通转换结果:");
        System.out.println(shentongSql);
        System.out.println("✅ MySQL标准日期时间类型转换成功\n");
    }

    /**
     * 测试缺少表别名的子查询语法被MySQL强制校验正确拒绝
     * 验证不完整的子查询语法被正确检测和拒绝
     */
    @Test
    @DisplayName("缺少表别名的子查询语法拒绝测试")
    void testMissingTableAliasSyntaxRejection() throws Exception {
        System.out.println("=== 缺少表别名的子查询语法拒绝测试 ===\n");

        String invalidSql = """
            CREATE TABLE group_test (
                a CHAR(6),
                b INT
            );

            INSERT INTO group_test VALUES ('200001', 1000);
            INSERT INTO group_test VALUES ('200001', 1100);
            INSERT INTO group_test VALUES ('200001', 1200);
            INSERT INTO group_test VALUES ('200004', 1300);
            INSERT INTO group_test VALUES ('200002', 1400);
            INSERT INTO group_test VALUES ('200007', 1500);
            INSERT INTO group_test VALUES ('200007', 1600);

            SELECT COUNT(*) FROM (SELECT a FROM group_test GROUP BY a);
            SELECT a FROM group_test GROUP BY a LIMIT 10;
            """;

        String result = convertMySqlToShentong(invalidSql);

        // 验证缺少表别名的语法被正确拒绝 - 根据MySQL 8.4官方文档，派生表必须有别名
        assertTrue(result.isEmpty(), "缺少表别名的子查询语法应该被MySQL强制校验拒绝，转换结果应为空");

        System.out.println("✅ 缺少表别名的子查询语法被正确拒绝\n");
    }

    /**
     * 测试MySQL标准GROUP BY和LIMIT语法的转换
     * 使用正确的MySQL语法进行GROUP BY测试
     */
    @Test
    @DisplayName("MySQL标准GROUP BY语法转换测试")
    void testMySqlStandardGroupByFeature() throws Exception {
        System.out.println("=== MySQL标准GROUP BY语法转换测试 ===\n");

        // 使用正确的MySQL语法，包含表别名
        String mysqlSql = """
            CREATE TABLE group_test (
                a CHAR(6),
                b INT
            );

            INSERT INTO group_test VALUES ('200001', 1000);
            INSERT INTO group_test VALUES ('200001', 1100);
            INSERT INTO group_test VALUES ('200001', 1200);
            INSERT INTO group_test VALUES ('200004', 1300);
            INSERT INTO group_test VALUES ('200002', 1400);
            INSERT INTO group_test VALUES ('200007', 1500);
            INSERT INTO group_test VALUES ('200007', 1600);

            SELECT COUNT(*) FROM (SELECT a FROM group_test GROUP BY a) AS subquery;
            SELECT a FROM group_test GROUP BY a LIMIT 10;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        assertBasicConversionRequirements(shentongSql);

        // 验证MySQL标准GROUP BY语法转换
        assertTrue(shentongSql.contains("GROUP BY"), "应支持GROUP BY");
        assertTrue(shentongSql.contains("COUNT(*)"), "应支持COUNT(*)");
        assertTrue(shentongSql.contains("LIMIT") || shentongSql.contains("ROWNUM"), "LIMIT应该被转换为ROWNUM或保持");

        System.out.println("原始MySQL SQL:");
        System.out.println(mysqlSql);
        System.out.println("\n神通转换结果:");
        System.out.println(shentongSql);

        System.out.println("✅ MySQL标准GROUP BY语法转换成功\n");
    }
}
