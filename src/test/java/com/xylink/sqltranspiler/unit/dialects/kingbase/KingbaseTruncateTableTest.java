package com.xylink.sqltranspiler.unit.dialects.kingbase;

import com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator;

import com.xylink.sqltranspiler.core.ast.table.TruncateTable;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 金仓数据库TRUNCATE TABLE功能测试
 * 基于金仓官方文档的测试驱动开发
 * 参考：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/SQL_Statements_11.html#truncate
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保方言功能符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试方言特有功能
 *
 * <AUTHOR>
 */
public class KingbaseTruncateTableTest {

    private KingbaseGenerator generator;

    @BeforeEach
    public void setUp() {
        generator = new KingbaseGenerator();
    }

    private TruncateTable parseTruncateTable(String sql) {
        return (TruncateTable) MySqlHelper.parseStatement(sql);
    }

    @Test
    @DisplayName("测试基本TRUNCATE TABLE语句")
    public void testBasicTruncateTable() {
        String sql = "TRUNCATE TABLE employees;";
        TruncateTable truncateTable = parseTruncateTable(sql);
        String result = generator.generate(truncateTable);
        
        assertTrue(result.contains("TRUNCATE TABLE"));
        assertTrue(result.contains("\"employees\""));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试带模式名的TRUNCATE TABLE语句 - PostgreSQL兼容")
    public void testTruncateTableWithSchema() {
        String sql = "TRUNCATE TABLE hr.employees;";
        TruncateTable truncateTable = parseTruncateTable(sql);
        String result = generator.generate(truncateTable);
        
        assertTrue(result.contains("TRUNCATE TABLE"));
        assertTrue(result.contains("\"hr\".\"employees\""));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试带反引号的TRUNCATE TABLE语句")
    public void testTruncateTableWithBackticks() {
        String sql = "TRUNCATE TABLE `user_data`;";
        TruncateTable truncateTable = parseTruncateTable(sql);
        String result = generator.generate(truncateTable);
        
        assertTrue(result.contains("TRUNCATE TABLE"));
        assertTrue(result.contains("\"user_data\""));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试复杂表名的TRUNCATE TABLE语句")
    public void testTruncateTableComplexName() {
        String sql = "TRUNCATE TABLE `order-details`;";
        TruncateTable truncateTable = parseTruncateTable(sql);
        String result = generator.generate(truncateTable);
        
        assertTrue(result.contains("TRUNCATE TABLE"));
        assertTrue(result.contains("\"order-details\""));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试大写TRUNCATE TABLE语句")
    public void testUppercaseTruncateTable() {
        String sql = "TRUNCATE TABLE EMPLOYEES;";
        TruncateTable truncateTable = parseTruncateTable(sql);
        String result = generator.generate(truncateTable);
        
        assertTrue(result.contains("TRUNCATE TABLE"));
        assertTrue(result.contains("\"EMPLOYEES\""));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试混合大小写TRUNCATE TABLE语句")
    public void testMixedCaseTruncateTable() {
        String sql = "truncate table Employee_Data;";
        TruncateTable truncateTable = parseTruncateTable(sql);
        String result = generator.generate(truncateTable);
        
        assertTrue(result.contains("TRUNCATE TABLE"));
        assertTrue(result.contains("\"Employee_Data\""));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试金仓数据库PostgreSQL兼容的TRUNCATE TABLE")
    public void testKingbasePostgreSQLCompatibility() {
        // 根据金仓官方文档，金仓数据库完全兼容PostgreSQL的TRUNCATE TABLE语法
        String sql = "TRUNCATE TABLE sales_data;";
        TruncateTable truncateTable = parseTruncateTable(sql);
        String result = generator.generate(truncateTable);
        
        assertTrue(result.contains("TRUNCATE TABLE"));
        assertTrue(result.contains("\"sales_data\""));
        assertTrue(result.endsWith(";"));
        
        // 验证生成的SQL格式正确
        assertEquals("TRUNCATE TABLE \"sales_data\";", result);
    }

    @Test
    @DisplayName("测试金仓数据库MySQL兼容特性")
    public void testKingbaseMySQLCompatibility() {
        // 根据金仓官方文档，金仓数据库支持MySQL兼容的TRUNCATE TABLE语法
        String sql = "TRUNCATE TABLE mysql_table;";
        TruncateTable truncateTable = parseTruncateTable(sql);
        String result = generator.generate(truncateTable);
        
        assertTrue(result.contains("TRUNCATE TABLE"));
        assertTrue(result.contains("\"mysql_table\""));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试多个TRUNCATE TABLE语句的转换一致性")
    public void testMultipleTruncateTableConsistency() {
        String[] sqls = {
            "TRUNCATE TABLE table1;",
            "TRUNCATE TABLE table2;",
            "TRUNCATE TABLE table3;"
        };
        
        for (String sql : sqls) {
            TruncateTable truncateTable = parseTruncateTable(sql);
            String result = generator.generate(truncateTable);
            
            assertTrue(result.contains("TRUNCATE TABLE"));
            assertTrue(result.endsWith(";"));
            assertFalse(result.contains("`")); // 确保反引号被转换为双引号
        }
    }

    @Test
    @DisplayName("测试TRUNCATE TABLE语句的事务特性")
    public void testTruncateTableTransactionBehavior() {
        // 根据金仓官方文档，TRUNCATE TABLE在PostgreSQL兼容模式下支持事务
        String sql = "TRUNCATE TABLE transaction_test;";
        TruncateTable truncateTable = parseTruncateTable(sql);
        String result = generator.generate(truncateTable);
        
        // 验证生成的语句保持TRUNCATE的语义
        assertTrue(result.contains("TRUNCATE TABLE"));
        assertFalse(result.contains("DELETE"));
        assertTrue(result.contains("\"transaction_test\""));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试金仓数据库官方文档示例")
    public void testKingbaseOfficialExample() {
        // 根据金仓官方文档的TRUNCATE TABLE示例
        String sql = "TRUNCATE TABLE log_table;";
        TruncateTable truncateTable = parseTruncateTable(sql);
        String result = generator.generate(truncateTable);
        
        assertTrue(result.contains("TRUNCATE TABLE"));
        assertTrue(result.contains("\"log_table\""));
        assertTrue(result.endsWith(";"));
        
        // 验证生成的SQL格式正确，符合PostgreSQL标准
        assertEquals("TRUNCATE TABLE \"log_table\";", result);
    }
}
