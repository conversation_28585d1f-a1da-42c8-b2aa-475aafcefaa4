package com.xylink.sqltranspiler.unit.dialects;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.create.CreateSequence;
import com.xylink.sqltranspiler.core.ast.create.CreateTableAsSelect;
import com.xylink.sqltranspiler.core.ast.create.CreateView;
import com.xylink.sqltranspiler.core.ast.dml.QueryStmt;
import com.xylink.sqltranspiler.core.ast.privilege.Grant;
import com.xylink.sqltranspiler.core.ast.privilege.Revoke;
import com.xylink.sqltranspiler.core.ast.table.TruncateTable;
import com.xylink.sqltranspiler.core.dialects.Generator;
import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator;
import com.xylink.sqltranspiler.core.dialects.shentong.ShentongGenerator;

/**
 * 系统性功能完整性测试
 * 验证今天实现的所有功能在三种国产数据库中的完整性
 * 基于官方文档：
 * - MySQL: https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦: https://eco.dameng.com/document/dm/zh-cn/pm/
 * - 金仓: https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
 * - 神通: 神通数据库官方文档
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保方言功能符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试方言特有功能
 *
 * <AUTHOR>
 */
@DisplayName("系统性功能完整性测试")
public class SystematicFeatureCompletenessTest {

    private DamengGenerator damengGenerator;
    private KingbaseGenerator kingbaseGenerator;
    private ShentongGenerator shentongGenerator;

    @BeforeEach
    public void setUp() {
        damengGenerator = new DamengGenerator();
        kingbaseGenerator = new KingbaseGenerator();
        shentongGenerator = new ShentongGenerator();
    }

    @Test
    @DisplayName("1. 分页查询功能完整性检查")
    public void testPaginationFeatureCompleteness() {
        String sql = "SELECT * FROM test_table ORDER BY id LIMIT 5 OFFSET 10";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(sql);

        // 神通数据库：LIMIT OFFSET → ROWNUM三层嵌套查询
        String shentongResult = shentongGenerator.generate(queryStmt);
        assertTrue(shentongResult.contains("ROWNUM"), "神通数据库应该转换为ROWNUM分页");
        assertTrue(shentongResult.contains("ROW_NUM BETWEEN"), "神通数据库应该使用BETWEEN语法");

        // 达梦数据库：原生支持LIMIT OFFSET语法（根据官方文档）
        String damengResult = damengGenerator.generate(queryStmt);
        // 检查LIMIT和OFFSET是否都存在（忽略格式化的换行）
        assertTrue(damengResult.contains("LIMIT 5") && damengResult.contains("OFFSET 10"),
                "达梦数据库应该保持LIMIT OFFSET语法");
        assertFalse(damengResult.contains("ROWNUM"), "达梦数据库原生支持LIMIT，不需要转换为ROWNUM");

        // 金仓数据库：保持PostgreSQL兼容的LIMIT OFFSET
        String kingbaseResult = kingbaseGenerator.generate(queryStmt);
        // 检查LIMIT和OFFSET是否都存在（忽略格式化的换行）
        assertTrue(kingbaseResult.contains("LIMIT 5") && kingbaseResult.contains("OFFSET 10"),
                "金仓数据库应该保持LIMIT OFFSET语法");
    }

    @Test
    @DisplayName("2. 权限管理功能完整性检查")
    public void testPrivilegeManagementCompleteness() {
        String grantSql = "GRANT SELECT, INSERT ON `test_table` TO `user1`";
        Grant grant = new Grant(grantSql);

        String revokeSql = "REVOKE SELECT, INSERT ON `test_table` FROM `user1`";
        Revoke revoke = new Revoke(revokeSql);

        // 检查三个数据库都正确实现权限管理
        String[] generators = {"神通", "达梦", "金仓"};
        Generator[] gens = {shentongGenerator, damengGenerator, kingbaseGenerator};

        for (int i = 0; i < generators.length; i++) {
            String grantResult = gens[i].generate(grant);
            String revokeResult = gens[i].generate(revoke);

            assertTrue(grantResult.contains("GRANT"), generators[i] + "数据库应该支持GRANT语句");
            assertTrue(grantResult.contains("SELECT, INSERT"), generators[i] + "数据库应该保持权限列表");
            assertTrue(grantResult.contains("\"test_table\""), generators[i] + "数据库应该转换反引号");
            assertTrue(grantResult.contains("\"user1\""), generators[i] + "数据库应该转换用户名反引号");

            assertTrue(revokeResult.contains("REVOKE"), generators[i] + "数据库应该支持REVOKE语句");
            assertTrue(revokeResult.contains("SELECT, INSERT"), generators[i] + "数据库应该保持权限列表");
            assertTrue(revokeResult.contains("FROM"), generators[i] + "数据库应该使用FROM语法");
        }
    }

    @Test
    @DisplayName("3. 视图功能完整性检查")
    public void testViewFeatureCompleteness() {
        String selectStatement = "SELECT * FROM test_table";
        CreateView createView = new CreateView(
            new com.xylink.sqltranspiler.core.ast.TableId("test_view"),
            selectStatement
        );

        // 检查三个数据库都正确实现视图功能
        String[] generators = {"神通", "达梦", "金仓"};
        Generator[] gens = {shentongGenerator, damengGenerator, kingbaseGenerator};

        for (int i = 0; i < generators.length; i++) {
            String result = gens[i].generate(createView);
            assertTrue(result.contains("CREATE VIEW"), generators[i] + "数据库应该支持CREATE VIEW");
            assertTrue(result.contains("test_view"), generators[i] + "数据库应该保持视图名");
            assertTrue(result.contains("AS"), generators[i] + "数据库应该支持AS语法");
            assertTrue(result.endsWith(";"), generators[i] + "数据库生成的SQL应该以分号结尾");
        }
    }

    @Test
    @DisplayName("4. 序列功能完整性检查")
    public void testSequenceFeatureCompleteness() {
        CreateSequence createSequence = new CreateSequence(
            new com.xylink.sqltranspiler.core.ast.TableId("test_seq"),
            1L, 1L
        );

        // 检查三个数据库都正确实现序列功能
        String[] generators = {"神通", "达梦", "金仓"};
        Generator[] gens = {shentongGenerator, damengGenerator, kingbaseGenerator};

        for (int i = 0; i < generators.length; i++) {
            String result = gens[i].generate(createSequence);
            assertTrue(result.contains("CREATE SEQUENCE"), generators[i] + "数据库应该支持CREATE SEQUENCE");
            assertTrue(result.contains("test_seq"), generators[i] + "数据库应该保持序列名");
            assertTrue(result.endsWith(";"), generators[i] + "数据库生成的SQL应该以分号结尾");
        }
    }

    @Test
    @DisplayName("5. TRUNCATE TABLE功能完整性检查")
    public void testTruncateTableCompleteness() {
        TruncateTable truncateTable = new TruncateTable(
            new com.xylink.sqltranspiler.core.ast.TableId("test_table")
        );

        // 检查三个数据库都正确实现TRUNCATE功能
        String[] generators = {"神通", "达梦", "金仓"};
        Generator[] gens = {shentongGenerator, damengGenerator, kingbaseGenerator};

        for (int i = 0; i < generators.length; i++) {
            String result = gens[i].generate(truncateTable);
            assertTrue(result.contains("TRUNCATE TABLE"), generators[i] + "数据库应该支持TRUNCATE TABLE");
            assertTrue(result.contains("test_table"), generators[i] + "数据库应该保持表名");
            assertTrue(result.endsWith(";"), generators[i] + "数据库生成的SQL应该以分号结尾");
        }
    }

    @Test
    @DisplayName("6. CREATE TABLE AS SELECT功能完整性检查")
    public void testCreateTableAsSelectCompleteness() {
        String ctasSql = "SELECT * FROM source_table WHERE id > 100";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(ctasSql);
        CreateTableAsSelect ctas = new CreateTableAsSelect(
            new com.xylink.sqltranspiler.core.ast.TableId("target_table"),
            queryStmt
        );

        // 检查三个数据库都正确实现CTAS功能
        String[] generators = {"神通", "达梦", "金仓"};
        Generator[] gens = {shentongGenerator, damengGenerator, kingbaseGenerator};

        for (int i = 0; i < generators.length; i++) {
            String result = gens[i].generate(ctas);
            assertTrue(result.contains("CREATE TABLE"), generators[i] + "数据库应该支持CREATE TABLE");
            assertTrue(result.contains("target_table"), generators[i] + "数据库应该保持目标表名");
            assertTrue(result.contains("AS"), generators[i] + "数据库应该支持AS语法");
            assertTrue(result.contains("SELECT"), generators[i] + "数据库应该包含SELECT查询");
            assertTrue(result.endsWith(";"), generators[i] + "数据库生成的SQL应该以分号结尾");
        }
    }

    @Test
    @DisplayName("7. 伪列支持完整性检查")
    public void testPseudoColumnCompleteness() {
        String sqlWithRowid = "SELECT ROWID, * FROM test_table";
        String sqlWithRownum = "SELECT * FROM test_table WHERE ROWNUM <= 10";
        String sqlWithSysattr = "SELECT SYSATTR_ROWVERSION, * FROM test_table";

        QueryStmt rowidQuery = new QueryStmt();
        rowidQuery.setSql(sqlWithRowid);
        QueryStmt rownumQuery = new QueryStmt();
        rownumQuery.setSql(sqlWithRownum);
        QueryStmt sysattrQuery = new QueryStmt();
        sysattrQuery.setSql(sqlWithSysattr);

        // 神通数据库：原生支持所有伪列
        String shentongRowid = shentongGenerator.generate(rowidQuery);
        String shentongRownum = shentongGenerator.generate(rownumQuery);
        String shentongSysattr = shentongGenerator.generate(sysattrQuery);
        
        assertTrue(shentongRowid.contains("ROWID"), "神通数据库应该支持ROWID伪列");
        assertTrue(shentongRownum.contains("ROWNUM"), "神通数据库应该支持ROWNUM伪列");
        assertTrue(shentongSysattr.contains("SYSATTR_ROWVERSION"), "神通数据库应该支持SYSATTR_ROWVERSION伪列");

        // 达梦数据库：支持ROWID和ROWNUM，转换SYSATTR_ROWVERSION
        String damengRowid = damengGenerator.generate(rowidQuery);
        String damengRownum = damengGenerator.generate(rownumQuery);
        String damengSysattr = damengGenerator.generate(sysattrQuery);
        
        assertTrue(damengRowid.contains("ROWID"), "达梦数据库应该支持ROWID伪列");
        assertTrue(damengRownum.contains("ROWNUM"), "达梦数据库应该支持ROWNUM伪列");
        assertTrue(damengSysattr.contains("ROWID"), "达梦数据库应该将SYSATTR_ROWVERSION转换为ROWID");

        // 金仓数据库：转换为PostgreSQL兼容的伪列
        String kingbaseRowid = kingbaseGenerator.generate(rowidQuery);
        String kingbaseRownum = kingbaseGenerator.generate(rownumQuery);
        String kingbaseSysattr = kingbaseGenerator.generate(sysattrQuery);
        
        assertTrue(kingbaseRowid.contains("ctid"), "金仓数据库应该将ROWID转换为ctid");
        assertTrue(kingbaseRownum.contains("ROW_NUMBER()"), "金仓数据库应该将ROWNUM转换为ROW_NUMBER()");
        assertTrue(kingbaseSysattr.contains("ctid"), "金仓数据库应该将SYSATTR_ROWVERSION转换为ctid");
    }

    @Test
    @DisplayName("8. 反引号转换完整性检查")
    public void testBacktickConversionCompleteness() {
        String sql = "SELECT `id`, `name` FROM `test_table` WHERE `status` = 'active'";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(sql);

        // 检查三个数据库都正确转换反引号为双引号
        String[] generators = {"神通", "达梦", "金仓"};
        Generator[] gens = {shentongGenerator, damengGenerator, kingbaseGenerator};

        for (int i = 0; i < generators.length; i++) {
            String result = gens[i].generate(queryStmt);
            assertFalse(result.contains("`"), generators[i] + "数据库不应该包含反引号");
            assertTrue(result.toUpperCase().contains("ID") || result.contains("id"),
                       generators[i] + "数据库应该转换字段名反引号为标识符");
            assertTrue(result.toUpperCase().contains("NAME") || result.contains("name"),
                       generators[i] + "数据库应该转换字段名反引号为标识符");
            assertTrue(result.toUpperCase().contains("TEST_TABLE") || result.contains("test_table"),
                       generators[i] + "数据库应该转换表名反引号为标识符");
            assertTrue(result.toUpperCase().contains("STATUS") || result.contains("status"),
                       generators[i] + "数据库应该转换条件字段反引号为标识符");
        }
    }
}
