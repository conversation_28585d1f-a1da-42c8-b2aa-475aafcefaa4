package com.xylink.sqltranspiler.unit.dialects.kingbase;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.common.SetStatement;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import com.xylink.sqltranspiler.shared.base.BaseKingbaseConversionTest;

/**
 * 金仓SET语句转换测试
 * 验证MySQL SET语句到金仓数据库的转换功能，特别是字符集转换
 * 参考文档：
 * - MySQL: https://dev.mysql.com/doc/refman/8.4/en/set-variable.html
 * - 金仓: https://help.kingbase.com.cn/v8/development/sql-plsql/sql-quick/index.html
 * - 金仓字符集: https://help.kingbase.com.cn/v9/admin/reference/local/local-3.html
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保方言功能符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试方言特有功能
 *
 * <AUTHOR>
 */
@DisplayName("金仓SET语句转换测试")
public class KingbaseSetStatementTest extends BaseKingbaseConversionTest {

    @BeforeEach
    protected void setUp() {
        super.setUp();
    }

    @Test
    @DisplayName("SET NAMES utf8mb4 转换为 UTF8")
    void testSetNamesUtf8mb4() {
        String sql = "SET NAMES utf8mb4";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement, "解析结果不应为null");
        assertTrue(statement instanceof SetStatement, "应该解析为SetStatement类型");
        
        SetStatement setStatement = (SetStatement) statement;
        assertEquals("names", setStatement.getVariableName());
        assertEquals("utf8mb4", setStatement.getValue());
        
        // 测试转换为金仓SQL - 应该转换为UTF8
        String kingbaseSql = generator.generate(statement);
        assertNotNull(kingbaseSql);
        assertEquals("SET NAMES UTF8;", kingbaseSql);
        assertFalse(kingbaseSql.contains("utf8mb4"), "不应包含MySQL的utf8mb4字符集");
        assertTrue(kingbaseSql.contains("UTF8"), "应包含金仓的UTF8字符集");
    }

    @Test
    @DisplayName("SET NAMES utf8 转换为 UTF8")
    void testSetNamesUtf8() {
        String sql = "SET NAMES utf8";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof SetStatement);
        
        SetStatement setStatement = (SetStatement) statement;
        assertEquals("names", setStatement.getVariableName());
        assertEquals("utf8", setStatement.getValue());
        
        // 测试转换为金仓SQL
        String kingbaseSql = generator.generate(statement);
        assertNotNull(kingbaseSql);
        assertEquals("SET NAMES UTF8;", kingbaseSql);
        assertTrue(kingbaseSql.contains("UTF8"), "应包含金仓的UTF8字符集");
    }

    @Test
    @DisplayName("SET NAMES latin1 转换为 LATIN1")
    void testSetNamesLatin1() {
        String sql = "SET NAMES latin1";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof SetStatement);
        
        SetStatement setStatement = (SetStatement) statement;
        assertEquals("names", setStatement.getVariableName());
        assertEquals("latin1", setStatement.getValue());
        
        // 测试转换为金仓SQL
        String kingbaseSql = generator.generate(statement);
        assertNotNull(kingbaseSql);
        assertEquals("SET NAMES LATIN1;", kingbaseSql);
        assertTrue(kingbaseSql.contains("LATIN1"), "应包含金仓的LATIN1字符集");
    }

    @Test
    @DisplayName("SET NAMES gbk 转换为 GBK")
    void testSetNamesGbk() {
        String sql = "SET NAMES gbk";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof SetStatement);
        
        SetStatement setStatement = (SetStatement) statement;
        assertEquals("names", setStatement.getVariableName());
        assertEquals("gbk", setStatement.getValue());
        
        // 测试转换为金仓SQL
        String kingbaseSql = generator.generate(statement);
        assertNotNull(kingbaseSql);
        assertEquals("SET NAMES GBK;", kingbaseSql);
        assertTrue(kingbaseSql.contains("GBK"), "应包含金仓的GBK字符集");
    }

    @Test
    @DisplayName("SET NAMES ascii 转换为 SQL_ASCII")
    void testSetNamesAscii() {
        String sql = "SET NAMES ascii";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof SetStatement);
        
        SetStatement setStatement = (SetStatement) statement;
        assertEquals("names", setStatement.getVariableName());
        assertEquals("ascii", setStatement.getValue());
        
        // 测试转换为金仓SQL
        String kingbaseSql = generator.generate(statement);
        assertNotNull(kingbaseSql);
        assertEquals("SET NAMES SQL_ASCII;", kingbaseSql);
        assertTrue(kingbaseSql.contains("SQL_ASCII"), "应包含金仓的SQL_ASCII字符集");
    }

    @Test
    @DisplayName("SET NAMES gb2312 转换为 EUC_CN")
    void testSetNamesGb2312() {
        String sql = "SET NAMES gb2312";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof SetStatement);
        
        SetStatement setStatement = (SetStatement) statement;
        assertEquals("names", setStatement.getVariableName());
        assertEquals("gb2312", setStatement.getValue());
        
        // 测试转换为金仓SQL
        String kingbaseSql = generator.generate(statement);
        assertNotNull(kingbaseSql);
        assertEquals("SET NAMES EUC_CN;", kingbaseSql);
        assertTrue(kingbaseSql.contains("EUC_CN"), "应包含金仓的EUC_CN字符集");
    }

    @Test
    @DisplayName("SET NAMES 未知字符集处理")
    void testSetNamesUnknownCharset() {
        String sql = "SET NAMES unknown_charset";

        // 根据MySQL 8.4官方文档，未知字符集名应该导致解析失败或创建fallback语句
        try {
            Statement statement = MySqlHelper.parseStatement(sql);
            if (statement instanceof SetStatement) {
                SetStatement setStatement = (SetStatement) statement;
                assertEquals("names", setStatement.getVariableName());
                assertEquals("unknown_charset", setStatement.getValue());

                // 测试转换为金仓SQL - 应该使用默认的UTF8
                String kingbaseSql = generator.generate(statement);
                assertNotNull(kingbaseSql);
                assertEquals("SET NAMES UTF8;", kingbaseSql);
                assertTrue(kingbaseSql.contains("UTF8"), "未知字符集应默认转换为UTF8");
            } else {
                // 如果解析器创建了fallback语句（如QueryStmt），这也是可接受的行为
                // 根据MySQL 8.4官方文档，无效的字符集名可能导致解析失败
                assertTrue(statement != null, "解析器应该返回某种类型的语句对象");
                System.out.println("解析器为未知字符集创建了fallback语句: " + statement.getClass().getSimpleName());
            }
        } catch (Exception e) {
            // 解析失败也是预期的，因为unknown_charset不是有效的字符集名
            assertTrue(e.getMessage().contains("unknown_charset") ||
                      e.getMessage().contains("no viable alternative") ||
                      e.getMessage().contains("sql count: 0"),
                      "应该因为未知字符集而解析失败");
        }
    }

    @Test
    @DisplayName("SET NAMES 大小写不敏感转换")
    void testSetNamesCaseInsensitive() {
        String sql = "SET NAMES UTF8MB4";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof SetStatement);
        
        SetStatement setStatement = (SetStatement) statement;
        assertEquals("names", setStatement.getVariableName());
        assertEquals("UTF8MB4", setStatement.getValue());
        
        // 测试转换为金仓SQL - 应该正确处理大写
        String kingbaseSql = generator.generate(statement);
        assertNotNull(kingbaseSql);
        assertEquals("SET NAMES UTF8;", kingbaseSql);
        assertTrue(kingbaseSql.contains("UTF8"), "应正确转换大写的UTF8MB4");
    }

    @Test
    @DisplayName("SET NAMES 带引号的字符集名")
    void testSetNamesWithQuotes() {
        String sql = "SET NAMES 'utf8mb4'";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof SetStatement);
        
        SetStatement setStatement = (SetStatement) statement;
        assertEquals("names", setStatement.getVariableName());
        assertEquals("'utf8mb4'", setStatement.getValue());
        
        // 测试转换为金仓SQL
        String kingbaseSql = generator.generate(statement);
        assertNotNull(kingbaseSql);
        assertEquals("SET NAMES UTF8;", kingbaseSql);
        assertTrue(kingbaseSql.contains("UTF8"), "应正确处理带引号的字符集名");
    }

    @Test
    @DisplayName("SET NAMES 与其他SET语句的区别")
    void testSetNamesVsOtherSetStatements() {
        // 测试SET NAMES语句
        String setNamesSql = "SET NAMES utf8mb4";
        Statement setNamesStatement = MySqlHelper.parseStatement(setNamesSql);
        String setNamesResult = generator.generate(setNamesStatement);
        assertEquals("SET NAMES UTF8;", setNamesResult);
        
        // 测试其他SET语句（应该保持原样）
        String setVariableSql = "SET @var = 'value'";
        Statement setVariableStatement = MySqlHelper.parseStatement(setVariableSql);
        String setVariableResult = generator.generate(setVariableStatement);
        assertTrue(setVariableResult.contains("SET @var = 'value'"), 
                  "其他SET语句应保持原样");
        assertFalse(setVariableResult.contains("UTF8"), 
                   "其他SET语句不应进行字符集转换");
    }

    @Test
    @DisplayName("验证字符集转换映射表完整性")
    void testCharsetMappingCompleteness() {
        // 测试所有支持的字符集映射
        String[][] charsetMappings = {
            {"utf8mb4", "UTF8"},
            {"utf8", "UTF8"},
            {"latin1", "LATIN1"},
            {"ascii", "SQL_ASCII"},
            {"gbk", "GBK"},
            {"gb2312", "EUC_CN"}
        };

        for (String[] mapping : charsetMappings) {
            String mysqlCharset = mapping[0];
            String expectedKingbaseCharset = mapping[1];

            String sql = "SET NAMES " + mysqlCharset;
            Statement statement = MySqlHelper.parseStatement(sql);
            String kingbaseSql = generator.generate(statement);

            assertEquals("SET NAMES " + expectedKingbaseCharset + ";", kingbaseSql,
                        "字符集映射错误: " + mysqlCharset + " -> " + expectedKingbaseCharset);
        }
    }

    @Test
    @DisplayName("验证unsigned数据类型转换")
    void testUnsignedDataTypeConversion() {
        // 根据金仓官方文档表97第6行，金仓数据库原生支持UNSIGNED类型
        // SIGNED/UNSIGNED - 有符号整数和无符号整数

        // 测试bigint unsigned转换
        String sql1 = "CREATE TABLE test (id bigint unsigned not null primary key)";
        Statement statement1 = MySqlHelper.parseStatement(sql1);
        String kingbaseSql1 = generator.generate(statement1);
        assertTrue(kingbaseSql1.toUpperCase().contains("BIGINT"), "bigint unsigned应该转换为BIGINT");
        assertTrue(kingbaseSql1.toLowerCase().contains("unsigned"), "根据金仓官方文档表97，应保留unsigned关键字");
        assertFalse(kingbaseSql1.toLowerCase().contains("bigintunsigned"), "不应包含连写的bigintunsigned");

        // 测试int unsigned转换
        String sql2 = "CREATE TABLE test (id int unsigned not null)";
        Statement statement2 = MySqlHelper.parseStatement(sql2);
        String kingbaseSql2 = generator.generate(statement2);
        assertTrue(kingbaseSql2.toUpperCase().contains("INT"), "int unsigned应该转换为INT");
        assertTrue(kingbaseSql2.toLowerCase().contains("unsigned"), "根据金仓官方文档表97，应保留unsigned关键字");
        assertFalse(kingbaseSql2.toLowerCase().contains("intunsigned"), "不应包含连写的intunsigned");
    }
}
