package com.xylink.sqltranspiler.unit.dialects.kingbase;

import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.shared.base.BaseKingbaseConversionTest;

/**
 * 金仓数据库窗口函数测试
 * 基于金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/Function.html#id30
 * 测试MySQL到金仓数据库的窗口函数转换功能
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保函数转换符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试复杂函数组合的转换正确性
 *
 * <AUTHOR>
 */
@DisplayName("金仓数据库窗口函数转换测试")
public class KingbaseWindowFunctionTest extends BaseKingbaseConversionTest {

    /**
     * 验证窗口函数转换
     */
    private void assertWindowFunctionConversion(String result) {
        assertBasicConversionRequirements(result);
        assertTrue(result.contains("OVER"), "应包含OVER子句");
    }

    @Test
    @DisplayName("ROW_NUMBER窗口函数转换")
    public void testRowNumberWindowFunction() throws Exception {
        String sql = """
            SELECT 
                employee_id,
                department_id,
                salary,
                ROW_NUMBER() OVER (PARTITION BY department_id ORDER BY salary DESC) as row_num
            FROM employees;
            """;
        
        String result = convertMySqlToKingbase(sql);
        
        assertWindowFunctionConversion(result);
        assertTrue(result.contains("ROW_NUMBER()"), "应支持ROW_NUMBER函数");
        assertTrue(result.contains("PARTITION BY"), "应支持PARTITION BY子句");
        assertTrue(result.contains("ORDER BY"), "应支持ORDER BY子句");
    }

    @Test
    @DisplayName("RANK和DENSE_RANK窗口函数转换")
    public void testRankWindowFunctions() throws Exception {
        String sql = """
            SELECT 
                student_id,
                score,
                RANK() OVER (ORDER BY score DESC) as rank_pos,
                DENSE_RANK() OVER (ORDER BY score DESC) as dense_rank_pos
            FROM student_scores;
            """;
        
        String result = convertMySqlToKingbase(sql);
        
        assertWindowFunctionConversion(result);
        assertTrue(result.contains("RANK()"), "应支持RANK函数");
        assertTrue(result.contains("DENSE_RANK()"), "应支持DENSE_RANK函数");
    }

    @Test
    @DisplayName("LAG和LEAD窗口函数转换")
    public void testLagLeadWindowFunctions() throws Exception {
        String sql = """
            SELECT 
                date_col,
                value_col,
                LAG(value_col, 1) OVER (ORDER BY date_col) as prev_value,
                LEAD(value_col, 1) OVER (ORDER BY date_col) as next_value
            FROM time_series_data;
            """;
        
        String result = convertMySqlToKingbase(sql);
        
        assertWindowFunctionConversion(result);
        assertTrue(result.contains("LAG("), "应支持LAG函数");
        assertTrue(result.contains("LEAD("), "应支持LEAD函数");
    }

    @Test
    @DisplayName("FIRST_VALUE和LAST_VALUE窗口函数转换")
    public void testFirstLastValueWindowFunctions() throws Exception {
        String sql = """
            SELECT 
                product_id,
                sale_date,
                amount,
                FIRST_VALUE(amount) OVER (PARTITION BY product_id ORDER BY sale_date) as first_sale,
                LAST_VALUE(amount) OVER (PARTITION BY product_id ORDER BY sale_date 
                    ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) as last_sale
            FROM sales;
            """;
        
        String result = convertMySqlToKingbase(sql);
        
        assertWindowFunctionConversion(result);
        assertTrue(result.contains("FIRST_VALUE("), "应支持FIRST_VALUE函数");
        assertTrue(result.contains("LAST_VALUE("), "应支持LAST_VALUE函数");
        assertTrue(result.contains("ROWS BETWEEN"), "应支持窗口框架定义");
    }

    @Test
    @DisplayName("NTH_VALUE窗口函数转换")
    public void testNthValueWindowFunction() throws Exception {
        String sql = """
            SELECT 
                employee_id,
                salary,
                NTH_VALUE(salary, 2) OVER (ORDER BY salary DESC 
                    ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) as second_highest_salary
            FROM employees;
            """;
        
        String result = convertMySqlToKingbase(sql);
        
        assertWindowFunctionConversion(result);
        assertTrue(result.contains("NTH_VALUE("), "应支持NTH_VALUE函数");
        assertTrue(result.contains("CURRENT ROW"), "应支持CURRENT ROW");
    }

    @Test
    @DisplayName("NTILE窗口函数转换")
    public void testNtileWindowFunction() throws Exception {
        String sql = """
            SELECT 
                customer_id,
                total_purchases,
                NTILE(4) OVER (ORDER BY total_purchases DESC) as quartile
            FROM customer_summary;
            """;
        
        String result = convertMySqlToKingbase(sql);
        
        assertWindowFunctionConversion(result);
        assertTrue(result.contains("NTILE("), "应支持NTILE函数");
    }

    @Test
    @DisplayName("NTILE分组窗口函数转换")
    public void testNtileGroupingWindowFunction() throws Exception {
        String sql = """
            SELECT
                student_id,
                test_score,
                NTILE(5) OVER (ORDER BY test_score DESC) as score_quintile,
                RANK() OVER (ORDER BY test_score DESC) as score_rank
            FROM test_results;
            """;

        String result = convertMySqlToKingbase(sql);

        assertWindowFunctionConversion(result);
        assertTrue(result.contains("NTILE("), "应支持NTILE函数");
        assertTrue(result.contains("RANK()"), "应支持RANK函数");
    }

    @Test
    @DisplayName("聚合函数作为窗口函数转换")
    public void testAggregateAsWindowFunction() throws Exception {
        String sql = """
            SELECT 
                order_date,
                daily_sales,
                SUM(daily_sales) OVER (ORDER BY order_date 
                    ROWS BETWEEN 6 PRECEDING AND CURRENT ROW) as rolling_7day_sum,
                AVG(daily_sales) OVER (ORDER BY order_date 
                    ROWS BETWEEN 6 PRECEDING AND CURRENT ROW) as rolling_7day_avg
            FROM daily_sales_summary;
            """;
        
        String result = convertMySqlToKingbase(sql);
        
        assertWindowFunctionConversion(result);
        assertTrue(result.contains("SUM("), "应支持SUM作为窗口函数");
        assertTrue(result.contains("AVG("), "应支持AVG作为窗口函数");
        assertTrue(result.contains("PRECEDING"), "应支持PRECEDING");
    }

    @Test
    @DisplayName("复杂窗口函数查询转换")
    public void testComplexWindowFunctionQuery() throws Exception {
        String sql = """
            SELECT 
                department,
                employee_name,
                salary,
                ROW_NUMBER() OVER (PARTITION BY department ORDER BY salary DESC) as dept_rank,
                RANK() OVER (ORDER BY salary DESC) as overall_rank,
                salary - LAG(salary) OVER (PARTITION BY department ORDER BY salary DESC) as salary_diff,
                ROUND(salary / SUM(salary) OVER (PARTITION BY department) * 100, 2) as dept_salary_pct
            FROM employee_details
            WHERE salary > 0;
            """;
        
        String result = convertMySqlToKingbase(sql);
        
        assertWindowFunctionConversion(result);
        assertTrue(result.contains("ROW_NUMBER()"), "应支持ROW_NUMBER");
        assertTrue(result.contains("RANK()"), "应支持RANK");
        assertTrue(result.contains("LAG("), "应支持LAG");
        assertTrue(result.contains("SUM("), "应支持SUM作为窗口函数");
        assertTrue(result.contains("ROUND("), "应支持ROUND函数");
    }

    @Test
    @DisplayName("窗口函数与子查询结合转换")
    public void testWindowFunctionWithSubquery() throws Exception {
        String sql = """
            SELECT 
                dept_name,
                avg_salary,
                RANK() OVER (ORDER BY avg_salary DESC) as dept_rank
            FROM (
                SELECT 
                    department as dept_name,
                    AVG(salary) as avg_salary
                FROM employees
                GROUP BY department
            ) dept_averages;
            """;
        
        String result = convertMySqlToKingbase(sql);
        
        assertWindowFunctionConversion(result);
        assertTrue(result.contains("RANK()"), "应支持RANK函数");
        assertTrue(result.contains("AVG("), "应支持AVG函数");
        assertTrue(result.contains("GROUP BY"), "应支持GROUP BY");
    }
}
