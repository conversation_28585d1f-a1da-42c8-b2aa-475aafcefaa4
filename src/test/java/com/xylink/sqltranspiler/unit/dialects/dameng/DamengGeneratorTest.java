package com.xylink.sqltranspiler.unit.dialects.dameng;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import com.xylink.sqltranspiler.shared.base.BaseDamengTest;
import com.xylink.sqltranspiler.shared.matchers.SqlMatchers;

/**
 * 达梦生成器单元测试
 * 测试达梦SQL生成器的核心功能：
 * 1. CREATE TABLE语句生成
 * 2. 数据类型转换
 * 3. 标识符处理
 * 4. 约束转换
 * 严格遵循达梦官方文档：
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 达梦产品手册：https://eco.dameng.com/document/dm/zh-cn/pm/
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保方言功能符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试方言特有功能
 *
 * <AUTHOR>
 */
@DisplayName("达梦生成器单元测试")
public class DamengGeneratorTest extends BaseDamengTest {

    @Test
    @DisplayName("测试基本CREATE TABLE语句生成")
    void testBasicCreateTableGeneration() {
        String mysqlSql = """
            CREATE TABLE users (
                id int(11) NOT NULL AUTO_INCREMENT,
                name varchar(100) NOT NULL,
                email varchar(255) DEFAULT NULL,
                PRIMARY KEY (id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """;

        CreateTable createTable = (CreateTable) MySqlHelper.parseStatement(mysqlSql);
        String damengSql = generator.generate(createTable);

        logConversionResult(mysqlSql, damengSql, "dameng");

        // 使用新的断言方法
        assertNotNull(damengSql, "生成的达梦SQL不应为null");
        assertDamengConversionRequirements(damengSql);
        
        // 使用自定义匹配器
        SqlMatchers.assertIsCreateTableStatement(damengSql);
        SqlMatchers.assertContainsTable(damengSql, "users");
        // 根据达梦官方文档，只有保留关键字和混合大小写标识符才需要双引号
        // 普通标识符（如users, id, name）不需要双引号，这符合达梦最佳实践
        // SqlMatchers.assertUsesDoubleQuoteIdentifiers(damengSql);
        SqlMatchers.assertNotUsesBacktickIdentifiers(damengSql);
        SqlMatchers.assertEndsWithSemicolon(damengSql);
        
        // 达梦特定验证
        assertDamengDataTypeConversions(damengSql);
        SqlMatchers.assertDamengSpecificSyntax(damengSql);
    }

    @Test
    @DisplayName("测试数据类型转换")
    void testDataTypeConversion() {
        String mysqlSql = """
            CREATE TABLE data_types_test (
                tiny_col tinyint(3) NOT NULL,
                bool_flag tinyint(1) DEFAULT 1,
                int_col int(11) DEFAULT NULL,
                big_col bigint(20) NOT NULL,
                varchar_col varchar(255) NOT NULL
            );
            """;

        CreateTable createTable = (CreateTable) MySqlHelper.parseStatement(mysqlSql);
        String damengSql = generator.generate(createTable);

        logConversionResult(mysqlSql, damengSql, "dameng");

        // 验证数据类型转换
        assertDamengConversionRequirements(damengSql);
        assertDamengDataTypeConversions(damengSql);
        
        // 验证不包含MySQL特有的长度规格
        SqlMatchers.assertNotContainsDataType(damengSql, "int(11)");
        SqlMatchers.assertNotContainsDataType(damengSql, "bigint(20)");
        SqlMatchers.assertNotContainsDataType(damengSql, "tinyint(3)");
    }

    @Test
    @DisplayName("测试AUTO_INCREMENT转换为IDENTITY")
    void testAutoIncrementToIdentityConversion() {
        String mysqlSql = """
            CREATE TABLE auto_increment_test (
                id int(11) NOT NULL AUTO_INCREMENT,
                name varchar(100) NOT NULL,
                PRIMARY KEY (id)
            );
            """;

        CreateTable createTable = (CreateTable) MySqlHelper.parseStatement(mysqlSql);
        String damengSql = generator.generate(createTable);

        logConversionResult(mysqlSql, damengSql, "dameng");

        assertDamengConversionRequirements(damengSql);
        
        // 验证AUTO_INCREMENT转换
        SqlMatchers.assertNotContainsMySqlSpecificSyntax(damengSql);

        // 根据达梦官方文档，AUTO_INCREMENT应该转换为IDENTITY(1,1)
        // 参考：https://eco.dameng.com/document/dm/zh-cn/faq/faq-mysql-dm8-migrate.html
        assertTrue(damengSql.contains("IDENTITY"),
            "达梦数据库应该将AUTO_INCREMENT转换为IDENTITY");

        // 验证IDENTITY语法格式：IDENTITY(start, increment)
        assertTrue(damengSql.contains("IDENTITY(1,1)"),
            "达梦IDENTITY语法应为IDENTITY(1,1)格式，符合官方文档规范");
    }

    @Test
    @DisplayName("测试标识符双引号处理")
    void testIdentifierQuoting() {
        String mysqlSql = """
            CREATE TABLE `order` (
                `id` int(11) NOT NULL,
                `user` varchar(100) NOT NULL,
                `status` varchar(50) DEFAULT NULL
            );
            """;

        CreateTable createTable = (CreateTable) MySqlHelper.parseStatement(mysqlSql);
        String damengSql = generator.generate(createTable);

        logConversionResult(mysqlSql, damengSql, "dameng");

        assertDamengConversionRequirements(damengSql);
        
        // 验证标识符转换
        SqlMatchers.assertUsesDoubleQuoteIdentifiers(damengSql);
        SqlMatchers.assertNotUsesBacktickIdentifiers(damengSql);
        
        // 验证保留字正确处理
        SqlMatchers.assertContainsTable(damengSql, "order");
        SqlMatchers.assertContainsColumn(damengSql, "user");
        SqlMatchers.assertContainsColumn(damengSql, "status");
    }

    @Test
    @DisplayName("测试字符集转换")
    void testCharsetConversion() {
        String mysqlSql = """
            CREATE TABLE charset_test (
                id int NOT NULL,
                content varchar(1000) DEFAULT NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """;

        CreateTable createTable = (CreateTable) MySqlHelper.parseStatement(mysqlSql);
        String damengSql = generator.generate(createTable);

        logConversionResult(mysqlSql, damengSql, "dameng");

        assertDamengConversionRequirements(damengSql);
        
        // 验证字符集转换
        SqlMatchers.assertNotContainsMySqlSpecificSyntax(damengSql);
        
        // 如果包含字符集，应该是达梦标准格式
        if (damengSql.contains("CHARACTER SET")) {
            assertTrue(damengSql.contains("CHARACTER SET UTF8"), 
                "达梦应使用CHARACTER SET UTF8");
        }
    }
}
