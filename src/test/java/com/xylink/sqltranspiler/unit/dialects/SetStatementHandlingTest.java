package com.xylink.sqltranspiler.unit.dialects;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.Transpiler;
import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator;
import com.xylink.sqltranspiler.core.dialects.shentong.ShentongGenerator;

import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.read.ListAppender;

/**
 * 测试各数据库对MySQL SET语句的处理
 * 根据官方文档要求：
 * - MySQL: https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦: https://eco.dameng.com/document/dm/zh-cn/pm/
 * - 金仓: https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
 * - 神通: 官方文档规范
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保方言功能符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试方言特有功能
 *
 * <AUTHOR>
 */
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@DisplayName("SET语句处理测试")
public class SetStatementHandlingTest {

    private Transpiler transpiler;
    private ListAppender<ILoggingEvent> logAppender;
    private Logger damengLogger;
    private Logger kingbaseLogger;
    private Logger shentongLogger;

    @BeforeEach
    void setUp() {
        transpiler = new Transpiler();
        
        // 设置日志捕获器
        logAppender = new ListAppender<>();
        logAppender.start();
        
        // 为各数据库的Generator添加日志捕获
        damengLogger = (Logger) LoggerFactory.getLogger(DamengGenerator.class);
        kingbaseLogger = (Logger) LoggerFactory.getLogger(KingbaseGenerator.class);
        shentongLogger = (Logger) LoggerFactory.getLogger(ShentongGenerator.class);
        
        damengLogger.addAppender(logAppender);
        kingbaseLogger.addAppender(logAppender);
        shentongLogger.addAppender(logAppender);
    }

    @Test
    @DisplayName("达梦数据库：SET FOREIGN_KEY_CHECKS转换为注释")
    void testDamengSetForeignKeyChecks() {
        String inputSql = "SET foreign_key_checks = 0;";

        logAppender.list.clear();
        String result = transpiler.transpile(inputSql, "mysql", "dameng").translatedSql();

        // 验证转换结果
        assertNotNull(result, "转换结果不应为null");
        assertTrue(result.contains("-- SET foreign_key_checks = 0"),
                "达梦数据库应将SET foreign_key_checks转换为注释");
        assertTrue(result.contains("达梦数据库中外键检查通过其他方式配置"),
                "应包含达梦数据库的说明注释");

        // 验证警告日志
        boolean foundWarning = logAppender.list.stream()
                .anyMatch(event -> event.getLevel().toString().equals("WARN") &&
                        event.getFormattedMessage().contains("UNSUPPORTED_FEATURE") &&
                        event.getFormattedMessage().contains("SET FOREIGN_KEY_CHECKS") &&
                        event.getFormattedMessage().contains("DM database"));
        
        assertTrue(foundWarning, "达梦数据库应记录SET FOREIGN_KEY_CHECKS不支持的警告日志");
    }

    @Test
    @DisplayName("达梦数据库：SET NAMES转换为注释")
    void testDamengSetNames() {
        String inputSql = "SET names utf8mb4;";

        logAppender.list.clear();
        String result = transpiler.transpile(inputSql, "mysql", "dameng").translatedSql();

        // 验证转换结果 - 根据官方确认，达梦数据库不支持SET NAMES语句
        assertTrue(result.contains("-- SET names utf8mb4"),
                "达梦数据库应将SET names转换为注释");
        assertTrue(result.contains("达梦数据库中字符集通过连接字符串指定"),
                "应包含达梦数据库的说明注释");

        // 验证警告日志
        boolean foundWarning = logAppender.list.stream()
                .anyMatch(event -> event.getLevel().toString().equals("WARN") &&
                        event.getFormattedMessage().contains("SET NAMES") &&
                        event.getFormattedMessage().contains("DM database"));

        assertTrue(foundWarning, "达梦数据库应记录SET NAMES不支持的警告日志");
    }

    @Test
    @DisplayName("达梦数据库：SET SQL_MODE转换为注释")
    void testDamengSetSqlMode() {
        String inputSql = "SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';";

        logAppender.list.clear();
        String result = transpiler.transpile(inputSql, "mysql", "dameng").translatedSql();

        // 验证转换结果
        assertTrue(result.contains("-- SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO'"),
                "达梦数据库应将SET sql_mode转换为注释");
        assertTrue(result.contains("达梦数据库不支持MySQL的SQL_MODE"),
                "应包含达梦数据库的说明注释");

        // 验证警告日志
        boolean foundWarning = logAppender.list.stream()
                .anyMatch(event -> event.getLevel().toString().equals("WARN") &&
                        event.getFormattedMessage().contains("SET SQL_MODE") &&
                        event.getFormattedMessage().contains("DM database"));
        
        assertTrue(foundWarning, "达梦数据库应记录SET SQL_MODE不支持的警告日志");
    }

    @Test
    @DisplayName("达梦数据库：SET AUTOCOMMIT正确转换")
    void testDamengSetAutocommit() {
        String inputSql = "SET autocommit = 0;";

        logAppender.list.clear();
        String result = transpiler.transpile(inputSql, "mysql", "dameng").translatedSql();

        // 验证转换结果
        assertTrue(result.contains("SET AUTOCOMMIT OFF"),
                "达梦数据库应将SET autocommit = 0转换为SET AUTOCOMMIT OFF");
        assertFalse(result.contains("--"),
                "SET AUTOCOMMIT是支持的，不应转换为注释");

        // 验证没有警告日志（AUTOCOMMIT是支持的）
        boolean foundWarning = logAppender.list.stream()
                .anyMatch(event -> event.getLevel().toString().equals("WARN") &&
                        event.getFormattedMessage().contains("AUTOCOMMIT"));
        
        assertFalse(foundWarning, "达梦数据库支持SET AUTOCOMMIT，不应记录警告");
    }

    @Test
    @DisplayName("金仓数据库：SET语句处理")
    void testKingbaseSetStatements() {
        String inputSql = "SET foreign_key_checks = 0;\n" +
                "SET names utf8mb4;\n" +
                "SET autocommit = 1;";

        String result = transpiler.transpile(inputSql, "mysql", "kingbase").translatedSql();

        // 金仓数据库通常会保留或转换SET语句
        assertNotNull(result, "转换结果不应为null");
        // 具体的转换逻辑取决于金仓数据库的实现
    }

    @Test
    @DisplayName("神通数据库：SET语句处理")
    void testShentongSetStatements() {
        String inputSql = "SET foreign_key_checks = 0;\n" +
                "SET names utf8mb4;\n" +
                "SET autocommit = 1;";

        String result = transpiler.transpile(inputSql, "mysql", "shentong").translatedSql();

        // 神通数据库通常会保留或转换SET语句
        assertNotNull(result, "转换结果不应为null");
        // 具体的转换逻辑取决于神通数据库的实现
    }

    @Test
    @DisplayName("测试多个SET语句的批量处理")
    void testMultipleSetStatements() {
        String inputSql = "SET foreign_key_checks = 0;\n" +
                "SET names utf8mb4;\n" +
                "SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';\n" +
                "SET autocommit = 0;\n" +
                "CREATE TABLE test_table (id INT);\n" +
                "COMMIT;";

        logAppender.list.clear();
        String result = transpiler.transpile(inputSql, "mysql", "dameng").translatedSql();

        // 验证所有SET语句都被正确处理
        assertTrue(result.contains("-- SET foreign_key_checks = 0"),
                "foreign_key_checks应转换为注释");
        assertTrue(result.contains("-- SET names utf8mb4"),
                "names应转换为注释");
        assertTrue(result.contains("-- SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO'"),
                "sql_mode应转换为注释");
        assertTrue(result.contains("SET AUTOCOMMIT OFF"),
                "autocommit应正确转换");
        assertTrue(result.contains("CREATE TABLE test_table"), 
                "CREATE TABLE语句应保留");
        assertTrue(result.contains("COMMIT"), 
                "COMMIT语句应保留");

        // 验证记录了正确数量的警告
        long warningCount = logAppender.list.stream()
                .filter(event -> event.getLevel().toString().equals("WARN") &&
                        event.getFormattedMessage().contains("UNSUPPORTED_FEATURE"))
                .count();
        assertEquals(3, warningCount, "应记录3个不支持的SET语句警告");
    }

    @Test
    @DisplayName("测试SET语句的大小写不敏感")
    void testSetStatementCaseInsensitive() {
        String inputSql = "set FOREIGN_KEY_CHECKS = 0;\n" +
                "Set Names UTF8MB4;\n" +
                "SET sql_mode = 'no_auto_value_on_zero';";

        logAppender.list.clear();
        String result = transpiler.transpile(inputSql, "mysql", "dameng").translatedSql();

        // 验证大小写不敏感的处理
        assertTrue(result.contains("-- set FOREIGN_KEY_CHECKS = 0"),
                "应保留原始大小写并转换为注释");
        assertTrue(result.contains("-- Set Names UTF8MB4"),
                "应保留原始大小写并转换为注释");

        // 验证记录了警告
        long warningCount = logAppender.list.stream()
                .filter(event -> event.getLevel().toString().equals("WARN"))
                .count();
        assertEquals(3, warningCount, "应记录3个警告，不受大小写影响");
    }

    @Test
    @DisplayName("测试SET语句中的特殊字符和引号")
    void testSetStatementWithSpecialCharacters() {
        String inputSql = "SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO';";

        logAppender.list.clear();
        String result = transpiler.transpile(inputSql, "mysql", "dameng").translatedSql();

        // 验证包含特殊字符的SET语句被正确处理
        assertTrue(result.contains("-- SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'"), 
                "应正确处理包含特殊字符的SET语句");

        // 验证记录了警告
        boolean foundWarning = logAppender.list.stream()
                .anyMatch(event -> event.getLevel().toString().equals("WARN") &&
                        event.getFormattedMessage().contains("SET SQL_MODE"));
        
        assertTrue(foundWarning, "应记录SET SQL_MODE的警告");
    }
}
