package com.xylink.sqltranspiler.unit.dialects.kingbase;

import com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator;

import com.xylink.sqltranspiler.core.ast.alter.AlterView;
import com.xylink.sqltranspiler.core.ast.create.CreateView;
import com.xylink.sqltranspiler.core.ast.drop.DropView;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 金仓数据库视图功能测试
 * 基于金仓官方文档的测试驱动开发
 * 参考：https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保方言功能符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试方言特有功能
 *
 * <AUTHOR>
 */
public class KingbaseViewTest {

    private KingbaseGenerator generator;

    @BeforeEach
    public void setUp() {
        generator = new KingbaseGenerator();
    }

    private CreateView parseCreateView(String sql) {
        return (CreateView) MySqlHelper.parseStatement(sql);
    }

    private AlterView parseAlterView(String sql) {
        return (AlterView) MySqlHelper.parseStatement(sql);
    }

    private DropView parseDropView(String sql) {
        return (DropView) MySqlHelper.parseStatement(sql);
    }

    @Test
    @DisplayName("测试基本CREATE VIEW语句")
    public void testBasicCreateView() {
        String sql = "CREATE VIEW employee_view AS SELECT id, name FROM employees;";
        CreateView createView = parseCreateView(sql);
        String result = generator.generate(createView);
        
        assertTrue(result.contains("CREATE VIEW"));
        assertTrue(result.contains("employee_view"));
        assertTrue(result.contains("AS SELECT id, name FROM employees"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试CREATE OR REPLACE VIEW语句 - 金仓PostgreSQL兼容")
    public void testCreateOrReplaceView() {
        String sql = "CREATE OR REPLACE VIEW employee_view AS SELECT id, name FROM employees;";
        CreateView createView = parseCreateView(sql);
        String result = generator.generate(createView);
        
        assertTrue(result.contains("CREATE OR REPLACE VIEW"));
        assertTrue(result.contains("employee_view"));
        assertTrue(result.contains("AS SELECT id, name FROM employees"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试带列列表的CREATE VIEW语句")
    public void testCreateViewWithColumnList() {
        String sql = "CREATE VIEW employee_view (emp_id, emp_name) AS SELECT id, name FROM employees;";
        CreateView createView = parseCreateView(sql);
        String result = generator.generate(createView);
        
        assertTrue(result.contains("CREATE VIEW"));
        assertTrue(result.contains("employee_view"));
        assertTrue(result.contains("(\"emp_id\", \"emp_name\")"));
        assertTrue(result.contains("AS SELECT id, name FROM employees"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试带WITH CHECK OPTION的CREATE VIEW语句 - PostgreSQL兼容")
    public void testCreateViewWithCheckOption() {
        String sql = "CREATE VIEW employee_view AS SELECT id, name FROM employees WITH CHECK OPTION;";
        CreateView createView = parseCreateView(sql);
        String result = generator.generate(createView);
        
        assertTrue(result.contains("CREATE VIEW"));
        assertTrue(result.contains("employee_view"));
        assertTrue(result.contains("AS SELECT id, name FROM employees"));
        assertTrue(result.contains("WITH CHECK OPTION"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试带CASCADED CHECK OPTION的CREATE VIEW语句")
    public void testCreateViewWithCascadedCheckOption() {
        String sql = "CREATE VIEW employee_view AS SELECT id, name FROM employees WITH CASCADED CHECK OPTION;";
        CreateView createView = parseCreateView(sql);
        String result = generator.generate(createView);
        
        assertTrue(result.contains("CREATE VIEW"));
        assertTrue(result.contains("employee_view"));
        assertTrue(result.contains("AS SELECT id, name FROM employees"));
        assertTrue(result.contains("WITH CASCADED CHECK OPTION"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试基本ALTER VIEW语句")
    public void testBasicAlterView() {
        String sql = "ALTER VIEW employee_view AS SELECT id, name, department FROM employees;";
        AlterView alterView = parseAlterView(sql);
        String result = generator.generate(alterView);
        
        assertTrue(result.contains("ALTER VIEW"));
        assertTrue(result.contains("employee_view"));
        assertTrue(result.contains("AS SELECT id, name, department FROM employees"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试带列列表的ALTER VIEW语句")
    public void testAlterViewWithColumnList() {
        String sql = "ALTER VIEW employee_view (emp_id, emp_name, dept) AS SELECT id, name, department FROM employees;";
        AlterView alterView = parseAlterView(sql);
        String result = generator.generate(alterView);
        
        assertTrue(result.contains("ALTER VIEW"));
        assertTrue(result.contains("employee_view"));
        assertTrue(result.contains("(\"emp_id\", \"emp_name\", \"dept\")"));
        assertTrue(result.contains("AS SELECT id, name, department FROM employees"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试基本DROP VIEW语句")
    public void testBasicDropView() {
        String sql = "DROP VIEW employee_view;";
        DropView dropView = parseDropView(sql);
        String result = generator.generate(dropView);
        
        assertTrue(result.contains("DROP VIEW"));
        assertTrue(result.contains("employee_view"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试DROP VIEW IF EXISTS语句")
    public void testDropViewIfExists() {
        String sql = "DROP VIEW IF EXISTS employee_view;";
        DropView dropView = parseDropView(sql);
        String result = generator.generate(dropView);
        
        assertTrue(result.contains("DROP VIEW"));
        assertTrue(result.contains("IF EXISTS"));
        assertTrue(result.contains("employee_view"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试DROP多个VIEW语句")
    public void testDropMultipleViews() {
        String sql = "DROP VIEW view1, view2, view3;";
        DropView dropView = parseDropView(sql);
        String result = generator.generate(dropView);
        
        assertTrue(result.contains("DROP VIEW"));
        assertTrue(result.contains("view1"));
        assertTrue(result.contains("view2"));
        assertTrue(result.contains("view3"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试DROP VIEW CASCADE语句 - PostgreSQL兼容")
    public void testDropViewCascade() {
        String sql = "DROP VIEW employee_view CASCADE;";
        DropView dropView = parseDropView(sql);
        String result = generator.generate(dropView);
        
        assertTrue(result.contains("DROP VIEW"));
        assertTrue(result.contains("employee_view"));
        assertTrue(result.contains("CASCADE"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试DROP VIEW RESTRICT语句 - PostgreSQL兼容")
    public void testDropViewRestrict() {
        String sql = "DROP VIEW employee_view RESTRICT;";
        DropView dropView = parseDropView(sql);
        String result = generator.generate(dropView);
        
        assertTrue(result.contains("DROP VIEW"));
        assertTrue(result.contains("employee_view"));
        assertTrue(result.contains("RESTRICT"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试复杂的CREATE VIEW语句 - 金仓PostgreSQL兼容")
    public void testComplexCreateView() {
        String sql = "CREATE OR REPLACE VIEW department_summary (dept_name, emp_count, avg_salary) AS " +
                    "SELECT d.name, COUNT(e.id), AVG(e.salary) FROM departments d " +
                    "LEFT JOIN employees e ON d.id = e.department_id GROUP BY d.name " +
                    "WITH CASCADED CHECK OPTION;";
        CreateView createView = parseCreateView(sql);
        String result = generator.generate(createView);
        
        assertTrue(result.contains("CREATE OR REPLACE VIEW"));
        assertTrue(result.contains("department_summary"));
        assertTrue(result.contains("(\"dept_name\", \"emp_count\", \"avg_salary\")"));
        assertTrue(result.contains("AS SELECT"));
        assertTrue(result.contains("LEFT JOIN"));
        assertTrue(result.contains("GROUP BY"));
        assertTrue(result.contains("WITH CASCADED CHECK OPTION"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试带模式名的CREATE VIEW语句")
    public void testCreateViewWithSchema() {
        String sql = "CREATE VIEW hr.employee_view AS SELECT id, name FROM hr.employees;";
        CreateView createView = parseCreateView(sql);
        String result = generator.generate(createView);
        
        assertTrue(result.contains("CREATE VIEW"));
        assertTrue(result.contains("\"hr\".\"employee_view\""));
        assertTrue(result.contains("AS SELECT id, name FROM hr.employees"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试金仓数据库MySQL兼容特性")
    public void testKingbaseMySQLCompatibility() {
        // 根据金仓官方文档，金仓数据库支持MySQL兼容的视图语法
        String sql = "CREATE VIEW sales_view AS SELECT * FROM sales WHERE amount > 1000;";
        CreateView createView = parseCreateView(sql);
        String result = generator.generate(createView);
        
        assertTrue(result.contains("CREATE VIEW"));
        assertTrue(result.contains("sales_view"));
        assertTrue(result.contains("AS SELECT * FROM sales WHERE amount > 1000"));
        assertTrue(result.endsWith(";"));
    }
}
