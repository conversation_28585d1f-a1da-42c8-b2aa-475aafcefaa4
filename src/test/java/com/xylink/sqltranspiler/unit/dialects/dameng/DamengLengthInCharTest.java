package com.xylink.sqltranspiler.unit.dialects.dameng;

import com.xylink.sqltranspiler.Transpiler;
import com.xylink.sqltranspiler.core.context.TranspilationResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试达梦数据库LENGTH_IN_CHAR配置对VARCHAR长度转换的影响
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保方言功能符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试方言特有功能
 *
 * <AUTHOR>
 */
public class DamengLengthInCharTest {
    private static final Logger log = LoggerFactory.getLogger(DamengLengthInCharTest.class);
    
    private Transpiler transpiler;
    
    @BeforeEach
    void setUp() {
        transpiler = new Transpiler();
    }
    
    @Test
    void testLengthInCharTrue_ShouldKeepOriginalLength() {
        // 设置LENGTH_IN_CHAR=1（字符模式）
        transpiler.setDamengLengthInChar(true);
        
        String sql = "CREATE TABLE test_table (" +
                    "short_text varchar(10) NOT NULL," +
                    "medium_text varchar(100) DEFAULT NULL," +
                    "long_text varchar(255) DEFAULT NULL" +
                    ") ENGINE=InnoDB;";
        
        TranspilationResult transpilationResult = transpiler.transpile(sql, "mysql", "dameng");
        String result = transpilationResult.translatedSql();

        log.info("LENGTH_IN_CHAR=1 result:\n{}", result);

        // 验证长度保持不变（注意达梦输出为大写VARCHAR）
        assertTrue(result.contains("VARCHAR(10)"), "short_text should keep length 10");
        assertTrue(result.contains("VARCHAR(100)"), "medium_text should keep length 100");
        assertTrue(result.contains("VARCHAR(255)"), "long_text should keep length 255");

        // 验证没有长度调整
        assertFalse(result.contains("VARCHAR(30)"), "Should not convert to byte length");
        assertFalse(result.contains("VARCHAR(300)"), "Should not convert to byte length");
        assertFalse(result.contains("VARCHAR(765)"), "Should not convert to byte length");
    }
    
    @Test
    void testLengthInCharFalse_ShouldConvertToByteLength() {
        // 设置LENGTH_IN_CHAR=0（字节模式，默认）
        transpiler.setDamengLengthInChar(false);
        
        String sql = "CREATE TABLE test_table (" +
                    "short_text varchar(10) NOT NULL," +
                    "medium_text varchar(100) DEFAULT NULL," +
                    "long_text varchar(255) DEFAULT NULL" +
                    ") ENGINE=InnoDB;";
        
        TranspilationResult transpilationResult = transpiler.transpile(sql, "mysql", "dameng");
        String result = transpilationResult.translatedSql();

        log.info("LENGTH_IN_CHAR=0 result:\n{}", result);

        // 验证长度转换为字节长度（3倍）（注意达梦输出为大写VARCHAR）
        assertTrue(result.contains("VARCHAR(30)"), "short_text should convert to 30 bytes (10*3)");
        assertTrue(result.contains("VARCHAR(300)"), "medium_text should convert to 300 bytes (100*3)");
        assertTrue(result.contains("VARCHAR(765)"), "long_text should convert to 765 bytes (255*3)");

        // 验证原始长度不存在
        assertFalse(result.contains("VARCHAR(10)"), "Should not keep original character length");
        assertFalse(result.contains("VARCHAR(100)"), "Should not keep original character length");
        assertFalse(result.contains("VARCHAR(255)"), "Should not keep original character length");
    }
    
    @Test
    void testDefaultBehavior_ShouldBeCharacterBased() {
        // 不设置配置，应该默认为字符模式
        String sql = "CREATE TABLE test_table (" +
                    "test_column varchar(50) NOT NULL" +
                    ") ENGINE=InnoDB;";

        TranspilationResult transpilationResult = transpiler.transpile(sql, "mysql", "dameng");
        String result = transpilationResult.translatedSql();

        log.info("Default behavior result:\n{}", result);

        // 默认应该保持字符长度（注意达梦输出为大写VARCHAR）
        assertTrue(result.contains("VARCHAR(50)"), "Should keep character length by default");
        assertFalse(result.contains("VARCHAR(150)"), "Should not convert to byte length by default");
    }
    
    @Test
    void testConfigurationPersistence() {
        // 测试默认配置
        assertTrue(transpiler.isDamengLengthInChar(), "Default configuration should be true (character-based)");

        // 测试配置是否正确保持
        transpiler.setDamengLengthInChar(false);
        assertFalse(transpiler.isDamengLengthInChar(), "Configuration should be set to false");

        transpiler.setDamengLengthInChar(true);
        assertTrue(transpiler.isDamengLengthInChar(), "Configuration should be set to true");
    }
    
    @Test
    void testLargeVarcharLength_ShouldNotExceedLimit() {
        // 测试大长度VARCHAR不超过达梦限制
        transpiler.setDamengLengthInChar(false);
        
        String sql = "CREATE TABLE test_table (" +
                    "large_text varchar(15000) DEFAULT NULL" +  // 15000 * 3 = 45000 > 32767
                    ") ENGINE=InnoDB;";
        
        TranspilationResult transpilationResult = transpiler.transpile(sql, "mysql", "dameng");
        String result = transpilationResult.translatedSql();

        log.info("Large VARCHAR result:\n{}", result);

        // 应该保持原始长度，因为计算的字节长度超过了达梦限制
        // 数据类型转换逻辑会处理CLOB转换（注意达梦输出为大写）
        assertTrue(result.contains("VARCHAR(15000)") || result.contains("CLOB"),
                  "Large VARCHAR should either keep original length or convert to CLOB");
    }
}
