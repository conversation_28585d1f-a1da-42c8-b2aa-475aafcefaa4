package com.xylink.sqltranspiler.unit.dialects.dameng;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.dml.QueryStmt;
import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;

/**
 * 达梦数据库窗口函数PARTITION BY丢失问题修复测试
 * 
 * 基于审查报告发现的严重问题：窗口函数PARTITION BY子句完全丢失
 * 严格验证达梦数据库窗口函数转换的官方标准合规性
 * 
 * 官方文档依据：
 * - 达梦数据库官方文档: https://eco.dameng.com/document/dm/zh-cn/pm/
 * - 达梦数据库完全支持窗口函数，包括PARTITION BY和ORDER BY子句
 * 
 * 测试原则：
 * 1. 不简化测试用例SQL - 使用真实复杂的窗口函数
 * 2. 严格验证PARTITION BY和ORDER BY子句的完整保留
 * 3. 基于达梦官方文档验证转换结果的正确性
 */
@DisplayName("达梦数据库窗口函数PARTITION BY丢失问题修复测试")
public class DamengWindowFunctionTest {

    private DamengGenerator generator;

    @BeforeEach
    void setUp() {
        generator = new DamengGenerator();
    }

    @Test
    @DisplayName("基础窗口函数ROW_NUMBER() OVER (PARTITION BY ... ORDER BY ...)转换测试")
    void testBasicWindowFunctionPartitionBy() {
        // 基于达梦官方文档，这是标准的窗口函数语法
        String mysqlSql = """
            SELECT 
                id, 
                name,
                dept_id,
                salary,
                ROW_NUMBER() OVER (PARTITION BY dept_id ORDER BY salary DESC) as rn
            FROM employees;
            """;
        
        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "SQL解析不应失败");
        assertTrue(statement instanceof QueryStmt, "应该解析为QueryStmt");
        
        String damengSql = generator.generate(statement);
        assertNotNull(damengSql, "达梦转换结果不应为null");

        // 输出转换结果用于调试
        System.out.println("转换结果: " + damengSql);

        // 关键验证：PARTITION BY子句必须完整保留
        assertTrue(damengSql.contains("PARTITION BY"), 
                  "达梦转换结果必须包含PARTITION BY子句 - 这是严重的官方标准违反");
        assertTrue(damengSql.contains("ORDER BY"), 
                  "达梦转换结果必须包含ORDER BY子句");
        assertTrue(damengSql.contains("ROW_NUMBER()"), 
                  "达梦转换结果必须包含ROW_NUMBER()函数");
        
        // 验证字段名处理符合达梦官方文档规范
        // 根据达梦官方文档，普通字段名（非保留字）不需要双引号
        assertTrue(damengSql.contains("dept_id"),
                  "字段名dept_id应被正确保留");
        assertTrue(damengSql.contains("salary"),
                  "字段名salary应被正确保留");
        
        System.out.println("✅ 基础窗口函数转换测试通过");
        System.out.println("MySQL原始SQL: " + mysqlSql.replaceAll("\\s+", " "));
        System.out.println("达梦转换结果: " + damengSql);
        
        // 如果测试失败，输出详细信息用于调试
        if (!damengSql.contains("PARTITION BY")) {
            System.err.println("❌ 严重错误：PARTITION BY子句丢失！");
            System.err.println("这违反了达梦数据库官方文档标准");
            System.err.println("转换结果: " + damengSql);
        }
    }

    @Test
    @DisplayName("多个窗口函数RANK()和DENSE_RANK()转换测试")
    void testMultipleWindowFunctions() {
        String mysqlSql = """
            SELECT 
                id, 
                name,
                dept_id,
                salary,
                ROW_NUMBER() OVER (PARTITION BY dept_id ORDER BY salary DESC) as rn,
                RANK() OVER (PARTITION BY dept_id ORDER BY salary DESC) as rank_num,
                DENSE_RANK() OVER (PARTITION BY dept_id ORDER BY salary DESC) as dense_rank_num
            FROM employees
            WHERE salary > 5000;
            """;
        
        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "SQL解析不应失败");
        
        String damengSql = generator.generate(statement);
        assertNotNull(damengSql, "达梦转换结果不应为null");
        
        // 验证所有窗口函数的PARTITION BY子句都被保留
        int partitionByCount = countOccurrences(damengSql, "PARTITION BY");
        assertEquals(3, partitionByCount, 
                    "应该有3个PARTITION BY子句（对应3个窗口函数）");
        
        int orderByCount = countOccurrences(damengSql, "ORDER BY");
        assertTrue(orderByCount >= 3, 
                  "应该至少有3个ORDER BY子句（窗口函数内的）");
        
        // 验证所有窗口函数都存在
        assertTrue(damengSql.contains("ROW_NUMBER()"), "应包含ROW_NUMBER()");
        assertTrue(damengSql.contains("RANK()"), "应包含RANK()");
        assertTrue(damengSql.contains("DENSE_RANK()"), "应包含DENSE_RANK()");
        
        System.out.println("✅ 多个窗口函数转换测试通过");
        System.out.println("PARTITION BY出现次数: " + partitionByCount);
        System.out.println("ORDER BY出现次数: " + orderByCount);
    }

    @Test
    @DisplayName("复杂窗口函数与聚合函数SUM() OVER转换测试")
    void testComplexWindowFunctionWithAggregation() {
        String mysqlSql = """
            SELECT 
                id,
                dept_id,
                salary,
                SUM(salary) OVER (PARTITION BY dept_id) as dept_total_salary,
                AVG(salary) OVER (PARTITION BY dept_id ORDER BY hire_date ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) as running_avg,
                LAG(salary, 1) OVER (PARTITION BY dept_id ORDER BY hire_date) as prev_salary
            FROM employees
            ORDER BY dept_id, hire_date;
            """;
        
        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "SQL解析不应失败");
        
        String damengSql = generator.generate(statement);
        assertNotNull(damengSql, "达梦转换结果不应为null");
        
        // 验证复杂窗口函数的PARTITION BY子句
        assertTrue(damengSql.contains("PARTITION BY"), 
                  "复杂窗口函数的PARTITION BY子句必须保留");
        
        // 验证聚合窗口函数
        assertTrue(damengSql.contains("SUM(") && damengSql.contains(") OVER"), 
                  "SUM() OVER窗口函数必须正确转换");
        assertTrue(damengSql.contains("AVG(") && damengSql.contains(") OVER"), 
                  "AVG() OVER窗口函数必须正确转换");
        
        // 验证LAG函数（达梦数据库支持）
        assertTrue(damengSql.contains("LAG("), 
                  "LAG()函数应该被正确转换");
        
        // 验证ROWS BETWEEN子句（达梦数据库支持）
        if (mysqlSql.contains("ROWS BETWEEN")) {
            // 如果原SQL包含ROWS BETWEEN，转换结果也应该包含
            // 注意：这里可能需要根据达梦具体语法进行调整
            System.out.println("原SQL包含ROWS BETWEEN子句，检查转换结果");
        }
        
        System.out.println("✅ 复杂窗口函数转换测试通过");
        System.out.println("转换结果包含关键窗口函数特性");
    }

    @Test
    @DisplayName("窗口函数与CTE结合使用转换测试")
    void testWindowFunctionWithCTE() {
        String mysqlSql = """
            WITH dept_stats AS (
                SELECT 
                    dept_id,
                    COUNT(*) as emp_count,
                    AVG(salary) as avg_salary
                FROM employees 
                GROUP BY dept_id
            ),
            ranked_employees AS (
                SELECT 
                    e.id,
                    e.name,
                    e.dept_id,
                    e.salary,
                    ROW_NUMBER() OVER (PARTITION BY e.dept_id ORDER BY e.salary DESC) as dept_rank,
                    ds.avg_salary
                FROM employees e
                JOIN dept_stats ds ON e.dept_id = ds.dept_id
            )
            SELECT * FROM ranked_employees WHERE dept_rank <= 3;
            """;
        
        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "CTE与窗口函数组合SQL解析不应失败");
        
        String damengSql = generator.generate(statement);
        assertNotNull(damengSql, "达梦转换结果不应为null");
        
        // 验证CTE结构保留
        assertTrue(damengSql.contains("WITH"), 
                  "CTE的WITH关键字必须保留");
        
        // 验证窗口函数的PARTITION BY保留
        assertTrue(damengSql.contains("PARTITION BY"), 
                  "CTE内窗口函数的PARTITION BY子句必须保留");
        assertTrue(damengSql.contains("ROW_NUMBER()"), 
                  "CTE内的ROW_NUMBER()函数必须保留");
        
        System.out.println("✅ 窗口函数与CTE结合转换测试通过");
        System.out.println("CTE和窗口函数特性都被正确保留");
    }

    @Test
    @DisplayName("窗口函数错误处理测试")
    void testWindowFunctionErrorHandling() {
        // 测试不完整的窗口函数语法
        String invalidSql = "SELECT id, ROW_NUMBER() OVER () FROM employees;";
        
        try {
            Statement statement = MySqlHelper.parseStatement(invalidSql);
            if (statement != null) {
                String damengSql = generator.generate(statement);
                System.out.println("转换结果: " + damengSql);
                // 即使是不完整的窗口函数，也不应该导致转换器崩溃
                assertNotNull(damengSql, "即使是不完整的窗口函数，转换器也不应崩溃");
            }
        } catch (Exception e) {
            System.out.println("✅ 转换器正确处理了不完整的窗口函数语法: " + e.getMessage());
        }
    }

    /**
     * 辅助方法：计算字符串中子字符串的出现次数
     */
    private int countOccurrences(String text, String substring) {
        int count = 0;
        int index = 0;
        while ((index = text.indexOf(substring, index)) != -1) {
            count++;
            index += substring.length();
        }
        return count;
    }
}
