package com.xylink.sqltranspiler.unit.dialects.shentong;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import com.xylink.sqltranspiler.shared.base.BaseShentongConversionTest;

/**
 * 神通数据库窗口函数转换测试
 * 基于官方文档测试窗口函数支持：
 * - MySQL官方文档：https://dev.mysql.com/doc/refman/8.4/en/window-functions.html
 * - 神通数据库官方文档：shentong.md 第19601-20568行
 * 测试覆盖的窗口函数：
 * 1. ROW_NUMBER() - 行号函数
 * 2. RANK() - 排名函数（有间隙）
 * 3. DENSE_RANK() - 密集排名函数（无间隙）
 * 4. LAG() - 访问前面行的数据
 * 5. LEAD() - 访问后面行的数据
 * 6. FIRST_VALUE() - 窗口内第一个值
 * 7. LAST_VALUE() - 窗口内最后一个值
 * 8. PERCENT_RANK() - 百分比排名
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保函数转换符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试复杂函数组合的转换正确性
 *
 * <AUTHOR>
 */
@DisplayName("神通数据库窗口函数转换测试")
public class ShentongWindowFunctionTest extends BaseShentongConversionTest {
    
    private static final Logger log = LoggerFactory.getLogger(ShentongWindowFunctionTest.class);

    /**
     * 测试ROW_NUMBER()窗口函数
     * 
     * 根据神通官方文档第19601行：ROW_NUMBER()
     * 语法：ROW_NUMBER() OVER (ORDER BY column)
     * 功能：按照order_by_clause排序的已排序行组，从1开始为每一行分配一个唯一的数字
     */
    @Test
    @DisplayName("ROW_NUMBER()窗口函数转换测试")
    public void testRowNumberFunction() throws Exception {
        log.info("=== ROW_NUMBER()窗口函数转换测试 ===");
        
        // 基础ROW_NUMBER()测试
        String mysqlSql = """
            SELECT 
                id, 
                name, 
                salary,
                ROW_NUMBER() OVER (ORDER BY salary DESC) as row_num
            FROM employees;
            """;
        
        log.info("原始MySQL SQL: {}", mysqlSql);
        
        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "SQL解析不应失败");
        
        String shentongSql = generator.generate(statement);
        assertNotNull(shentongSql, "神通转换结果不应为null");
        
        log.info("神通转换结果: {}", shentongSql);
        
        // 验证ROW_NUMBER()函数保持不变（神通数据库原生支持）
        assertTrue(shentongSql.contains("ROW_NUMBER()"), "应包含ROW_NUMBER()函数");
        assertTrue(shentongSql.contains("OVER (ORDER BY"), "应包含OVER子句");
        assertTrue(shentongSql.contains("ORDER BY salary DESC"), "应保持ORDER BY子句");
        assertTrue(shentongSql.contains("row_num"), "应保持别名");
        assertTrue(shentongSql.endsWith(";"), "应以分号结尾");
        
        log.info("✅ ROW_NUMBER()函数转换测试通过");
    }

    /**
     * 测试RANK()窗口函数
     * 
     * 根据神通官方文档第19697行：RANK()
     * 功能：计算一组值的排名
     */
    @Test
    @DisplayName("RANK()窗口函数转换测试")
    public void testRankFunction() throws Exception {
        log.info("=== RANK()窗口函数转换测试 ===");
        
        String mysqlSql = """
            SELECT 
                id, 
                department,
                salary,
                RANK() OVER (PARTITION BY department ORDER BY salary DESC) as dept_rank
            FROM employees;
            """;
        
        log.info("原始MySQL SQL: {}", mysqlSql);
        
        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "SQL解析不应失败");
        
        String shentongSql = generator.generate(statement);
        assertNotNull(shentongSql, "神通转换结果不应为null");
        
        log.info("神通转换结果: {}", shentongSql);
        
        // 验证RANK()函数和PARTITION BY子句
        assertTrue(shentongSql.contains("RANK()"), "应包含RANK()函数");
        assertTrue(shentongSql.contains("PARTITION BY department"), "应保持PARTITION BY子句");
        assertTrue(shentongSql.contains("ORDER BY salary DESC"), "应保持ORDER BY子句");
        assertTrue(shentongSql.contains("dept_rank"), "应保持别名");
        
        log.info("✅ RANK()函数转换测试通过");
    }

    /**
     * 测试DENSE_RANK()窗口函数
     * 
     * 根据神通官方文档第19794行：DENSE_RANK()
     * 功能：计算一组值的排名，排名的值连续
     */
    @Test
    @DisplayName("DENSE_RANK()窗口函数转换测试")
    public void testDenseRankFunction() throws Exception {
        log.info("=== DENSE_RANK()窗口函数转换测试 ===");
        
        String mysqlSql = """
            SELECT 
                id, 
                score,
                DENSE_RANK() OVER (ORDER BY score DESC) as dense_rank_num
            FROM student_scores;
            """;
        
        log.info("原始MySQL SQL: {}", mysqlSql);
        
        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "SQL解析不应失败");
        
        String shentongSql = generator.generate(statement);
        assertNotNull(shentongSql, "神通转换结果不应为null");
        
        log.info("神通转换结果: {}", shentongSql);
        
        // 验证DENSE_RANK()函数
        assertTrue(shentongSql.contains("DENSE_RANK()"), "应包含DENSE_RANK()函数");
        assertTrue(shentongSql.contains("OVER (ORDER BY"), "应包含OVER子句");
        assertTrue(shentongSql.contains("ORDER BY score DESC"), "应保持ORDER BY子句");
        assertTrue(shentongSql.contains("dense_rank_num"), "应保持别名");
        
        log.info("✅ DENSE_RANK()函数转换测试通过");
    }

    /**
     * 测试LAG()窗口函数
     * 
     * 根据神通官方文档第20184行：LAG()
     * 功能：提供在不使用自连接的情况下访问表中多个行的途径，能根据给定的物理偏移量访问前面位置的行
     */
    @Test
    @DisplayName("LAG()窗口函数转换测试")
    public void testLagFunction() throws Exception {
        log.info("=== LAG()窗口函数转换测试 ===");
        
        // 基础LAG函数测试
        String mysqlSql = """
            SELECT 
                employee_id,
                hire_date,
                salary,
                LAG(salary, 1) OVER (ORDER BY hire_date) as prev_salary
            FROM employees;
            """;
        
        log.info("原始MySQL SQL: {}", mysqlSql);
        
        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "SQL解析不应失败");
        
        String shentongSql = generator.generate(statement);
        assertNotNull(shentongSql, "神通转换结果不应为null");
        
        log.info("神通转换结果: {}", shentongSql);
        
        // 验证LAG()函数
        assertTrue(shentongSql.contains("LAG(salary, 1)"), "应包含LAG函数");
        assertTrue(shentongSql.contains("OVER (ORDER BY hire_date)"), "应保持OVER子句");
        assertTrue(shentongSql.contains("prev_salary"), "应保持别名");
        
        log.info("✅ LAG()函数转换测试通过");
    }

    /**
     * 测试带默认值的LAG()函数
     */
    @Test
    @DisplayName("LAG()函数带默认值转换测试")
    public void testLagFunctionWithDefault() throws Exception {
        log.info("=== LAG()函数带默认值转换测试 ===");
        
        String mysqlSql = """
            SELECT 
                employee_id,
                salary,
                LAG(salary, 2, 0) OVER (PARTITION BY department ORDER BY employee_id) as prev_salary
            FROM employees;
            """;
        
        log.info("原始MySQL SQL: {}", mysqlSql);
        
        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "SQL解析不应失败");
        
        String shentongSql = generator.generate(statement);
        assertNotNull(shentongSql, "神通转换结果不应为null");
        
        log.info("神通转换结果: {}", shentongSql);
        
        // 验证LAG()函数的完整语法
        assertTrue(shentongSql.contains("LAG(salary, 2, 0)"), "应包含完整的LAG函数参数");
        assertTrue(shentongSql.contains("PARTITION BY department"), "应保持PARTITION BY子句");
        assertTrue(shentongSql.contains("ORDER BY employee_id"), "应保持ORDER BY子句");
        
        log.info("✅ LAG()函数带默认值转换测试通过");
    }

    /**
     * 测试LEAD()窗口函数
     * 
     * 根据神通官方文档第20294行：LEAD()
     * 功能：提供在不使用自连接的情况下访问表中多个行的途径，能根据给定的物理偏移量访问后面位置的行
     */
    @Test
    @DisplayName("LEAD()窗口函数转换测试")
    public void testLeadFunction() throws Exception {
        log.info("=== LEAD()窗口函数转换测试 ===");
        
        String mysqlSql = """
            SELECT 
                employee_id,
                hire_date,
                salary,
                LEAD(salary, 1) OVER (ORDER BY hire_date) as next_salary
            FROM employees;
            """;
        
        log.info("原始MySQL SQL: {}", mysqlSql);
        
        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "SQL解析不应失败");
        
        String shentongSql = generator.generate(statement);
        assertNotNull(shentongSql, "神通转换结果不应为null");
        
        log.info("神通转换结果: {}", shentongSql);
        
        // 验证LEAD()函数
        assertTrue(shentongSql.contains("LEAD(salary, 1)"), "应包含LEAD函数");
        assertTrue(shentongSql.contains("OVER (ORDER BY hire_date)"), "应保持OVER子句");
        assertTrue(shentongSql.contains("next_salary"), "应保持别名");
        
        log.info("✅ LEAD()函数转换测试通过");
    }

    /**
     * 测试FIRST_VALUE()窗口函数
     * 
     * 根据神通官方文档第20400行：FIRST_VALUE()
     * 功能：返回已排序集的第一个值
     */
    @Test
    @DisplayName("FIRST_VALUE()窗口函数转换测试")
    public void testFirstValueFunction() throws Exception {
        log.info("=== FIRST_VALUE()窗口函数转换测试 ===");
        
        String mysqlSql = """
            SELECT 
                product_id,
                sale_date,
                amount,
                FIRST_VALUE(amount) OVER (PARTITION BY product_id ORDER BY sale_date) as first_sale
            FROM sales;
            """;
        
        log.info("原始MySQL SQL: {}", mysqlSql);
        
        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "SQL解析不应失败");
        
        String shentongSql = generator.generate(statement);
        assertNotNull(shentongSql, "神通转换结果不应为null");
        
        log.info("神通转换结果: {}", shentongSql);
        
        // 验证FIRST_VALUE()函数
        assertTrue(shentongSql.contains("FIRST_VALUE(amount)"), "应包含FIRST_VALUE函数");
        assertTrue(shentongSql.contains("PARTITION BY product_id"), "应保持PARTITION BY子句");
        assertTrue(shentongSql.contains("ORDER BY sale_date"), "应保持ORDER BY子句");
        assertTrue(shentongSql.contains("first_sale"), "应保持别名");
        
        log.info("✅ FIRST_VALUE()函数转换测试通过");
    }

    /**
     * 测试LAST_VALUE()窗口函数
     * 
     * 根据神通官方文档第20496行：LAST_VALUE()
     * 功能：返回已排序集的最后一个值
     */
    @Test
    @DisplayName("LAST_VALUE()窗口函数转换测试")
    public void testLastValueFunction() throws Exception {
        log.info("=== LAST_VALUE()窗口函数转换测试 ===");
        
        String mysqlSql = """
            SELECT 
                product_id,
                sale_date,
                amount,
                LAST_VALUE(amount) OVER (PARTITION BY product_id ORDER BY sale_date 
                    ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) as last_sale
            FROM sales;
            """;
        
        log.info("原始MySQL SQL: {}", mysqlSql);
        
        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "SQL解析不应失败");
        
        String shentongSql = generator.generate(statement);
        assertNotNull(shentongSql, "神通转换结果不应为null");
        
        log.info("神通转换结果: {}", shentongSql);
        
        // 验证LAST_VALUE()函数和窗口框架
        assertTrue(shentongSql.contains("LAST_VALUE(amount)"), "应包含LAST_VALUE函数");
        assertTrue(shentongSql.contains("PARTITION BY product_id"), "应保持PARTITION BY子句");
        assertTrue(shentongSql.contains("ORDER BY sale_date"), "应保持ORDER BY子句");
        assertTrue(shentongSql.contains("ROWS BETWEEN"), "应保持窗口框架定义");
        assertTrue(shentongSql.contains("UNBOUNDED PRECEDING"), "应保持窗口框架起始");
        assertTrue(shentongSql.contains("UNBOUNDED FOLLOWING"), "应保持窗口框架结束");
        assertTrue(shentongSql.contains("last_sale"), "应保持别名");
        
        log.info("✅ LAST_VALUE()函数转换测试通过");
    }

    /**
     * 测试NTILE()窗口函数
     *
     * NTILE函数将结果集分成指定数量的组
     */
    @Test
    @DisplayName("NTILE()窗口函数转换测试")
    public void testNtileFunction() throws Exception {
        log.info("=== NTILE()窗口函数转换测试 ===");

        String mysqlSql = """
            SELECT
                student_id,
                score,
                NTILE(4) OVER (ORDER BY score DESC) as quartile
            FROM student_scores;
            """;

        log.info("原始MySQL SQL: {}", mysqlSql);

        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "SQL解析不应失败");

        String shentongSql = generator.generate(statement);
        assertNotNull(shentongSql, "神通转换结果不应为null");

        log.info("神通转换结果: {}", shentongSql);

        // 验证NTILE()函数
        assertTrue(shentongSql.contains("NTILE(4)"), "应包含NTILE函数");
        assertTrue(shentongSql.contains("OVER (ORDER BY score DESC)"), "应保持OVER子句");
        assertTrue(shentongSql.contains("quartile"), "应保持别名");

        log.info("✅ NTILE()函数转换测试通过");
    }

    /**
     * 测试复合窗口函数查询
     * 验证多个窗口函数在同一查询中的转换
     */
    @Test
    @DisplayName("复合窗口函数转换测试")
    public void testMultipleWindowFunctions() throws Exception {
        log.info("=== 复合窗口函数转换测试 ===");

        String mysqlSql = """
            SELECT
                employee_id,
                department_id,
                salary,
                ROW_NUMBER() OVER (ORDER BY salary DESC) as overall_rank,
                RANK() OVER (PARTITION BY department_id ORDER BY salary DESC) as dept_rank,
                DENSE_RANK() OVER (PARTITION BY department_id ORDER BY salary DESC) as dept_dense_rank,
                LAG(salary, 1) OVER (PARTITION BY department_id ORDER BY employee_id) as prev_salary,
                LEAD(salary, 1) OVER (PARTITION BY department_id ORDER BY employee_id) as next_salary,
                FIRST_VALUE(salary) OVER (PARTITION BY department_id ORDER BY salary DESC) as max_dept_salary,
                LAST_VALUE(salary) OVER (PARTITION BY department_id ORDER BY salary DESC
                    ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) as min_dept_salary
            FROM employees
            ORDER BY department_id, salary DESC;
            """;

        log.info("原始MySQL SQL: {}", mysqlSql);

        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "SQL解析不应失败");

        String shentongSql = generator.generate(statement);
        assertNotNull(shentongSql, "神通转换结果不应为null");

        log.info("神通转换结果: {}", shentongSql);

        // 验证所有窗口函数都正确转换
        assertTrue(shentongSql.contains("ROW_NUMBER()"), "应包含ROW_NUMBER函数");
        assertTrue(shentongSql.contains("RANK()"), "应包含RANK函数");
        assertTrue(shentongSql.contains("DENSE_RANK()"), "应包含DENSE_RANK函数");
        assertTrue(shentongSql.contains("LAG(salary, 1)"), "应包含LAG函数");
        assertTrue(shentongSql.contains("LEAD(salary, 1)"), "应包含LEAD函数");
        assertTrue(shentongSql.contains("FIRST_VALUE(salary)"), "应包含FIRST_VALUE函数");
        assertTrue(shentongSql.contains("LAST_VALUE(salary)"), "应包含LAST_VALUE函数");

        // 验证PARTITION BY子句数量
        int partitionByCount = countOccurrences(shentongSql, "PARTITION BY");
        assertTrue(partitionByCount >= 6, "应该有至少6个PARTITION BY子句");

        // 验证ORDER BY子句数量
        int orderByCount = countOccurrences(shentongSql, "ORDER BY");
        assertTrue(orderByCount >= 7, "应该有至少7个ORDER BY子句（包括最后的ORDER BY）");

        // 验证窗口框架定义
        assertTrue(shentongSql.contains("ROWS BETWEEN"), "应包含窗口框架定义");

        log.info("✅ 复合窗口函数转换测试通过");
    }

    /**
     * 测试聚合窗口函数
     * 验证SUM、AVG、COUNT等聚合函数与OVER子句的组合
     */
    @Test
    @DisplayName("聚合窗口函数转换测试")
    public void testAggregateWindowFunctions() throws Exception {
        log.info("=== 聚合窗口函数转换测试 ===");

        String mysqlSql = """
            SELECT
                department_id,
                employee_id,
                salary,
                SUM(salary) OVER (PARTITION BY department_id) as dept_total_salary,
                AVG(salary) OVER (PARTITION BY department_id) as dept_avg_salary,
                COUNT(*) OVER (PARTITION BY department_id) as dept_employee_count,
                MAX(salary) OVER (PARTITION BY department_id) as dept_max_salary,
                MIN(salary) OVER (PARTITION BY department_id) as dept_min_salary,
                SUM(salary) OVER (PARTITION BY department_id ORDER BY salary
                    ROWS UNBOUNDED PRECEDING) as running_total
            FROM employees;
            """;

        log.info("原始MySQL SQL: {}", mysqlSql);

        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "SQL解析不应失败");

        String shentongSql = generator.generate(statement);
        assertNotNull(shentongSql, "神通转换结果不应为null");

        log.info("神通转换结果: {}", shentongSql);

        // 验证聚合窗口函数
        assertTrue(shentongSql.contains("SUM(salary) OVER"), "应包含SUM窗口函数");
        assertTrue(shentongSql.contains("AVG(salary) OVER"), "应包含AVG窗口函数");
        assertTrue(shentongSql.contains("COUNT(*) OVER"), "应包含COUNT窗口函数");
        assertTrue(shentongSql.contains("MAX(salary) OVER"), "应包含MAX窗口函数");
        assertTrue(shentongSql.contains("MIN(salary) OVER"), "应包含MIN窗口函数");

        // 验证窗口框架
        assertTrue(shentongSql.contains("ROWS UNBOUNDED PRECEDING"), "应包含窗口框架定义");

        log.info("✅ 聚合窗口函数转换测试通过");
    }

    /**
     * 测试窗口函数的错误处理
     * 验证不支持的窗口函数语法的处理
     */
    @Test
    @DisplayName("窗口函数错误处理测试")
    public void testWindowFunctionErrorHandling() throws Exception {
        log.info("=== 窗口函数错误处理测试 ===");

        // 测试基本的窗口函数，确保不会出现转换错误
        String mysqlSql = """
            SELECT
                id,
                name,
                ROW_NUMBER() OVER (ORDER BY id) as row_num
            FROM test_table;
            """;

        log.info("原始MySQL SQL: {}", mysqlSql);

        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "SQL解析不应失败");

        String shentongSql = generator.generate(statement);
        assertNotNull(shentongSql, "神通转换结果不应为null");
        assertFalse(shentongSql.contains("ERROR"), "转换结果不应包含错误信息");
        assertFalse(shentongSql.contains("FAILED"), "转换结果不应包含失败信息");

        log.info("神通转换结果: {}", shentongSql);
        log.info("✅ 窗口函数错误处理测试通过");
    }

    /**
     * 测试窗口函数与其他SQL特性的组合
     * 验证窗口函数在复杂查询中的转换
     */
    @Test
    @DisplayName("窗口函数与其他SQL特性组合测试")
    public void testWindowFunctionWithOtherFeatures() throws Exception {
        log.info("=== 窗口函数与其他SQL特性组合测试 ===");

        String mysqlSql = """
            SELECT
                e.employee_id,
                e.department_id,
                e.salary,
                d.department_name,
                ROW_NUMBER() OVER (PARTITION BY e.department_id ORDER BY e.salary DESC) as dept_salary_rank,
                RANK() OVER (ORDER BY e.salary DESC) as overall_rank,
                LAG(e.salary, 1) OVER (PARTITION BY e.department_id ORDER BY e.employee_id) as prev_salary,
                AVG(e.salary) OVER (PARTITION BY e.department_id) as dept_avg_salary
            FROM employees e
            JOIN departments d ON e.department_id = d.department_id
            WHERE e.salary > 30000
            ORDER BY e.department_id, e.salary DESC;
            """;

        log.info("原始MySQL SQL: {}", mysqlSql);

        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "SQL解析不应失败");

        String shentongSql = generator.generate(statement);
        assertNotNull(shentongSql, "神通转换结果不应为null");

        log.info("神通转换结果: {}", shentongSql);

        // 验证窗口函数在复杂查询中正确转换
        assertTrue(shentongSql.contains("ROW_NUMBER()"), "应包含ROW_NUMBER函数");
        assertTrue(shentongSql.contains("RANK()"), "应包含RANK函数");
        assertTrue(shentongSql.contains("LAG("), "应包含LAG函数");
        assertTrue(shentongSql.contains("AVG("), "应包含AVG函数");
        assertTrue(shentongSql.contains("PARTITION BY"), "应包含PARTITION BY子句");
        assertTrue(shentongSql.contains("JOIN"), "应保持JOIN语法");
        assertTrue(shentongSql.contains("WHERE"), "应保持WHERE子句");

        log.info("✅ 窗口函数与其他SQL特性组合测试通过");
    }

    /**
     * 测试窗口函数的性能优化场景
     * 验证相同窗口定义的重用
     */
    @Test
    @DisplayName("窗口函数性能优化测试")
    public void testWindowFunctionPerformanceOptimization() throws Exception {
        log.info("=== 窗口函数性能优化测试 ===");

        String mysqlSql = """
            SELECT
                employee_id,
                salary,
                ROW_NUMBER() OVER (PARTITION BY department_id ORDER BY salary DESC) as row_num,
                RANK() OVER (PARTITION BY department_id ORDER BY salary DESC) as rank_num,
                DENSE_RANK() OVER (PARTITION BY department_id ORDER BY salary DESC) as dense_rank_num,
                PERCENT_RANK() OVER (PARTITION BY department_id ORDER BY salary DESC) as percent_rank_num
            FROM employees;
            """;

        log.info("原始MySQL SQL: {}", mysqlSql);

        // 根据数据库规则：严格禁止简化测试用例SQL以使测试通过
        // 必须使用完整的原始SQL进行测试，如果PERCENT_RANK不支持，应该实现支持而不是简化测试
        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "SQL解析不应失败");

        String shentongSql = generator.generate(statement);
        assertNotNull(shentongSql, "神通转换结果不应为null");

        log.info("神通转换结果: {}", shentongSql);

        // 验证相同窗口定义的函数都正确转换
        // 原始SQL包含4个窗口函数：ROW_NUMBER, RANK, DENSE_RANK, PERCENT_RANK
        int partitionByCount = countOccurrences(shentongSql, "PARTITION BY department_id");
        assertEquals(4, partitionByCount, "应该有4个相同的PARTITION BY子句");

        int orderByCount = countOccurrences(shentongSql, "ORDER BY salary DESC");
        assertTrue(orderByCount >= 4, "应该有至少4个相同的ORDER BY子句");

        log.info("✅ 窗口函数性能优化测试通过");
    }

    /**
     * 测试窗口函数的边界条件
     * 验证空结果集和单行结果集的处理
     */
    @Test
    @DisplayName("窗口函数边界条件测试")
    public void testWindowFunctionBoundaryConditions() throws Exception {
        log.info("=== 窗口函数边界条件测试 ===");

        // 测试带有复杂WHERE条件的窗口函数
        String mysqlSql = """
            SELECT
                employee_id,
                salary,
                ROW_NUMBER() OVER (ORDER BY salary) as row_num,
                LAG(salary, 1, 0) OVER (ORDER BY salary) as prev_salary,
                LEAD(salary, 1, 999999) OVER (ORDER BY salary) as next_salary
            FROM employees
            WHERE department_id IS NOT NULL
              AND salary BETWEEN 20000 AND 100000
            ORDER BY salary;
            """;

        log.info("原始MySQL SQL: {}", mysqlSql);

        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "SQL解析不应失败");

        String shentongSql = generator.generate(statement);
        assertNotNull(shentongSql, "神通转换结果不应为null");

        log.info("神通转换结果: {}", shentongSql);

        // 验证边界条件处理
        assertTrue(shentongSql.contains("LAG(salary, 1, 0)"), "应包含带默认值的LAG函数");
        assertTrue(shentongSql.contains("LEAD(salary, 1, 999999)"), "应包含带默认值的LEAD函数");
        assertTrue(shentongSql.contains("WHERE"), "应保持WHERE条件");
        assertTrue(shentongSql.contains("BETWEEN"), "应保持BETWEEN条件");
        assertTrue(shentongSql.contains("IS NOT NULL"), "应保持IS NOT NULL条件");

        log.info("✅ 窗口函数边界条件测试通过");
    }

    /**
     * 辅助方法：计算字符串中子字符串的出现次数
     */
    private int countOccurrences(String text, String substring) {
        int count = 0;
        int index = 0;
        while ((index = text.indexOf(substring, index)) != -1) {
            count++;
            index += substring.length();
        }
        return count;
    }
}
