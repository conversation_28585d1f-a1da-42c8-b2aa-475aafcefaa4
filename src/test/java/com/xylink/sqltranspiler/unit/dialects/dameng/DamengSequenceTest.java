package com.xylink.sqltranspiler.unit.dialects.dameng;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.TableId;
import com.xylink.sqltranspiler.core.ast.alter.AlterSequence;
import com.xylink.sqltranspiler.core.ast.create.CreateSequence;
import com.xylink.sqltranspiler.core.ast.drop.DropSequence;
import com.xylink.sqltranspiler.shared.base.BaseDamengTest;

/**
 * 达梦数据库序列功能测试
 * 基于达梦官方文档的测试驱动开发
 * 参考：https://eco.dameng.com/document/dm/zh-cn/pm/definition-statement.html
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保方言功能符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试方言特有功能
 *
 * <AUTHOR>
 */
public class DamengSequenceTest extends BaseDamengTest {

    @Test
    @DisplayName("测试基本CREATE SEQUENCE语句")
    public void testBasicCreateSequence() {
        TableId sequenceId = new TableId("test_seq");
        CreateSequence createSequence = new CreateSequence(sequenceId);
        String result = generator.generate(createSequence);
        
        assertTrue(result.contains("CREATE SEQUENCE"));
        assertTrue(result.contains("test_seq"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试带参数的CREATE SEQUENCE语句")
    public void testCreateSequenceWithParameters() {
        TableId sequenceId = new TableId("employee_seq");
        CreateSequence createSequence = new CreateSequence(sequenceId, 1L, 1L, 1L, 999999L, 20L, false, false, null);
        String result = generator.generate(createSequence);
        
        assertTrue(result.contains("CREATE SEQUENCE"));
        assertTrue(result.contains("employee_seq"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("START WITH 1"));
        assertTrue(result.contains("INCREMENT BY 1"));
        assertTrue(result.contains("MINVALUE 1"));
        assertTrue(result.contains("MAXVALUE 999999"));
        assertTrue(result.contains("CACHE 20"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试CREATE SEQUENCE IF NOT EXISTS语句")
    public void testCreateSequenceIfNotExists() {
        TableId sequenceId = new TableId("safe_seq");
        CreateSequence createSequence = new CreateSequence(sequenceId, true);
        String result = generator.generate(createSequence);
        
        assertTrue(result.contains("CREATE SEQUENCE"));
        assertTrue(result.contains("IF NOT EXISTS"));
        assertTrue(result.contains("safe_seq"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试带CYCLE的CREATE SEQUENCE语句")
    public void testCreateSequenceWithCycle() {
        TableId sequenceId = new TableId("cycle_seq");
        CreateSequence createSequence = new CreateSequence(sequenceId, 1L, 1L, 1L, 100L, null, true, false, null);
        String result = generator.generate(createSequence);
        
        assertTrue(result.contains("CREATE SEQUENCE"));
        assertTrue(result.contains("cycle_seq"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("START WITH 1"));
        assertTrue(result.contains("INCREMENT BY 1"));
        assertTrue(result.contains("MINVALUE 1"));
        assertTrue(result.contains("MAXVALUE 100"));
        assertTrue(result.contains("CYCLE"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试基本ALTER SEQUENCE语句")
    public void testBasicAlterSequence() {
        TableId sequenceId = new TableId("test_seq");
        AlterSequence alterSequence = new AlterSequence(sequenceId, 100L);
        String result = generator.generate(alterSequence);
        
        assertTrue(result.contains("ALTER SEQUENCE"));
        assertTrue(result.contains("test_seq"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("RESTART WITH 100"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试带多个参数的ALTER SEQUENCE语句")
    public void testAlterSequenceWithMultipleParameters() {
        TableId sequenceId = new TableId("employee_seq");
        AlterSequence alterSequence = new AlterSequence(sequenceId, 1000L, 2L, 1L, 9999999L, 50L, false, null);
        String result = generator.generate(alterSequence);
        
        assertTrue(result.contains("ALTER SEQUENCE"));
        assertTrue(result.contains("employee_seq"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("RESTART WITH 1000"));
        assertTrue(result.contains("INCREMENT BY 2"));
        assertTrue(result.contains("MINVALUE 1"));
        assertTrue(result.contains("MAXVALUE 9999999"));
        assertTrue(result.contains("CACHE 50"));
        assertTrue(result.contains("NO CYCLE"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试基本DROP SEQUENCE语句")
    public void testBasicDropSequence() {
        TableId sequenceId = new TableId("test_seq");
        List<TableId> sequenceIds = Arrays.asList(sequenceId);
        DropSequence dropSequence = new DropSequence(sequenceIds);
        String result = generator.generate(dropSequence);
        
        assertTrue(result.contains("DROP SEQUENCE"));
        assertTrue(result.contains("test_seq"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试DROP SEQUENCE IF EXISTS语句")
    public void testDropSequenceIfExists() {
        TableId sequenceId = new TableId("safe_seq");
        List<TableId> sequenceIds = Arrays.asList(sequenceId);
        DropSequence dropSequence = new DropSequence(sequenceIds, true);
        String result = generator.generate(dropSequence);
        
        assertTrue(result.contains("DROP SEQUENCE"));
        assertTrue(result.contains("IF EXISTS"));
        assertTrue(result.contains("safe_seq"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试DROP多个SEQUENCE语句")
    public void testDropMultipleSequences() {
        TableId seq1 = new TableId("seq1");
        TableId seq2 = new TableId("seq2");
        TableId seq3 = new TableId("seq3");
        List<TableId> sequenceIds = Arrays.asList(seq1, seq2, seq3);
        DropSequence dropSequence = new DropSequence(sequenceIds);
        String result = generator.generate(dropSequence);
        
        assertTrue(result.contains("DROP SEQUENCE"));
        assertTrue(result.contains("seq1"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("seq2"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("seq3"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试DROP SEQUENCE CASCADE语句")
    public void testDropSequenceCascade() {
        TableId sequenceId = new TableId("cascade_seq");
        List<TableId> sequenceIds = Arrays.asList(sequenceId);
        DropSequence dropSequence = new DropSequence(sequenceIds, false, true, false);
        String result = generator.generate(dropSequence);
        
        assertTrue(result.contains("DROP SEQUENCE"));
        assertTrue(result.contains("cascade_seq"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("CASCADE"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试DROP SEQUENCE RESTRICT语句")
    public void testDropSequenceRestrict() {
        TableId sequenceId = new TableId("restrict_seq");
        List<TableId> sequenceIds = Arrays.asList(sequenceId);
        DropSequence dropSequence = new DropSequence(sequenceIds, false, false, true);
        String result = generator.generate(dropSequence);
        
        assertTrue(result.contains("DROP SEQUENCE"));
        assertTrue(result.contains("restrict_seq"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("RESTRICT"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试带模式名的CREATE SEQUENCE语句")
    public void testCreateSequenceWithSchema() {
        TableId sequenceId = new TableId("hr", "employee_seq");
        CreateSequence createSequence = new CreateSequence(sequenceId, 1L, 1L);
        String result = generator.generate(createSequence);
        
        assertTrue(result.contains("CREATE SEQUENCE"));
        assertTrue(result.contains("hr.employee_seq"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("START WITH 1"));
        assertTrue(result.contains("INCREMENT BY 1"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试达梦数据库官方文档示例")
    public void testDamengOfficialExample() {
        // 根据达梦官方文档的序列示例
        TableId sequenceId = new TableId("order_seq");
        CreateSequence createSequence = new CreateSequence(sequenceId, 1000L, 1L, 1L, 999999999L, 20L, false, false, null);
        String result = generator.generate(createSequence);
        
        assertTrue(result.contains("CREATE SEQUENCE"));
        assertTrue(result.contains("order_seq"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.contains("START WITH 1000"));
        assertTrue(result.contains("INCREMENT BY 1"));
        assertTrue(result.contains("MINVALUE 1"));
        assertTrue(result.contains("MAXVALUE 999999999"));
        assertTrue(result.contains("CACHE 20"));
        assertTrue(result.endsWith(";"));
        
        // 根据达梦官方文档修正后，验证生成的SQL格式正确，符合达梦数据库标准
        assertEquals("CREATE SEQUENCE order_seq START WITH 1000 INCREMENT BY 1 MINVALUE 1 MAXVALUE 999999999 CACHE 20;", result);
    }
}
