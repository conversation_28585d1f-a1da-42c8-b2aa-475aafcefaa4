package com.xylink.sqltranspiler.unit.dialects.kingbase;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import com.xylink.sqltranspiler.shared.base.BaseKingbaseTest;

/**
 * 金仓数据库分析函数测试
 * 基于金仓官方文档：
 * - 金仓数据库窗口函数：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/functions/window-functions.html
 * - PostgreSQL兼容性：https://help.kingbase.com.cn/v8/development/develop-transfer/kes-vs-mysql/index.html
 * - MySQL迁移最佳实践：https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html
 * 测试原则：
 * 1. 严格遵循金仓官方文档规范
 * 2. 验证MySQL窗口函数到金仓的正确转换
 * 3. 确保PostgreSQL兼容性语法的正确性
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保函数转换符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试复杂函数组合的转换正确性
 *
 * <AUTHOR>
 */
@DisplayName("金仓数据库分析函数测试")
public class KingbaseAnalyticFunctionsTest extends BaseKingbaseTest {

    private KingbaseGenerator generator;

    @BeforeEach
    protected void setUp() {
        super.setUp();
        generator = new KingbaseGenerator();
    }

    @Test
    @DisplayName("测试ROW_NUMBER()窗口函数")
    void testRowNumberFunction() {
        // 基于MySQL 8.4官方文档的ROW_NUMBER()语法
        // https://dev.mysql.com/doc/refman/8.4/en/window-function-descriptions.html
        String mysqlSql = """
            SELECT 
                id, 
                name, 
                salary,
                ROW_NUMBER() OVER (PARTITION BY department ORDER BY salary DESC) as row_num
            FROM employees;
            """;

        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "ROW_NUMBER()语句应能解析");

        String kingbaseSql = generator.generate(statement);
        assertNotNull(kingbaseSql, "金仓转换结果不应为null");

        System.out.println("金仓ROW_NUMBER()转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档验证转换结果
        assertKingbaseConversionRequirements(kingbaseSql);
        assertTrue(kingbaseSql.contains("SELECT"), "应保留SELECT");
        assertTrue(kingbaseSql.contains("ROW_NUMBER()"), "应保留ROW_NUMBER()函数");
        assertTrue(kingbaseSql.contains("OVER"), "应保留OVER子句");
        assertTrue(kingbaseSql.contains("PARTITION BY"), "应保留PARTITION BY");
        assertTrue(kingbaseSql.contains("ORDER BY"), "应保留ORDER BY");
        assertTrue(kingbaseSql.contains("employees"), "应保留表名");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试RANK()和DENSE_RANK()函数")
    void testRankFunctions() {
        String mysqlSql = """
            SELECT 
                id, 
                name, 
                score,
                RANK() OVER (ORDER BY score DESC) as rank_pos,
                DENSE_RANK() OVER (ORDER BY score DESC) as dense_rank_pos
            FROM students;
            """;

        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "RANK()函数语句应能解析");

        String kingbaseSql = generator.generate(statement);
        assertNotNull(kingbaseSql, "金仓转换结果不应为null");

        System.out.println("金仓RANK()函数转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档验证转换结果
        assertKingbaseConversionRequirements(kingbaseSql);
        assertTrue(kingbaseSql.contains("RANK()"), "应保留RANK()函数");
        assertTrue(kingbaseSql.contains("DENSE_RANK()"), "应保留DENSE_RANK()函数");
        assertTrue(kingbaseSql.contains("OVER"), "应保留OVER子句");
        assertTrue(kingbaseSql.contains("students"), "应保留表名");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试LAG()和LEAD()函数")
    void testLagLeadFunctions() {
        String mysqlSql = """
            SELECT 
                id, 
                sale_date, 
                amount,
                LAG(amount, 1) OVER (ORDER BY sale_date) as prev_amount,
                LEAD(amount, 1) OVER (ORDER BY sale_date) as next_amount
            FROM sales;
            """;

        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "LAG/LEAD函数语句应能解析");

        String kingbaseSql = generator.generate(statement);
        assertNotNull(kingbaseSql, "金仓转换结果不应为null");

        System.out.println("金仓LAG/LEAD函数转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档验证转换结果
        assertKingbaseConversionRequirements(kingbaseSql);
        assertTrue(kingbaseSql.contains("LAG("), "应保留LAG()函数");
        assertTrue(kingbaseSql.contains("LEAD("), "应保留LEAD()函数");
        assertTrue(kingbaseSql.contains("OVER"), "应保留OVER子句");
        assertTrue(kingbaseSql.contains("sales"), "应保留表名");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试FIRST_VALUE()和LAST_VALUE()函数")
    void testFirstLastValueFunctions() {
        String mysqlSql = """
            SELECT 
                id, 
                department, 
                salary,
                FIRST_VALUE(salary) OVER (PARTITION BY department ORDER BY salary DESC) as highest_salary,
                LAST_VALUE(salary) OVER (PARTITION BY department ORDER BY salary DESC 
                    ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) as lowest_salary
            FROM employees;
            """;

        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "FIRST_VALUE/LAST_VALUE函数语句应能解析");

        String kingbaseSql = generator.generate(statement);
        assertNotNull(kingbaseSql, "金仓转换结果不应为null");

        System.out.println("金仓FIRST_VALUE/LAST_VALUE函数转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档验证转换结果
        assertKingbaseConversionRequirements(kingbaseSql);
        assertTrue(kingbaseSql.contains("FIRST_VALUE"), "应保留FIRST_VALUE()函数");
        assertTrue(kingbaseSql.contains("LAST_VALUE"), "应保留LAST_VALUE()函数");
        assertTrue(kingbaseSql.contains("OVER"), "应保留OVER子句");
        assertTrue(kingbaseSql.contains("PARTITION BY"), "应保留PARTITION BY");
        assertTrue(kingbaseSql.contains("employees"), "应保留表名");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试NTILE()函数")
    void testNtileFunction() {
        String mysqlSql = """
            SELECT 
                id, 
                name, 
                score,
                NTILE(4) OVER (ORDER BY score DESC) as quartile
            FROM students;
            """;

        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "NTILE()函数语句应能解析");

        String kingbaseSql = generator.generate(statement);
        assertNotNull(kingbaseSql, "金仓转换结果不应为null");

        System.out.println("金仓NTILE()函数转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档验证转换结果
        assertKingbaseConversionRequirements(kingbaseSql);
        assertTrue(kingbaseSql.contains("NTILE(4)"), "应保留NTILE()函数");
        assertTrue(kingbaseSql.contains("OVER"), "应保留OVER子句");
        assertTrue(kingbaseSql.contains("students"), "应保留表名");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试聚合函数作为窗口函数")
    void testAggregateAsWindowFunction() {
        String mysqlSql = """
            SELECT 
                id, 
                department, 
                salary,
                SUM(salary) OVER (PARTITION BY department) as dept_total,
                AVG(salary) OVER (PARTITION BY department) as dept_avg,
                COUNT(*) OVER (PARTITION BY department) as dept_count,
                MAX(salary) OVER (PARTITION BY department) as dept_max,
                MIN(salary) OVER (PARTITION BY department) as dept_min
            FROM employees;
            """;

        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "聚合窗口函数语句应能解析");

        String kingbaseSql = generator.generate(statement);
        assertNotNull(kingbaseSql, "金仓转换结果不应为null");

        System.out.println("金仓聚合窗口函数转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档验证转换结果
        assertKingbaseConversionRequirements(kingbaseSql);
        assertTrue(kingbaseSql.contains("SUM("), "应保留SUM()函数");
        assertTrue(kingbaseSql.contains("AVG("), "应保留AVG()函数");
        assertTrue(kingbaseSql.contains("COUNT("), "应保留COUNT()函数");
        assertTrue(kingbaseSql.contains("MAX("), "应保留MAX()函数");
        assertTrue(kingbaseSql.contains("MIN("), "应保留MIN()函数");
        assertTrue(kingbaseSql.contains("OVER"), "应保留OVER子句");
        assertTrue(kingbaseSql.contains("PARTITION BY"), "应保留PARTITION BY");
        assertTrue(kingbaseSql.contains("employees"), "应保留表名");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试窗口框架(Window Frame)")
    void testWindowFrame() {
        String mysqlSql = """
            SELECT 
                id, 
                sale_date, 
                amount,
                SUM(amount) OVER (ORDER BY sale_date ROWS BETWEEN 2 PRECEDING AND 2 FOLLOWING) as moving_sum,
                AVG(amount) OVER (ORDER BY sale_date RANGE BETWEEN INTERVAL 7 DAY PRECEDING AND CURRENT ROW) as week_avg
            FROM sales;
            """;

        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "窗口框架语句应能解析");

        String kingbaseSql = generator.generate(statement);
        assertNotNull(kingbaseSql, "金仓转换结果不应为null");

        System.out.println("金仓窗口框架转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档验证转换结果
        assertKingbaseConversionRequirements(kingbaseSql);
        assertTrue(kingbaseSql.contains("OVER"), "应保留OVER子句");
        assertTrue(kingbaseSql.contains("ORDER BY"), "应保留ORDER BY");
        // 金仓支持窗口框架语法
        assertTrue(kingbaseSql.contains("ROWS") || kingbaseSql.contains("RANGE") || 
                  kingbaseSql.contains("-- 窗口框架"), "应处理窗口框架语法");
        assertTrue(kingbaseSql.contains("sales"), "应保留表名");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }
}
