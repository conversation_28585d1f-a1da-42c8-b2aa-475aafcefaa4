package com.xylink.sqltranspiler.unit.dialects.kingbase;

import com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator;

import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;

/**
 * 金仓数据库索引转换测试
 * 基于金仓官方文档：https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保方言功能符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试方言特有功能
 *
 * <AUTHOR>
 */
public class KingbaseIndexTest {

    private KingbaseGenerator generator;

    @BeforeEach
    void setUp() {
        generator = new KingbaseGenerator();
    }

    private CreateTable parseCreateTable(String sql) {
        return (CreateTable) MySqlHelper.parseStatement(sql);
    }

    @Test
    @DisplayName("测试CREATE TABLE中的索引转换")
    void testCreateTableWithIndexes() {
        String mysql = """
            create table if not exists contact.t_en_department_extend
            (
                id          varchar(64)  not null,
                dep_id      bigint(20) not null,
                field_code  varchar(64)  not null,
                field_value varchar(256) not null,
                create_time bigint(20) default '0',
                update_time bigint(20) default '0',
                primary key (id),
                key           idx_dep_id_field_code (dep_id,field_code),
                key           idx_field_code_field_value (field_code,field_value)
            );
            """;

        CreateTable createTable = parseCreateTable(mysql);
        String result = generator.generate(createTable);

        System.out.println("MySQL原始SQL:");
        System.out.println(mysql);
        System.out.println("\n金仓转换结果:");
        System.out.println(result);

        // 验证表创建语句存在
        assertTrue(result.contains("CREATE TABLE"), "应该包含CREATE TABLE语句");
        assertTrue(result.contains("t_en_department_extend"), "应该包含表名");

        // 验证索引语句存在 - 这是关键测试点
        assertTrue(result.contains("CREATE INDEX"), "应该包含CREATE INDEX语句");

        // 验证索引名（达梦解析器会按照命名规范重新格式化索引名）
        assertTrue(result.contains("idx_t_en_department_extend_dep_id_field_code"),
                  "应该包含第一个索引名（格式化后）");
        assertTrue(result.contains("idx_t_en_department_extend_field_code_field_value"),
                  "应该包含第二个索引名（格式化后）");

        // 验证索引列
        assertTrue(result.contains("dep_id") && result.contains("field_code"),
                  "应该包含第一个索引的列");
        assertTrue(result.contains("field_value"),
                  "应该包含第二个索引的列");
    }

    @Test
    @DisplayName("测试唯一索引转换")
    void testCreateTableWithUniqueIndex() {
        String mysql = """
            create table test_unique_index
            (
                id int not null,
                name varchar(50),
                email varchar(100),
                primary key (id),
                unique key uk_email (email),
                key idx_name (name)
            );
            """;

        CreateTable createTable = parseCreateTable(mysql);
        String result = generator.generate(createTable);

        System.out.println("MySQL原始SQL:");
        System.out.println(mysql);
        System.out.println("\n金仓转换结果:");
        System.out.println(result);

        // 验证唯一索引
        assertTrue(result.contains("CREATE UNIQUE INDEX"), "应该包含CREATE UNIQUE INDEX语句");
        assertTrue(result.contains("udx_test_unique_index_uk_email"), "应该包含唯一索引名（格式化后）");

        // 验证普通索引
        assertTrue(result.contains("CREATE INDEX"), "应该包含CREATE INDEX语句");
        assertTrue(result.contains("idx_test_unique_index_name"), "应该包含普通索引名（格式化后）");
    }

    @Test
    @DisplayName("测试多列索引转换")
    void testCreateTableWithMultiColumnIndex() {
        String mysql = """
            create table test_multi_column
            (
                id int not null,
                user_id int not null,
                status varchar(20),
                created_at timestamp,
                primary key (id),
                key idx_user_status (user_id, status),
                key idx_status_created (status, created_at)
            );
            """;

        CreateTable createTable = parseCreateTable(mysql);
        String result = generator.generate(createTable);

        System.out.println("MySQL原始SQL:");
        System.out.println(mysql);
        System.out.println("\n金仓转换结果:");
        System.out.println(result);

        // 验证多列索引
        assertTrue(result.contains("CREATE INDEX"), "应该包含CREATE INDEX语句");
        assertTrue(result.contains("idx_test_multi_column_user_status"), "应该包含第一个多列索引名（格式化后）");
        assertTrue(result.contains("idx_test_multi_column_status_created"), "应该包含第二个多列索引名（格式化后）");

        // 验证索引列的组合
        assertTrue(result.contains("user_id") && result.contains("status"),
                  "应该包含第一个索引的多个列");
        assertTrue(result.contains("created_at"),
                  "应该包含第二个索引的时间列");
    }

    @Test
    @DisplayName("测试索引命名约定")
    void testIndexNamingConvention() {
        String mysql = """
            create table test_naming
            (
                id int not null,
                name varchar(50),
                primary key (id),
                key (name)
            );
            """;

        CreateTable createTable = parseCreateTable(mysql);
        String result = generator.generate(createTable);

        System.out.println("MySQL原始SQL:");
        System.out.println(mysql);
        System.out.println("\n金仓转换结果:");
        System.out.println(result);

        // 验证自动生成的索引名
        assertTrue(result.contains("CREATE INDEX"), "应该包含CREATE INDEX语句");
        // 金仓数据库应该自动生成合适的索引名
        assertTrue(result.contains("name"), "应该包含索引列名");
    }
}
