package com.xylink.sqltranspiler.unit.dialects.dameng;

import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.table.TruncateTable;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;

/**
 * 达梦数据库TRUNCATE TABLE功能测试
 * 基于达梦官方文档的测试驱动开发
 * 参考：https://eco.dameng.com/document/dm/zh-cn/pm/definition-statement.html
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保方言功能符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试方言特有功能
 *
 * <AUTHOR>
 */
public class DamengTruncateTableTest {

    private DamengGenerator generator;

    @BeforeEach
    public void setUp() {
        generator = new DamengGenerator();
    }

    private TruncateTable parseTruncateTable(String sql) {
        Statement statement = MySqlHelper.parseStatement(sql);
        if (statement instanceof TruncateTable) {
            return (TruncateTable) statement;
        }
        throw new IllegalArgumentException("解析结果不是TruncateTable类型: " + statement.getClass());
    }

    @Test
    @DisplayName("测试基本TRUNCATE TABLE语句")
    public void testBasicTruncateTable() {
        String sql = "TRUNCATE TABLE employees;";
        TruncateTable truncateTable = parseTruncateTable(sql);
        String result = generator.generate((Statement) truncateTable);
        
        assertTrue(result.contains("TRUNCATE TABLE"));
        assertTrue(result.contains("employees"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试带模式名的TRUNCATE TABLE语句")
    public void testTruncateTableWithSchema() {
        String sql = "TRUNCATE TABLE hr.employees;";
        TruncateTable truncateTable = parseTruncateTable(sql);
        String result = generator.generate((Statement) truncateTable);
        
        assertTrue(result.contains("TRUNCATE TABLE"));
        assertTrue(result.contains("hr.employees"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试带反引号的TRUNCATE TABLE语句")
    public void testTruncateTableWithBackticks() {
        String sql = "TRUNCATE TABLE `user_data`;";
        TruncateTable truncateTable = parseTruncateTable(sql);
        String result = generator.generate((Statement) truncateTable);
        
        assertTrue(result.contains("TRUNCATE TABLE"));
        assertTrue(result.contains("user_data"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试复杂表名的TRUNCATE TABLE语句")
    public void testTruncateTableComplexName() {
        String sql = "TRUNCATE TABLE `order-details`;";
        TruncateTable truncateTable = parseTruncateTable(sql);
        String result = generator.generate((Statement) truncateTable);
        
        assertTrue(result.contains("TRUNCATE TABLE"));
        assertTrue(result.contains("\"order-details\""));  // 包含特殊字符，需要双引号
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试大写TRUNCATE TABLE语句")
    public void testUppercaseTruncateTable() {
        String sql = "TRUNCATE TABLE EMPLOYEES;";
        TruncateTable truncateTable = parseTruncateTable(sql);
        String result = generator.generate((Statement) truncateTable);
        
        assertTrue(result.contains("TRUNCATE TABLE"));
        assertTrue(result.contains("EMPLOYEES"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试混合大小写TRUNCATE TABLE语句")
    public void testMixedCaseTruncateTable() {
        String sql = "truncate table Employee_Data;";
        TruncateTable truncateTable = parseTruncateTable(sql);
        String result = generator.generate((Statement) truncateTable);
        
        assertTrue(result.contains("TRUNCATE TABLE"));
        assertTrue(result.contains("Employee_Data"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试带数字的表名TRUNCATE TABLE语句")
    public void testTruncateTableWithNumbers() {
        String sql = "TRUNCATE TABLE table_2024;";
        TruncateTable truncateTable = parseTruncateTable(sql);
        String result = generator.generate((Statement) truncateTable);
        
        assertTrue(result.contains("TRUNCATE TABLE"));
        assertTrue(result.contains("table_2024"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试达梦数据库TRUNCATE TABLE官方文档示例")
    public void testDamengOfficialExample() {
        // 根据达梦官方文档，TRUNCATE TABLE用于快速删除表中的所有数据
        String sql = "TRUNCATE TABLE sales_data;";
        TruncateTable truncateTable = parseTruncateTable(sql);
        String result = generator.generate((Statement) truncateTable);
        
        assertTrue(result.contains("TRUNCATE TABLE"));
        assertTrue(result.contains("sales_data"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.endsWith(";"));
        
        // 根据达梦官方文档修正后，验证生成的SQL格式正确
        assertEquals("TRUNCATE TABLE sales_data;", result);
    }

    @Test
    @DisplayName("测试多个TRUNCATE TABLE语句的转换一致性")
    public void testMultipleTruncateTableConsistency() {
        String[] sqls = {
            "TRUNCATE TABLE table1;",
            "TRUNCATE TABLE table2;",
            "TRUNCATE TABLE table3;"
        };
        
        for (String sql : sqls) {
            TruncateTable truncateTable = parseTruncateTable(sql);
            String result = generator.generate((Statement) truncateTable);
            
            assertTrue(result.contains("TRUNCATE TABLE"));
            assertTrue(result.endsWith(";"));
            assertFalse(result.contains("`")); // 确保反引号被转换为双引号
        }
    }

    @Test
    @DisplayName("测试TRUNCATE TABLE语句的性能特性")
    public void testTruncateTablePerformance() {
        // 根据达梦官方文档，TRUNCATE TABLE比DELETE更快，因为它不记录日志
        String sql = "TRUNCATE TABLE large_table;";
        TruncateTable truncateTable = parseTruncateTable(sql);
        String result = generator.generate((Statement) truncateTable);
        
        // 验证生成的语句保持TRUNCATE的语义，而不是转换为DELETE
        assertTrue(result.contains("TRUNCATE TABLE"));
        assertFalse(result.contains("DELETE"));
        assertTrue(result.contains("large_table"));  // 根据达梦官方文档，普通标识符不需要双引号
        assertTrue(result.endsWith(";"));
    }
}
