package com.xylink.sqltranspiler.unit.dialects.shentong;

import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.shared.base.BaseShentongConversionTest;

/**
 * 神通数据库注释处理测试
 * 基于神通数据库官方文档 shentong.md 第2.3节注释规范
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保方言功能符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试方言特有功能
 *
 * <AUTHOR>
 */
public class ShentongCommentHandlingTest extends BaseShentongConversionTest {

    /**
     * 测试双连字符注释处理
     * 根据官方文档第118-122行: 注释以两个连续的负号开头
     */
    @Test
    @DisplayName("验证双连字符注释处理")
    public void testDoubleHyphenCommentHandling() throws Exception {
        String mysqlSql = """
            -- 这是一个单行注释
            CREATE TABLE users (
                id INT PRIMARY KEY, -- 用户ID
                username VARCHAR(50), -- 用户名
                email VARCHAR(100) -- 邮箱地址
            );
            
            INSERT INTO users VALUES (1, 'test', '<EMAIL>');
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证官方文档合规性
        assertOfficialDocumentCompliance(shentongSql);
        
        // 验证SQL语句被正确保留
        assertTrue(shentongSql.contains("CREATE TABLE"), "应包含CREATE TABLE语句");
        assertTrue(shentongSql.contains("INSERT INTO"), "应包含INSERT INTO语句");
    }

    /**
     * 测试C风格注释处理
     * 根据官方文档第124行: 类似C语言的注释
     */
    @Test
    @DisplayName("验证C风格注释处理")
    public void testCStyleCommentHandling() throws Exception {
        String mysqlSql = """
            /* 多行注释 */
            CREATE TABLE products (
                id INT PRIMARY KEY, /* 产品ID */
                name VARCHAR(100), /* 产品名称 */
                price DECIMAL(10,2) /* 产品价格 */
            );
            
            INSERT INTO products VALUES 
            (1, 'Product A', 99.99),
            (2, 'Product B', 149.99);
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证官方文档合规性
        assertOfficialDocumentCompliance(shentongSql);
        
        // 验证SQL语句被正确保留
        assertTrue(shentongSql.contains("CREATE TABLE"), "应包含CREATE TABLE语句");
        assertTrue(shentongSql.contains("INSERT INTO"), "应包含INSERT INTO语句");
        assertTrue(shentongSql.contains("products"), "应包含表名");
        
        // 验证数据完整性
        assertTrue(shentongSql.contains("Product A"), "应保留产品数据");
        assertTrue(shentongSql.contains("99.99"), "应保留价格数据");
    }

    /**
     * 测试注释与字符串字面量的区分
     */
    @Test
    @DisplayName("验证注释与字符串字面量区分")
    public void testCommentVsStringLiteralDistinction() throws Exception {
        String mysqlSql = """
            CREATE TABLE comment_string_test (
                id INT PRIMARY KEY,
                description VARCHAR(200)
            );
            
            INSERT INTO comment_string_test VALUES 
            (1, 'This string contains -- double hyphens'),
            (2, 'This string contains /* C-style symbols */'),
            (3, 'Mixed comment symbols in string');
            
            SELECT * FROM comment_string_test WHERE description LIKE '%comment%';
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证官方文档合规性
        assertOfficialDocumentCompliance(shentongSql);
        
        // 验证SQL语句结构
        assertTrue(shentongSql.contains("CREATE TABLE"), "应包含CREATE TABLE语句");
        assertTrue(shentongSql.contains("INSERT INTO"), "应包含INSERT INTO语句");
        assertTrue(shentongSql.contains("SELECT"), "应包含SELECT语句");
        
        // 验证字符串数据被正确保留
        assertTrue(shentongSql.contains("double hyphens") ||
                   shentongSql.contains("This string contains"), 
                   "应包含字符串数据");
        assertTrue(shentongSql.contains("C-style symbols") ||
                   shentongSql.contains("comment symbols"), 
                   "应包含C风格注释符号的字符串");
        
        // 验证LIKE查询
        assertTrue(shentongSql.contains("LIKE"), "应包含LIKE查询");
    }
}
