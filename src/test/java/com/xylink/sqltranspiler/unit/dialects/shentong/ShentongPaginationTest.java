package com.xylink.sqltranspiler.unit.dialects.shentong;

import com.xylink.sqltranspiler.core.dialects.shentong.ShentongGenerator;

import com.xylink.sqltranspiler.core.ast.dml.QueryStmt;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 神通数据库分页查询支持测试
 * 基于神通官方文档第2.6节ROWNUM伪列的测试驱动开发
 * 参考神通数据库官方文档：ROWNUM伪列实现分页显示
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保方言功能符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试方言特有功能
 *
 * <AUTHOR>
 */
public class ShentongPaginationTest {

    private ShentongGenerator generator;

    @BeforeEach
    public void setUp() {
        generator = new ShentongGenerator();
    }

    @Test
    @DisplayName("测试LIMIT转换为ROWNUM - 神通官方文档标准")
    public void testLimitToRownum() {
        String sql = "SELECT * FROM test_table ORDER BY id LIMIT 5";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(sql);
        String result = generator.generate(queryStmt);

        assertTrue(result.contains("SELECT * FROM"));
        assertTrue(result.contains("SELECT ROWNUM AS ROW_NUM, A.* FROM"));
        assertTrue(result.contains("test_table"));
        assertTrue(result.contains("ORDER BY id")); // 没有反引号的字段名不会被加引号
        assertTrue(result.contains("WHERE B.ROW_NUM <= 5"));
        assertFalse(result.contains("LIMIT")); // 确保LIMIT被转换
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试LIMIT OFFSET转换为ROWNUM - 神通官方文档标准")
    public void testLimitOffsetToRownum() {
        String sql = "SELECT * FROM test_table ORDER BY id LIMIT 3 OFFSET 2";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(sql);
        String result = generator.generate(queryStmt);

        assertTrue(result.contains("SELECT * FROM"));
        assertTrue(result.contains("SELECT ROWNUM AS ROW_NUM, A.* FROM"));
        assertTrue(result.contains("test_table"));
        assertTrue(result.contains("ORDER BY id")); // 没有反引号的字段名不会被加引号
        assertTrue(result.contains("WHERE B.ROW_NUM BETWEEN 3 AND 5")); // OFFSET 2, LIMIT 3 -> BETWEEN 3 AND 5
        assertFalse(result.contains("LIMIT")); // 确保LIMIT被转换
        assertFalse(result.contains("OFFSET")); // 确保OFFSET被转换
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试复杂查询的分页转换 - 神通官方文档标准")
    public void testComplexQueryPagination() {
        String sql = "SELECT `id`, `name`, `email` FROM `users` WHERE `status` = 'active' ORDER BY `created_date` DESC LIMIT 10 OFFSET 20";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(sql);
        String result = generator.generate(queryStmt);
        
        assertTrue(result.contains("SELECT * FROM"));
        assertTrue(result.contains("SELECT ROWNUM AS ROW_NUM, A.* FROM"));
        assertTrue(result.contains("SELECT \"id\", \"name\", \"email\""));
        assertTrue(result.contains("FROM \"users\""));
        assertTrue(result.contains("WHERE \"status\" = 'active'"));
        assertTrue(result.contains("ORDER BY \"created_date\" DESC"));
        assertTrue(result.contains("WHERE B.ROW_NUM BETWEEN 21 AND 30")); // OFFSET 20, LIMIT 10 -> BETWEEN 21 AND 30
        assertFalse(result.contains("LIMIT")); // 确保LIMIT被转换
        assertFalse(result.contains("OFFSET")); // 确保OFFSET被转换
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试JOIN查询的分页转换 - 神通官方文档标准")
    public void testJoinQueryPagination() {
        String sql = "SELECT u.`name`, p.`title` FROM `users` u JOIN `posts` p ON u.`id` = p.`user_id` ORDER BY p.`created_date` LIMIT 5 OFFSET 10";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(sql);
        String result = generator.generate(queryStmt);
        
        assertTrue(result.contains("SELECT * FROM"));
        assertTrue(result.contains("SELECT ROWNUM AS ROW_NUM, A.* FROM"));
        assertTrue(result.contains("SELECT u.\"name\", p.\"title\""));
        assertTrue(result.contains("FROM \"users\" u"));
        assertTrue(result.contains("JOIN \"posts\" p"));
        assertTrue(result.contains("ON u.\"id\" = p.\"user_id\""));
        assertTrue(result.contains("ORDER BY p.\"created_date\""));
        assertTrue(result.contains("WHERE B.ROW_NUM BETWEEN 11 AND 15")); // OFFSET 10, LIMIT 5 -> BETWEEN 11 AND 15
        assertFalse(result.contains("LIMIT")); // 确保LIMIT被转换
        assertFalse(result.contains("OFFSET")); // 确保OFFSET被转换
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试聚合查询的分页转换 - 神通官方文档标准")
    public void testAggregateQueryPagination() {
        String sql = "SELECT `department`, COUNT(*) as `count` FROM `employees` GROUP BY `department` HAVING COUNT(*) > 5 ORDER BY `count` DESC LIMIT 3";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(sql);
        String result = generator.generate(queryStmt);
        
        assertTrue(result.contains("SELECT * FROM"));
        assertTrue(result.contains("SELECT ROWNUM AS ROW_NUM, A.* FROM"));
        assertTrue(result.contains("SELECT \"department\", COUNT(*) as \"count\""));
        assertTrue(result.contains("FROM \"employees\""));
        assertTrue(result.contains("GROUP BY \"department\""));
        assertTrue(result.contains("HAVING COUNT(*) > 5"));
        assertTrue(result.contains("ORDER BY \"count\" DESC"));
        assertTrue(result.contains("WHERE B.ROW_NUM <= 3"));
        assertFalse(result.contains("LIMIT")); // 确保LIMIT被转换
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试子查询的分页转换 - 神通官方文档标准")
    public void testSubqueryPagination() {
        String sql = "SELECT * FROM (SELECT `id`, `name` FROM `users` WHERE `age` > 18) AS `adults` ORDER BY `name` LIMIT 2 OFFSET 1";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(sql);
        String result = generator.generate(queryStmt);
        
        assertTrue(result.contains("SELECT * FROM"));
        assertTrue(result.contains("SELECT ROWNUM AS ROW_NUM, A.* FROM"));
        assertTrue(result.contains("SELECT * FROM (SELECT \"id\", \"name\""));
        assertTrue(result.contains("FROM \"users\""));
        assertTrue(result.contains("WHERE \"age\" > 18"));
        assertTrue(result.contains("AS \"adults\""));
        assertTrue(result.contains("ORDER BY \"name\""));
        assertTrue(result.contains("WHERE B.ROW_NUM BETWEEN 2 AND 3")); // OFFSET 1, LIMIT 2 -> BETWEEN 2 AND 3
        assertFalse(result.contains("LIMIT")); // 确保LIMIT被转换
        assertFalse(result.contains("OFFSET")); // 确保OFFSET被转换
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试神通数据库官方文档示例")
    public void testShentongOfficialExample() {
        // 根据神通官方文档第2.6节的分页显示示例
        String sql = "SELECT * FROM tab3 ORDER BY a DESC LIMIT 3 OFFSET 0";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(sql);
        String result = generator.generate(queryStmt);

        assertTrue(result.contains("SELECT * FROM"));
        assertTrue(result.contains("SELECT ROWNUM AS ROW_NUM, A.* FROM"));
        assertTrue(result.contains("SELECT * FROM tab3"));
        assertTrue(result.contains("ORDER BY a DESC")); // 没有反引号的字段名不会被加引号
        assertTrue(result.contains("WHERE B.ROW_NUM BETWEEN 1 AND 3")); // OFFSET 0, LIMIT 3 -> BETWEEN 1 AND 3
        assertFalse(result.contains("LIMIT")); // 确保LIMIT被转换
        assertFalse(result.contains("OFFSET")); // 确保OFFSET被转换
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试大偏移量分页转换")
    public void testLargeOffsetPagination() {
        String sql = "SELECT * FROM test_table ORDER BY id LIMIT 10 OFFSET 100";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(sql);
        String result = generator.generate(queryStmt);

        assertTrue(result.contains("SELECT * FROM"));
        assertTrue(result.contains("SELECT ROWNUM AS ROW_NUM, A.* FROM"));
        assertTrue(result.contains("test_table"));
        assertTrue(result.contains("ORDER BY id")); // 没有反引号的字段名不会被加引号
        assertTrue(result.contains("WHERE B.ROW_NUM BETWEEN 101 AND 110")); // OFFSET 100, LIMIT 10 -> BETWEEN 101 AND 110
        assertFalse(result.contains("LIMIT")); // 确保LIMIT被转换
        assertFalse(result.contains("OFFSET")); // 确保OFFSET被转换
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试单行查询转换")
    public void testSingleRowQuery() {
        String sql = "SELECT * FROM test_table ORDER BY id LIMIT 1";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(sql);
        String result = generator.generate(queryStmt);

        assertTrue(result.contains("SELECT * FROM"));
        assertTrue(result.contains("SELECT ROWNUM AS ROW_NUM, A.* FROM"));
        assertTrue(result.contains("test_table"));
        assertTrue(result.contains("ORDER BY id")); // 没有反引号的字段名不会被加引号
        assertTrue(result.contains("WHERE B.ROW_NUM <= 1"));
        assertFalse(result.contains("LIMIT")); // 确保LIMIT被转换
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试无ORDER BY的分页转换")
    public void testPaginationWithoutOrderBy() {
        String sql = "SELECT * FROM test_table LIMIT 5 OFFSET 2";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(sql);
        String result = generator.generate(queryStmt);
        
        assertTrue(result.contains("SELECT * FROM"));
        assertTrue(result.contains("SELECT ROWNUM AS ROW_NUM, A.* FROM"));
        assertTrue(result.contains("SELECT * FROM test_table"));
        assertTrue(result.contains("WHERE B.ROW_NUM BETWEEN 3 AND 7")); // OFFSET 2, LIMIT 5 -> BETWEEN 3 AND 7
        assertFalse(result.contains("LIMIT")); // 确保LIMIT被转换
        assertFalse(result.contains("OFFSET")); // 确保OFFSET被转换
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试ROWNUM分页查询结构")
    public void testRownumQueryStructure() {
        String sql = "SELECT `name`, `age` FROM `users` ORDER BY `age` LIMIT 5 OFFSET 10";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(sql);
        String result = generator.generate(queryStmt);
        
        // 验证三层嵌套结构
        assertTrue(result.contains("SELECT * FROM (")); // 外层查询
        assertTrue(result.contains("SELECT ROWNUM AS ROW_NUM, A.* FROM (")); // 中层查询
        assertTrue(result.contains("SELECT \"name\", \"age\"")); // 内层查询
        assertTrue(result.contains(") A")); // 中层查询别名
        assertTrue(result.contains(") B WHERE B.ROW_NUM BETWEEN 11 AND 15")); // 外层查询条件
        
        // 验证ROWNUM伪列的正确使用
        assertTrue(result.contains("ROWNUM AS ROW_NUM"));
        assertTrue(result.contains("B.ROW_NUM BETWEEN"));
        
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试边界情况处理")
    public void testEdgeCases() {
        // 测试OFFSET为0的情况
        String sql1 = "SELECT * FROM test_table LIMIT 3 OFFSET 0";
        QueryStmt queryStmt1 = new QueryStmt();
        queryStmt1.setSql(sql1);
        String result1 = generator.generate(queryStmt1);
        
        assertTrue(result1.contains("WHERE B.ROW_NUM BETWEEN 1 AND 3"));
        
        // 测试大数值的情况
        String sql2 = "SELECT * FROM test_table LIMIT 1000 OFFSET 5000";
        QueryStmt queryStmt2 = new QueryStmt();
        queryStmt2.setSql(sql2);
        String result2 = generator.generate(queryStmt2);
        
        assertTrue(result2.contains("WHERE B.ROW_NUM BETWEEN 5001 AND 6000"));
        
        assertTrue(result1.endsWith(";"));
        assertTrue(result2.endsWith(";"));
    }

    @Test
    @DisplayName("测试没有LIMIT的查询不被转换")
    public void testQueryWithoutLimitNotConverted() {
        String sql = "SELECT * FROM test_table ORDER BY id";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(sql);
        String result = generator.generate(queryStmt);

        // 没有LIMIT的查询应该不被转换为ROWNUM结构
        assertFalse(result.contains("ROWNUM"));
        assertFalse(result.contains("ROW_NUM"));
        // 根据实际输出，表名保持原样
        assertTrue(result.contains("FROM test_table"));
        assertTrue(result.contains("ORDER BY id")); // 没有反引号的字段名不会被加引号
        assertTrue(result.endsWith(";"));
    }
}
