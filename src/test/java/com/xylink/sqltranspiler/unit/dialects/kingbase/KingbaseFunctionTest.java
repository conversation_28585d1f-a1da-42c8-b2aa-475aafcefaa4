package com.xylink.sqltranspiler.unit.dialects.kingbase;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import com.xylink.sqltranspiler.shared.base.BaseKingbaseTest;

/**
 * 金仓数据库函数测试
 * 基于金仓官方文档：
 * - 金仓数据库函数：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/functions/
 * - PostgreSQL兼容性：https://help.kingbase.com.cn/v8/development/develop-transfer/kes-vs-mysql/index.html
 * - MySQL迁移最佳实践：https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html
 * 测试原则：
 * 1. 严格遵循金仓官方文档规范
 * 2. 验证MySQL函数到金仓的正确转换
 * 3. 确保PostgreSQL兼容性语法的正确性
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保函数转换符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试复杂函数组合的转换正确性
 *
 * <AUTHOR>
 */
@DisplayName("金仓数据库函数测试")
public class KingbaseFunctionTest extends BaseKingbaseTest {

    private KingbaseGenerator generator;

    @BeforeEach
    protected void setUp() {
        super.setUp();
        generator = new KingbaseGenerator();
    }

    @Test
    @DisplayName("测试字符串函数转换")
    void testStringFunctions() {
        // 基于MySQL 8.4官方文档的字符串函数
        // https://dev.mysql.com/doc/refman/8.4/en/string-functions.html
        String mysqlSql = """
            SELECT 
                CONCAT(first_name, ' ', last_name) as full_name,
                LENGTH(email) as email_length,
                SUBSTRING(phone, 1, 3) as area_code,
                UPPER(first_name) as upper_name,
                LOWER(last_name) as lower_name,
                TRIM(BOTH ' ' FROM description) as clean_desc,
                REPLACE(email, '@old.com', '@new.com') as new_email
            FROM users;
            """;

        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "字符串函数语句应能解析");

        String kingbaseSql = generator.generate(statement);
        assertNotNull(kingbaseSql, "金仓转换结果不应为null");

        System.out.println("金仓字符串函数转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档验证转换结果
        assertKingbaseConversionRequirements(kingbaseSql);
        assertTrue(kingbaseSql.contains("SELECT"), "应保留SELECT");
        // 金仓支持PostgreSQL兼容的字符串函数
        assertTrue(kingbaseSql.contains("CONCAT") || kingbaseSql.contains("||"), 
                  "应保留CONCAT或转换为||操作符");
        assertTrue(kingbaseSql.contains("LENGTH") || kingbaseSql.contains("CHAR_LENGTH"), 
                  "应保留LENGTH或转换为CHAR_LENGTH");
        assertTrue(kingbaseSql.contains("SUBSTRING") || kingbaseSql.contains("SUBSTR"), 
                  "应保留SUBSTRING或转换为SUBSTR");
        assertTrue(kingbaseSql.contains("UPPER"), "应保留UPPER函数");
        assertTrue(kingbaseSql.contains("LOWER"), "应保留LOWER函数");
        assertTrue(kingbaseSql.contains("TRIM"), "应保留TRIM函数");
        assertTrue(kingbaseSql.contains("REPLACE"), "应保留REPLACE函数");
        // 根据金仓官方文档，普通标识符不需要双引号
        assertTrue(kingbaseSql.contains("users"), "应包含表名users");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试数值函数转换")
    void testNumericFunctions() {
        String mysqlSql = """
            SELECT 
                ABS(balance) as abs_balance,
                ROUND(price, 2) as rounded_price,
                CEIL(rating) as ceiling_rating,
                FLOOR(discount) as floor_discount,
                MOD(id, 10) as id_mod,
                POWER(base_value, 2) as squared_value,
                SQRT(area) as sqrt_area,
                RAND() as random_value
            FROM products;
            """;

        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "数值函数语句应能解析");

        String kingbaseSql = generator.generate(statement);
        assertNotNull(kingbaseSql, "金仓转换结果不应为null");

        System.out.println("金仓数值函数转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档验证转换结果
        assertKingbaseConversionRequirements(kingbaseSql);
        assertTrue(kingbaseSql.contains("ABS"), "应保留ABS函数");
        assertTrue(kingbaseSql.contains("ROUND"), "应保留ROUND函数");
        assertTrue(kingbaseSql.contains("CEIL") || kingbaseSql.contains("CEILING"), 
                  "应保留CEIL或转换为CEILING");
        assertTrue(kingbaseSql.contains("FLOOR"), "应保留FLOOR函数");
        assertTrue(kingbaseSql.contains("MOD") || kingbaseSql.contains("%"), 
                  "应保留MOD或转换为%操作符");
        assertTrue(kingbaseSql.contains("POWER") || kingbaseSql.contains("POW"), 
                  "应保留POWER或转换为POW");
        assertTrue(kingbaseSql.contains("SQRT"), "应保留SQRT函数");
        assertTrue(kingbaseSql.contains("RANDOM") || kingbaseSql.contains("RAND"), 
                  "应保留RAND或转换为RANDOM");
        // 根据金仓官方文档，普通标识符不需要双引号
        assertTrue(kingbaseSql.contains("products"), "应包含表名products");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试日期时间函数转换")
    void testDateTimeFunctions() {
        String mysqlSql = """
            SELECT 
                NOW() as current_time,
                CURDATE() as current_date,
                YEAR(created_at) as created_year,
                MONTH(created_at) as created_month,
                DAY(created_at) as created_day,
                DATE_FORMAT(created_at, '%Y-%m-%d') as formatted_date,
                DATEDIFF(end_date, start_date) as date_diff,
                DATE_ADD(created_at, INTERVAL 30 DAY) as future_date
            FROM events;
            """;

        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "日期时间函数语句应能解析");

        String kingbaseSql = generator.generate(statement);
        assertNotNull(kingbaseSql, "金仓转换结果不应为null");

        System.out.println("金仓日期时间函数转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档验证转换结果
        assertKingbaseConversionRequirements(kingbaseSql);
        assertTrue(kingbaseSql.contains("NOW") || kingbaseSql.contains("CURRENT_TIMESTAMP"), 
                  "应保留NOW或转换为CURRENT_TIMESTAMP");
        assertTrue(kingbaseSql.contains("CURRENT_DATE") || kingbaseSql.contains("CURDATE"), 
                  "应保留CURDATE或转换为CURRENT_DATE");
        assertTrue(kingbaseSql.contains("EXTRACT") || kingbaseSql.contains("YEAR"), 
                  "应转换为EXTRACT或保留YEAR函数");
        // 金仓可能需要转换DATE_FORMAT为TO_CHAR
        assertTrue(kingbaseSql.contains("TO_CHAR") || kingbaseSql.contains("DATE_FORMAT") || 
                  kingbaseSql.contains("-- DATE_FORMAT"), "应处理日期格式化");
        // 根据金仓官方文档，普通标识符不需要强制使用双引号
        assertTrue(kingbaseSql.contains("events"), "应包含表名events");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试条件函数转换")
    void testConditionalFunctions() {
        String mysqlSql = """
            SELECT 
                IFNULL(description, 'No description') as safe_desc,
                COALESCE(phone, mobile, 'No contact') as contact_info,
                CASE 
                    WHEN status = 1 THEN 'Active'
                    WHEN status = 0 THEN 'Inactive'
                    ELSE 'Unknown'
                END as status_text,
                IF(price > 100, 'Expensive', 'Affordable') as price_category,
                NULLIF(discount, 0) as valid_discount
            FROM products;
            """;

        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "条件函数语句应能解析");

        String kingbaseSql = generator.generate(statement);
        assertNotNull(kingbaseSql, "金仓转换结果不应为null");

        System.out.println("金仓条件函数转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档验证转换结果
        assertKingbaseConversionRequirements(kingbaseSql);
        // IFNULL在金仓中应转换为COALESCE
        assertTrue(kingbaseSql.contains("COALESCE") || kingbaseSql.contains("IFNULL"), 
                  "应保留COALESCE或转换IFNULL为COALESCE");
        assertTrue(kingbaseSql.contains("CASE"), "应保留CASE表达式");
        assertTrue(kingbaseSql.contains("WHEN"), "应保留WHEN子句");
        assertTrue(kingbaseSql.contains("THEN"), "应保留THEN子句");
        assertTrue(kingbaseSql.contains("ELSE"), "应保留ELSE子句");
        assertTrue(kingbaseSql.contains("END"), "应保留END关键字");
        assertTrue(kingbaseSql.contains("NULLIF"), "应保留NULLIF函数");
        // IF函数在金仓中应转换为CASE表达式
        assertTrue(kingbaseSql.contains("CASE") || kingbaseSql.contains("IF"), 
                  "应转换IF为CASE或保留IF");
        // 根据金仓官方文档，普通标识符不需要强制使用双引号
        assertTrue(kingbaseSql.contains("products"), "应包含表名products");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试聚合函数转换")
    void testAggregateFunctions() {
        String mysqlSql = """
            SELECT 
                COUNT(*) as total_count,
                COUNT(DISTINCT category_id) as unique_categories,
                SUM(price) as total_price,
                AVG(rating) as avg_rating,
                MAX(created_at) as latest_date,
                MIN(price) as min_price,
                GROUP_CONCAT(name SEPARATOR ', ') as name_list,
                STDDEV(price) as price_stddev,
                VARIANCE(rating) as rating_variance
            FROM products
            GROUP BY category_id;
            """;

        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "聚合函数语句应能解析");

        String kingbaseSql = generator.generate(statement);
        assertNotNull(kingbaseSql, "金仓转换结果不应为null");

        System.out.println("金仓聚合函数转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档验证转换结果
        assertKingbaseConversionRequirements(kingbaseSql);
        assertTrue(kingbaseSql.contains("COUNT"), "应保留COUNT函数");
        assertTrue(kingbaseSql.contains("DISTINCT"), "应保留DISTINCT");
        assertTrue(kingbaseSql.contains("SUM"), "应保留SUM函数");
        assertTrue(kingbaseSql.contains("AVG"), "应保留AVG函数");
        assertTrue(kingbaseSql.contains("MAX"), "应保留MAX函数");
        assertTrue(kingbaseSql.contains("MIN"), "应保留MIN函数");
        // GROUP_CONCAT在金仓中应转换为STRING_AGG
        assertTrue(kingbaseSql.contains("STRING_AGG") || kingbaseSql.contains("GROUP_CONCAT") || 
                  kingbaseSql.contains("-- GROUP_CONCAT"), "应转换GROUP_CONCAT为STRING_AGG");
        assertTrue(kingbaseSql.contains("STDDEV") || kingbaseSql.contains("STDDEV_POP"), 
                  "应保留STDDEV或转换为STDDEV_POP");
        assertTrue(kingbaseSql.contains("VARIANCE") || kingbaseSql.contains("VAR_POP"), 
                  "应保留VARIANCE或转换为VAR_POP");
        assertTrue(kingbaseSql.contains("GROUP BY"), "应保留GROUP BY");
        // 根据金仓官方文档，普通标识符不需要双引号，只有保留字、包含特殊字符或大写字母的标识符才需要双引号
        assertTrue(kingbaseSql.contains("products"), "应包含表名products");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试JSON函数转换")
    void testJsonFunctions() {
        String mysqlSql = """
            SELECT 
                JSON_EXTRACT(metadata, '$.name') as json_name,
                JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.description')) as json_desc,
                JSON_VALID(metadata) as is_valid_json,
                JSON_TYPE(metadata) as json_type,
                JSON_LENGTH(metadata) as json_length
            FROM products
            WHERE JSON_EXTRACT(metadata, '$.active') = true;
            """;

        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "JSON函数语句应能解析");

        String kingbaseSql = generator.generate(statement);
        assertNotNull(kingbaseSql, "金仓转换结果不应为null");

        System.out.println("金仓JSON函数转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档验证转换结果
        assertKingbaseConversionRequirements(kingbaseSql);
        assertTrue(kingbaseSql.contains("SELECT"), "应保留SELECT");
        // 金仓支持PostgreSQL风格的JSON操作符
        assertTrue(kingbaseSql.contains("->") || kingbaseSql.contains("->>") || 
                  kingbaseSql.contains("JSON_EXTRACT") || kingbaseSql.contains("-- JSON"), 
                  "应转换为金仓JSON操作符或保留JSON函数");
        assertTrue(kingbaseSql.contains("WHERE"), "应保留WHERE子句");
        // 根据金仓官方文档，普通标识符不需要强制使用双引号
        assertTrue(kingbaseSql.contains("products"), "应包含表名products");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }
}
