package com.xylink.sqltranspiler.unit.dialects.dameng;

import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 达梦数据库分析函数测试
 * 根据达梦官方文档，测试MySQL分析函数到达梦的转换
 * 参考：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保函数转换符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试复杂函数组合的转换正确性
 *
 * <AUTHOR>
 */
@DisplayName("达梦数据库分析函数测试")
public class DamengAnalyticFunctionsTest {

    private DamengGenerator generator;

    @BeforeEach
    void setUp() {
        generator = new DamengGenerator();
    }

    private CreateTable parseCreateTable(String sql) {
        return (CreateTable) MySqlHelper.parseStatement(sql);
    }

    @Test
    @DisplayName("测试窗口函数相关表结构")
    void testWindowFunctionTableStructure() {
        String mysqlSql = "CREATE TABLE sales_data (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "employee_id INT NOT NULL, " +
                "department_id INT NOT NULL, " +
                "sale_amount DECIMAL(10,2) NOT NULL, " +
                "sale_date DATE NOT NULL, " +
                "quarter INT NOT NULL, " +
                "year INT NOT NULL, " +
                "region VARCHAR(50), " +
                "product_category VARCHAR(100), " +
                "INDEX idx_employee (employee_id), " +
                "INDEX idx_department (department_id), " +
                "INDEX idx_date (sale_date)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("窗口函数相关表结构:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证分析函数相关表结构
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("sales_data"), "应保留表名");
        assertTrue(damengSql.contains("employee_id"), "应保留员工ID列");
        assertTrue(damengSql.contains("department_id"), "应保留部门ID列");
        assertTrue(damengSql.contains("sale_amount"), "应保留销售金额列");
        assertTrue(damengSql.contains("sale_date"), "应保留销售日期列");
        assertTrue(damengSql.contains("DECIMAL(10,2)"), "应保留金额数据类型");
        assertTrue(damengSql.contains("DATE"), "应保留日期数据类型");
    }

    @Test
    @DisplayName("测试排名函数相关表结构")
    void testRankingFunctionTableStructure() {
        String mysqlSql = "CREATE TABLE employee_performance (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "employee_id INT NOT NULL, " +
                "performance_score DECIMAL(5,2) NOT NULL, " +
                "evaluation_date DATE NOT NULL, " +
                "department VARCHAR(50) NOT NULL, " +
                "position VARCHAR(100), " +
                "salary DECIMAL(10,2), " +
                "bonus DECIMAL(8,2) DEFAULT 0, " +
                "INDEX idx_employee_date (employee_id, evaluation_date), " +
                "INDEX idx_department (department), " +
                "INDEX idx_score (performance_score)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("排名函数相关表结构:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证排名函数相关表结构
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("employee_performance"), "应保留表名");
        assertTrue(damengSql.contains("performance_score"), "应保留绩效分数列");
        assertTrue(damengSql.contains("evaluation_date"), "应保留评估日期列");
        assertTrue(damengSql.contains("department"), "应保留部门列");
        assertTrue(damengSql.contains("salary"), "应保留薪资列");
        assertTrue(damengSql.contains("DECIMAL(5,2)"), "应保留绩效分数数据类型");
        assertTrue(damengSql.contains("DECIMAL(10,2)"), "应保留薪资数据类型");
    }

    @Test
    @DisplayName("测试聚合函数相关表结构")
    void testAggregateFunctionTableStructure() {
        String mysqlSql = "CREATE TABLE order_analytics (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "order_id INT NOT NULL, " +
                "customer_id INT NOT NULL, " +
                "order_amount DECIMAL(12,2) NOT NULL, " +
                "order_date DATETIME NOT NULL, " +
                "product_count INT NOT NULL, " +
                "discount_amount DECIMAL(8,2) DEFAULT 0, " +
                "shipping_cost DECIMAL(6,2) DEFAULT 0, " +
                "tax_amount DECIMAL(8,2) DEFAULT 0, " +
                "total_amount DECIMAL(12,2) NOT NULL, " +
                "INDEX idx_customer_date (customer_id, order_date), " +
                "INDEX idx_order_date (order_date), " +
                "INDEX idx_amount (order_amount)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("聚合函数相关表结构:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证聚合函数相关表结构
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("order_analytics"), "应保留表名");
        assertTrue(damengSql.contains("order_amount"), "应保留订单金额列");
        assertTrue(damengSql.contains("customer_id"), "应保留客户ID列");
        assertTrue(damengSql.contains("product_count"), "应保留产品数量列");
        assertTrue(damengSql.contains("total_amount"), "应保留总金额列");
        assertTrue(damengSql.contains("DECIMAL(12,2)"), "应保留金额数据类型");
        // 根据达梦官方文档，DATETIME类型转换为TIMESTAMP类型
        assertTrue(damengSql.contains("TIMESTAMP"), "应保留日期时间数据类型");
    }

    @Test
    @DisplayName("测试移动平均相关表结构")
    void testMovingAverageTableStructure() {
        String mysqlSql = "CREATE TABLE stock_prices (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "symbol VARCHAR(10) NOT NULL, " +
                "price_date DATE NOT NULL, " +
                "open_price DECIMAL(10,4) NOT NULL, " +
                "high_price DECIMAL(10,4) NOT NULL, " +
                "low_price DECIMAL(10,4) NOT NULL, " +
                "close_price DECIMAL(10,4) NOT NULL, " +
                "volume BIGINT NOT NULL, " +
                "adjusted_close DECIMAL(10,4), " +
                "UNIQUE KEY uk_symbol_date (symbol, price_date), " +
                "INDEX idx_symbol (symbol), " +
                "INDEX idx_date (price_date)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("移动平均相关表结构:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证移动平均相关表结构
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("stock_prices"), "应保留表名");
        assertTrue(damengSql.contains("symbol"), "应保留股票代码列");
        assertTrue(damengSql.contains("price_date"), "应保留价格日期列");
        assertTrue(damengSql.contains("open_price"), "应保留开盘价列");
        assertTrue(damengSql.contains("close_price"), "应保留收盘价列");
        assertTrue(damengSql.contains("volume"), "应保留成交量列");
        assertTrue(damengSql.contains("DECIMAL(10,4)"), "应保留价格数据类型");
        assertTrue(damengSql.contains("BIGINT"), "应保留成交量数据类型");
    }

    @Test
    @DisplayName("测试累计函数相关表结构")
    void testCumulativeFunctionTableStructure() {
        String mysqlSql = "CREATE TABLE financial_transactions (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "account_id INT NOT NULL, " +
                "transaction_date DATETIME NOT NULL, " +
                "transaction_type ENUM('debit', 'credit') NOT NULL, " +
                "amount DECIMAL(15,2) NOT NULL, " +
                "balance_before DECIMAL(15,2), " +
                "balance_after DECIMAL(15,2), " +
                "description VARCHAR(255), " +
                "reference_number VARCHAR(50), " +
                "INDEX idx_account_date (account_id, transaction_date), " +
                "INDEX idx_type (transaction_type), " +
                "INDEX idx_reference (reference_number)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("累计函数相关表结构:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证累计函数相关表结构
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("financial_transactions"), "应保留表名");
        assertTrue(damengSql.contains("account_id"), "应保留账户ID列");
        assertTrue(damengSql.contains("transaction_date"), "应保留交易日期列");
        assertTrue(damengSql.contains("amount"), "应保留金额列");
        assertTrue(damengSql.contains("balance_before"), "应保留交易前余额列");
        assertTrue(damengSql.contains("balance_after"), "应保留交易后余额列");
        assertTrue(damengSql.contains("DECIMAL(15,2)"), "应保留金额数据类型");
    }

    @Test
    @DisplayName("测试分组分析相关表结构")
    void testGroupAnalysisTableStructure() {
        String mysqlSql = "CREATE TABLE product_sales_analysis (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "product_id INT NOT NULL, " +
                "category_id INT NOT NULL, " +
                "brand_id INT NOT NULL, " +
                "sale_date DATE NOT NULL, " +
                "quantity_sold INT NOT NULL, " +
                "unit_price DECIMAL(8,2) NOT NULL, " +
                "total_revenue DECIMAL(12,2) NOT NULL, " +
                "cost_price DECIMAL(8,2), " +
                "profit DECIMAL(10,2), " +
                "region_id INT, " +
                "store_id INT, " +
                "INDEX idx_product_date (product_id, sale_date), " +
                "INDEX idx_category (category_id), " +
                "INDEX idx_brand (brand_id), " +
                "INDEX idx_region_store (region_id, store_id)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("分组分析相关表结构:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证分组分析相关表结构
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("product_sales_analysis"), "应保留表名");
        assertTrue(damengSql.contains("product_id"), "应保留产品ID列");
        assertTrue(damengSql.contains("category_id"), "应保留分类ID列");
        assertTrue(damengSql.contains("brand_id"), "应保留品牌ID列");
        assertTrue(damengSql.contains("quantity_sold"), "应保留销售数量列");
        assertTrue(damengSql.contains("total_revenue"), "应保留总收入列");
        assertTrue(damengSql.contains("profit"), "应保留利润列");
        assertTrue(damengSql.contains("DECIMAL(12,2)"), "应保留收入数据类型");
    }

    @Test
    @DisplayName("测试时间序列分析相关表结构")
    void testTimeSeriesAnalysisTableStructure() {
        String mysqlSql = "CREATE TABLE time_series_data (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "metric_name VARCHAR(100) NOT NULL, " +
                "timestamp_value TIMESTAMP NOT NULL, " +
                "numeric_value DECIMAL(20,6), " +
                "string_value VARCHAR(255), " +
                "category VARCHAR(50), " +
                "source_system VARCHAR(50), " +
                "data_quality_score DECIMAL(3,2), " +
                "is_anomaly BOOLEAN DEFAULT FALSE, " +
                "trend_direction ENUM('up', 'down', 'stable'), " +
                "INDEX idx_metric_timestamp (metric_name, timestamp_value), " +
                "INDEX idx_category (category), " +
                "INDEX idx_source (source_system), " +
                "INDEX idx_quality (data_quality_score)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("时间序列分析相关表结构:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证时间序列分析相关表结构
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("time_series_data"), "应保留表名");
        assertTrue(damengSql.contains("metric_name"), "应保留指标名称列");
        assertTrue(damengSql.contains("timestamp_value"), "应保留时间戳列");
        assertTrue(damengSql.contains("numeric_value"), "应保留数值列");
        assertTrue(damengSql.contains("data_quality_score"), "应保留数据质量分数列");
        assertTrue(damengSql.contains("DECIMAL(20,6)"), "应保留高精度数值类型");
        assertTrue(damengSql.contains("TIMESTAMP"), "应保留时间戳类型");
    }

    @Test
    @DisplayName("测试达梦分析函数特有特性表结构")
    void testDamengAnalyticSpecificFeatures() {
        String mysqlSql = "CREATE TABLE dameng_analytics_features (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "entity_id INT NOT NULL, " +
                "measure_value DECIMAL(18,8) NOT NULL, " +
                "dimension1 VARCHAR(100), " +
                "dimension2 VARCHAR(100), " +
                "dimension3 VARCHAR(100), " +
                "time_dimension DATE NOT NULL, " +
                "hierarchy_level INT, " +
                "parent_id INT, " +
                "sort_order INT, " +
                "weight_factor DECIMAL(10,4) DEFAULT 1.0, " +
                "INDEX idx_entity_time (entity_id, time_dimension), " +
                "INDEX idx_hierarchy (hierarchy_level, parent_id), " +
                "INDEX idx_dimensions (dimension1, dimension2, dimension3)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("达梦分析函数特有特性表结构:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证达梦分析函数特有特性
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("dameng_analytics_features"), "应保留表名");
        assertTrue(damengSql.contains("measure_value"), "应保留度量值列");
        assertTrue(damengSql.contains("dimension1"), "应保留维度列");
        assertTrue(damengSql.contains("time_dimension"), "应保留时间维度列");
        assertTrue(damengSql.contains("hierarchy_level"), "应保留层次级别列");
        assertTrue(damengSql.contains("weight_factor"), "应保留权重因子列");
        assertTrue(damengSql.contains("DECIMAL(18,8)"), "应保留高精度度量值类型");
        assertTrue(damengSql.contains("DECIMAL(10,4)"), "应保留权重因子类型");
    }
}
