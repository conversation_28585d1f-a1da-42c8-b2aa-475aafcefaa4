package com.xylink.sqltranspiler.unit.dialects.dameng;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.privilege.Grant;
import com.xylink.sqltranspiler.core.ast.privilege.Revoke;
import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;

/**
 * 达梦数据库权限管理测试
 * 基于达梦官方文档的测试驱动开发
 * 参考：https://eco.dameng.com/document/dm/zh-cn/pm/definition-statement.html
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保方言功能符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试方言特有功能
 *
 * <AUTHOR>
 */
public class DamengPrivilegeTest {

    private DamengGenerator generator;

    @BeforeEach
    public void setUp() {
        generator = new DamengGenerator();
    }

    @Test
    @DisplayName("测试基本GRANT语句 - 达梦官方文档标准")
    public void testBasicGrant() {
        String originalSql = "GRANT SELECT, INSERT ON test_table TO user1";
        Grant grant = new Grant(originalSql);
        String result = generator.generate(grant);
        
        assertTrue(result.contains("GRANT"));
        assertTrue(result.contains("SELECT, INSERT"));
        assertTrue(result.contains("ON"));
        assertTrue(result.contains("test_table"));
        assertTrue(result.contains("TO"));
        assertTrue(result.contains("user1"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试GRANT ALL PRIVILEGES语句")
    public void testGrantAllPrivileges() {
        String originalSql = "GRANT ALL PRIVILEGES ON testdb.* TO user2";
        Grant grant = new Grant(originalSql);
        String result = generator.generate(grant);

        assertTrue(result.contains("GRANT"));
        assertTrue(result.contains("ALL PRIVILEGES"));
        assertTrue(result.contains("ON"));
        assertTrue(result.contains("testdb.*"));
        assertTrue(result.contains("TO"));
        assertTrue(result.contains("user2"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试GRANT WITH GRANT OPTION语句")
    public void testGrantWithGrantOption() {
        String originalSql = "GRANT SELECT, UPDATE ON table1 TO user3 WITH GRANT OPTION";
        Grant grant = new Grant(originalSql);
        String result = generator.generate(grant);
        
        assertTrue(result.contains("GRANT"));
        assertTrue(result.contains("SELECT, UPDATE"));
        assertTrue(result.contains("ON"));
        assertTrue(result.contains("table1"));
        assertTrue(result.contains("TO"));
        assertTrue(result.contains("user3"));
        assertTrue(result.contains("WITH GRANT OPTION"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试GRANT多个用户语句")
    public void testGrantMultipleUsers() {
        String originalSql = "GRANT INSERT, DELETE ON test_db.test_table TO user1, user2, user3";
        Grant grant = new Grant(originalSql);
        String result = generator.generate(grant);
        
        assertTrue(result.contains("GRANT"));
        assertTrue(result.contains("INSERT, DELETE"));
        assertTrue(result.contains("ON"));
        assertTrue(result.contains("test_db.test_table"));
        assertTrue(result.contains("TO"));
        assertTrue(result.contains("user1, user2, user3"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试GRANT反引号转换")
    public void testGrantBacktickConversion() {
        String originalSql = "GRANT SELECT ON `testdb`.`table` TO `user`";
        Grant grant = new Grant(originalSql);
        String result = generator.generate(grant);

        assertTrue(result.contains("GRANT"));
        assertTrue(result.contains("SELECT"));
        assertTrue(result.contains("ON"));
        assertTrue(result.contains("\"testdb\".\"table\""));
        assertTrue(result.contains("TO"));
        assertTrue(result.contains("\"user\""));
        assertFalse(result.contains("`")); // 确保反引号被转换
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试基本REVOKE语句 - 达梦官方文档标准")
    public void testBasicRevoke() {
        String originalSql = "REVOKE SELECT, INSERT ON test_table FROM user1";
        Revoke revoke = new Revoke(originalSql);
        String result = generator.generate(revoke);
        
        assertTrue(result.contains("REVOKE"));
        assertTrue(result.contains("SELECT, INSERT"));
        assertTrue(result.contains("ON"));
        assertTrue(result.contains("test_table"));
        assertTrue(result.contains("FROM"));
        assertTrue(result.contains("user1"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试REVOKE ALL PRIVILEGES语句")
    public void testRevokeAllPrivileges() {
        String originalSql = "REVOKE ALL PRIVILEGES ON testdb.* FROM user2";
        Revoke revoke = new Revoke(originalSql);
        String result = generator.generate(revoke);

        assertTrue(result.contains("REVOKE"));
        assertTrue(result.contains("ALL PRIVILEGES"));
        assertTrue(result.contains("ON"));
        assertTrue(result.contains("testdb.*"));
        assertTrue(result.contains("FROM"));
        assertTrue(result.contains("user2"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试REVOKE GRANT OPTION语句")
    public void testRevokeGrantOption() {
        String originalSql = "REVOKE ALL PRIVILEGES, GRANT OPTION FROM user3";
        Revoke revoke = new Revoke(originalSql);
        String result = generator.generate(revoke);
        
        assertTrue(result.contains("REVOKE"));
        assertTrue(result.contains("ALL PRIVILEGES, GRANT OPTION"));
        assertTrue(result.contains("FROM"));
        assertTrue(result.contains("user3"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试REVOKE多个用户语句")
    public void testRevokeMultipleUsers() {
        String originalSql = "REVOKE UPDATE, DELETE ON test_db.test_table FROM user1, user2, user3";
        Revoke revoke = new Revoke(originalSql);
        String result = generator.generate(revoke);
        
        assertTrue(result.contains("REVOKE"));
        assertTrue(result.contains("UPDATE, DELETE"));
        assertTrue(result.contains("ON"));
        assertTrue(result.contains("test_db.test_table"));
        assertTrue(result.contains("FROM"));
        assertTrue(result.contains("user1, user2, user3"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试REVOKE反引号转换")
    public void testRevokeBacktickConversion() {
        String originalSql = "REVOKE SELECT ON `testdb`.`table` FROM `user`";
        Revoke revoke = new Revoke(originalSql);
        String result = generator.generate(revoke);

        assertTrue(result.contains("REVOKE"));
        assertTrue(result.contains("SELECT"));
        assertTrue(result.contains("ON"));
        assertTrue(result.contains("\"testdb\".\"table\""));
        assertTrue(result.contains("FROM"));
        assertTrue(result.contains("\"user\""));
        assertFalse(result.contains("`")); // 确保反引号被转换
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试达梦数据库官方文档示例")
    public void testDamengOfficialExample() {
        // 根据达梦官方文档的权限管理示例
        String grantSql = "GRANT CREATE, ALTER, DROP ON SCHEMA test_schema TO db_admin WITH GRANT OPTION";
        Grant grant = new Grant(grantSql);
        String grantResult = generator.generate(grant);
        
        assertTrue(grantResult.contains("GRANT"));
        assertTrue(grantResult.contains("CREATE, ALTER, DROP"));
        assertTrue(grantResult.contains("ON"));
        assertTrue(grantResult.contains("SCHEMA"));
        assertTrue(grantResult.contains("test_schema"));
        assertTrue(grantResult.contains("TO"));
        assertTrue(grantResult.contains("db_admin"));
        assertTrue(grantResult.contains("WITH GRANT OPTION"));
        assertTrue(grantResult.endsWith(";"));
        
        String revokeSql = "REVOKE CREATE, ALTER ON SCHEMA test_schema FROM db_admin";
        Revoke revoke = new Revoke(revokeSql);
        String revokeResult = generator.generate(revoke);
        
        assertTrue(revokeResult.contains("REVOKE"));
        assertTrue(revokeResult.contains("CREATE, ALTER"));
        assertTrue(revokeResult.contains("ON"));
        assertTrue(revokeResult.contains("SCHEMA"));
        assertTrue(revokeResult.contains("test_schema"));
        assertTrue(revokeResult.contains("FROM"));
        assertTrue(revokeResult.contains("db_admin"));
        assertTrue(revokeResult.endsWith(";"));
    }

    @Test
    @DisplayName("测试程序化构建GRANT语句")
    public void testProgrammaticGrant() {
        List<String> privileges = Arrays.asList("SELECT", "INSERT", "UPDATE");
        List<String> users = Arrays.asList("user1", "user2");
        
        Grant grant = new Grant(privileges, "TABLE", "test_db.test_table", users, true);
        String result = generator.generate(grant);
        
        assertTrue(result.contains("GRANT"));
        assertTrue(result.contains("SELECT, INSERT, UPDATE"));
        assertTrue(result.contains("ON"));
        assertTrue(result.contains("TABLE"));
        assertTrue(result.contains("test_db.test_table"));
        assertTrue(result.contains("TO"));
        assertTrue(result.contains("user1, user2"));
        assertTrue(result.contains("WITH GRANT OPTION"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试程序化构建REVOKE语句")
    public void testProgrammaticRevoke() {
        List<String> privileges = Arrays.asList("DELETE", "DROP");
        List<String> users = Arrays.asList("user3", "user4");
        
        Revoke revoke = new Revoke(privileges, "TABLE", "test_db.test_table", users, false, false);
        String result = generator.generate(revoke);
        
        assertTrue(result.contains("REVOKE"));
        assertTrue(result.contains("DELETE, DROP"));
        assertTrue(result.contains("ON"));
        assertTrue(result.contains("TABLE"));
        assertTrue(result.contains("test_db.test_table"));
        assertTrue(result.contains("FROM"));
        assertTrue(result.contains("user3, user4"));
        assertTrue(result.endsWith(";"));
    }
}
