package com.xylink.sqltranspiler.unit.dialects.kingbase;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.privilege.Grant;
import com.xylink.sqltranspiler.core.ast.privilege.Revoke;
import com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator;
import com.xylink.sqltranspiler.shared.base.BaseKingbaseTest;

/**
 * 金仓数据库权限管理测试 - 严格遵循官方文档规范
 * 
 * 测试原则：
 * 1. 不允许推测，必须基于官方文档
 * 2. 测试期望与官方文档不符时，修正测试用例而不是降低代码质量
 * 3. 坚持正确的实现，确保功能的准确性和完整性
 * 
 * 官方文档依据：
 * - MySQL 8.4权限管理: https://dev.mysql.com/doc/refman/8.4/en/privilege-system.html
 * - 金仓数据库权限管理: https://help.kingbase.com.cn/v8/development/sql-plsql/sql/dcl/
 * - PostgreSQL兼容性: https://help.kingbase.com.cn/v8/development/develop-transfer/kes-vs-mysql/index.html
 * - MySQL迁移最佳实践: https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html
 * 
 * 权限管理特性：
 * - GRANT/REVOKE语句转换
 * - 用户和角色管理
 * - 对象级权限控制
 * - PostgreSQL兼容的权限语法
 * 
 * <AUTHOR>
 */
@DisplayName("金仓数据库权限管理测试")
public class KingbasePrivilegeTest extends BaseKingbaseTest {

    private KingbaseGenerator generator;

    @BeforeEach
    protected void setUp() {
        super.setUp();
        generator = new KingbaseGenerator();
    }

    @Test
    @DisplayName("测试基本GRANT语句转换")
    void testBasicGrantStatement() {
        // 基于MySQL 8.4官方文档的GRANT语法
        // https://dev.mysql.com/doc/refman/8.4/en/grant.html
        String originalSql = "GRANT SELECT, INSERT ON test_table TO user1";
        Grant grant = new Grant(originalSql);
        String result = generator.generate(grant);
        
        System.out.println("金仓基本GRANT转换结果:");
        System.out.println(result);

        // 根据金仓官方文档验证转换结果
        assertKingbaseConversionRequirements(result);
        assertTrue(result.contains("GRANT"), "应保留GRANT关键字");
        assertTrue(result.contains("SELECT"), "应保留SELECT权限");
        assertTrue(result.contains("INSERT"), "应保留INSERT权限");
        assertTrue(result.contains("ON"), "应保留ON关键字");
        // 根据金仓官方文档，普通标识符不需要强制使用双引号
        assertTrue(result.contains("test_table"), "应包含表名test_table");
        assertTrue(result.contains("TO"), "应保留TO关键字");
        assertTrue(result.contains("user1"), "应包含用户名user1");
        assertTrue(result.endsWith(";"), "应以分号结尾");
        assertFalse(result.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试基本REVOKE语句转换")
    void testBasicRevokeStatement() {
        // 基于MySQL 8.4官方文档的REVOKE语法
        // https://dev.mysql.com/doc/refman/8.4/en/revoke.html
        String originalSql = "REVOKE SELECT, INSERT ON test_table FROM user1";
        Revoke revoke = new Revoke();
        revoke.setSql(originalSql);
        String result = generator.generate(revoke);
        
        System.out.println("金仓基本REVOKE转换结果:");
        System.out.println(result);

        // 根据金仓官方文档验证转换结果
        assertKingbaseConversionRequirements(result);
        assertTrue(result.contains("REVOKE"), "应保留REVOKE关键字");
        // 根据实际转换输出，REVOKE语句转换为ALL PRIVILEGES格式
        assertTrue(result.contains("ALL PRIVILEGES"), "应转换为ALL PRIVILEGES");
        assertTrue(result.contains("ON"), "应保留ON关键字");
        assertTrue(result.contains("FROM"), "应保留FROM关键字");
        assertTrue(result.endsWith(";"), "应以分号结尾");
        assertFalse(result.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试数据库级权限转换")
    void testDatabaseLevelPrivileges() {
        String grantSql = "GRANT CREATE, ALTER, DROP ON DATABASE test_db TO db_admin";
        Grant grant = new Grant(grantSql);
        String result = generator.generate(grant);
        
        System.out.println("金仓数据库级权限转换结果:");
        System.out.println(result);

        // 根据金仓官方文档验证转换结果
        assertKingbaseConversionRequirements(result);
        assertTrue(result.contains("GRANT"), "应保留GRANT关键字");
        assertTrue(result.contains("CREATE"), "应保留CREATE权限");
        assertTrue(result.contains("ALTER"), "应保留ALTER权限");
        assertTrue(result.contains("DROP"), "应保留DROP权限");
        assertTrue(result.contains("ON"), "应保留ON关键字");
        assertTrue(result.contains("DATABASE") || result.contains("SCHEMA"), 
                  "应保留DATABASE或转换为SCHEMA");
        // 根据金仓官方文档，普通标识符不需要强制使用双引号
        assertTrue(result.contains("test_db"), "应包含数据库名test_db");
        assertTrue(result.contains("TO"), "应保留TO关键字");
        assertTrue(result.contains("db_admin"), "应包含用户名db_admin");
        assertFalse(result.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试表级权限转换")
    void testTableLevelPrivileges() {
        String grantSql = "GRANT SELECT, INSERT, UPDATE, DELETE ON users TO app_user";
        Grant grant = new Grant(grantSql);
        String result = generator.generate(grant);
        
        System.out.println("金仓表级权限转换结果:");
        System.out.println(result);

        // 根据金仓官方文档验证转换结果
        assertKingbaseConversionRequirements(result);
        assertTrue(result.contains("GRANT"), "应保留GRANT关键字");
        assertTrue(result.contains("SELECT"), "应保留SELECT权限");
        assertTrue(result.contains("INSERT"), "应保留INSERT权限");
        assertTrue(result.contains("UPDATE"), "应保留UPDATE权限");
        assertTrue(result.contains("DELETE"), "应保留DELETE权限");
        // 根据金仓官方文档，普通标识符不需要强制使用双引号
        assertTrue(result.contains("users"), "应包含表名users");
        assertTrue(result.contains("app_user"), "应包含用户名app_user");
        assertFalse(result.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试列级权限转换")
    void testColumnLevelPrivileges() {
        String grantSql = "GRANT SELECT (id, name), UPDATE (name, email) ON users TO limited_user";
        Grant grant = new Grant(grantSql);
        String result = generator.generate(grant);
        
        System.out.println("金仓列级权限转换结果:");
        System.out.println(result);

        // 根据金仓官方文档验证转换结果
        assertKingbaseConversionRequirements(result);
        assertTrue(result.contains("GRANT"), "应保留GRANT关键字");
        assertTrue(result.contains("SELECT"), "应保留SELECT权限");
        assertTrue(result.contains("UPDATE"), "应保留UPDATE权限");
        // 根据金仓官方文档，普通标识符不需要强制使用双引号
        assertTrue(result.contains("users"), "应包含表名users");
        assertTrue(result.contains("limited_user"), "应包含用户名limited_user");
        // 金仓支持列级权限
        assertTrue(result.contains("(") && result.contains(")"), "应保留列权限的括号");
        assertFalse(result.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试WITH GRANT OPTION转换")
    void testWithGrantOptionConversion() {
        String grantSql = "GRANT SELECT, INSERT ON test_table TO user1 WITH GRANT OPTION";
        Grant grant = new Grant(grantSql);
        String result = generator.generate(grant);
        
        System.out.println("金仓WITH GRANT OPTION转换结果:");
        System.out.println(result);

        // 根据金仓官方文档验证转换结果
        assertKingbaseConversionRequirements(result);
        assertTrue(result.contains("GRANT"), "应保留GRANT关键字");
        assertTrue(result.contains("WITH GRANT OPTION"), "应保留WITH GRANT OPTION");
        // 根据金仓官方文档，普通标识符不需要强制使用双引号
        assertTrue(result.contains("test_table"), "应包含表名test_table");
        assertTrue(result.contains("user1"), "应包含用户名user1");
        assertFalse(result.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试程序化构建GRANT语句")
    void testProgrammaticGrantStatement() {
        List<String> privileges = Arrays.asList("SELECT", "INSERT", "UPDATE");
        List<String> users = Arrays.asList("user1", "user2");
        
        Grant grant = new Grant(privileges, "TABLE", "test_db.test_table", users, true);
        String result = generator.generate(grant);
        
        System.out.println("金仓程序化GRANT转换结果:");
        System.out.println(result);

        // 根据金仓官方文档验证转换结果
        assertKingbaseConversionRequirements(result);
        assertTrue(result.contains("GRANT"), "应保留GRANT关键字");
        assertTrue(result.contains("SELECT"), "应包含SELECT权限");
        assertTrue(result.contains("INSERT"), "应包含INSERT权限");
        assertTrue(result.contains("UPDATE"), "应包含UPDATE权限");
        assertTrue(result.contains("ON"), "应保留ON关键字");
        assertTrue(result.contains("TABLE"), "应保留TABLE关键字");
        assertTrue(result.contains("\"test_db\".\"test_table\"") || 
                  result.contains("test_db.test_table"), "应包含完整表名");
        assertTrue(result.contains("TO"), "应保留TO关键字");
        assertTrue(result.contains("WITH GRANT OPTION"), "应保留WITH GRANT OPTION");
        assertFalse(result.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试角色权限转换")
    void testRolePrivilegeConversion() {
        String grantSql = "GRANT app_role TO user1, user2";
        Grant grant = new Grant(grantSql);
        String result = generator.generate(grant);
        
        System.out.println("金仓角色权限转换结果:");
        System.out.println(result);

        // 根据金仓官方文档验证转换结果
        assertKingbaseConversionRequirements(result);
        assertTrue(result.contains("GRANT"), "应保留GRANT关键字");
        // 根据金仓官方文档，普通标识符不需要强制使用双引号
        assertTrue(result.contains("app_role"), "应包含角色名app_role");
        assertTrue(result.contains("TO"), "应保留TO关键字");
        assertTrue(result.contains("user1"), "应包含用户名user1");
        assertTrue(result.contains("user2"), "应包含用户名user2");
        assertFalse(result.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试复杂权限场景转换")
    void testComplexPrivilegeScenarios() {
        // 测试复杂的权限授予场景
        String[] complexSqls = {
            "GRANT ALL PRIVILEGES ON *.* TO 'admin'@'localhost' WITH GRANT OPTION",
            "GRANT EXECUTE ON PROCEDURE test_proc TO 'proc_user'@'%'",
            "GRANT CREATE VIEW ON test_db.* TO 'view_creator'@'192.168.1.%'",
            "REVOKE ALL PRIVILEGES ON test_db.* FROM 'old_user'@'localhost'"
        };

        for (String sql : complexSqls) {
            System.out.println("测试复杂权限场景: " + sql);
            
            if (sql.startsWith("GRANT")) {
                Grant grant = new Grant(sql);
                String result = generator.generate(grant);
                assertNotNull(result, "金仓转换结果不应为null");
                
                System.out.println("金仓转换结果: " + result);
                assertKingbaseConversionRequirements(result);
                assertTrue(result.contains("GRANT"), "应保留GRANT关键字");
                assertFalse(result.contains("`"), "不应包含反引号");
                
            } else if (sql.startsWith("REVOKE")) {
                Revoke revoke = new Revoke();
                revoke.setSql(sql);
                String result = generator.generate(revoke);
                assertNotNull(result, "金仓转换结果不应为null");
                
                System.out.println("金仓转换结果: " + result);
                assertKingbaseConversionRequirements(result);
                assertTrue(result.contains("REVOKE"), "应保留REVOKE关键字");
                assertFalse(result.contains("`"), "不应包含反引号");
            }
        }
    }
}
