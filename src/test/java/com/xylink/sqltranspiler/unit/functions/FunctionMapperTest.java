package com.xylink.sqltranspiler.unit.functions;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.dialects.DamengDialect;
import com.xylink.sqltranspiler.core.dialects.SqlDialect;
import com.xylink.sqltranspiler.core.functions.FunctionMapper;

/**
 * 函数映射器测试
 * 验证借鉴Apache Calcite设计思想的函数映射是否正常工作
 *
 * 测试原则：严格基于MySQL 8.4官方文档进行函数映射验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/built-in-function-reference.html
 * - MySQL函数参考：https://dev.mysql.com/doc/refman/8.4/en/functions.html
 * - MySQL数学函数：https://dev.mysql.com/doc/refman/8.4/en/mathematical-functions.html
 * - MySQL字符串函数：https://dev.mysql.com/doc/refman/8.4/en/string-functions.html
 * - MySQL日期时间函数：https://dev.mysql.com/doc/refman/8.4/en/date-and-time-functions.html
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 *
 * 验证策略：
 * 1. 根据MySQL官方文档验证函数映射的准确性
 * 2. 根据目标数据库官方文档验证转换结果的正确性
 * 3. 测试MySQL -> 标准SQL -> 目标数据库的转换链路
 * 4. 验证特殊函数的转换规则基于官方文档
 */
public class FunctionMapperTest {
    
    private final FunctionMapper functionMapper = new FunctionMapper();
    private final SqlDialect damengDialect = new DamengDialect();
    
    @Test
    public void testBasicFunctionMapping() {
        // 测试基础函数映射 - 基于MySQL 8.4官方文档
        
        // 日期时间函数
        assertEquals("SYSDATE", 
            functionMapper.mapFunction("NOW", Arrays.asList(), damengDialect));
        assertEquals("TRUNC(SYSDATE)", 
            functionMapper.mapFunction("CURDATE", Arrays.asList(), damengDialect));
        
        // 字符串函数
        assertEquals("LENGTH(str)", 
            functionMapper.mapFunction("LENGTH", Arrays.asList("str"), damengDialect));
        assertEquals("CONCAT(a, b)", 
            functionMapper.mapFunction("CONCAT", Arrays.asList("a", "b"), damengDialect));
        assertEquals("UPPER(name)", 
            functionMapper.mapFunction("UPPER", Arrays.asList("name"), damengDialect));
        
        // 数学函数
        assertEquals("ABS(num)", 
            functionMapper.mapFunction("ABS", Arrays.asList("num"), damengDialect));
        assertEquals("CEIL(value)", 
            functionMapper.mapFunction("CEIL", Arrays.asList("value"), damengDialect));
        assertEquals("FLOOR(value)", 
            functionMapper.mapFunction("FLOOR", Arrays.asList("value"), damengDialect));
    }
    
    @Test
    public void testSpecialFunctionTransformation() {
        // 测试特殊函数转换规则
        
        // IF函数转换：IF(condition, true_value, false_value) -> CASE WHEN ... END
        String ifResult = functionMapper.mapFunction("IF", 
            Arrays.asList("age > 18", "'adult'", "'minor'"), damengDialect);
        assertEquals("CASE WHEN age > 18 THEN 'adult' ELSE 'minor' END", ifResult);
        
        // DATE_FORMAT函数转换：DATE_FORMAT(date, format) -> TO_CHAR(date, format)
        String dateFormatResult = functionMapper.mapFunction("DATE_FORMAT", 
            Arrays.asList("created_at", "'%Y-%m-%d'"), damengDialect);
        assertEquals("TO_CHAR(created_at, 'YYYY-MM-DD')", dateFormatResult);
        
        // SUBSTRING函数转换：保持MySQL兼容语法
        String substringResult = functionMapper.mapFunction("SUBSTRING", 
            Arrays.asList("name", "1", "5"), damengDialect);
        assertEquals("SUBSTR(name, 1, 5)", substringResult);
    }
    
    @Test
    public void testFunctionSupportCheck() {
        // 测试函数支持检查
        
        // 标准映射的函数
        assertTrue(functionMapper.supportsFunction("NOW", damengDialect));
        assertTrue(functionMapper.supportsFunction("CONCAT", damengDialect));
        assertTrue(functionMapper.supportsFunction("LENGTH", damengDialect));
        
        // 特殊处理的函数
        assertTrue(functionMapper.supportsFunction("IF", damengDialect));
        assertTrue(functionMapper.supportsFunction("DATE_FORMAT", damengDialect));
        assertTrue(functionMapper.supportsFunction("SUBSTRING", damengDialect));
        
        // 不支持的函数
        assertFalse(functionMapper.supportsFunction("UNKNOWN_FUNCTION", damengDialect));
        assertFalse(functionMapper.supportsFunction("", damengDialect));
        assertFalse(functionMapper.supportsFunction(null, damengDialect));
    }
    
    @Test
    public void testEdgeCases() {
        // 测试边界情况
        
        // null和空字符串
        assertEquals("", functionMapper.mapFunction(null, Arrays.asList(), damengDialect));
        assertEquals("", functionMapper.mapFunction("", Arrays.asList(), damengDialect));
        
        // 空参数列表
        assertEquals("SYSDATE", 
            functionMapper.mapFunction("NOW", Arrays.asList(), damengDialect));
        
        // 大小写不敏感
        assertEquals("SYSDATE", 
            functionMapper.mapFunction("now", Arrays.asList(), damengDialect));
        assertEquals("SYSDATE", 
            functionMapper.mapFunction("Now", Arrays.asList(), damengDialect));
        assertEquals("SYSDATE", 
            functionMapper.mapFunction("NOW", Arrays.asList(), damengDialect));
    }
    
    @Test
    public void testIfFunctionRule() {
        // 测试IF函数转换规则的详细行为
        FunctionMapper.IfFunctionRule rule = new FunctionMapper.IfFunctionRule();
        
        // 正常情况
        String result1 = rule.transform("IF", 
            Arrays.asList("status = 1", "'active'", "'inactive'"), damengDialect);
        assertEquals("CASE WHEN status = 1 THEN 'active' ELSE 'inactive' END", result1);
        
        // 复杂条件
        String result2 = rule.transform("IF", 
            Arrays.asList("age >= 18 AND status = 'valid'", "1", "0"), damengDialect);
        assertEquals("CASE WHEN age >= 18 AND status = 'valid' THEN 1 ELSE 0 END", result2);
        
        // 参数数量不正确的情况
        String result3 = rule.transform("IF", Arrays.asList("condition"), damengDialect);
        assertEquals("IF(condition)", result3); // 回退到原函数
    }
    
    @Test
    public void testDateFormatRule() {
        // 测试DATE_FORMAT函数转换规则
        FunctionMapper.DateFormatFunctionRule rule = new FunctionMapper.DateFormatFunctionRule();
        
        // 达梦数据库格式转换
        String result1 = rule.transform("DATE_FORMAT", 
            Arrays.asList("created_at", "'%Y-%m-%d %H:%i:%s'"), damengDialect);
        assertEquals("TO_CHAR(created_at, 'YYYY-MM-DD HH24:MI:SS')", result1);
        
        // 简单日期格式
        String result2 = rule.transform("DATE_FORMAT", 
            Arrays.asList("birth_date", "'%Y-%m-%d'"), damengDialect);
        assertEquals("TO_CHAR(birth_date, 'YYYY-MM-DD')", result2);
        
        // 参数数量不正确的情况
        String result3 = rule.transform("DATE_FORMAT", Arrays.asList("date"), damengDialect);
        assertEquals("DATE_FORMAT(date)", result3); // 回退到原函数
    }
    
    @Test
    public void testSubstringRule() {
        // 测试SUBSTRING函数转换规则
        FunctionMapper.SubstringFunctionRule rule = new FunctionMapper.SubstringFunctionRule();
        
        // 三参数形式：SUBSTRING(str, pos, len)
        String result1 = rule.transform("SUBSTRING", 
            Arrays.asList("name", "1", "5"), damengDialect);
        assertEquals("SUBSTR(name, 1, 5)", result1);
        
        // 两参数形式：SUBSTRING(str, pos)
        String result2 = rule.transform("SUBSTRING", 
            Arrays.asList("description", "10"), damengDialect);
        assertEquals("SUBSTR(description, 10)", result2);
        
        // 参数数量不足的情况
        String result3 = rule.transform("SUBSTRING", Arrays.asList("str"), damengDialect);
        assertEquals("SUBSTR(str)", result3); // 使用达梦方言映射
    }
    
    @Test
    public void testGetSupportedMySqlFunctions() {
        // 测试获取支持的MySQL函数列表
        var supportedFunctions = FunctionMapper.getSupportedMySqlFunctions();
        
        // 验证包含基础函数
        assertTrue(supportedFunctions.contains("NOW"));
        assertTrue(supportedFunctions.contains("CONCAT"));
        assertTrue(supportedFunctions.contains("LENGTH"));
        assertTrue(supportedFunctions.contains("ABS"));
        
        // 验证包含特殊函数
        assertTrue(supportedFunctions.contains("IF"));
        assertTrue(supportedFunctions.contains("DATE_FORMAT"));
        assertTrue(supportedFunctions.contains("SUBSTRING"));
        
        // 验证不可变性
        assertThrows(UnsupportedOperationException.class, () -> {
            supportedFunctions.add("NEW_FUNCTION");
        });
    }
    
    @Test
    public void testCaseSensitivity() {
        // 测试大小写敏感性
        List<String> args = Arrays.asList("test_value");
        
        // 所有大小写变体应该产生相同结果
        String result1 = functionMapper.mapFunction("length", args, damengDialect);
        String result2 = functionMapper.mapFunction("LENGTH", args, damengDialect);
        String result3 = functionMapper.mapFunction("Length", args, damengDialect);
        String result4 = functionMapper.mapFunction("LenGth", args, damengDialect);
        
        assertEquals(result1, result2);
        assertEquals(result2, result3);
        assertEquals(result3, result4);
        assertEquals("LENGTH(test_value)", result1);
    }
}
