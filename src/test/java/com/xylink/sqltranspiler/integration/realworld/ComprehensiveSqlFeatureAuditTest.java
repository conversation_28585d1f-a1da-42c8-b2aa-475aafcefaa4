package com.xylink.sqltranspiler.integration.realworld;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.Transpiler;
import com.xylink.sqltranspiler.core.context.TranspilationResult;

/**
 * 全面的SQL功能审计测试
 * 基于官方文档全面测试SQL转换器的功能覆盖：
 * - MySQL: https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦: https://eco.dameng.com/document/dm/zh-cn/pm/
 * - 金仓: https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
 * - 神通: shentong.md
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
@DisplayName("全面SQL功能审计测试")
public class ComprehensiveSqlFeatureAuditTest {

    private Transpiler transpiler;

    @BeforeEach
    void setUp() {
        transpiler = new Transpiler();
    }

    @Test
    @DisplayName("DDL语句全面测试")
    void testDdlStatements() {
        String[] ddlStatements = {
            // 基础DDL
            "CREATE TABLE test (id INT PRIMARY KEY, name VARCHAR(50));",
            "ALTER TABLE test ADD COLUMN age INT;",
            "DROP TABLE test;",
            
            // 索引DDL
            "CREATE INDEX idx_name ON test(name);",
            "CREATE UNIQUE INDEX udx_email ON test(email);",
            "DROP INDEX idx_name ON test;",
            
            // 视图DDL
            "CREATE VIEW user_view AS SELECT id, name FROM users;",
            "DROP VIEW user_view;",
            
            // 数据库DDL
            "CREATE DATABASE test_db;",
            "DROP DATABASE test_db;",
            
            // 约束DDL
            "ALTER TABLE test ADD CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES users(id);",
            "ALTER TABLE test DROP CONSTRAINT fk_user;"
        };

        testStatementArray("DDL语句", ddlStatements);
    }

    @Test
    @DisplayName("DML语句全面测试")
    void testDmlStatements() {
        String[] dmlStatements = {
            // 基础DML
            "INSERT INTO test (name) VALUES ('test');",
            "UPDATE test SET name = 'updated' WHERE id = 1;",
            "DELETE FROM test WHERE id = 1;",
            "SELECT * FROM test;",
            
            // 复杂SELECT
            "SELECT t1.*, t2.name FROM test t1 JOIN users t2 ON t1.user_id = t2.id;",
            "SELECT COUNT(*) FROM test GROUP BY category HAVING COUNT(*) > 5;",
            "SELECT * FROM test WHERE id IN (SELECT user_id FROM orders);",
            
            // 批量操作
            "INSERT INTO test (name, age) VALUES ('a', 1), ('b', 2), ('c', 3);",
            "UPDATE test t1 JOIN users t2 ON t1.user_id = t2.id SET t1.status = 'active';",
            "DELETE t1 FROM test t1 JOIN users t2 ON t1.user_id = t2.id WHERE t2.status = 'inactive';"
        };

        testStatementArray("DML语句", dmlStatements);
    }

    @Test
    @DisplayName("高级SQL功能测试")
    void testAdvancedSqlFeatures() {
        String[] advancedStatements = {
            // 窗口函数
            "SELECT id, ROW_NUMBER() OVER (ORDER BY id) as rn FROM test;",
            "SELECT id, RANK() OVER (PARTITION BY category ORDER BY score DESC) as rank FROM test;",
            
            // CTE
            "WITH recursive_cte AS (SELECT id, parent_id FROM test WHERE parent_id IS NULL UNION ALL SELECT t.id, t.parent_id FROM test t JOIN recursive_cte r ON t.parent_id = r.id) SELECT * FROM recursive_cte;",
            
            // 子查询
            "SELECT * FROM test WHERE id = (SELECT MAX(id) FROM test);",
            "SELECT * FROM test WHERE EXISTS (SELECT 1 FROM orders WHERE orders.user_id = test.id);",
            
            // UNION
            "SELECT id, name FROM test UNION SELECT id, name FROM users;",
            "SELECT id FROM test UNION ALL SELECT id FROM users;",
            
            // CASE表达式
            "SELECT id, CASE WHEN age < 18 THEN 'minor' ELSE 'adult' END as category FROM test;"
        };

        testStatementArray("高级SQL功能", advancedStatements);
    }

    @Test
    @DisplayName("数据类型转换测试")
    void testDataTypeConversions() {
        String[] dataTypeStatements = {
            // 数值类型
            "CREATE TABLE test (id INT, big_id BIGINT, small_id TINYINT, decimal_val DECIMAL(10,2));",
            
            // 字符串类型
            "CREATE TABLE test (name VARCHAR(255), description TEXT, fixed_char CHAR(10));",
            
            // 日期时间类型
            "CREATE TABLE test (created_at DATETIME, birth_date DATE, update_time TIMESTAMP);",
            
            // 特殊类型
            "CREATE TABLE test (is_active BOOLEAN, data_blob BLOB, json_data JSON);",
            
            // 枚举类型
            "CREATE TABLE test (status ENUM('active', 'inactive', 'pending'));"
        };

        testStatementArray("数据类型转换", dataTypeStatements);
    }

    @Test
    @DisplayName("函数转换测试")
    void testFunctionConversions() {
        String[] functionStatements = {
            // 日期函数
            "SELECT NOW(), CURDATE(), CURTIME() FROM test;",
            "SELECT DATE_FORMAT(created_at, '%Y-%m-%d') FROM test;",
            "SELECT YEAR(created_at), MONTH(created_at), DAY(created_at) FROM test;",
            
            // 字符串函数
            "SELECT CONCAT(first_name, ' ', last_name) as full_name FROM test;",
            "SELECT LENGTH(name), UPPER(name), LOWER(name) FROM test;",
            "SELECT SUBSTRING(name, 1, 10) FROM test;",
            
            // 数学函数
            "SELECT ABS(score), ROUND(score, 2), CEIL(score), FLOOR(score) FROM test;",
            
            // 聚合函数
            "SELECT COUNT(*), SUM(amount), AVG(score), MAX(created_at), MIN(created_at) FROM test;",
            
            // 条件函数
            "SELECT IFNULL(name, 'Unknown'), COALESCE(name, email, 'N/A') FROM test;",
            "SELECT IF(age >= 18, 'Adult', 'Minor') FROM test;"
        };

        testStatementArray("函数转换", functionStatements);
    }

    @Test
    @DisplayName("存储过程和函数测试")
    void testStoredProceduresAndFunctions() {
        String[] procedureStatements = {
            // 存储过程
            """
            CREATE PROCEDURE GetUserById(IN user_id INT)
            BEGIN
                SELECT * FROM users WHERE id = user_id;
            END;
            """,
            
            // 函数
            """
            CREATE FUNCTION CalculateAge(birth_date DATE) RETURNS INT
            READS SQL DATA
            DETERMINISTIC
            BEGIN
                RETURN YEAR(CURDATE()) - YEAR(birth_date);
            END;
            """,
            
            // 删除
            "DROP PROCEDURE IF EXISTS GetUserById;",
            "DROP FUNCTION IF EXISTS CalculateAge;"
        };

        testStatementArray("存储过程和函数", procedureStatements);
    }

    @Test
    @DisplayName("事务控制测试")
    void testTransactionControl() {
        String[] transactionStatements = {
            "START TRANSACTION;",
            "BEGIN;",
            "COMMIT;",
            "ROLLBACK;",
            "SAVEPOINT sp1;",
            "ROLLBACK TO SAVEPOINT sp1;",
            "RELEASE SAVEPOINT sp1;",
            "SET AUTOCOMMIT = 0;",
            "SET AUTOCOMMIT = 1;"
        };

        testStatementArray("事务控制", transactionStatements);
    }

    @Test
    @DisplayName("权限管理测试")
    void testPermissionManagement() {
        String[] permissionStatements = {
            "GRANT SELECT, INSERT ON test TO 'user'@'localhost';",
            "GRANT ALL PRIVILEGES ON test.* TO 'admin'@'%';",
            "REVOKE SELECT ON test FROM 'user'@'localhost';",
            "REVOKE ALL PRIVILEGES ON test.* FROM 'admin'@'%';",
            "CREATE USER 'newuser'@'localhost' IDENTIFIED BY 'password';",
            "DROP USER 'newuser'@'localhost';"
        };

        testStatementArray("权限管理", permissionStatements);
    }

    @Test
    @DisplayName("MySQL特有功能测试")
    void testMysqlSpecificFeatures() {
        String[] mysqlStatements = {
            // REPLACE语句
            "REPLACE INTO test (id, name) VALUES (1, 'test');",
            
            // INSERT ... ON DUPLICATE KEY UPDATE
            "INSERT INTO test (id, name) VALUES (1, 'test') ON DUPLICATE KEY UPDATE name = VALUES(name);",
            
            // LOAD DATA
            "LOAD DATA INFILE '/tmp/data.csv' INTO TABLE test FIELDS TERMINATED BY ',' LINES TERMINATED BY '\\n';",
            
            // SHOW语句
            "SHOW TABLES;",
            "SHOW COLUMNS FROM test;",
            "SHOW INDEX FROM test;",
            
            // EXPLAIN
            "EXPLAIN SELECT * FROM test WHERE id = 1;"
        };

        testStatementArray("MySQL特有功能", mysqlStatements);
    }

    private void testStatementArray(String category, String[] statements) {
        String[] databases = {"dameng", "kingbase", "shentong"};
        
        System.out.println("\n=== " + category + " 测试结果 ===");
        
        for (String db : databases) {
            int successCount = 0;
            int totalCount = statements.length;
            
            System.out.println("\n" + db.toUpperCase() + " 数据库:");
            
            for (int i = 0; i < statements.length; i++) {
                String sql = statements[i];
                try {
                    TranspilationResult result = transpiler.transpile(sql, "mysql", db);
                    if (result != null && result.successCount() > 0) {
                        successCount++;
                        System.out.println("  ✅ [" + (i+1) + "] " + getSqlType(sql));
                    } else {
                        System.out.println("  ❌ [" + (i+1) + "] " + getSqlType(sql) + " - 转换失败");
                    }
                } catch (Exception e) {
                    System.out.println("  ❌ [" + (i+1) + "] " + getSqlType(sql) + " - 异常: " + e.getMessage());
                }
            }
            
            double successRate = (double) successCount / totalCount * 100;
            System.out.println("  📊 成功率: " + successCount + "/" + totalCount + " (" + String.format("%.1f", successRate) + "%)");
            
            // 要求至少70%的成功率
            assertTrue(successRate >= 70.0, 
                      db + " " + category + "成功率应该至少达到70%，实际: " + String.format("%.1f", successRate) + "%");
        }
    }

    private String getSqlType(String sql) {
        String upperSql = sql.trim().toUpperCase();
        if (upperSql.startsWith("CREATE TABLE")) return "CREATE TABLE";
        if (upperSql.startsWith("CREATE INDEX")) return "CREATE INDEX";
        if (upperSql.startsWith("CREATE VIEW")) return "CREATE VIEW";
        if (upperSql.startsWith("CREATE DATABASE")) return "CREATE DATABASE";
        if (upperSql.startsWith("CREATE PROCEDURE")) return "CREATE PROCEDURE";
        if (upperSql.startsWith("CREATE FUNCTION")) return "CREATE FUNCTION";
        if (upperSql.startsWith("ALTER TABLE")) return "ALTER TABLE";
        if (upperSql.startsWith("DROP")) return "DROP";
        if (upperSql.startsWith("INSERT")) return "INSERT";
        if (upperSql.startsWith("UPDATE")) return "UPDATE";
        if (upperSql.startsWith("DELETE")) return "DELETE";
        if (upperSql.startsWith("SELECT")) return "SELECT";
        if (upperSql.startsWith("WITH")) return "CTE";
        if (upperSql.startsWith("GRANT")) return "GRANT";
        if (upperSql.startsWith("REVOKE")) return "REVOKE";
        if (upperSql.startsWith("START") || upperSql.startsWith("BEGIN")) return "TRANSACTION";
        if (upperSql.startsWith("COMMIT") || upperSql.startsWith("ROLLBACK")) return "TRANSACTION";
        if (upperSql.startsWith("REPLACE")) return "REPLACE";
        if (upperSql.startsWith("SHOW")) return "SHOW";
        if (upperSql.startsWith("EXPLAIN")) return "EXPLAIN";
        return "OTHER";
    }
}
