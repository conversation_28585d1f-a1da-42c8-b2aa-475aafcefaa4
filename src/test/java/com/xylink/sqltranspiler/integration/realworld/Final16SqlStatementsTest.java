package com.xylink.sqltranspiler.integration.realworld;

import com.xylink.sqltranspiler.shared.base.BaseConversionTest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 最终验证：16个SQL语句转换测试
 * 严格按照MySQL、达梦、金仓官方文档标准验证
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
public class Final16SqlStatementsTest extends BaseConversionTest {

    @Test
    @DisplayName("1. ALTER TABLE ADD COLUMN - 最终验证")
    void testSql1() throws Exception {
        String sql = "alter table test.a add column user_name varchar(10) not null default '';";
        
        String damengSql = convertMySqlToDameng(sql);
        assertBasicConversionRequirements(damengSql);
        assertTrue(damengSql.contains("ALTER TABLE"), "达梦应包含ALTER TABLE");
        assertTrue(damengSql.contains("ADD COLUMN"), "达梦应包含ADD COLUMN");
        assertTrue(damengSql.toUpperCase().contains("TEST") && damengSql.contains("A"), "达梦表名应正确转换");
        assertTrue(damengSql.toUpperCase().contains("USER_NAME") || damengSql.contains("user_name"), "达梦列名应正确转换");
        
        String kingbaseSql = convertMySqlToKingbase(sql);
        assertBasicConversionRequirements(kingbaseSql);
        assertTrue(kingbaseSql.toUpperCase().contains("ALTER TABLE"), "金仓应包含ALTER TABLE");
        assertTrue(kingbaseSql.toUpperCase().contains("ADD COLUMN"), "金仓应包含ADD COLUMN");
        assertTrue(kingbaseSql.contains("\"test\".\"a\""), "金仓表名应使用双引号");
        assertTrue(kingbaseSql.contains("\"user_name\""), "金仓列名应使用双引号");
    }

    @Test
    @DisplayName("2. ALTER TABLE MODIFY COLUMN - 最终验证")
    void testSql2() throws Exception {
        String sql = "alter table test.a modify column user_name varchar(20) not null default '10';";
        
        String damengSql = convertMySqlToDameng(sql);
        assertBasicConversionRequirements(damengSql);
        assertTrue(damengSql.contains("ALTER TABLE"), "达梦应包含ALTER TABLE");
        assertTrue(damengSql.contains("ALTER COLUMN"), "达梦应使用ALTER COLUMN");
        assertTrue(damengSql.toUpperCase().contains("TEST") && damengSql.contains("A"), "达梦表名应正确转换");
        
        String kingbaseSql = convertMySqlToKingbase(sql);
        assertBasicConversionRequirements(kingbaseSql);
        assertTrue(kingbaseSql.toUpperCase().contains("ALTER TABLE"), "金仓应包含ALTER TABLE");
        assertTrue(kingbaseSql.toUpperCase().contains("MODIFY COLUMN"), "金仓应包含MODIFY COLUMN");
        assertTrue(kingbaseSql.contains("\"test\".\"a\""), "金仓表名应使用双引号");
    }

    @Test
    @DisplayName("3. ALTER TABLE DROP COLUMN - 最终验证")
    void testSql3() throws Exception {
        String sql = "alter table test.a drop column user_name;";
        
        String damengSql = convertMySqlToDameng(sql);
        assertBasicConversionRequirements(damengSql);
        assertTrue(damengSql.contains("DROP COLUMN"), "达梦应包含DROP COLUMN");
        
        String kingbaseSql = convertMySqlToKingbase(sql);
        assertBasicConversionRequirements(kingbaseSql);
        assertTrue(kingbaseSql.toUpperCase().contains("DROP COLUMN"), "金仓应包含DROP COLUMN");
    }

    @Test
    @DisplayName("4. ALTER TABLE CHANGE COLUMN - 最终验证")
    void testSql4() throws Exception {
        String sql = "alter table test.a change column user_name user_name1 varchar(20) not null default '10';";
        
        String damengSql = convertMySqlToDameng(sql);
        assertBasicConversionRequirements(damengSql);
        assertTrue(damengSql.contains("ALTER TABLE"), "达梦应包含ALTER TABLE");
        
        String kingbaseSql = convertMySqlToKingbase(sql);
        assertBasicConversionRequirements(kingbaseSql);
        assertTrue(kingbaseSql.toUpperCase().contains("ALTER TABLE"), "金仓应包含ALTER TABLE");
    }

    @Test
    @DisplayName("5. ALTER TABLE MODIFY AUTO_INCREMENT - 最终验证")
    void testSql5() throws Exception {
        String sql = "alter table test.a modify column id int not null auto_increment;";
        
        String damengSql = convertMySqlToDameng(sql);
        assertBasicConversionRequirements(damengSql);
        assertTrue(damengSql.contains("ALTER TABLE"), "达梦应包含ALTER TABLE");
        assertTrue(damengSql.toUpperCase().contains("ID") || damengSql.contains("id"), "达梦列名应正确转换");
        
        String kingbaseSql = convertMySqlToKingbase(sql);
        assertBasicConversionRequirements(kingbaseSql);
        assertTrue(kingbaseSql.toUpperCase().contains("ALTER TABLE"), "金仓应包含ALTER TABLE");
        assertTrue(kingbaseSql.contains("\"id\""), "金仓列名应使用双引号");
    }

    @Test
    @DisplayName("6. ALTER TABLE MODIFY 移除AUTO_INCREMENT - 最终验证")
    void testSql6() throws Exception {
        String sql = "alter table test.a modify column id int not null;";
        
        String damengSql = convertMySqlToDameng(sql);
        assertBasicConversionRequirements(damengSql);
        assertTrue(damengSql.contains("ALTER TABLE"), "达梦应包含ALTER TABLE");
        
        String kingbaseSql = convertMySqlToKingbase(sql);
        assertBasicConversionRequirements(kingbaseSql);
        assertTrue(kingbaseSql.toUpperCase().contains("ALTER TABLE"), "金仓应包含ALTER TABLE");
    }

    @Test
    @DisplayName("7. ALTER TABLE ADD PRIMARY KEY - 最终验证")
    void testSql7() throws Exception {
        String sql = "alter table test.a add primary key (id);";
        
        String damengSql = convertMySqlToDameng(sql);
        assertBasicConversionRequirements(damengSql);
        assertTrue(damengSql.contains("ADD PRIMARY KEY"), "达梦应包含ADD PRIMARY KEY");
        
        String kingbaseSql = convertMySqlToKingbase(sql);
        assertBasicConversionRequirements(kingbaseSql);
        assertTrue(kingbaseSql.toUpperCase().contains("ADD PRIMARY KEY"), "金仓应包含ADD PRIMARY KEY");
    }

    @Test
    @DisplayName("8. ALTER TABLE ADD UNIQUE - 最终验证")
    void testSql8() throws Exception {
        String sql = "alter table test.a add unique uniq_user_name(user_name);";
        
        String damengSql = convertMySqlToDameng(sql);
        assertBasicConversionRequirements(damengSql);
        assertTrue(damengSql.contains("ADD UNIQUE"), "达梦应包含ADD UNIQUE");
        
        String kingbaseSql = convertMySqlToKingbase(sql);
        assertBasicConversionRequirements(kingbaseSql);
        assertTrue(kingbaseSql.toUpperCase().contains("ADD UNIQUE"), "金仓应包含ADD UNIQUE");
    }

    @Test
    @DisplayName("9. ALTER TABLE ADD INDEX - 最终验证")
    void testSql9() throws Exception {
        String sql = "alter table test.a add index idx_user_name (user_name);";
        
        String damengSql = convertMySqlToDameng(sql);
        assertBasicConversionRequirements(damengSql);
        assertTrue(damengSql.contains("ADD INDEX"), "达梦应包含ADD INDEX");
        
        String kingbaseSql = convertMySqlToKingbase(sql);
        assertBasicConversionRequirements(kingbaseSql);
        assertTrue(kingbaseSql.toUpperCase().contains("ADD INDEX"), "金仓应包含ADD INDEX");
    }

    @Test
    @DisplayName("10. ALTER TABLE DROP PRIMARY KEY - 最终验证")
    void testSql10() throws Exception {
        String sql = "alter table test.a drop primary key;";
        
        String damengSql = convertMySqlToDameng(sql);
        assertBasicConversionRequirements(damengSql);
        assertTrue(damengSql.contains("DROP PRIMARY KEY"), "达梦应包含DROP PRIMARY KEY");
        
        String kingbaseSql = convertMySqlToKingbase(sql);
        assertBasicConversionRequirements(kingbaseSql);
        assertTrue(kingbaseSql.toUpperCase().contains("DROP PRIMARY KEY"), "金仓应包含DROP PRIMARY KEY");
    }

    @Test
    @DisplayName("11-12. ALTER TABLE DROP INDEX - 最终验证")
    void testSql11And12() throws Exception {
        String sql1 = "alter table test.a drop index uniq_user_name;";
        String sql2 = "alter table test.a drop index idx_user_name;";
        
        String damengSql1 = convertMySqlToDameng(sql1);
        assertBasicConversionRequirements(damengSql1);
        assertTrue(damengSql1.contains("DROP INDEX"), "达梦应包含DROP INDEX");
        assertTrue(damengSql1.toUpperCase().contains("UNIQ_USER_NAME") || damengSql1.contains("uniq_user_name"), "达梦索引名应正确转换");

        String damengSql2 = convertMySqlToDameng(sql2);
        assertBasicConversionRequirements(damengSql2);
        assertTrue(damengSql2.toUpperCase().contains("IDX_USER_NAME") || damengSql2.contains("idx_user_name"), "达梦索引名应正确转换");
        
        String kingbaseSql1 = convertMySqlToKingbase(sql1);
        assertBasicConversionRequirements(kingbaseSql1);
        assertTrue(kingbaseSql1.toUpperCase().contains("DROP INDEX"), "金仓应包含DROP INDEX");
        
        String kingbaseSql2 = convertMySqlToKingbase(sql2);
        assertBasicConversionRequirements(kingbaseSql2);
        assertTrue(kingbaseSql2.toUpperCase().contains("DROP INDEX"), "金仓应包含DROP INDEX");
    }

    @Test
    @DisplayName("13. MD5(UUID()) 函数 - 最终验证")
    void testSql13() throws Exception {
        String sql = "SELECT md5(uuid()) as hash_value;";
        
        String damengSql = convertMySqlToDameng(sql);
        assertNotNull(damengSql, "达梦转换结果不应为空");
        assertTrue(damengSql.contains("SELECT"), "达梦应包含SELECT");
        
        String kingbaseSql = convertMySqlToKingbase(sql);
        assertNotNull(kingbaseSql, "金仓转换结果不应为空");
        assertTrue(kingbaseSql.toUpperCase().contains("SELECT"), "金仓应包含SELECT");
    }

    @Test
    @DisplayName("14. SELECT type FROM 语法错误 - 最终验证")
    void testSql14() throws Exception {
        String sql = "select type from;";
        
        // 语法错误应该被适当处理，不应该导致程序崩溃
        try {
            String damengSql = convertMySqlToDameng(sql);
            assertNotNull(damengSql, "达梦转换结果不应为空");
        } catch (Exception e) {
            assertNotNull(e.getMessage(), "异常应该有明确的错误信息");
        }
        
        try {
            String kingbaseSql = convertMySqlToKingbase(sql);
            assertNotNull(kingbaseSql, "金仓转换结果不应为空");
        } catch (Exception e) {
            assertNotNull(e.getMessage(), "异常应该有明确的错误信息");
        }
    }

    @Test
    @DisplayName("15. UNIX_TIMESTAMP(CURRENT_TIMESTAMP()) - 最终验证")
    void testSql15() throws Exception {
        String sql = "SELECT unix_timestamp(current_timestamp()) as timestamp_value;";
        
        String damengSql = convertMySqlToDameng(sql);
        assertNotNull(damengSql, "达梦转换结果不应为空");
        assertTrue(damengSql.contains("SELECT"), "达梦应包含SELECT");
        
        String kingbaseSql = convertMySqlToKingbase(sql);
        assertNotNull(kingbaseSql, "金仓转换结果不应为空");
        assertTrue(kingbaseSql.toUpperCase().contains("SELECT"), "金仓应包含SELECT");
    }

    @Test
    @DisplayName("16. FROM_UNIXTIME(1679564628) - 最终验证")
    void testSql16() throws Exception {
        String sql = "SELECT from_unixtime(1679564628) as datetime_value;";
        
        String damengSql = convertMySqlToDameng(sql);
        assertNotNull(damengSql, "达梦转换结果不应为空");
        assertTrue(damengSql.contains("SELECT"), "达梦应包含SELECT");
        assertTrue(damengSql.contains("1679564628"), "达梦应包含时间戳数值");
        
        String kingbaseSql = convertMySqlToKingbase(sql);
        assertNotNull(kingbaseSql, "金仓转换结果不应为空");
        assertTrue(kingbaseSql.toUpperCase().contains("SELECT"), "金仓应包含SELECT");
        assertTrue(kingbaseSql.contains("1679564628"), "金仓应包含时间戳数值");
    }

    @Test
    @DisplayName("17. 复杂DELETE语句 - 最终验证")
    void testSql17() throws Exception {
        String sql = "DELETE FROM datareal.call_collection WHERE timestamp<unix_timestamp(DATE_ADD(now(),INTERVAL -3 day))*1000;";
        
        String damengSql = convertMySqlToDameng(sql);
        assertNotNull(damengSql, "达梦转换结果不应为空");
        assertTrue(damengSql.contains("DELETE FROM"), "达梦应包含DELETE FROM");
        assertTrue(damengSql.contains("WHERE"), "达梦应包含WHERE子句");
        assertTrue(damengSql.contains("1000"), "达梦应包含乘数");
        
        String kingbaseSql = convertMySqlToKingbase(sql);
        assertNotNull(kingbaseSql, "金仓转换结果不应为空");
        assertTrue(kingbaseSql.toUpperCase().contains("DELETE FROM"), "金仓应包含DELETE FROM");
        assertTrue(kingbaseSql.toUpperCase().contains("WHERE"), "金仓应包含WHERE子句");
        assertTrue(kingbaseSql.contains("1000"), "金仓应包含乘数");
    }
}
