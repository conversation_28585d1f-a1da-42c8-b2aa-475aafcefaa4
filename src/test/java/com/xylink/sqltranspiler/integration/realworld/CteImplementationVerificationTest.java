package com.xylink.sqltranspiler.integration.realworld;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator;
import com.xylink.sqltranspiler.core.dialects.shentong.ShentongGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;

/**
 * 验证金仓和神通数据库的CTE实现是否正确
 * 参考文档：
 * - 金仓：https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
 * - 神通：shentong.md
 * - MySQL：https://dev.mysql.com/doc/refman/8.4/en/with.html
 * - 达梦：https://eco.dameng.com/document/dm/zh-cn/sql-dev/advanced-hierarchical-query.html
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
public class CteImplementationVerificationTest {

    private DamengGenerator damengGenerator;
    private KingbaseGenerator kingbaseGenerator;
    private ShentongGenerator shentongGenerator;

    @BeforeEach
    void setUp() {
        damengGenerator = new DamengGenerator();
        kingbaseGenerator = new KingbaseGenerator();
        shentongGenerator = new ShentongGenerator();
    }

    @Test
    @DisplayName("验证简单CTE在各数据库中的实现")
    void testSimpleCteImplementation() {
        // 根据MySQL官方文档的标准CTE语法
        String sql = """
            WITH user_stats AS (
                SELECT department_id, COUNT(*) as emp_count, AVG(salary) as avg_salary
                FROM employees
                GROUP BY department_id
            )
            SELECT d.department_name, us.emp_count, us.avg_salary
            FROM departments d
            JOIN user_stats us ON d.department_id = us.department_id
            WHERE us.emp_count > 5;
            """;

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement, "CTE语句应该能够被解析");

        // 测试达梦数据库 - 应该支持
        String damengResult = damengGenerator.generate(statement);
        assertNotNull(damengResult, "达梦数据库应该能够生成SQL");
        assertTrue(damengResult.contains("WITH") || damengResult.contains("-- Failed"), 
                  "达梦数据库应该支持CTE或有明确的失败说明");
        
        if (damengResult.contains("WITH")) {
            System.out.println("✅ 达梦数据库支持CTE：" + damengResult.substring(0, Math.min(100, damengResult.length())) + "...");
        } else {
            System.out.println("❌ 达梦数据库CTE转换失败：" + damengResult);
        }

        // 测试金仓数据库 - 根据官方文档应该支持
        String kingbaseResult = kingbaseGenerator.generate(statement);
        assertNotNull(kingbaseResult, "金仓数据库应该能够生成SQL");
        assertTrue(kingbaseResult.contains("WITH") || kingbaseResult.contains("-- Unsupported"), 
                  "根据金仓官方文档，应该支持WITH AS语句");
        
        if (kingbaseResult.contains("WITH")) {
            System.out.println("✅ 金仓数据库支持CTE：" + kingbaseResult.substring(0, Math.min(100, kingbaseResult.length())) + "...");
        } else {
            System.out.println("❌ 金仓数据库CTE转换失败：" + kingbaseResult);
        }

        // 测试神通数据库 - 实际测试发现神通数据库支持CTE
        String shentongResult = shentongGenerator.generate(statement);
        assertNotNull(shentongResult, "神通数据库应该能够生成SQL");
        assertTrue(shentongResult.contains("WITH") || shentongResult.contains("-- CTE") || shentongResult.contains("不支持"),
                  "神通数据库应该支持CTE或明确说明不支持");

        if (shentongResult.contains("WITH")) {
            System.out.println("✅ 神通数据库支持CTE：" + shentongResult.substring(0, Math.min(100, shentongResult.length())) + "...");
        } else {
            System.out.println("⚠️ 神通数据库不支持CTE：" + shentongResult.substring(0, Math.min(100, shentongResult.length())) + "...");
        }
    }

    @Test
    @DisplayName("验证递归CTE在各数据库中的实现")
    void testRecursiveCteImplementation() {
        // 根据MySQL官方文档的递归CTE语法
        String sql = """
            WITH RECURSIVE employee_hierarchy AS (
                SELECT id, name, manager_id, 1 as level
                FROM employees
                WHERE manager_id IS NULL
                UNION ALL
                SELECT e.id, e.name, e.manager_id, eh.level + 1
                FROM employees e
                INNER JOIN employee_hierarchy eh ON e.manager_id = eh.id
            )
            SELECT * FROM employee_hierarchy ORDER BY level, name;
            """;

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement, "递归CTE语句应该能够被解析");

        // 测试达梦数据库 - 应该支持或转换为CONNECT BY
        String damengResult = damengGenerator.generate(statement);
        assertNotNull(damengResult, "达梦数据库应该能够生成SQL");
        assertTrue(damengResult.contains("WITH") || damengResult.contains("CONNECT BY") ||
                   damengResult.contains("-- Failed"),
                  "达梦数据库应该支持递归CTE或转换为CONNECT BY语法");

        if (damengResult.contains("WITH RECURSIVE")) {
            System.out.println("✅ 达梦数据库支持递归CTE：" + damengResult.substring(0, Math.min(100, damengResult.length())) + "...");
        } else if (damengResult.contains("WITH")) {
            System.out.println("✅ 达梦数据库支持CTE（可能不支持RECURSIVE关键字）：" + damengResult.substring(0, Math.min(100, damengResult.length())) + "...");
        } else if (damengResult.contains("CONNECT BY")) {
            System.out.println("✅ 达梦数据库将递归CTE转换为CONNECT BY：" + damengResult.substring(0, Math.min(100, damengResult.length())) + "...");
        } else {
            System.out.println("❌ 达梦数据库递归CTE转换失败：" + damengResult);
        }

        // 测试金仓数据库 - 根据官方文档应该支持
        String kingbaseResult = kingbaseGenerator.generate(statement);
        assertNotNull(kingbaseResult, "金仓数据库应该能够生成SQL");
        assertTrue(kingbaseResult.contains("WITH RECURSIVE") || kingbaseResult.contains("-- Unsupported"), 
                  "根据金仓官方文档，应该支持WITH AS语句（包括递归）");
        
        if (kingbaseResult.contains("WITH RECURSIVE")) {
            System.out.println("✅ 金仓数据库支持递归CTE：" + kingbaseResult.substring(0, Math.min(100, kingbaseResult.length())) + "...");
        } else {
            System.out.println("❌ 金仓数据库递归CTE转换失败：" + kingbaseResult);
        }

        // 测试神通数据库 - 实际测试发现神通数据库支持CTE
        String shentongResult = shentongGenerator.generate(statement);
        assertNotNull(shentongResult, "神通数据库应该能够生成SQL");
        assertTrue(shentongResult.contains("WITH") || shentongResult.contains("-- CTE") || shentongResult.contains("不支持"),
                  "神通数据库应该支持CTE或明确说明不支持递归CTE");

        if (shentongResult.contains("WITH")) {
            System.out.println("✅ 神通数据库支持递归CTE：" + shentongResult.substring(0, Math.min(100, shentongResult.length())) + "...");
        } else {
            System.out.println("⚠️ 神通数据库不支持递归CTE：" + shentongResult.substring(0, Math.min(100, shentongResult.length())) + "...");
        }
    }

    @Test
    @DisplayName("验证多个CTE在各数据库中的实现")
    void testMultipleCteImplementation() {
        // 根据MySQL官方文档的多个CTE语法
        String sql = """
            WITH 
            sales_summary AS (
                SELECT region, SUM(amount) as total_sales
                FROM sales
                GROUP BY region
            ),
            top_regions AS (
                SELECT region, total_sales
                FROM sales_summary
                WHERE total_sales > 100000
            )
            SELECT r.region, r.total_sales, e.emp_count
            FROM top_regions r
            LEFT JOIN (
                SELECT region, COUNT(*) as emp_count
                FROM employees
                GROUP BY region
            ) e ON r.region = e.region;
            """;

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement, "多个CTE语句应该能够被解析");

        // 测试达梦数据库
        String damengResult = damengGenerator.generate(statement);
        assertNotNull(damengResult, "达梦数据库应该能够生成SQL");
        System.out.println("达梦数据库多个CTE结果：" + damengResult.substring(0, Math.min(150, damengResult.length())) + "...");

        // 测试金仓数据库
        String kingbaseResult = kingbaseGenerator.generate(statement);
        assertNotNull(kingbaseResult, "金仓数据库应该能够生成SQL");
        System.out.println("金仓数据库多个CTE结果：" + kingbaseResult.substring(0, Math.min(150, kingbaseResult.length())) + "...");

        // 测试神通数据库
        String shentongResult = shentongGenerator.generate(statement);
        assertNotNull(shentongResult, "神通数据库应该能够生成SQL");
        System.out.println("神通数据库多个CTE结果：" + shentongResult.substring(0, Math.min(150, shentongResult.length())) + "...");
    }
}
