package com.xylink.sqltranspiler.integration.conversion.kingbase;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.List;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.shared.base.BaseKingbaseConversionTest;
import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;

/**
 * 金仓转换集成测试
 * 测试完整的MySQL到金仓转换流程
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
@DisplayName("金仓转换集成测试")
public class KingbaseConversionIntegrationTest extends BaseKingbaseConversionTest {

    @Test
    @DisplayName("测试完整的用户表转换场景")
    void testCompleteUserTableConversion() throws Exception {
        String mysqlSql = """
            -- Test MySQL to KingbaseES conversion
            CREATE TABLE `users` (
                `id` INT AUTO_INCREMENT PRIMARY KEY,
                `username` VARCHAR(100) NOT NULL,
                `email` VARCHAR(255) UNIQUE,
                `password_hash` VARCHAR(255) NOT NULL,
                `first_name` VARCHAR(50),
                `last_name` VARCHAR(50),
                `date_of_birth` DATE,
                `is_active` TINYINT(1) DEFAULT 1,
                `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            
            INSERT INTO `users` (`username`, `email`, `password_hash`, `first_name`, `last_name`, `is_active`) VALUES
            ('john_doe', '<EMAIL>', 'hashed_password_1', 'John', 'Doe', 1),
            ('jane_smith', '<EMAIL>', 'hashed_password_2', 'Jane', 'Smith', 1);
            """;
        
        List<Statement> statements = MySqlHelper.parseMultiStatement(mysqlSql);
        assertEquals(2, statements.size());
        
        StringBuilder result = new StringBuilder();
        for (Statement statement : statements) {
            result.append(generator.generate(statement)).append("\n");
        }
        
        String kingbaseSql = result.toString();
        
        // 验证CREATE TABLE转换
        assertCreateTableConversion(kingbaseSql);
        assertTrue(kingbaseSql.contains("SERIAL"), "应包含SERIAL类型");
        assertTrue(kingbaseSql.contains("BOOLEAN"), "TINYINT(1)应转换为BOOLEAN");
        assertFalse(kingbaseSql.contains("AUTO_INCREMENT"), "不应包含AUTO_INCREMENT");
        assertFalse(kingbaseSql.contains("ENGINE="), "不应包含ENGINE选项");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
        
        // 验证INSERT语句转换
        assertInsertStatementConversion(kingbaseSql);
        assertTrue(kingbaseSql.contains("\"users\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("\"username\""), "列名应使用双引号");
    }

    @Test
    @DisplayName("测试电商订单系统转换场景")
    void testEcommerceOrderSystemConversion() throws Exception {
        String mysqlSql = """
            CREATE TABLE `products` (
                `id` INT AUTO_INCREMENT PRIMARY KEY,
                `name` VARCHAR(255) NOT NULL,
                `description` TEXT,
                `price` DECIMAL(10,2) NOT NULL,
                `stock_quantity` INT DEFAULT 0,
                `category_id` INT,
                `is_active` TINYINT(1) DEFAULT 1,
                `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

            CREATE TABLE `orders` (
                `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
                `user_id` INT NOT NULL,
                `total_amount` DECIMAL(12,2) NOT NULL,
                `status` ENUM('pending', 'processing', 'shipped', 'delivered', 'cancelled') DEFAULT 'pending',
                `order_date` DATETIME DEFAULT CURRENT_TIMESTAMP,
                `shipping_address` JSON
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

            INSERT INTO `products` (`name`, `price`, `stock_quantity`) VALUES
            ('Laptop Computer', 999.99, 50),
            ('Wireless Mouse', 29.99, 200);
            """;
        
        String kingbaseSql = convertMySqlToKingbase(mysqlSql);
        
        // 验证多表转换
        assertTrue(kingbaseSql.contains("\"products\""), "应包含products表");
        assertTrue(kingbaseSql.contains("\"orders\""), "应包含orders表");
        
        // 验证数据类型转换
        assertTrue(kingbaseSql.contains("SERIAL"), "INT AUTO_INCREMENT应转换为SERIAL");
        assertTrue(kingbaseSql.contains("BIGSERIAL"), "BIGINT AUTO_INCREMENT应转换为BIGSERIAL");
        assertTrue(kingbaseSql.contains("BOOLEAN"), "TINYINT(1)应转换为BOOLEAN");
        assertTrue(kingbaseSql.contains("ENUM"), "应支持ENUM类型");
        assertTrue(kingbaseSql.contains("JSON"), "应支持JSON类型");
        
        // 验证MySQL特有语法被移除
        assertFalse(kingbaseSql.contains("AUTO_INCREMENT"), "不应包含AUTO_INCREMENT");
        assertFalse(kingbaseSql.contains("ENGINE="), "不应包含ENGINE选项");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
        
        // 验证INSERT语句
        assertTrue(kingbaseSql.contains("INSERT INTO"), "应包含INSERT语句");
    }

    @Test
    @DisplayName("测试复杂查询转换场景")
    void testComplexQueryConversion() throws Exception {
        String mysqlSql = """
            SELECT 
                `u`.`id`,
                `u`.`username`,
                `u`.`email`,
                COUNT(`o`.`id`) as order_count,
                SUM(`o`.`total_amount`) as total_spent,
                AVG(`o`.`total_amount`) as avg_order_value,
                MAX(`o`.`order_date`) as last_order_date,
                IFNULL(`u`.`first_name`, 'Unknown') as display_name,
                CASE 
                    WHEN COUNT(`o`.`id`) > 10 THEN 'VIP'
                    WHEN COUNT(`o`.`id`) > 5 THEN 'Regular'
                    ELSE 'New'
                END as customer_tier,
                NOW() as report_generated_at
            FROM `users` `u`
            LEFT JOIN `orders` `o` ON `u`.`id` = `o`.`user_id`
            WHERE `u`.`is_active` = 1
            GROUP BY `u`.`id`, `u`.`username`, `u`.`email`, `u`.`first_name`
            HAVING COUNT(`o`.`id`) > 0
            ORDER BY total_spent DESC, `u`.`username` ASC
            LIMIT 100;
            """;
        
        String kingbaseSql = convertMySqlToKingbase(mysqlSql);
        
        assertSelectStatementConversion(kingbaseSql);
        
        // 验证复杂查询转换
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
        assertTrue(kingbaseSql.contains("\"users\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("\"orders\""), "表名应使用双引号");
        
        // 验证函数转换
        assertTrue(kingbaseSql.contains("COALESCE("), "IFNULL应转换为COALESCE");
        assertTrue(kingbaseSql.contains("NOW()"), "应支持NOW()函数");
        
        // 验证聚合函数
        assertTrue(kingbaseSql.contains("COUNT("), "应支持COUNT函数");
        assertTrue(kingbaseSql.contains("SUM("), "应支持SUM函数");
        assertTrue(kingbaseSql.contains("AVG("), "应支持AVG函数");
        assertTrue(kingbaseSql.contains("MAX("), "应支持MAX函数");
        
        // 验证SQL结构
        assertTrue(kingbaseSql.contains("LEFT JOIN"), "应支持LEFT JOIN");
        assertTrue(kingbaseSql.contains("GROUP BY"), "应支持GROUP BY");
        assertTrue(kingbaseSql.contains("HAVING"), "应支持HAVING");
        assertTrue(kingbaseSql.contains("ORDER BY"), "应支持ORDER BY");
        assertTrue(kingbaseSql.contains("LIMIT"), "应支持LIMIT");
    }

    @Test
    @DisplayName("测试数据库和索引管理转换")
    void testDatabaseAndIndexManagementConversion() throws Exception {
        String mysqlSql = """
            CREATE DATABASE `test_db` DEFAULT CHARSET=utf8mb4;
            
            USE `test_db`;
            
            CREATE INDEX `idx_user_email` ON `users` (`email`);
            CREATE UNIQUE INDEX `idx_user_username` ON `users` (`username`);
            
            DROP INDEX `idx_old_index` ON `users`;
            DROP TABLE IF EXISTS `temp_table`;
            """;
        
        String kingbaseSql = convertMySqlToKingbase(mysqlSql);
        
        assertBasicConversionRequirements(kingbaseSql);
        
        // 验证数据库操作
        assertTrue(kingbaseSql.contains("CREATE DATABASE"), "应支持CREATE DATABASE");
        assertTrue(kingbaseSql.contains("USE"), "应支持USE语句");
        
        // 验证索引操作
        assertTrue(kingbaseSql.contains("CREATE INDEX"), "应支持CREATE INDEX");
        assertTrue(kingbaseSql.contains("CREATE UNIQUE INDEX"), "应支持CREATE UNIQUE INDEX");
        
        // 验证DROP操作
        assertTrue(kingbaseSql.contains("DROP TABLE IF EXISTS"), "应支持DROP TABLE IF EXISTS");
        
        // 验证标识符转换
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
        assertTrue(kingbaseSql.contains("\"test_db\""), "数据库名应使用双引号");
        assertTrue(kingbaseSql.contains("\"users\""), "表名应使用双引号");
    }

    @Test
    @DisplayName("测试事务和存储过程转换")
    void testTransactionAndProcedureConversion() throws Exception {
        String mysqlSql = """
            START TRANSACTION;
            
            UPDATE `users` SET `last_login` = NOW() WHERE `id` = 1;
            INSERT INTO `user_logs` (`user_id`, `action`, `timestamp`) VALUES (1, 'login', NOW());
            
            COMMIT;
            """;
        
        String kingbaseSql = convertMySqlToKingbase(mysqlSql);
        
        assertBasicConversionRequirements(kingbaseSql);
        
        // 验证事务语句
        assertTrue(kingbaseSql.contains("START TRANSACTION") || kingbaseSql.contains("BEGIN"), 
                   "应支持事务开始语句");
        assertTrue(kingbaseSql.contains("COMMIT"), "应支持COMMIT语句");
        
        // 验证UPDATE和INSERT语句
        assertTrue(kingbaseSql.contains("UPDATE"), "应支持UPDATE语句");
        assertTrue(kingbaseSql.contains("INSERT INTO"), "应支持INSERT语句");
        
        // 验证函数转换
        assertTrue(kingbaseSql.contains("NOW()"), "应支持NOW()函数");
        
        // 验证标识符转换
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试特殊字符和转义的处理")
    void testSpecialCharactersAndEscaping() throws Exception {
        String mysqlSql = """
            CREATE TABLE `special_chars` (
                `field-with-dash` VARCHAR(100),
                `field with space` VARCHAR(100),
                `field_with_underscore` VARCHAR(100),
                `123numeric_start` VARCHAR(100),
                `中文字段` VARCHAR(100),
                `emoji_field` VARCHAR(100)
            );

            INSERT INTO `special_chars` VALUES
            ('Value with ''single quotes''', 'Value with "double quotes"', 'Normal value', '123', '中文值', '😀');
            """;

        String kingbaseSql = convertMySqlToKingbase(mysqlSql);

        // 验证特殊字段名转换（根据金仓官方文档，使用PostgreSQL标准双引号）
        assertTrue(kingbaseSql.contains("\"field-with-dash\""), "应正确处理带连字符的字段名");
        assertTrue(kingbaseSql.contains("\"field with space\""), "应正确处理带空格的字段名");
        assertTrue(kingbaseSql.contains("\"field_with_underscore\""), "应正确处理带下划线的字段名");
        assertTrue(kingbaseSql.contains("\"123numeric_start\""), "应正确处理数字开头的字段名");
        assertTrue(kingbaseSql.contains("\"中文字段\""), "应正确处理中文字段名");
        assertTrue(kingbaseSql.contains("\"emoji_field\""), "应正确处理emoji字段名");

        // 验证特殊值转换
        assertTrue(kingbaseSql.contains("'Value with ''single quotes'''"), "应正确转义单引号");
        assertTrue(kingbaseSql.contains("'Value with \"double quotes\"'"), "应正确处理双引号");
        assertTrue(kingbaseSql.contains("'中文值'"), "应正确处理中文值");
        assertTrue(kingbaseSql.contains("'😀'"), "应正确处理emoji值");
    }
}
