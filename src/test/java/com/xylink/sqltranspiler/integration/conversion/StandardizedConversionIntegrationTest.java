package com.xylink.sqltranspiler.integration.conversion;

import static org.junit.jupiter.api.Assertions.*;

import java.util.Map;
import java.util.HashMap;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInfo;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.MethodOrderer.OrderAnnotation;
import org.junit.jupiter.api.Order;

import com.xylink.sqltranspiler.core.dialects.DamengDialect;
import com.xylink.sqltranspiler.core.dialects.KingbaseDialect;
import com.xylink.sqltranspiler.core.dialects.ShentongDialect;
import com.xylink.sqltranspiler.core.dialects.SqlDialect;
import com.xylink.sqltranspiler.infrastructure.util.SqlConversionUtils;
import com.xylink.sqltranspiler.infrastructure.formatter.SqlFormatter;

/**
 * 标准化转换集成测试
 * 遵循 .augment/rules/rule-db.md 和 .augment/rules/rule-test.md：
 * - 基于官方文档的准确转换测试
 * - 动态验证机制
 * - 质量反馈机制
 * - 测试驱动开发
 * 测试范围：
 * - 跨方言转换一致性
 * - SqlConversionUtils集成
 * - SqlFormatter增强功能
 * - 端到端转换流程
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
@TestMethodOrder(OrderAnnotation.class)
@DisplayName("标准化转换集成测试")
public class StandardizedConversionIntegrationTest {
    
    private Map<String, SqlDialect> dialects;
    
    @BeforeEach
    void setUp() {
        dialects = new HashMap<>();
        dialects.put("dameng", new DamengDialect());
        dialects.put("kingbase", new KingbaseDialect());
        dialects.put("shentong", new ShentongDialect());
    }
    
    @Test
    @Order(1)
    @DisplayName("跨方言标识符引用一致性测试")
    void testCrossDialectIdentifierQuoting(TestInfo testInfo) {
        System.out.println("\n=== " + testInfo.getDisplayName() + " ===");
        
        String[] testIdentifiers = {
            "user_table",    // 普通标识符
            "SELECT",        // 保留字
            "UserTable",     // 混合大小写
            "user-table",    // 特殊字符
            "123invalid"     // 无效标识符
        };
        
        for (String identifier : testIdentifiers) {
            System.out.printf("测试标识符: %s%n", identifier);
            
            for (Map.Entry<String, SqlDialect> entry : dialects.entrySet()) {
                String dialectName = entry.getKey();
                SqlDialect dialect = entry.getValue();
                
                String quoted = dialect.quoteIdentifier(identifier);
                assertNotNull(quoted, dialectName + "方言标识符引用不应为null");
                
                // 验证引用逻辑一致性
                boolean needsQuoting = dialect.requiresQuoting(identifier);
                if (needsQuoting) {
                    assertTrue(quoted.contains("\""), 
                        dialectName + "方言需要引用的标识符应包含双引号: " + quoted);
                }
                
                System.out.printf("  %s: %s (需要引用: %s)%n", 
                    dialectName, quoted, needsQuoting);
            }
        }
        
        System.out.println("✅ 跨方言标识符引用一致性测试通过");
    }
    
    @Test
    @Order(2)
    @DisplayName("跨方言数据类型映射一致性测试")
    void testCrossDialectDataTypeMapping(TestInfo testInfo) {
        System.out.println("\n=== " + testInfo.getDisplayName() + " ===");
        
        String[] mysqlTypes = {
            "VARCHAR", "TEXT", "INT", "BIGINT", "DATETIME", 
            "TIMESTAMP", "JSON", "BLOB", "DECIMAL"
        };
        
        for (String mysqlType : mysqlTypes) {
            System.out.printf("测试MySQL类型: %s%n", mysqlType);
            
            for (Map.Entry<String, SqlDialect> entry : dialects.entrySet()) {
                String dialectName = entry.getKey();
                SqlDialect dialect = entry.getValue();
                
                String mappedType = dialect.mapDataType(mysqlType, null, null, null);
                assertNotNull(mappedType, dialectName + "方言类型映射不应为null");
                assertFalse(mappedType.trim().isEmpty(), 
                    dialectName + "方言类型映射不应为空");
                
                // 验证类型支持
                assertTrue(dialect.supportsDataType(mysqlType), 
                    dialectName + "方言应支持MySQL类型: " + mysqlType);
                
                System.out.printf("  %s: %s%n", dialectName, mappedType);
            }
        }
        
        System.out.println("✅ 跨方言数据类型映射一致性测试通过");
    }
    
    @Test
    @Order(3)
    @DisplayName("跨方言函数映射一致性测试")
    void testCrossDialectFunctionMapping(TestInfo testInfo) {
        System.out.println("\n=== " + testInfo.getDisplayName() + " ===");
        
        String[] mysqlFunctions = {
            "NOW", "CURDATE", "CURTIME", "CONCAT", "LENGTH", 
            "SUBSTRING", "IFNULL", "RAND", "ABS", "UPPER"
        };
        
        for (String mysqlFunction : mysqlFunctions) {
            System.out.printf("测试MySQL函数: %s%n", mysqlFunction);
            
            for (Map.Entry<String, SqlDialect> entry : dialects.entrySet()) {
                String dialectName = entry.getKey();
                SqlDialect dialect = entry.getValue();
                
                String mappedFunction = dialect.mapFunction(mysqlFunction);
                assertNotNull(mappedFunction, dialectName + "方言函数映射不应为null");
                assertFalse(mappedFunction.trim().isEmpty(), 
                    dialectName + "方言函数映射不应为空");
                
                // 验证函数支持
                assertTrue(dialect.supportsFunction(mysqlFunction), 
                    dialectName + "方言应支持MySQL函数: " + mysqlFunction);
                
                System.out.printf("  %s: %s%n", dialectName, mappedFunction);
            }
        }
        
        System.out.println("✅ 跨方言函数映射一致性测试通过");
    }
    
    @Test
    @Order(4)
    @DisplayName("SqlConversionUtils集成测试")
    void testSqlConversionUtilsIntegration(TestInfo testInfo) {
        System.out.println("\n=== " + testInfo.getDisplayName() + " ===");
        
        String testSql = "SELECT NOW(), CURDATE(), IFNULL(name, 'Unknown'), " +
                        "SUBSTRING(email, 1, 10) FROM users WHERE id > 0";
        
        System.out.printf("原始SQL: %s%n", testSql);
        
        for (Map.Entry<String, SqlDialect> entry : dialects.entrySet()) {
            String dialectName = entry.getKey();
            SqlDialect dialect = entry.getValue();
            
            // 测试函数转换
            String convertedSql = SqlConversionUtils.convertFunctionsWithMapper(testSql, dialect);
            assertNotNull(convertedSql, dialectName + "转换结果不应为null");
            assertFalse(convertedSql.trim().isEmpty(), dialectName + "转换结果不应为空");
            
            // 验证函数转换效果
            if ("dameng".equals(dialectName)) {
                assertTrue(convertedSql.contains("SYSDATE") || convertedSql.contains("NOW"), 
                    "达梦转换应包含时间函数");
            } else if ("kingbase".equals(dialectName)) {
                assertTrue(convertedSql.contains("NOW") || convertedSql.contains("CURRENT"), 
                    "金仓转换应包含时间函数");
            } else if ("shentong".equals(dialectName)) {
                assertTrue(convertedSql.contains("CURRENT_TIMESTAMP") || convertedSql.contains("NOW"), 
                    "神通转换应包含时间函数");
            }
            
            System.out.printf("  %s转换: %s%n", dialectName, convertedSql);
        }
        
        System.out.println("✅ SqlConversionUtils集成测试通过");
    }
    
    @Test
    @Order(5)
    @DisplayName("SqlFormatter增强功能测试")
    void testSqlFormatterEnhancement(TestInfo testInfo) {
        System.out.println("\n=== " + testInfo.getDisplayName() + " ===");
        
        String testSql = "SELECT NOW(),CURDATE(),SUBSTRING(name,1,10) FROM users";
        
        System.out.printf("原始SQL: %s%n", testSql);
        
        // 测试标准格式化
        String formattedSql = SqlFormatter.format(testSql);
        assertNotNull(formattedSql, "格式化结果不应为null");
        System.out.printf("标准格式化: %s%n", formattedSql);
        
        // 测试增强格式化（带函数转换）
        for (Map.Entry<String, SqlDialect> entry : dialects.entrySet()) {
            String dialectName = entry.getKey();
            SqlDialect dialect = entry.getValue();
            
            String enhancedFormatted = SqlFormatter.formatWithFunctionMapping(testSql, dialect);
            assertNotNull(enhancedFormatted, dialectName + "增强格式化结果不应为null");
            
            // 验证格式化和函数转换都生效
            assertNotEquals(testSql, enhancedFormatted, 
                dialectName + "增强格式化应该改变原始SQL");
            
            System.out.printf("  %s增强格式化: %s%n", dialectName, enhancedFormatted);
        }
        
        System.out.println("✅ SqlFormatter增强功能测试通过");
    }
    
    @Test
    @Order(6)
    @DisplayName("端到端转换流程测试")
    void testEndToEndConversionPipeline(TestInfo testInfo) {
        System.out.println("\n=== " + testInfo.getDisplayName() + " ===");
        
        String complexSql = """
            CREATE TABLE users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                email TEXT,
                created_at DATETIME DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            INSERT INTO users (name, email) VALUES ('John', '<EMAIL>');
            
            SELECT IFNULL(name, 'Unknown') AS safe_name,
                   CONCAT(name, ' - ', email) AS display_name,
                   SUBSTRING(email, 1, 10) AS email_prefix,
                   NOW() AS query_time
            FROM users
            WHERE LENGTH(name) > 0
            ORDER BY created_at DESC
            LIMIT 10 OFFSET 0;
            """;
        
        System.out.println("复杂SQL端到端转换测试:");
        
        for (Map.Entry<String, SqlDialect> entry : dialects.entrySet()) {
            String dialectName = entry.getKey();
            SqlDialect dialect = entry.getValue();
            
            try {
                // 1. 函数转换
                String functionConverted = SqlConversionUtils.convertFunctionsWithMapper(complexSql, dialect);
                
                // 2. 格式化
                String formatted = SqlFormatter.formatWithFunctionMapping(functionConverted, dialect);
                
                // 3. 验证转换结果
                assertNotNull(formatted, dialectName + "端到端转换结果不应为null");
                assertFalse(formatted.trim().isEmpty(), dialectName + "端到端转换结果不应为空");
                
                // 4. 验证包含基本SQL结构
                assertTrue(formatted.contains("CREATE TABLE"), dialectName + "应包含CREATE TABLE");
                assertTrue(formatted.contains("INSERT INTO"), dialectName + "应包含INSERT INTO");
                assertTrue(formatted.contains("SELECT"), dialectName + "应包含SELECT");
                
                System.out.printf("✅ %s端到端转换成功%n", dialectName);
                
            } catch (Exception e) {
                fail(dialectName + "端到端转换失败: " + e.getMessage());
            }
        }
        
        System.out.println("✅ 端到端转换流程测试通过");
    }
    
    @Test
    @Order(7)
    @DisplayName("转换性能基准测试")
    void testConversionPerformanceBenchmark(TestInfo testInfo) {
        System.out.println("\n=== " + testInfo.getDisplayName() + " ===");
        
        String testSql = "SELECT NOW(), IFNULL(name, 'test'), SUBSTRING(email, 1, 10) FROM users";
        
        for (Map.Entry<String, SqlDialect> entry : dialects.entrySet()) {
            String dialectName = entry.getKey();
            SqlDialect dialect = entry.getValue();
            
            long startTime = System.nanoTime();
            
            // 执行100次转换
            for (int i = 0; i < 100; i++) {
                SqlConversionUtils.convertFunctionsWithMapper(testSql, dialect);
                SqlFormatter.formatWithFunctionMapping(testSql, dialect);
            }
            
            long endTime = System.nanoTime();
            long duration = (endTime - startTime) / 1_000_000; // 转换为毫秒
            
            assertTrue(duration < 1000, 
                dialectName + "100次转换应在1秒内完成，实际: " + duration + "ms");
            
            System.out.printf("✅ %s性能: 100次转换耗时 %d ms%n", dialectName, duration);
        }
        
        System.out.println("✅ 转换性能基准测试通过");
    }
}
