package com.xylink.sqltranspiler.integration.conversion.shentong;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.shared.base.BaseTranspilerTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 神通数据库分区表转换测试
 * 严格遵循神通数据库官方文档：
 * - 神通数据库官方文档：@shentong.md
 * - 神通数据库分区表支持文档
 * 神通数据库支持分区表：
 * 1. RANGE分区 - 支持，基于Oracle兼容语法
 * 2. LIST分区 - 支持，基于Oracle兼容语法
 * 3. HASH分区 - 支持，基于Oracle兼容语法
 * 测试策略：
 * 1. 验证MySQL分区语法正确转换为神通兼容语法
 * 2. 验证YEAR()函数处理
 * 3. 验证AUTO_INCREMENT转换
 * 4. 验证标识符处理（神通标准）
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
@DisplayName("神通数据库分区表转换测试")
public class ShentongPartitionTableTest extends BaseTranspilerTest {

    @Test
    @DisplayName("测试范围分区表转换 - 基于神通官方文档")
    void testRangePartitionByDate() {
        // 基于MySQL官方文档的RANGE分区示例
        // https://dev.mysql.com/doc/refman/8.4/en/partitioning-range.html
        String mysqlSql = """
            CREATE TABLE sales_data (
                id INT AUTO_INCREMENT PRIMARY KEY,
                sale_date DATE NOT NULL,
                amount DECIMAL(10,2),
                customer_id INT
            )
            PARTITION BY RANGE (YEAR(sale_date)) (
                PARTITION p2020 VALUES LESS THAN (2021),
                PARTITION p2021 VALUES LESS THAN (2022),
                PARTITION p2022 VALUES LESS THAN (2023),
                PARTITION pmax VALUES LESS THAN MAXVALUE
            );
            """;

        String result = convertMySqlTo(mysqlSql, "shentong");

        // 添加调试输出
        System.out.println("神通范围分区测试实际输出:");
        System.out.println(result);

        // 验证基本表结构转换（根据神通官方文档）
        assertTrue(result.contains("CREATE TABLE"), "应包含表名转换");
        assertTrue(result.contains("id") && (result.contains("IDENTITY") || result.contains("AUTO_INCREMENT")),
                  "应转换AUTO_INCREMENT");
        assertTrue(result.contains("sale_date") && result.contains("DATE"), "应保留列定义");
        assertTrue(result.contains("amount") && result.contains("DECIMAL"), "应保留列定义");
        assertTrue(result.contains("customer_id") && result.contains("INT"), "应保留列定义");

        // 验证分区定义转换 - 神通支持范围分区
        assertTrue(result.contains("PARTITION BY RANGE"), "应保留RANGE分区类型");
        assertTrue(result.contains("VALUES LESS THAN"), "应保留分区条件");
    }

    @Test
    @DisplayName("测试哈希分区表转换 - 基于神通官方文档")
    void testHashPartition() {
        String mysqlSql = """
            CREATE TABLE user_data (
                user_id INT PRIMARY KEY,
                username VARCHAR(50) NOT NULL,
                email VARCHAR(100),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            PARTITION BY HASH(user_id)
            PARTITIONS 4;
            """;

        String result = convertMySqlTo(mysqlSql, "shentong");

        // 添加调试输出
        System.out.println("神通哈希分区测试实际输出:");
        System.out.println(result);

        // 验证列定义保留（根据神通官方文档）
        assertTrue(result.contains("user_id") && result.contains("PRIMARY KEY"), "应保留主键列定义");
        assertTrue(result.contains("username") && result.contains("VARCHAR(50)"), "应保留列定义");
        assertTrue(result.contains("email") && result.contains("VARCHAR(100)"), "应保留列定义");
        assertTrue(result.contains("created_at") && result.contains("TIMESTAMP"), "应转换TIMESTAMP");

        // 验证哈希分区转换 - 神通支持哈希分区
        assertTrue(result.contains("PARTITION BY HASH"), "应保留HASH分区类型");
    }

    @Test
    @DisplayName("测试列表分区表转换 - 基于神通官方文档")
    void testListPartition() {
        String mysqlSql = """
            CREATE TABLE employee_data (
                emp_id INT PRIMARY KEY,
                name VARCHAR(100),
                department VARCHAR(20),
                salary DECIMAL(10,2)
            )
            PARTITION BY LIST (department) (
                PARTITION p_sales VALUES IN ('SALES', 'MARKETING'),
                PARTITION p_tech VALUES IN ('IT', 'DEVELOPMENT'),
                PARTITION p_admin VALUES IN ('HR', 'FINANCE'),
                PARTITION p_other VALUES IN (DEFAULT)
            );
            """;

        String result = convertMySqlTo(mysqlSql, "shentong");

        // 添加调试输出
        System.out.println("神通列表分区测试实际输出:");
        System.out.println(result);

        // 验证列定义保留（根据神通官方文档）
        assertTrue(result.contains("emp_id") && result.contains("PRIMARY KEY"), "应保留主键列定义");
        assertTrue(result.contains("name") && result.contains("VARCHAR(100)"), "应保留列定义");
        assertTrue(result.contains("department") && result.contains("VARCHAR(20)"), "应保留列定义");
        assertTrue(result.contains("salary") && result.contains("DECIMAL"), "应保留列定义");

        // 验证列表分区转换 - 神通支持列表分区
        assertTrue(result.contains("PARTITION BY LIST"), "应保留LIST分区类型");
        assertTrue(result.contains("VALUES IN"), "应保留VALUES IN语法");
    }

    @Test
    @DisplayName("测试MySQL官方文档示例：RANGE分区表转换")
    void testMySqlOfficialRangePartitionExample() {
        // 基于MySQL 8.4官方文档的RANGE分区示例
        // https://dev.mysql.com/doc/refman/8.4/en/create-table.html
        String mysqlSql = """
            CREATE TABLE t1 (
                year_col  INT,
                some_data INT
            )
            PARTITION BY RANGE (year_col) (
                PARTITION p0 VALUES LESS THAN (1991),
                PARTITION p1 VALUES LESS THAN (1995),
                PARTITION p2 VALUES LESS THAN (1999),
                PARTITION p3 VALUES LESS THAN (2002),
                PARTITION p4 VALUES LESS THAN (2006),
                PARTITION p5 VALUES LESS THAN MAXVALUE
            );
            """;

        String result = convertMySqlTo(mysqlSql, "shentong");

        // 验证这是正确的MySQL语法，不应被拒绝
        assertNotNull(result, "正确的MySQL RANGE分区语法不应被拒绝");
        assertFalse(result.trim().isEmpty(), "转换结果不应为空");

        // 验证列定义完整性
        assertTrue(result.contains("year_col"), "应包含year_col列定义");
        assertTrue(result.contains("some_data"), "应包含some_data列定义");
        assertTrue(result.contains("INT"), "应包含INT数据类型");
        assertTrue(result.contains("CREATE TABLE"), "应包含CREATE TABLE语句");

        System.out.println("=== MySQL官方RANGE分区示例转换结果（神通） ===");
        System.out.println(result);
    }

    @Test
    @DisplayName("测试MySQL官方文档示例：HASH分区表转换")
    void testMySqlOfficialHashPartitionExample() {
        // 基于MySQL 8.4官方文档的HASH分区示例
        // https://dev.mysql.com/doc/refman/8.4/en/create-table.html
        String mysqlSql = """
            CREATE TABLE t1 (col1 INT, col2 CHAR(5))
                PARTITION BY HASH(col1);
            """;

        String result = convertMySqlTo(mysqlSql, "shentong");

        // 验证这是正确的MySQL语法，不应被拒绝
        assertNotNull(result, "正确的MySQL HASH分区语法不应被拒绝");
        assertFalse(result.trim().isEmpty(), "转换结果不应为空");

        // 验证列定义完整性
        assertTrue(result.contains("col1"), "应包含col1列定义");
        assertTrue(result.contains("col2"), "应包含col2列定义");
        assertTrue(result.contains("INT"), "应包含INT数据类型");
        assertTrue(result.contains("CHAR"), "应包含CHAR数据类型");
        assertTrue(result.contains("CREATE TABLE"), "应包含CREATE TABLE语句");

        System.out.println("=== MySQL官方HASH分区示例转换结果（神通） ===");
        System.out.println(result);
    }

    @Test
    @DisplayName("测试MySQL官方文档示例：LIST分区表转换")
    void testMySqlOfficialListPartitionExample() {
        // 基于MySQL 8.4官方文档的LIST分区示例
        // https://dev.mysql.com/doc/refman/8.4/en/partitioning-list.html
        String mysqlSql = """
            CREATE TABLE client_firms (
                id   INT,
                name VARCHAR(35)
            )
            PARTITION BY LIST (id) (
                PARTITION r0 VALUES IN (1, 5, 9, 13, 17, 21),
                PARTITION r1 VALUES IN (2, 6, 10, 14, 18, 22),
                PARTITION r2 VALUES IN (3, 7, 11, 15, 19, 23),
                PARTITION r3 VALUES IN (4, 8, 12, 16, 20, 24)
            );
            """;

        String result = convertMySqlTo(mysqlSql, "shentong");

        // 验证这是正确的MySQL语法，不应被拒绝
        assertNotNull(result, "正确的MySQL LIST分区语法不应被拒绝");
        assertFalse(result.trim().isEmpty(), "转换结果不应为空");

        // 验证列定义完整性
        assertTrue(result.contains("id"), "应包含id列定义");
        assertTrue(result.contains("name"), "应包含name列定义");
        assertTrue(result.contains("VARCHAR"), "应包含VARCHAR数据类型");
        assertTrue(result.contains("CREATE TABLE"), "应包含CREATE TABLE语句");

        System.out.println("=== MySQL官方LIST分区示例转换结果（神通） ===");
        System.out.println(result);
    }

    @Test
    @DisplayName("双重测试策略：验证MySQL分区表语法正确性和列定义完整性（神通）")
    void testPartitionTableColumnDefinitionsNotLost() {
        // 先测试一个简单的非分区表，确保基本功能正常
        String simpleTable = """
            CREATE TABLE test_simple (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                age INT,
                created_date DATE
            );
            """;

        String simpleResult = convertMySqlTo(simpleTable, "shentong");
        System.out.println("=== 简单表转换结果（神通） ===");
        System.out.println(simpleResult);
        System.out.println("=== 简单表结果结束 ===");

        // 验证简单表转换正常
        assertTrue(simpleResult.contains("id"), "简单表应包含id列定义");
        assertTrue(simpleResult.contains("name") && simpleResult.contains("VARCHAR(100)"),
                  "简单表应包含name列定义");
        assertTrue(simpleResult.contains("PRIMARY KEY"), "简单表应包含主键定义");

        // 再测试分区表，确保分区表的列定义不会丢失
        String partitionTable = """
            CREATE TABLE test_partition (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                age INT,
                created_date DATE
            )
            PARTITION BY RANGE (YEAR(created_date)) (
                PARTITION p1 VALUES LESS THAN (2020),
                PARTITION p2 VALUES LESS THAN (2021),
                PARTITION p3 VALUES LESS THAN MAXVALUE
            );
            """;

        String result = convertMySqlTo(partitionTable, "shentong");

        // 打印实际结果用于调试
        System.out.println("=== 分区表转换结果（神通） ===");
        System.out.println(result);
        System.out.println("=== 分区表结果结束 ===");

        // 这是修复的关键测试 - 确保所有列定义都存在
        assertTrue(result.contains("id"), "修复后应包含id列定义");
        assertTrue(result.contains("name") && result.contains("VARCHAR(100)"), "修复后应包含name列定义");
        assertTrue(result.contains("age") && result.contains("INT"), "修复后应包含age列定义");
        assertTrue(result.contains("created_date") && result.contains("DATE"), "修复后应包含created_date列定义");

        // 验证分区定义存在
        assertTrue(result.contains("PARTITION BY RANGE"), "应包含分区定义");

        // 验证主键定义存在
        assertTrue(result.contains("PRIMARY KEY"), "应包含主键定义");
    }

    @Test
    @DisplayName("测试复杂分区表转换 - 基于神通官方文档")
    void testComplexPartitionTable() {
        String mysqlSql = """
            CREATE TABLE order_details (
                order_id INT AUTO_INCREMENT,
                product_id INT NOT NULL,
                quantity INT DEFAULT 1,
                price DECIMAL(10,2) NOT NULL,
                order_date DATE NOT NULL,
                status ENUM('pending', 'shipped', 'delivered') DEFAULT 'pending',
                PRIMARY KEY (order_id, order_date),
                INDEX idx_product (product_id),
                INDEX idx_date (order_date),
                UNIQUE KEY uk_order_product (order_id, product_id)
            )
            ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            PARTITION BY RANGE (YEAR(order_date)) (
                PARTITION p2023 VALUES LESS THAN (2024),
                PARTITION p2024 VALUES LESS THAN (2025),
                PARTITION pmax VALUES LESS THAN MAXVALUE
            );
            """;

        String result = convertMySqlTo(mysqlSql, "shentong");

        // 添加调试输出
        System.out.println("神通复杂分区表测试实际输出:");
        System.out.println(result);

        // 验证列定义转换（根据神通官方文档）
        assertTrue(result.contains("order_id"), "应转换AUTO_INCREMENT");
        assertTrue(result.contains("product_id") && result.contains("NOT NULL"), "应保留列定义");
        assertTrue(result.contains("quantity") && result.contains("DEFAULT 1"), "应保留默认值");
        assertTrue(result.contains("price") && result.contains("DECIMAL"), "应保留列定义");
        assertTrue(result.contains("order_date") && result.contains("DATE"), "应保留列定义");
        assertTrue(result.contains("status") && result.contains("VARCHAR"), "应转换ENUM类型");

        // 验证主键约束
        assertTrue(result.contains("PRIMARY KEY"), "应保留复合主键");

        // 验证分区定义
        assertTrue(result.contains("PARTITION BY RANGE"), "应保留RANGE分区");
        assertTrue(result.contains("VALUES LESS THAN"), "应保留分区定义");
    }

    @Test
    @DisplayName("测试间隔分区转换 - 基于神通官方文档")
    void testIntervalPartition() {
        // 根据神通数据库官方文档，神通支持Oracle兼容的分区功能
        // 间隔分区是范围分区的扩展，可以自动创建分区
        String mysqlSql = """
            CREATE TABLE log_data (
                log_id INT AUTO_INCREMENT PRIMARY KEY,
                log_message TEXT,
                log_level VARCHAR(10),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            PARTITION BY RANGE (YEAR(created_at)) (
                PARTITION p_before_2024 VALUES LESS THAN (2024),
                PARTITION p_2024 VALUES LESS THAN (2025),
                PARTITION p_future VALUES LESS THAN MAXVALUE
            );
            """;

        String result = convertMySqlTo(mysqlSql, "shentong");

        // 添加调试输出
        System.out.println("神通间隔分区测试实际输出:");
        System.out.println(result);

        // 验证列定义转换（根据神通官方文档）
        assertTrue(result.contains("log_id") && (result.contains("IDENTITY") || result.contains("AUTO_INCREMENT")),
                  "应转换AUTO_INCREMENT");
        assertTrue(result.contains("log_message") && result.contains("TEXT"), "应保留TEXT类型");
        assertTrue(result.contains("log_level") && result.contains("VARCHAR(10)"), "应保留列定义");
        assertTrue(result.contains("created_at") && result.contains("TIMESTAMP"), "应转换TIMESTAMP");

        // 验证分区定义
        assertTrue(result.contains("PARTITION BY RANGE"), "应保留RANGE分区");
        assertTrue(result.contains("p_before_2024"), "应保留分区名");
        assertTrue(result.contains("VALUES LESS THAN"), "应保留分区条件");
        assertTrue(result.contains("MAXVALUE"), "应保留MAXVALUE");
    }
}
