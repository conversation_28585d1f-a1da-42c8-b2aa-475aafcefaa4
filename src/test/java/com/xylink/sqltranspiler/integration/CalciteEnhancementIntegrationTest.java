package com.xylink.sqltranspiler.integration;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInfo;

import com.xylink.sqltranspiler.core.dialects.DamengDialect;
import com.xylink.sqltranspiler.core.dialects.KingbaseDialect;
import com.xylink.sqltranspiler.core.dialects.SqlDialect;
import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator;
import com.xylink.sqltranspiler.core.dialects.Generator;
import com.xylink.sqltranspiler.infrastructure.formatter.SqlFormatter;
import com.xylink.sqltranspiler.infrastructure.util.SqlConversionUtils;

/**
 * Calcite增强功能集成测试
 * 验证借鉴Apache Calcite设计思想的增强功能：
 * 1. SqlDialect接口的标准化方言抽象
 * 2. Generator与方言接口的集成
 * 3. SqlFormatter的函数转换增强
 * 4. SqlConversionUtils的方言集成
 * 遵循 .augment/rules/rule-db.md：
 * - 基于官方文档的准确实现
 * - 动态验证机制
 * - 质量反馈机制
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
public class CalciteEnhancementIntegrationTest {
    
    private SqlDialect damengDialect;
    private SqlDialect kingbaseDialect;
    private Generator damengGenerator;
    private Generator kingbaseGenerator;
    
    @BeforeEach
    void setUp() {
        damengDialect = new DamengDialect();
        kingbaseDialect = new KingbaseDialect();
        damengGenerator = new DamengGenerator();
        kingbaseGenerator = new KingbaseGenerator();
    }
    
    @Test
    void testDialectStandardization(TestInfo testInfo) {
        System.out.println("\n=== " + testInfo.getDisplayName() + " ===");
        
        // 测试方言接口的标准化实现
        System.out.println("测试方言接口标准化:");
        
        // 1. 基础信息验证
        assertNotNull(damengDialect.getName(), "达梦方言名称不能为null");
        assertNotNull(kingbaseDialect.getName(), "金仓方言名称不能为null");
        
        assertEquals("DM", damengDialect.getName(), "达梦方言名称应为DM");
        assertEquals("KingbaseES", kingbaseDialect.getName(), "金仓方言名称应为KingbaseES");
        
        System.out.printf("✅ 方言名称: 达梦=%s, 金仓=%s%n", 
            damengDialect.getName(), kingbaseDialect.getName());
        
        // 2. 标识符引用测试
        String identifier = "SELECT";
        String damengQuoted = damengDialect.quoteIdentifier(identifier);
        String kingbaseQuoted = kingbaseDialect.quoteIdentifier(identifier);
        
        assertTrue(damengQuoted.contains("\""), "达梦应该使用双引号引用保留字");
        assertTrue(kingbaseQuoted.contains("\""), "金仓应该使用双引号引用保留字");
        
        System.out.printf("✅ 标识符引用: 达梦=%s, 金仓=%s%n", damengQuoted, kingbaseQuoted);
        
        // 3. 数据类型映射测试
        String mysqlType = "TEXT";
        String damengType = damengDialect.mapDataType(mysqlType, null, null, null);
        String kingbaseType = kingbaseDialect.mapDataType(mysqlType, null, null, null);
        
        assertEquals("CLOB", damengType, "达梦应该将TEXT映射为CLOB");
        assertEquals("TEXT", kingbaseType, "金仓应该保持TEXT类型");
        
        System.out.printf("✅ 数据类型映射: MySQL TEXT -> 达梦=%s, 金仓=%s%n", 
            damengType, kingbaseType);
        
        // 4. 函数映射测试
        String mysqlFunction = "NOW";
        String damengFunction = damengDialect.mapFunction(mysqlFunction);
        String kingbaseFunction = kingbaseDialect.mapFunction(mysqlFunction);
        
        assertTrue(damengFunction.contains("SYSDATE"), "达梦应该将NOW映射为SYSDATE");
        assertTrue(kingbaseFunction.contains("NOW"), "金仓应该保持NOW函数");
        
        System.out.printf("✅ 函数映射: MySQL NOW -> 达梦=%s, 金仓=%s%n", 
            damengFunction, kingbaseFunction);
    }
    
    @Test
    void testGeneratorDialectIntegration(TestInfo testInfo) {
        System.out.println("\n=== " + testInfo.getDisplayName() + " ===");
        
        // 测试Generator与方言接口的集成
        System.out.println("测试Generator与方言接口集成:");
        
        // 验证Generator实例存在且可用
        assertNotNull(damengGenerator, "达梦生成器不能为null");
        assertNotNull(kingbaseGenerator, "金仓生成器不能为null");
        
        System.out.println("✅ Generator实例创建成功");
        
        // 测试Generator的基本功能
        // 注意：这里我们主要测试Generator能够正常工作，而不是具体的SQL生成
        // 因为具体的SQL生成测试在其他测试类中已经覆盖
        
        try {
            // 测试达梦生成器
            String damengResult = damengGenerator.getClass().getSimpleName();
            assertTrue(damengResult.contains("Dameng"), "达梦生成器类名应包含Dameng");
            
            // 测试金仓生成器
            String kingbaseResult = kingbaseGenerator.getClass().getSimpleName();
            assertTrue(kingbaseResult.contains("Kingbase"), "金仓生成器类名应包含Kingbase");
            
            System.out.printf("✅ Generator类型验证: 达梦=%s, 金仓=%s%n", 
                damengResult, kingbaseResult);
                
        } catch (Exception e) {
            fail("Generator基本功能测试失败: " + e.getMessage());
        }
    }
    
    @Test
    void testSqlFormatterEnhancement(TestInfo testInfo) {
        System.out.println("\n=== " + testInfo.getDisplayName() + " ===");
        
        // 测试SqlFormatter的函数转换增强
        System.out.println("测试SqlFormatter函数转换增强:");
        
        String testSql = "SELECT NOW(), CURDATE(), SUBSTRING(name, 1, 10) FROM users";
        
        // 测试标准格式化
        String formatted = SqlFormatter.format(testSql);
        assertNotNull(formatted, "格式化结果不能为null");
        assertFalse(formatted.isEmpty(), "格式化结果不能为空");
        
        System.out.printf("原始SQL: %s%n", testSql);
        System.out.printf("格式化后: %s%n", formatted);
        
        // 测试增强的函数转换格式化
        String damengFormatted = SqlFormatter.formatWithFunctionMapping(testSql, damengDialect);
        String kingbaseFormatted = SqlFormatter.formatWithFunctionMapping(testSql, kingbaseDialect);
        
        assertNotNull(damengFormatted, "达梦函数转换格式化结果不能为null");
        assertNotNull(kingbaseFormatted, "金仓函数转换格式化结果不能为null");
        
        // 验证函数转换
        assertTrue(damengFormatted.contains("SYSDATE") || damengFormatted.contains("NOW"), 
            "达梦格式化应该包含转换后的时间函数");
        assertTrue(kingbaseFormatted.contains("NOW") || kingbaseFormatted.contains("CURRENT"), 
            "金仓格式化应该包含转换后的时间函数");
        
        System.out.printf("✅ 达梦函数转换: %s%n", damengFormatted);
        System.out.printf("✅ 金仓函数转换: %s%n", kingbaseFormatted);
    }
    
    @Test
    void testSqlConversionUtilsIntegration(TestInfo testInfo) {
        System.out.println("\n=== " + testInfo.getDisplayName() + " ===");
        
        // 测试SqlConversionUtils与方言的集成
        System.out.println("测试SqlConversionUtils方言集成:");
        
        String testSql = "SELECT NOW(), LENGTH(email), IFNULL(name, 'Unknown') FROM users";
        
        // 测试方言集成的函数转换
        String damengConverted = SqlConversionUtils.convertFunctionsWithMapper(testSql, damengDialect);
        String kingbaseConverted = SqlConversionUtils.convertFunctionsWithMapper(testSql, kingbaseDialect);
        
        assertNotNull(damengConverted, "达梦转换结果不能为null");
        assertNotNull(kingbaseConverted, "金仓转换结果不能为null");
        
        System.out.printf("原始SQL: %s%n", testSql);
        System.out.printf("达梦转换: %s%n", damengConverted);
        System.out.printf("金仓转换: %s%n", kingbaseConverted);
        
        // 验证函数转换效果
        assertTrue(damengConverted.contains("SYSDATE") || damengConverted.contains("NOW"), 
            "达梦转换应该包含时间函数");
        assertTrue(kingbaseConverted.contains("NOW") || kingbaseConverted.contains("CURRENT"), 
            "金仓转换应该包含时间函数");
        
        System.out.println("✅ SqlConversionUtils方言集成验证通过");
    }
    
    @Test
    void testEndToEndIntegration(TestInfo testInfo) {
        System.out.println("\n=== " + testInfo.getDisplayName() + " ===");
        
        // 端到端集成测试
        System.out.println("端到端Calcite增强功能集成测试:");
        
        String complexSql = "CREATE TABLE users (" +
                           "id INT PRIMARY KEY, " +
                           "name VARCHAR(100), " +
                           "email TEXT, " +
                           "created_at DATETIME DEFAULT NOW(), " +
                           "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                           ")";
        
        // 测试完整的转换流程
        try {
            // 1. 使用方言进行数据类型映射
            String damengType = damengDialect.mapDataType("TEXT", null, null, null);
            String kingbaseType = kingbaseDialect.mapDataType("TEXT", null, null, null);
            
            assertEquals("CLOB", damengType, "达梦TEXT类型映射验证");
            assertEquals("TEXT", kingbaseType, "金仓TEXT类型映射验证");
            
            // 2. 使用方言进行函数映射
            String damengNow = damengDialect.mapFunction("NOW");
            String kingbaseNow = kingbaseDialect.mapFunction("NOW");
            
            assertTrue(damengNow.contains("SYSDATE"), "达梦NOW函数映射验证");
            assertTrue(kingbaseNow.contains("NOW"), "金仓NOW函数映射验证");
            
            // 3. 使用SqlFormatter进行格式化和函数转换
            String formattedSql = SqlFormatter.format(complexSql);
            assertNotNull(formattedSql, "SQL格式化验证");
            
            // 4. 使用SqlConversionUtils进行综合转换
            String convertedSql = SqlConversionUtils.convertFunctionsWithMapper(complexSql, damengDialect);
            assertNotNull(convertedSql, "SQL综合转换验证");
            
            System.out.println("✅ 端到端集成测试通过");
            System.out.printf("原始SQL: %s%n", complexSql);
            System.out.printf("转换后: %s%n", convertedSql);
            
        } catch (Exception e) {
            fail("端到端集成测试失败: " + e.getMessage());
        }
    }
    
    @Test
    void testCalciteDesignPrinciples(TestInfo testInfo) {
        System.out.println("\n=== " + testInfo.getDisplayName() + " ===");
        
        // 验证Calcite设计原则的实现
        System.out.println("验证Calcite设计原则实现:");
        
        // 1. 轻量级原则 - 方言实例应该轻量
        long startTime = System.nanoTime();
        SqlDialect testDialect1 = new DamengDialect();
        SqlDialect testDialect2 = new KingbaseDialect();
        long endTime = System.nanoTime();
        
        long creationTime = (endTime - startTime) / 1_000_000; // 转换为毫秒
        assertTrue(creationTime < 100, "方言实例创建应该在100ms内完成");
        
        System.out.printf("✅ 轻量级验证: 方言创建耗时 %d ms%n", creationTime);
        
        // 2. 标准化原则 - 所有方言都实现相同接口
        assertTrue(testDialect1 instanceof SqlDialect, "达梦方言应实现SqlDialect接口");
        assertTrue(testDialect2 instanceof SqlDialect, "金仓方言应实现SqlDialect接口");
        
        System.out.println("✅ 标准化验证: 所有方言都实现SqlDialect接口");
        
        // 3. 可扩展性原则 - 方言接口支持扩展
        assertNotNull(testDialect1.getName(), "方言名称方法可用");
        assertNotNull(testDialect1.getDatabaseProduct(), "数据库产品方法可用");
        assertTrue(testDialect1.supportsDataType("VARCHAR"), "数据类型支持检查可用");
        assertTrue(testDialect1.supportsFunction("NOW"), "函数支持检查可用");
        
        System.out.println("✅ 可扩展性验证: 方言接口支持完整的扩展功能");
        
        // 4. 性能原则 - 方言操作应该高效
        startTime = System.nanoTime();
        for (int i = 0; i < 1000; i++) {
            testDialect1.quoteIdentifier("test_table");
            testDialect1.mapDataType("VARCHAR", 255, null, null);
            testDialect1.mapFunction("NOW");
        }
        endTime = System.nanoTime();
        
        long operationTime = (endTime - startTime) / 1_000_000; // 转换为毫秒
        assertTrue(operationTime < 100, "1000次方言操作应该在100ms内完成");
        
        System.out.printf("✅ 性能验证: 1000次方言操作耗时 %d ms%n", operationTime);
        
        System.out.println("🎯 Calcite设计原则验证全部通过!");
    }
}
