package com.xylink.sqltranspiler.compliance.kingbase;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.shared.base.BaseKingbaseConversionTest;

/**
 * 金仓数据库官方文档合规性测试
 * 基于金仓官方文档：https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
 * 严格遵循官方文档规范：
 * - MySQL迁移指南: https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
 * - 数据类型映射: https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-3.html#id13
 * - SQL语法兼容: https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
 * 测试原则：
 * 1. 不允许推测，必须基于官方文档
 * 2. 测试期望与官方文档不符时，修正测试用例而不是降低代码质量
 * 3. 坚持正确的实现，确保功能的准确性和完整性
 * 测试MySQL到金仓数据库转换的官方合规性：
 * - 数据类型转换合规性（基于官方映射表）
 * - 函数转换合规性（基于官方兼容性列表）
 * - SQL语句转换合规性（基于官方语法规范）
 * - 标识符处理合规性（基于官方标识符规则）
 * - 特殊语法处理合规性（基于官方迁移指南）
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
@DisplayName("金仓数据库官方合规性测试")
public class KingbaseOfficialComplianceTest extends BaseKingbaseConversionTest {

    private static final Logger log = LoggerFactory.getLogger(KingbaseOfficialComplianceTest.class);

    @Test
    @DisplayName("验证金仓官方标准 - AUTO_INCREMENT原生支持")
    public void testAutoIncrementNativeSupport() throws Exception {
        // 基于金仓官方文档：金仓数据库原生支持MySQL的AUTO_INCREMENT语法
        // 参考：https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
        String sql = """
            CREATE TABLE serial_test (
                small_id SMALLINT AUTO_INCREMENT,
                normal_id INT AUTO_INCREMENT,
                big_id BIGINT AUTO_INCREMENT
            );
            """;
        
        String result = convertMySqlToKingbase(sql);
        log.info("转换结果: {}", result);
        
        // 验证基本转换要求
        assertBasicConversionRequirements(result);
        
        // 根据金仓官方文档，金仓数据库原生支持AUTO_INCREMENT
        // 因此转换结果应该保持AUTO_INCREMENT语法或转换为等效的SERIAL类型
        boolean hasAutoIncrement = result.toUpperCase().contains("AUTO_INCREMENT");
        boolean hasSerial = result.toUpperCase().contains("SERIAL") ||
                           result.toUpperCase().contains("SMALLSERIAL") ||
                           result.toUpperCase().contains("BIGSERIAL");
        boolean hasIdentity = result.toUpperCase().contains("IDENTITY");

        assertTrue(hasAutoIncrement || hasSerial || hasIdentity,
                  "金仓数据库应该保持AUTO_INCREMENT语法或转换为SERIAL/IDENTITY类型");

        log.info("✅ 金仓AUTO_INCREMENT官方标准验证通过");
    }

    @Test
    @DisplayName("验证金仓官方标准 - 标识符引用必须使用双引号")
    public void testIdentifierQuoting() throws Exception {
        String sql = """
            CREATE TABLE `test_table` (
                `id` INT PRIMARY KEY,
                `user_name` VARCHAR(50),
                `order_date` DATE
            );
            """;
        
        String result = convertMySqlToKingbase(sql);
        log.info("转换结果: {}", result);
        
        // 验证基本转换要求
        assertBasicConversionRequirements(result);
        
        // 验证标识符引用转换
        assertTrue(result.contains("\"test_table\"") || result.contains("test_table"), 
                  "表名应正确转换");
        assertTrue(result.contains("\"id\"") || result.contains("id"), 
                  "列名应正确转换");
        
        // 验证不包含MySQL的反引号
        assertFalse(result.contains("`"), "不应包含MySQL的反引号标识符");
    }

    @Test
    @DisplayName("验证金仓官方标准 - 引擎和字符集子句必须移除")
    public void testEngineAndCharsetRemoval() throws Exception {
        String sql = """
            CREATE TABLE test_table (
                id INT PRIMARY KEY,
                name VARCHAR(50)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """;
        
        String result = convertMySqlToKingbase(sql);
        log.info("转换结果: {}", result);
        
        // 验证基本转换要求
        assertBasicConversionRequirements(result);
        
        // 验证引擎和字符集子句移除
        assertFalse(result.contains("ENGINE="), "不应包含MySQL的ENGINE语法");
        assertFalse(result.contains("DEFAULT CHARSET"), "不应包含MySQL的DEFAULT CHARSET语法");
        assertFalse(result.contains("COLLATE"), "不应包含MySQL的COLLATE语法");

        log.info("✅ 金仓引擎和字符集子句移除验证通过");
    }

    @Test
    @DisplayName("验证金仓官方标准 - MySQL函数兼容性支持")
    public void testMySqlFunctionCompatibility() throws Exception {
        // 基于金仓官方文档：金仓数据库对MySQL函数有很好的兼容性
        // 参考：https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id3
        String sql = """
            SELECT
                IFNULL(name, 'Unknown') as display_name,
                COALESCE(email, phone, 'No Contact') as contact,
                CONCAT(first_name, ' ', last_name) as full_name,
                NOW() as current_time,
                DATE_FORMAT(created_at, '%Y-%m-%d') as formatted_date
            FROM users
            WHERE IFNULL(status, 'active') = 'active';
            """;

        String result = convertMySqlToKingbase(sql);
        log.info("金仓函数兼容性转换结果: {}", result);

        // 验证基本转换要求
        assertBasicConversionRequirements(result);

        // 根据金仓官方文档，金仓对MySQL函数有很好的兼容性
        // 验证函数转换的正确性
        String upperResult = result.toUpperCase();

        // IFNULL函数 - 金仓可能转换为COALESCE或保持IFNULL
        boolean hasIfnullOrCoalesce = upperResult.contains("IFNULL") ||
                                     upperResult.contains("COALESCE");
        assertTrue(hasIfnullOrCoalesce,
                  "金仓应该支持IFNULL函数或转换为COALESCE");

        // CONCAT函数 - 金仓原生支持
        assertTrue(upperResult.contains("CONCAT") || upperResult.contains("||"),
                  "金仓应该支持CONCAT函数或字符串连接操作符");

        // NOW函数 - 金仓原生支持
        assertTrue(upperResult.contains("NOW") || upperResult.contains("CURRENT_TIMESTAMP"),
                  "金仓应该支持NOW函数或CURRENT_TIMESTAMP");

        // 验证WHERE子句中的函数处理
        assertTrue(upperResult.contains("WHERE"),
                  "WHERE子句应该被正确处理");

        log.info("✅ 金仓MySQL函数兼容性验证通过");
    }

    @Test
    @DisplayName("验证金仓官方标准 - TINYINT(1)必须映射为BOOLEAN")
    public void testTinyintToBooleanMapping() throws Exception {
        String sql = """
            CREATE TABLE boolean_test (
                id INT PRIMARY KEY,
                is_active TINYINT(1),
                flag TINYINT(1) DEFAULT 1
            );
            """;
        
        String result = convertMySqlToKingbase(sql);
        log.info("转换结果: {}", result);
        
        // 验证基本转换要求
        assertBasicConversionRequirements(result);
        
        // 验证TINYINT(1)转换
        assertTrue(result.contains("BOOLEAN") || result.contains("BOOL") || 
                  result.contains("BIT") || result.contains("SMALLINT"), 
                  "TINYINT(1)应转换为BOOLEAN或其他等效类型");
        
        // 验证默认值保持
        assertTrue(result.contains("DEFAULT"), "默认值应保持");
    }

    @Test
    @DisplayName("验证金仓官方标准 - 函数转换合规性")
    public void testFunctionConversionCompliance() throws Exception {
        String sql = """
            SELECT 
                IFNULL(name, 'Unknown') as name,
                DATE_FORMAT(created_at, '%Y-%m-%d') as formatted_date,
                CONCAT(first_name, ' ', last_name) as full_name,
                NOW() as current_time
            FROM users;
            """;
        
        String result = convertMySqlToKingbase(sql);
        log.info("转换结果: {}", result);
        
        // 验证基本转换要求
        assertBasicConversionRequirements(result);
        
        // 验证函数转换
        assertTrue(result.contains("COALESCE") || result.contains("NVL") || result.contains("IFNULL"), 
                  "IFNULL应正确转换");
        assertTrue(result.contains("TO_CHAR") || result.contains("DATE_FORMAT"), 
                  "DATE_FORMAT应正确转换");
        assertTrue(result.contains("CONCAT") || result.contains("||"), 
                  "CONCAT应正确转换");
        assertTrue(result.contains("NOW()") || result.contains("CURRENT_TIMESTAMP"), 
                  "NOW()应正确转换");
    }

    @Test
    @DisplayName("验证金仓官方标准 - SQL语句合规性")
    public void testSQLStatementCompliance() throws Exception {
        String sql = """
            SELECT * FROM employees 
            WHERE department = 'IT' 
            ORDER BY salary DESC 
            LIMIT 10 OFFSET 20;
            """;
        
        String result = convertMySqlToKingbase(sql);
        log.info("转换结果: {}", result);
        
        // 验证基本转换要求
        assertBasicConversionRequirements(result);
        
        // 验证SQL语句转换
        assertTrue(result.contains("SELECT"), "SELECT应保持");
        assertTrue(result.contains("FROM"), "FROM应保持");
        assertTrue(result.contains("WHERE"), "WHERE应保持");
        assertTrue(result.contains("ORDER BY"), "ORDER BY应保持");
        assertTrue(result.contains("LIMIT"), "LIMIT应保持");
        assertTrue(result.contains("OFFSET"), "OFFSET应保持");
    }

    @Test
    @DisplayName("验证金仓官方标准 - 综合合规性测试")
    public void testComprehensiveCompliance() throws Exception {
        String sql = """
            CREATE TABLE `orders` (
                `id` INT AUTO_INCREMENT PRIMARY KEY,
                `customer_id` INT NOT NULL,
                `order_date` DATETIME DEFAULT CURRENT_TIMESTAMP,
                `total_amount` DECIMAL(10,2) UNSIGNED,
                `status` ENUM('pending', 'processing', 'shipped', 'delivered'),
                `is_paid` TINYINT(1) DEFAULT 0,
                `notes` TEXT,
                FOREIGN KEY (`customer_id`) REFERENCES `customers`(`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            
            INSERT INTO `orders` (`customer_id`, `total_amount`, `status`, `is_paid`)
            VALUES (1, 299.99, 'pending', 0),
                   (2, 499.50, 'processing', 1);
                   
            SELECT o.id, c.name, o.total_amount, o.status
            FROM `orders` o
            JOIN `customers` c ON o.customer_id = c.id
            WHERE o.is_paid = 1
            ORDER BY o.order_date DESC
            LIMIT 10;
            """;
        
        String result = convertMySqlToKingbase(sql);
        log.info("转换结果: {}", result);
        
        // 验证基本转换要求
        assertBasicConversionRequirements(result);
        
        // 验证综合合规性
        assertTrue(result.contains("CREATE TABLE"), "应包含CREATE TABLE");
        assertTrue(result.contains("PRIMARY KEY"), "应包含PRIMARY KEY");
        assertTrue(result.contains("FOREIGN KEY"), "应包含FOREIGN KEY");
        assertTrue(result.contains("REFERENCES"), "应包含REFERENCES");
        assertTrue(result.contains("INSERT INTO"), "应包含INSERT INTO");
        assertTrue(result.contains("VALUES"), "应包含VALUES");
        assertTrue(result.contains("SELECT"), "应包含SELECT");
        assertTrue(result.contains("JOIN"), "应包含JOIN");
        assertTrue(result.contains("ORDER BY"), "应包含ORDER BY");
        assertTrue(result.contains("LIMIT"), "应包含LIMIT");
        
        // 验证不包含MySQL特有语法
        assertFalse(result.contains("ENGINE="), "不应包含ENGINE=");
        assertFalse(result.contains("DEFAULT CHARSET"), "不应包含DEFAULT CHARSET");
        assertFalse(result.contains("`"), "不应包含反引号");

        log.info("✅ 金仓综合合规性验证通过");
    }

    @Test
    @DisplayName("验证金仓官方标准 - MySQL数据类型原生兼容性")
    public void testMySqlDataTypeNativeCompatibility() throws Exception {
        // 基于金仓官方文档：金仓数据库对MySQL数据类型有很好的兼容性
        // 参考：https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
        String sql = """
            CREATE TABLE data_type_test (
                tiny_col TINYINT,
                small_col SMALLINT,
                medium_col MEDIUMINT,
                int_col INT,
                big_col BIGINT,
                decimal_col DECIMAL(10,2),
                float_col FLOAT,
                double_col DOUBLE,
                char_col CHAR(10),
                varchar_col VARCHAR(255),
                text_col TEXT,
                date_col DATE,
                time_col TIME,
                datetime_col DATETIME,
                timestamp_col TIMESTAMP,
                json_col JSON
            );
            """;

        String result = convertMySqlToKingbase(sql);
        log.info("金仓数据类型兼容性转换结果: {}", result);

        // 验证基本转换要求
        assertBasicConversionRequirements(result);

        // 根据金仓官方文档，金仓原生支持大部分MySQL数据类型
        String upperResult = result.toUpperCase();

        // 验证整数类型支持
        assertTrue(upperResult.contains("TINYINT") || upperResult.contains("SMALLINT"),
                  "金仓应该支持TINYINT类型");
        assertTrue(upperResult.contains("SMALLINT"),
                  "金仓应该支持SMALLINT类型");
        assertTrue(upperResult.contains("INT"),
                  "金仓应该支持INT类型");
        assertTrue(upperResult.contains("BIGINT"),
                  "金仓应该支持BIGINT类型");

        // 验证数值类型支持
        assertTrue(upperResult.contains("DECIMAL"),
                  "金仓应该支持DECIMAL类型");
        assertTrue(upperResult.contains("FLOAT"),
                  "金仓应该支持FLOAT类型");
        assertTrue(upperResult.contains("DOUBLE"),
                  "金仓应该支持DOUBLE类型");

        // 验证字符串类型支持
        assertTrue(upperResult.contains("CHAR"),
                  "金仓应该支持CHAR类型");
        assertTrue(upperResult.contains("VARCHAR"),
                  "金仓应该支持VARCHAR类型");
        assertTrue(upperResult.contains("TEXT"),
                  "金仓应该支持TEXT类型");

        // 验证日期时间类型支持
        assertTrue(upperResult.contains("DATE"),
                  "金仓应该支持DATE类型");
        assertTrue(upperResult.contains("TIME"),
                  "金仓应该支持TIME类型");
        assertTrue(upperResult.contains("DATETIME") || upperResult.contains("TIMESTAMP"),
                  "金仓应该支持DATETIME类型");

        // 验证JSON类型支持（金仓V8支持JSON）
        assertTrue(upperResult.contains("JSON") || upperResult.contains("TEXT"),
                  "金仓应该支持JSON类型或转换为TEXT");

        log.info("✅ 金仓MySQL数据类型原生兼容性验证通过");
    }

    @Test
    @DisplayName("验证金仓官方标准 - MySQL分页查询LIMIT OFFSET兼容性")
    public void testMySqlLimitOffsetCompatibility() throws Exception {
        // 基于金仓官方文档：金仓数据库原生支持MySQL的LIMIT OFFSET语法
        // 参考：https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
        String sql = """
            SELECT
                id,
                name,
                email,
                created_at
            FROM users
            WHERE status = 'active'
            ORDER BY created_at DESC
            LIMIT 20 OFFSET 100;
            """;

        String result = convertMySqlToKingbase(sql);
        log.info("金仓分页查询转换结果: {}", result);

        // 验证基本转换要求
        assertBasicConversionRequirements(result);

        // 根据金仓官方文档，金仓原生支持MySQL的LIMIT OFFSET语法
        String upperResult = result.toUpperCase();

        // 验证LIMIT OFFSET语法保持
        assertTrue(upperResult.contains("LIMIT") && upperResult.contains("OFFSET"),
                  "金仓应该原生支持MySQL的LIMIT OFFSET语法");

        // 验证ORDER BY子句保持
        assertTrue(upperResult.contains("ORDER BY"),
                  "ORDER BY子句应该被正确保持");

        // 验证WHERE子句保持
        assertTrue(upperResult.contains("WHERE"),
                  "WHERE子句应该被正确保持");

        // 验证SELECT列表保持
        assertTrue(upperResult.contains("SELECT"),
                  "SELECT语句应该被正确保持");

        log.info("✅ 金仓MySQL分页查询LIMIT OFFSET兼容性验证通过");
    }

    @Test
    @DisplayName("验证金仓官方标准 - MySQL存储引擎语法处理")
    public void testMySqlStorageEngineHandling() throws Exception {
        // 基于金仓官方文档：金仓数据库不使用MySQL的存储引擎概念
        // 参考：https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
        String sql = """
            CREATE TABLE storage_engine_test (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """;

        String result = convertMySqlToKingbase(sql);
        log.info("金仓存储引擎处理转换结果: {}", result);

        // 验证基本转换要求
        assertBasicConversionRequirements(result);

        // 根据金仓官方文档，金仓不使用MySQL的存储引擎概念
        String upperResult = result.toUpperCase();

        // 验证ENGINE子句被移除或忽略
        if (!upperResult.contains("ENGINE=")) {
            System.out.println("    ✅ 金仓正确移除了ENGINE子句");
        } else {
            System.out.println("    ⚠️ 金仓保留了ENGINE子句，需要验证处理方式");
        }

        // 验证字符集和排序规则处理
        if (!upperResult.contains("DEFAULT CHARSET") && !upperResult.contains("COLLATE")) {
            System.out.println("    ✅ 金仓正确处理了字符集和排序规则");
        } else {
            System.out.println("    ⚠️ 金仓保留了字符集设置，需要验证兼容性");
        }

        // 验证AUTO_INCREMENT转换
        assertTrue(upperResult.contains("AUTO_INCREMENT") ||
                  upperResult.contains("SERIAL") ||
                  upperResult.contains("IDENTITY"),
                  "AUTO_INCREMENT应该被正确处理");

        // 验证表结构完整性
        assertTrue(upperResult.contains("CREATE TABLE"),
                  "CREATE TABLE语句应该被正确保持");
        assertTrue(upperResult.contains("PRIMARY KEY"),
                  "PRIMARY KEY约束应该被正确保持");

        log.info("✅ 金仓MySQL存储引擎语法处理验证通过");
    }

    /**
     * 测试金仓数据库数据类型原生支持合规性
     * 基于官方文档：https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
     *
     * 官方文档依据：
     * - 数值类型（整型、浮点型、定点数类型）：原生支持
     * - 文本字符串类型（CHAR、VARCHAR、TINYTEXT、MEDIUMTEXT、LONGTEXT）：原生支持
     * - 位类型：原生支持
     * - 日期时间类型(YEAR、TIME、DATE、DATETIME、TIMESTAMP)：原生支持
     * - 枚举类型ENUM：原生支持
     * - 集合类型SET：原生支持
     * - 二进制类型（BINARY、VARBINARY、BLOB）：原生支持
     * - JSON类型（JSON对象、JSON数组）：原生支持
     */
    @Test
    @DisplayName("金仓数据库数据类型原生支持合规性测试")
    public void testKingbaseDataTypeNativeSupport() throws Exception {
        log.info("开始执行金仓数据库数据类型原生支持合规性测试");

        String mysqlDataTypeSql = """
            CREATE TABLE test_datatypes (
                -- 数值类型
                tiny_col TINYINT,
                small_col SMALLINT,
                int_col INT,
                big_col BIGINT,
                decimal_col DECIMAL(10,2),
                float_col FLOAT,
                double_col DOUBLE,

                -- 字符串类型
                char_col CHAR(50),
                varchar_col VARCHAR(255),
                text_col TEXT,

                -- 位类型
                bit_col BIT,

                -- 日期时间类型
                time_col TIME,
                date_col DATE,
                datetime_col DATETIME,
                timestamp_col TIMESTAMP,

                -- JSON类型
                json_col JSON
            );
            """;

        String result = convertMySqlToKingbase(mysqlDataTypeSql);

        // 验证数值类型原生支持（基于官方文档）
        assertTrue(result.contains("TINYINT") || result.contains("SMALLINT"), "金仓应原生支持TINYINT或转换为SMALLINT");
        assertTrue(result.contains("SMALLINT"), "金仓应原生支持SMALLINT");
        assertTrue(result.contains("INT") || result.contains("INTEGER"), "金仓应原生支持INT/INTEGER");
        assertTrue(result.contains("BIGINT"), "金仓应原生支持BIGINT");
        assertTrue(result.contains("DECIMAL"), "金仓应原生支持DECIMAL");
        assertTrue(result.contains("FLOAT") || result.contains("REAL"), "金仓应原生支持FLOAT/REAL");
        assertTrue(result.contains("DOUBLE") || result.contains("DOUBLE PRECISION"), "金仓应原生支持DOUBLE");

        // 验证字符串类型原生支持
        assertTrue(result.contains("CHAR"), "金仓应原生支持CHAR");
        assertTrue(result.contains("VARCHAR"), "金仓应原生支持VARCHAR");
        assertTrue(result.contains("TEXT"), "金仓应原生支持TEXT类型");

        // 验证日期时间类型原生支持
        assertTrue(result.contains("TIME"), "金仓应原生支持TIME");
        assertTrue(result.contains("DATE"), "金仓应原生支持DATE");
        assertTrue(result.contains("TIMESTAMP"), "金仓应原生支持TIMESTAMP");

        // 验证JSON类型原生支持
        assertTrue(result.contains("JSON"), "金仓应原生支持JSON类型");

        log.info("金仓数据库数据类型原生支持合规性测试通过");
    }
}
