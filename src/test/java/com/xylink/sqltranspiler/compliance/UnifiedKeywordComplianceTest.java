package com.xylink.sqltranspiler.compliance;

import static org.junit.jupiter.api.Assertions.*;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Stream;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * 统一关键字管理合规性测试
 * 验证所有代码都使用统一的关键字管理系统，没有硬编码的关键字定义
 * 遵循 .augment/rules/rule-db.md 规则：统一管理，避免重复定义
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
@DisplayName("统一关键字管理合规性测试")
public class UnifiedKeywordComplianceTest {

    // 允许的关键字定义位置（统一管理类）
    private static final List<String> ALLOWED_KEYWORD_DEFINITION_FILES = List.of(
        "src/main/java/com/xylink/sqltranspiler/common/constants/ReservedWords.java",
        "src/main/java/com/xylink/sqltranspiler/common/constants/SqlKeywords.java"
    );

    // 允许的配置文件（从配置文件读取保留字是合理的）
    private static final List<String> ALLOWED_CONFIG_FILES = List.of(
        "src/main/resources/strict-validation-rules.properties",
        "src/test/resources/"
    );

    // 允许的测试文件（测试中可能包含示例关键字）
    private static final List<String> ALLOWED_TEST_PATTERNS = List.of(
        "src/test/java/",
        "docs/"
    );

    @Test
    @DisplayName("验证没有硬编码的保留字定义")
    public void testNoHardcodedReservedWords() throws IOException {
        List<String> violations = new ArrayList<>();
        
        // 检查所有Java文件
        try (Stream<Path> paths = Files.walk(Paths.get("src/main/java"))) {
            paths.filter(Files::isRegularFile)
                 .filter(path -> path.toString().endsWith(".java"))
                 .filter(path -> !isAllowedFile(path.toString()))
                 .forEach(path -> {
                     try {
                         checkFileForHardcodedKeywords(path, violations);
                     } catch (IOException e) {
                         fail("无法读取文件: " + path + " - " + e.getMessage());
                     }
                 });
        }
        
        if (!violations.isEmpty()) {
            StringBuilder message = new StringBuilder("发现硬编码的关键字定义，违反统一管理规则：\n");
            for (String violation : violations) {
                message.append("  - ").append(violation).append("\n");
            }
            message.append("\n请使用 ReservedWords.isReservedWord() 或 SqlKeywords.isSqlKeyword() 替代硬编码定义");
            fail(message.toString());
        }
    }

    @Test
    @DisplayName("验证所有数据库方言使用统一保留字管理")
    public void testDialectsUseUnifiedReservedWords() throws IOException {
        List<String> dialectFiles = List.of(
            "src/main/java/com/xylink/sqltranspiler/core/dialects/DamengDialect.java",
            "src/main/java/com/xylink/sqltranspiler/core/dialects/KingbaseDialect.java",
            "src/main/java/com/xylink/sqltranspiler/core/dialects/ShentongDialect.java"
        );
        
        for (String dialectFile : dialectFiles) {
            Path path = Paths.get(dialectFile);
            if (Files.exists(path)) {
                String content = Files.readString(path);
                
                // 验证使用了统一的保留字管理
                assertTrue(content.contains("ReservedWords.isReservedWord"), 
                          dialectFile + " 应该使用 ReservedWords.isReservedWord() 方法");
                
                // 验证没有本地保留字定义
                assertFalse(content.matches("(?s).*private\\s+static\\s+final\\s+Set<String>\\s+\\w*RESERVED\\w*.*Set\\.of\\s*\\(.*"),
                           dialectFile + " 不应该包含本地保留字定义");
            }
        }
    }

    @Test
    @DisplayName("验证所有生成器使用统一SQL关键字管理")
    public void testGeneratorsUseUnifiedSqlKeywords() throws IOException {
        List<String> generatorFiles = List.of(
            "src/main/java/com/xylink/sqltranspiler/core/dialects/dameng/DamengGenerator.java",
            "src/main/java/com/xylink/sqltranspiler/core/dialects/kingbase/KingbaseGenerator.java",
            "src/main/java/com/xylink/sqltranspiler/core/dialects/shentong/ShentongGenerator.java"
        );
        
        for (String generatorFile : generatorFiles) {
            Path path = Paths.get(generatorFile);
            if (Files.exists(path)) {
                String content = Files.readString(path);
                
                // 如果文件中有关键字相关的逻辑，应该使用统一管理
                if (content.contains("keyword") || content.contains("reserved")) {
                    // 检查是否使用了统一的关键字管理
                    boolean usesUnifiedManagement = 
                        content.contains("SqlKeywords.") || 
                        content.contains("ReservedWords.") ||
                        !content.matches("(?s).*Set\\.of\\s*\\(\\s*\"[A-Z_]+\".*");
                    
                    assertTrue(usesUnifiedManagement, 
                              generatorFile + " 应该使用统一的关键字管理系统");
                }
            }
        }
    }

    @Test
    @DisplayName("验证预处理器使用统一关键字管理")
    public void testPreprocessorUsesUnifiedKeywords() throws IOException {
        Path preprocessorPath = Paths.get("src/main/java/com/xylink/sqltranspiler/infrastructure/parser/Preprocessor.java");
        
        if (Files.exists(preprocessorPath)) {
            String content = Files.readString(preprocessorPath);
            
            // 验证使用了统一的关键字管理
            assertTrue(content.contains("SqlKeywords.getAllKeywords()") || 
                      content.contains("ReservedWords.isReservedWord"),
                      "Preprocessor 应该使用统一的关键字管理系统");
            
            // 验证没有硬编码的关键字定义
            assertFalse(content.matches("(?s).*private\\s+static\\s+final\\s+Set<String>\\s+\\w*KEYWORD\\w*.*Set\\.of\\s*\\(.*"),
                       "Preprocessor 不应该包含硬编码的关键字定义");
        }
    }

    @Test
    @DisplayName("验证验证器使用统一关键字管理")
    public void testValidatorsUseUnifiedKeywords() throws IOException {
        List<String> validatorFiles = List.of(
            "src/main/java/com/xylink/sqltranspiler/core/validation/StrictSqlValidator.java",
            "src/main/java/com/xylink/sqltranspiler/infrastructure/parser/error/MySqlStrictSyntaxValidator.java"
        );
        
        for (String validatorFile : validatorFiles) {
            Path path = Paths.get(validatorFile);
            if (Files.exists(path)) {
                String content = Files.readString(path);
                
                // 如果文件中有关键字检查逻辑，应该使用统一管理
                if (content.contains("keyword") || content.contains("reserved")) {
                    // 检查是否使用了统一的关键字管理
                    boolean usesUnifiedManagement = 
                        content.contains("SqlKeywords.isSqlKeyword") || 
                        content.contains("ReservedWords.isReservedWord") ||
                        // 允许从配置文件解析保留字
                        content.contains("parseReservedWords");
                    
                    assertTrue(usesUnifiedManagement, 
                              validatorFile + " 应该使用统一的关键字管理系统");
                }
            }
        }
    }

    @Test
    @DisplayName("验证统一管理类的完整性")
    public void testUnifiedManagementClassesCompleteness() {
        // 验证ReservedWords类包含所有必要的数据库
        assertTrue(com.xylink.sqltranspiler.common.constants.ReservedWords.isReservedWord("SELECT", "mysql"));
        assertTrue(com.xylink.sqltranspiler.common.constants.ReservedWords.isReservedWord("SELECT", "dameng"));
        assertTrue(com.xylink.sqltranspiler.common.constants.ReservedWords.isReservedWord("SELECT", "kingbase"));
        assertTrue(com.xylink.sqltranspiler.common.constants.ReservedWords.isReservedWord("SELECT", "shentong"));
        
        // 验证SqlKeywords类包含基础关键字
        assertTrue(com.xylink.sqltranspiler.common.constants.SqlKeywords.isSqlKeyword("SELECT"));
        assertTrue(com.xylink.sqltranspiler.common.constants.SqlKeywords.isSqlKeyword("INSERT"));
        assertTrue(com.xylink.sqltranspiler.common.constants.SqlKeywords.isSqlKeyword("UPDATE"));
        assertTrue(com.xylink.sqltranspiler.common.constants.SqlKeywords.isSqlKeyword("DELETE"));
        
        // 验证关键字集合不为空
        assertFalse(com.xylink.sqltranspiler.common.constants.SqlKeywords.getAllKeywords().isEmpty());
        assertFalse(com.xylink.sqltranspiler.common.constants.ReservedWords.getReservedWords("mysql").isEmpty());
    }

    /**
     * 检查文件是否在允许的位置
     */
    private boolean isAllowedFile(String filePath) {
        // 统一管理类
        for (String allowedFile : ALLOWED_KEYWORD_DEFINITION_FILES) {
            if (filePath.endsWith(allowedFile.substring(allowedFile.lastIndexOf('/') + 1))) {
                return true;
            }
        }
        
        // 配置文件
        for (String allowedPattern : ALLOWED_CONFIG_FILES) {
            if (filePath.contains(allowedPattern)) {
                return true;
            }
        }
        
        // 测试文件
        for (String allowedPattern : ALLOWED_TEST_PATTERNS) {
            if (filePath.contains(allowedPattern)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 检查文件中是否有硬编码的关键字定义
     */
    private void checkFileForHardcodedKeywords(Path filePath, List<String> violations) throws IOException {
        String content = Files.readString(filePath);
        String fileName = filePath.toString();
        
        // 检查硬编码的Set.of定义，包含SQL关键字
        Pattern hardcodedSetPattern = Pattern.compile(
            "(?s)Set\\.of\\s*\\(\\s*\"(SELECT|INSERT|UPDATE|DELETE|CREATE|DROP|ALTER|TABLE|INDEX|VIEW|FROM|WHERE|ORDER|GROUP|HAVING|UNION|JOIN)\"",
            Pattern.CASE_INSENSITIVE
        );
        
        Matcher matcher = hardcodedSetPattern.matcher(content);
        if (matcher.find()) {
            violations.add(fileName + ": 发现硬编码的关键字定义 - " + matcher.group().substring(0, Math.min(50, matcher.group().length())));
        }
        
        // 检查硬编码的保留字常量定义
        Pattern reservedWordsPattern = Pattern.compile(
            "(?s)private\\s+static\\s+final\\s+Set<String>\\s+\\w*RESERVED\\w*.*Set\\.of\\s*\\(",
            Pattern.CASE_INSENSITIVE
        );
        
        matcher = reservedWordsPattern.matcher(content);
        if (matcher.find()) {
            violations.add(fileName + ": 发现硬编码的保留字常量定义");
        }
        
        // 检查硬编码的关键字常量定义
        Pattern keywordsPattern = Pattern.compile(
            "(?s)private\\s+static\\s+final\\s+Set<String>\\s+\\w*KEYWORD\\w*.*Set\\.of\\s*\\(",
            Pattern.CASE_INSENSITIVE
        );
        
        matcher = keywordsPattern.matcher(content);
        if (matcher.find()) {
            violations.add(fileName + ": 发现硬编码的关键字常量定义");
        }
    }
}
