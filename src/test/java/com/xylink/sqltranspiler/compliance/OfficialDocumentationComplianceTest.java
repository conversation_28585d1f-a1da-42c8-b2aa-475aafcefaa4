package com.xylink.sqltranspiler.compliance;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Stream;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * 官方文档合规性测试
 *
 * 测试原则：严格基于各数据库官方文档进行合规性验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据项目规范验证测试文件合规性
 * 2. 确保所有测试都有官方文档引用
 * 3. 验证测试原则声明的完整性
 * 4. 检查基于官方文档的验证逻辑
 *
 * 验证所有测试用例是否符合官方文档规范要求：
 * 1. 包含必要的官方文档链接
 * 2. 包含测试原则声明
 * 3. 包含基于官方文档的验证逻辑
 *
 * 基于规范：
 * - .augment/rules/rule-db.md
 * - .augment/rules/rule-test.md
 *
 * 基于官方文档的验证逻辑：
 * - 根据项目规范文档验证测试文件的合规性
 * - 确保每个测试都有明确的官方文档依据
 * - 验证测试逻辑符合官方文档规范
 *
 * <AUTHOR>
 */
@DisplayName("官方文档合规性测试")
public class OfficialDocumentationComplianceTest {

    // 必需的官方文档链接模式
    private static final Pattern MYSQL_DOC_PATTERN = Pattern.compile(
        "https://dev\\.mysql\\.com/doc/refman/8\\.4/en/", Pattern.CASE_INSENSITIVE);
    
    private static final Pattern DAMENG_DOC_PATTERN = Pattern.compile(
        "https://eco\\.dameng\\.com/document/dm/zh-cn/", Pattern.CASE_INSENSITIVE);
    
    private static final Pattern KINGBASE_DOC_PATTERN = Pattern.compile(
        "https://help\\.kingbase\\.com\\.cn/v8/", Pattern.CASE_INSENSITIVE);
    
    private static final Pattern SHENTONG_DOC_PATTERN = Pattern.compile(
        "@shentong\\.md|神通官方文档", Pattern.CASE_INSENSITIVE);

    // 必需的测试原则模式
    private static final Pattern TEST_PRINCIPLES_PATTERN = Pattern.compile(
        "测试原则|不允许推测|必须基于官方文档", Pattern.CASE_INSENSITIVE);

    // 测试文件根目录
    private static final String TEST_ROOT = "src/test/java/com/xylink/sqltranspiler";

    @Test
    @DisplayName("验证所有测试类的官方文档合规性")
    void validateAllTestClassesCompliance() throws IOException {
        List<String> nonCompliantFiles = new ArrayList<>();
        List<String> complianceIssues = new ArrayList<>();

        // 遍历所有测试文件
        try (Stream<Path> paths = Files.walk(Paths.get(TEST_ROOT))) {
            paths.filter(path -> path.toString().endsWith("Test.java"))
                 .filter(path -> !path.toString().contains("/shared/"))
                 .filter(path -> !path.toString().contains("/fixtures/"))
                 .forEach(path -> {
                     try {
                         validateTestFileCompliance(path, nonCompliantFiles, complianceIssues);
                     } catch (IOException e) {
                         fail("无法读取测试文件: " + path + " - " + e.getMessage());
                     }
                 });
        }

        // 生成合规性报告
        generateComplianceReport(nonCompliantFiles, complianceIssues);

        // 验证合规性
        if (!nonCompliantFiles.isEmpty()) {
            fail("发现 " + nonCompliantFiles.size() + " 个不符合官方文档规范的测试文件。详细信息请查看控制台输出。");
        }
    }

    private void validateTestFileCompliance(Path filePath, List<String> nonCompliantFiles, 
                                          List<String> complianceIssues) throws IOException {
        String content = Files.readString(filePath);
        String fileName = filePath.getFileName().toString();
        List<String> issues = new ArrayList<>();

        // 检查是否包含官方文档链接
        boolean hasOfficialDocLinks = checkOfficialDocumentationLinks(content, fileName, issues);

        // 检查是否包含测试原则
        boolean hasTestPrinciples = checkTestPrinciples(content, fileName, issues);

        // 检查是否包含基于官方文档的验证逻辑
        boolean hasDocBasedValidation = checkDocumentBasedValidation(content, fileName, issues);

        // 如果有任何不合规项，记录文件
        if (!hasOfficialDocLinks || !hasTestPrinciples || !hasDocBasedValidation) {
            nonCompliantFiles.add(fileName);
            complianceIssues.addAll(issues);
        }
    }

    private boolean checkOfficialDocumentationLinks(String content, String fileName, List<String> issues) {
        boolean hasLinks = false;

        // 检查MySQL文档链接
        if (MYSQL_DOC_PATTERN.matcher(content).find()) {
            hasLinks = true;
        }

        // 根据文件路径检查特定数据库的文档链接
        if (fileName.toLowerCase().contains("dameng")) {
            if (DAMENG_DOC_PATTERN.matcher(content).find()) {
                hasLinks = true;
            } else {
                issues.add(fileName + ": 缺少达梦官方文档链接");
            }
        }

        if (fileName.toLowerCase().contains("kingbase")) {
            if (KINGBASE_DOC_PATTERN.matcher(content).find()) {
                hasLinks = true;
            } else {
                issues.add(fileName + ": 缺少金仓官方文档链接");
            }
        }

        if (fileName.toLowerCase().contains("shentong")) {
            if (SHENTONG_DOC_PATTERN.matcher(content).find()) {
                hasLinks = true;
            } else {
                issues.add(fileName + ": 缺少神通官方文档引用");
            }
        }

        if (!hasLinks) {
            issues.add(fileName + ": 缺少官方文档链接");
        }

        return hasLinks;
    }

    private boolean checkTestPrinciples(String content, String fileName, List<String> issues) {
        boolean hasPrinciples = TEST_PRINCIPLES_PATTERN.matcher(content).find();
        
        if (!hasPrinciples) {
            issues.add(fileName + ": 缺少测试原则声明");
        }

        return hasPrinciples;
    }

    private boolean checkDocumentBasedValidation(String content, String fileName, List<String> issues) {
        // 检查是否包含基于官方文档的验证逻辑
        boolean hasDocValidation = content.contains("根据") && 
                                 (content.contains("官方文档") || content.contains("官方规范"));

        if (!hasDocValidation) {
            issues.add(fileName + ": 缺少基于官方文档的验证逻辑");
        }

        return hasDocValidation;
    }

    private void generateComplianceReport(List<String> nonCompliantFiles, List<String> complianceIssues) {
        System.out.println("\n=== 官方文档合规性验证报告 ===");
        System.out.println("验证时间: " + java.time.LocalDateTime.now());
        System.out.println("验证规范: .augment/rules/rule-db.md, .augment/rules/rule-test.md");
        
        if (nonCompliantFiles.isEmpty()) {
            System.out.println("✅ 所有测试文件都符合官方文档规范");
        } else {
            System.out.println("❌ 发现 " + nonCompliantFiles.size() + " 个不合规的测试文件");
            System.out.println("\n不合规文件列表:");
            nonCompliantFiles.forEach(file -> System.out.println("  - " + file));
            
            System.out.println("\n详细问题列表:");
            complianceIssues.forEach(issue -> System.out.println("  • " + issue));
            
            System.out.println("\n修复建议:");
            System.out.println("1. 添加官方文档链接到类注释中");
            System.out.println("2. 添加测试原则声明");
            System.out.println("3. 在验证逻辑中引用官方文档");
            System.out.println("4. 参考已合规的测试文件进行修改");
        }
        
        System.out.println("\n=== 报告结束 ===\n");
    }

    @Test
    @DisplayName("验证官方文档链接的有效性")
    void validateOfficialDocumentationLinksValidity() {
        // 这个测试验证我们使用的官方文档链接格式是否正确
        
        // MySQL 8.4官方文档链接验证
        String mysqlLink = "https://dev.mysql.com/doc/refman/8.4/en/";
        assertTrue(MYSQL_DOC_PATTERN.matcher(mysqlLink).find(), 
                  "MySQL官方文档链接模式应该匹配");

        // 达梦官方文档链接验证
        String damengLink = "https://eco.dameng.com/document/dm/zh-cn/sql-dev/";
        assertTrue(DAMENG_DOC_PATTERN.matcher(damengLink).find(), 
                  "达梦官方文档链接模式应该匹配");

        // 金仓官方文档链接验证
        String kingbaseLink = "https://help.kingbase.com.cn/v8/development/";
        assertTrue(KINGBASE_DOC_PATTERN.matcher(kingbaseLink).find(), 
                  "金仓官方文档链接模式应该匹配");

        // 神通官方文档引用验证
        String shentongRef = "@shentong.md 官方文档";
        assertTrue(SHENTONG_DOC_PATTERN.matcher(shentongRef).find(), 
                  "神通官方文档引用模式应该匹配");

        System.out.println("✅ 所有官方文档链接模式验证通过");
    }

    @Test
    @DisplayName("验证测试原则模式的有效性")
    void validateTestPrinciplesPatternValidity() {
        // 验证测试原则模式是否能正确匹配
        
        String principles1 = "测试原则：1. 不允许推测，必须基于官方文档";
        assertTrue(TEST_PRINCIPLES_PATTERN.matcher(principles1).find(), 
                  "测试原则模式应该匹配标准格式");

        String principles2 = "不允许推测，必须基于官方文档";
        assertTrue(TEST_PRINCIPLES_PATTERN.matcher(principles2).find(), 
                  "测试原则模式应该匹配简化格式");

        String principles3 = "必须基于官方文档进行验证";
        assertTrue(TEST_PRINCIPLES_PATTERN.matcher(principles3).find(), 
                  "测试原则模式应该匹配变体格式");

        System.out.println("✅ 测试原则模式验证通过");
    }
}
