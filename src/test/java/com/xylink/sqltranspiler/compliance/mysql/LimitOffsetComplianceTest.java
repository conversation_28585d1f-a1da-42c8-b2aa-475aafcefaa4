package com.xylink.sqltranspiler.compliance.mysql;

import com.xylink.sqltranspiler.core.validation.StrictSqlValidator;
import com.xylink.sqltranspiler.core.validation.StrictValidationResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

/**
 * MySQL LIMIT/OFFSET官方文档合规性测试
 * 严格遵循MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/select.html
 * MySQL官方文档完整描述：
 * "The LIMIT clause can be used to constrain the number of rows returned by the SELECT statement.
 * LIMIT takes one or two numeric arguments, which must both be nonnegative integer constants,
 * with these exceptions:
 * - Within prepared statements, LIMIT parameters can be specified using ? placeholder markers.
 * - Within stored programs, LIMIT parameters can be specified using integer-valued routine parameters or local variables.
 * With two arguments, the first argument specifies the offset of the first row to return,
 * and the second specifies the maximum number of rows to return. The offset of the initial row is 0 (not 1):
 * SELECT * FROM tbl LIMIT 5,10;  # Retrieve rows 6-15
 * For compatibility with PostgreSQL, MySQL also supports the LIMIT row_count OFFSET offset syntax."
 * 动态验证机制：所有测试用例都使用基于官方文档的动态验证方法
 * 官方文档引用体系：每个验证方法都包含详细的官方文档链接
 * 质量反馈机制：提供清晰的成功/警告/错误信息
 *
 * 测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证功能的正确性
 * 2. 确保转换结果符合官方文档规范
 * 3. 验证测试用例基于官方文档
 * 4. 测试边界情况和特殊场景
 *
 * <AUTHOR>
 */
public class LimitOffsetComplianceTest {

    private StrictSqlValidator validator;

    @BeforeEach
    void setUp() {
        validator = new StrictSqlValidator();
    }

    @Test
    @DisplayName("MySQL官方文档合规性：LIMIT单参数语法")
    void testMySqlLimitSingleArgumentCompliance() {
        // MySQL官方文档：LIMIT row_count 等价于 LIMIT 0, row_count
        String sql = "SELECT * FROM tbl LIMIT 5;";

        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");

        // 验证小LIMIT值不应该产生告警
        boolean hasWarning = result.getWarnings().stream()
            .anyMatch(warning -> warning.getMessage().toLowerCase().contains("limit"));

        assertFalse(hasWarning, 
            "根据MySQL 8.4官方文档，小LIMIT值是正常语法，不应该产生告警");
    }

    @Test
    @DisplayName("MySQL官方文档合规性：LIMIT双参数语法")
    void testMySqlLimitTwoArgumentsCompliance() {
        // MySQL官方文档：第一个参数指定返回第一行的偏移量，第二个参数指定返回的最大行数
        String sql = "SELECT * FROM tbl LIMIT 5,10;";

        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");

        // 验证正常LIMIT语法不应该产生告警
        boolean hasWarning = result.getWarnings().stream()
            .anyMatch(warning -> warning.getMessage().toLowerCase().contains("limit"));

        assertFalse(hasWarning, 
            "根据MySQL 8.4官方文档，LIMIT offset,count语法是标准语法，不应该产生告警");
    }

    @Test
    @DisplayName("MySQL官方文档合规性：LIMIT OFFSET语法（PostgreSQL兼容）")
    void testMySqlLimitOffsetPostgreSqlCompatibility() {
        // MySQL官方文档：为了与PostgreSQL兼容，MySQL也支持 LIMIT row_count OFFSET offset 语法
        String sql = "SELECT * FROM tbl LIMIT 10 OFFSET 5;";

        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");

        // 验证PostgreSQL兼容语法不应该产生告警
        boolean hasWarning = result.getWarnings().stream()
            .anyMatch(warning -> warning.getMessage().toLowerCase().contains("limit") ||
                     warning.getMessage().toLowerCase().contains("offset"));

        assertFalse(hasWarning, 
            "根据MySQL 8.4官方文档，LIMIT row_count OFFSET offset是PostgreSQL兼容语法，不应该产生告警");
    }

    @Test
    @DisplayName("MySQL官方文档合规性：初始行偏移量为0")
    void testMySqlOffsetStartsFromZero() {
        // MySQL官方文档：初始行的偏移量是0（不是1）
        String sql = "SELECT * FROM tbl LIMIT 5,10;  -- Retrieve rows 6-15";

        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");

        // 验证注释中的说明不会被误识别为LIMIT子句
        boolean hasWarning = result.getWarnings().stream()
            .anyMatch(warning -> warning.getMessage().toLowerCase().contains("limit"));

        assertFalse(hasWarning, 
            "根据MySQL 8.4官方文档，注释中的LIMIT说明不应该被识别为SQL子句");
    }

    @Test
    @DisplayName("MySQL官方文档合规性：大LIMIT值性能告警")
    void testMySqlLargeLimitPerformanceWarning() {
        // MySQL官方文档：虽然语法正确，但大LIMIT值可能导致性能问题
        String sql = "SELECT * FROM tbl LIMIT 5000;";

        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");

        // 验证大LIMIT值应该产生性能告警
        boolean hasPerformanceWarning = result.getWarnings().stream()
            .anyMatch(warning -> warning.getMessage().contains("大结果集") && 
                     warning.getMessage().contains("LIMIT") &&
                     warning.getMessage().contains("https://dev.mysql.com/doc/refman/8.4/en/select.html"));

        assertTrue(hasPerformanceWarning, 
            "根据性能最佳实践，大LIMIT值应该产生性能告警并包含官方文档链接");
    }

    @Test
    @DisplayName("MySQL官方文档合规性：大OFFSET值性能告警")
    void testMySqlLargeOffsetPerformanceWarning() {
        // MySQL官方文档：大OFFSET值在深度分页时效率低下
        String sql = "SELECT * FROM tbl LIMIT 10 OFFSET 2000;";

        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");

        // 验证大OFFSET值应该产生性能告警
        boolean hasPerformanceWarning = result.getWarnings().stream()
            .anyMatch(warning -> warning.getMessage().contains("大偏移量") && 
                     warning.getMessage().contains("OFFSET") &&
                     warning.getMessage().contains("https://dev.mysql.com/doc/refman/8.4/en/select.html"));

        assertTrue(hasPerformanceWarning, 
            "根据性能最佳实践，大OFFSET值应该产生性能告警并包含官方文档链接");
    }

    @Test
    @DisplayName("MySQL官方文档合规性：非SELECT语句不包含LIMIT")
    void testNonSelectStatementsDoNotContainLimit() {
        // MySQL官方文档：LIMIT子句用于SELECT语句，其他语句类型不包含LIMIT
        String sql = """
            INSERT INTO users (name, email) VALUES ('John', '<EMAIL>');
            UPDATE users SET email = '<EMAIL>' WHERE id = 1;
            DELETE FROM users WHERE id = 1;
            ALTER TABLE users ADD COLUMN age INT;
            CREATE TABLE test (id INT PRIMARY KEY);
            DROP TABLE test;
            """;

        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");

        // 验证非SELECT语句不应该产生LIMIT相关告警
        boolean hasLimitWarning = result.getWarnings().stream()
            .anyMatch(warning -> warning.getMessage().toLowerCase().contains("limit") ||
                     warning.getMessage().toLowerCase().contains("大结果集"));

        assertFalse(hasLimitWarning, 
            "根据MySQL 8.4官方文档，非SELECT语句不包含LIMIT子句，不应该产生相关告警");
    }

    @Test
    @DisplayName("MySQL官方文档合规性：动态阈值配置验证")
    void testDynamicThresholdConfiguration() {
        // 测试动态阈值配置是否正确工作
        String sql = "SELECT * FROM users LIMIT 999;";  // 刚好低于默认阈值1000

        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");

        // 验证低于阈值的LIMIT值不应该产生告警
        boolean hasWarning = result.getWarnings().stream()
            .anyMatch(warning -> warning.getMessage().contains("大结果集"));

        assertFalse(hasWarning, 
            "根据动态阈值配置，低于阈值的LIMIT值不应该产生告警");

        // 测试刚好达到阈值的情况
        String sqlAtThreshold = "SELECT * FROM users LIMIT 1000;";
        StrictValidationResult resultAtThreshold = validator.validate(sqlAtThreshold, "mysql", "dameng");

        boolean hasWarningAtThreshold = resultAtThreshold.getWarnings().stream()
            .anyMatch(warning -> warning.getMessage().contains("大结果集"));

        assertTrue(hasWarningAtThreshold, 
            "根据动态阈值配置，达到阈值的LIMIT值应该产生告警");
    }
}
