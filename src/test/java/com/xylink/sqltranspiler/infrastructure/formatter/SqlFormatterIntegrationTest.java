package com.xylink.sqltranspiler.infrastructure.formatter;

import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.Transpiler;

/**
 * SQL格式化器集成测试 - 验证所有数据库的格式化效果
 *
 * 测试原则：严格基于各数据库官方文档进行格式化验证，不允许推测或假设
 *
 * 官方文档依据：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - MySQL 8.4 SQL语法：https://dev.mysql.com/doc/refman/8.4/en/sql-syntax.html
 * - MySQL 8.4 SQL语句：https://dev.mysql.com/doc/refman/8.4/en/sql-statements.html
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * - 神通官方文档：@shentong.md
 *
 * 验证策略：
 * 1. 根据各数据库官方文档验证格式化集成的正确性
 * 2. 确保格式化结果符合官方文档规范
 * 3. 验证跨数据库格式化的一致性
 * 4. 测试复杂SQL语句的集成格式化
 *
 * 基于官方文档的验证逻辑：
 * - 每个格式化集成测试都必须有相应数据库官方文档的明确依据
 * - 格式化结果必须符合目标数据库官方文档的语法规范
 * - 验证逻辑必须引用具体的官方文档章节
 * - 集成测试必须覆盖官方文档中的关键语法特性
 *
 * <AUTHOR>
 */
public class SqlFormatterIntegrationTest {

    @Test
    public void testDamengFormattingIntegration() {
        Transpiler transpiler = new Transpiler();

        // 测试CREATE TABLE格式化
        String createTableSql = "CREATE TABLE users (id INT PRIMARY KEY, name VARCHAR(100), email VARCHAR(255), created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP);";
        String result = transpiler.transpile(createTableSql, "mysql", "dameng").translatedSql();

        System.out.println("=== 达梦数据库 CREATE TABLE 格式化 ===");
        System.out.println(result);

        // 验证格式化效果
        assertTrue(result.contains("CREATE TABLE"), "Should contain CREATE TABLE");
        assertTrue(result.contains("\n"), "Should have line breaks");
        assertTrue(result.contains("    "), "Should have indentation");

        // 测试SELECT格式化
        String selectSql = "SELECT u.id, u.name, COUNT(*) as total FROM users u WHERE u.created_at > '2023-01-01' GROUP BY u.id, u.name ORDER BY total DESC LIMIT 10;";
        String selectResult = transpiler.transpile(selectSql, "mysql", "dameng").translatedSql();

        System.out.println("\n=== 达梦数据库 SELECT 格式化 ===");
        System.out.println(selectResult);

        // 验证SELECT格式化
        assertTrue(selectResult.contains("SELECT"), "Should contain SELECT");
        assertTrue(selectResult.contains("FROM"), "Should contain FROM");
        assertTrue(selectResult.contains("WHERE"), "Should contain WHERE");
        assertTrue(selectResult.contains("GROUP BY"), "Should contain GROUP BY");
        assertTrue(selectResult.contains("ORDER BY"), "Should contain ORDER BY");
    }

    @Test
    public void testKingbaseFormattingIntegration() {
        Transpiler transpiler = new Transpiler();

        // 测试CREATE TABLE格式化
        String createTableSql = "CREATE TABLE orders (id INT AUTO_INCREMENT PRIMARY KEY, customer_name VARCHAR(100), total_amount DECIMAL(10,2));";
        String result = transpiler.transpile(createTableSql, "mysql", "kingbase").translatedSql();

        System.out.println("=== 金仓数据库 CREATE TABLE 格式化 ===");
        System.out.println(result);

        // 验证格式化效果
        assertTrue(result.contains("CREATE TABLE"), "Should contain CREATE TABLE");
        assertTrue(result.contains("\n"), "Should have line breaks");

        // 测试复杂SELECT格式化
        String complexSelectSql = "SELECT o.id, o.customer_name, SUM(oi.amount) as total FROM orders o LEFT JOIN order_items oi ON o.id = oi.order_id WHERE o.created_at BETWEEN '2023-01-01' AND '2023-12-31' GROUP BY o.id, o.customer_name HAVING SUM(oi.amount) > 1000 ORDER BY total DESC LIMIT 20;";
        String selectResult = transpiler.transpile(complexSelectSql, "mysql", "kingbase").translatedSql();

        System.out.println("\n=== 金仓数据库 复杂SELECT 格式化 ===");
        System.out.println(selectResult);

        // 验证复杂SELECT格式化
        assertTrue(selectResult.contains("LEFT JOIN"), "Should contain LEFT JOIN");
        assertTrue(selectResult.contains("HAVING"), "Should contain HAVING");
        String[] lines = selectResult.split("\n");
        assertTrue(lines.length > 5, "Should have multiple lines for complex query");
    }

    @Test
    public void testShentongFormattingIntegration() {
        Transpiler transpiler = new Transpiler();

        // 测试CREATE TABLE格式化（包含注释）
        String createTableSql = "CREATE TABLE products (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(200), price DECIMAL(10,2), category ENUM('electronics','books','clothing') DEFAULT 'electronics');";
        String result = transpiler.transpile(createTableSql, "mysql", "shentong").translatedSql();

        System.out.println("=== 神通数据库 CREATE TABLE 格式化 ===");
        System.out.println(result);

        // 验证格式化效果
        assertTrue(result.contains("-- 神通数据库CREATE TABLE语句"), "Should contain comment");
        assertTrue(result.contains("CREATE TABLE"), "Should contain CREATE TABLE");
        assertTrue(result.contains("\n"), "Should have line breaks");

        // 验证注释和CREATE TABLE分离
        String[] lines = result.split("\n");
        assertTrue(lines.length > 1, "Should have multiple lines");
        assertTrue(lines[0].startsWith("--"), "First line should be comment");
        assertTrue(lines[1].contains("CREATE TABLE"), "Second line should contain CREATE TABLE");

        // 测试INSERT格式化
        String insertSql = "INSERT INTO products (name, price, category) VALUES ('Laptop', 999.99, 'electronics'), ('Book', 29.99, 'books'), ('Shirt', 39.99, 'clothing');";
        String insertResult = transpiler.transpile(insertSql, "mysql", "shentong").translatedSql();

        System.out.println("\n=== 神通数据库 INSERT 格式化 ===");
        System.out.println(insertResult);

        // 验证INSERT格式化
        assertTrue(insertResult.contains("INSERT"), "Should contain INSERT");
        assertTrue(insertResult.contains("VALUES"), "Should contain VALUES");
    }

    @Test
    public void testAllDatabasesConsistentFormatting() {
        String testSql = "SELECT id, name FROM users WHERE created_at > '2023-01-01' ORDER BY name;";

        // 测试所有数据库的格式化一致性
        Transpiler transpiler = new Transpiler();

        String damengResult = transpiler.transpile(testSql, "mysql", "dameng").translatedSql();
        String kingbaseResult = transpiler.transpile(testSql, "mysql", "kingbase").translatedSql();
        String shentongResult = transpiler.transpile(testSql, "mysql", "shentong").translatedSql();
        
        System.out.println("=== 格式化一致性测试 ===");
        System.out.println("达梦:");
        System.out.println(damengResult);
        System.out.println("\n金仓:");
        System.out.println(kingbaseResult);
        System.out.println("\n神通:");
        System.out.println(shentongResult);
        
        // 验证所有数据库都有良好的格式化
        for (String result : new String[]{damengResult, kingbaseResult, shentongResult}) {
            assertTrue(result.contains("SELECT"), "Should contain SELECT");
            assertTrue(result.contains("FROM"), "Should contain FROM");
            assertTrue(result.contains("WHERE"), "Should contain WHERE");
            assertTrue(result.contains("ORDER BY"), "Should contain ORDER BY");
            assertTrue(result.contains("\n"), "Should have line breaks");
        }
        
        // 验证神通数据库有注释（如果适用）
        if (shentongResult.contains("--")) {
            assertTrue(shentongResult.split("\n").length > 1, "Shentong should have comment separation");
        }
    }

    @Test
    public void testFormattingPreservesSemantics() {
        Transpiler transpiler = new Transpiler();

        // 测试格式化不会改变SQL语义
        String originalSql = "SELECT COUNT(*) FROM users WHERE status='active' AND created_at>='2023-01-01';";
        String formattedSql = transpiler.transpile(originalSql, "mysql", "dameng").translatedSql();
        
        System.out.println("=== 语义保持测试 ===");
        System.out.println("原始SQL: " + originalSql);
        System.out.println("格式化后:");
        System.out.println(formattedSql);
        
        // 验证关键元素都存在
        assertTrue(formattedSql.contains("COUNT(*)"), "Should preserve COUNT(*)");
        assertTrue(formattedSql.contains("users"), "Should preserve table name");
        assertTrue(formattedSql.contains("status='active'"), "Should preserve WHERE condition");
        assertTrue(formattedSql.contains("created_at>='2023-01-01'"), "Should preserve date condition");
        assertTrue(formattedSql.contains("AND"), "Should preserve AND operator");
    }
}
