-- 数据类型转换参考结果 - 严格遵循官方文档规范
--
-- 重要说明：此文件仅作为参考，不作为硬编码期望值使用
-- 实际测试应基于达梦官方文档进行动态验证
--
-- 官方文档依据：
-- - 达梦数据库: https://eco.dameng.com/document/dm/zh-cn/sql-dev/
-- - 数据类型映射: https://eco.dameng.com/document/dm/zh-cn/sql-dev/dmpl-sql-datatype.html
--
-- 对应输入文件: mysql/basic/data_types.sql

CREATE TABLE "data_types_test" (
    "tiny_col" TINYINT NOT NULL,
    "small_col" SMALLINT DEFAULT 0,
    "int_col" INT DEFAULT NULL,
    "big_col" BIGINT NOT NULL,
    "bool_flag" BOOLEAN DEFAULT 1,
    "float_col" FLOAT(7,4) DEFAULT 0.0,
    "double_col" DOUBLE(15,8) DEFAULT NULL,
    "decimal_col" DECIMAL(10,2) DEFAULT 0.00,
    "char_col" CHAR(10) DEFAULT '',
    "varchar_col" VARCHAR(255) NOT NULL,
    "text_col" TEXT,
    "longtext_col" CLOB,
    "blob_col" BLOB,
    "longblob_col" BLOB,
    "date_col" DATE DEFAULT NULL,
    "datetime_col" DATETIME DEFAULT NULL,
    "timestamp_col" TIMESTAMP DEFAULT SYSDATE,
    PRIMARY KEY ("big_col")
) CHARACTER SET UTF8;
