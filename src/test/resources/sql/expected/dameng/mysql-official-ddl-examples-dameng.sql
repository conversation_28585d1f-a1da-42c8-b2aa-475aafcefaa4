-- 达梦数据库转换结果（基于达梦官方文档）
-- 基于官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
-- 严格遵循达梦官方语法规范

-- 1. 基础CREATE TABLE语句转换
-- MySQL AUTO_INCREMENT转换为达梦IDENTITY（基于达梦官方文档）
CREATE TABLE "users" (
    "id" INT IDENTITY(1,1) PRIMARY KEY,
    "username" VARCHAR(50) NOT NULL,
    "email" VARCHAR(100) UNIQUE,
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. 数据类型转换（基于达梦官方文档数据类型章节）
CREATE TABLE "data_types_example" (
    -- 数值类型转换
    "tiny_int" TINYINT,
    "small_int" SMALLINT,
    "medium_int" INT,        -- MEDIUMINT转换为INT
    "int_col" INT,
    "big_int" BIGINT,
    "decimal_col" DECIMAL(10,2),
    "float_col" FLOAT,
    "double_col" DOUBLE,
    
    -- 字符串类型转换
    "char_col" CHAR(10),
    "varchar_col" VARCHAR(255),
    "text_col" CLOB,         -- TEXT转换为CLOB
    "longtext_col" CLOB,     -- LONGTEXT转换为CLOB
    
    -- 日期时间类型转换
    "date_col" DATE,
    "time_col" TIME,
    "datetime_col" DATETIME,
    "timestamp_col" TIMESTAMP,
    "year_col" INT,          -- YEAR转换为INT
    
    -- JSON类型转换为CLOB（达梦8.0+支持JSON，但为兼容性使用CLOB）
    "json_col" CLOB
);

-- 3. 约束转换（基于达梦官方文档约束章节）
CREATE TABLE "products" (
    "id" INT IDENTITY(1,1) PRIMARY KEY,
    "name" VARCHAR(100) NOT NULL,
    "price" DECIMAL(10,2) NOT NULL CHECK ("price" > 0),
    "category_id" INT,
    "stock_quantity" INT DEFAULT 0 CHECK ("stock_quantity" >= 0),
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 外键约束单独创建（达梦推荐做法）
ALTER TABLE "products" ADD CONSTRAINT "fk_products_category" 
    FOREIGN KEY ("category_id") REFERENCES "categories"("id") ON DELETE SET NULL;

-- 4. 索引转换（基于达梦官方文档索引章节）
CREATE TABLE "orders" (
    "id" INT IDENTITY(1,1) PRIMARY KEY,
    "user_id" INT NOT NULL,
    "product_id" INT NOT NULL,
    "quantity" INT NOT NULL DEFAULT 1,
    "order_date" DATE NOT NULL,
    "status" VARCHAR(20) DEFAULT 'pending' CHECK ("status" IN ('pending', 'processing', 'shipped', 'delivered', 'cancelled')),
    "total_amount" DECIMAL(10,2) NOT NULL
);

-- 索引单独创建（达梦推荐做法）
CREATE INDEX "idx_orders_user_date" ON "orders" ("user_id", "order_date");
CREATE INDEX "idx_orders_product" ON "orders" ("product_id");
CREATE INDEX "idx_orders_status" ON "orders" ("status");
CREATE UNIQUE INDEX "uk_orders_order_ref" ON "orders" ("user_id", "product_id", "order_date");

-- 5. ENUM和SET类型转换（达梦使用CHECK约束实现）
CREATE TABLE "user_preferences" (
    "id" INT IDENTITY(1,1) PRIMARY KEY,
    "user_id" INT NOT NULL,
    "theme" VARCHAR(10) DEFAULT 'light' CHECK ("theme" IN ('light', 'dark', 'auto')),
    "notifications" VARCHAR(50) DEFAULT 'email',  -- SET类型简化为VARCHAR
    "language" VARCHAR(5) DEFAULT 'en' CHECK ("language" IN ('en', 'zh', 'ja', 'ko'))
);

-- 6. 分区表转换（达梦支持分区，但语法略有不同）
CREATE TABLE "sales_data" (
    "id" INT IDENTITY(1,1),
    "sale_date" DATE NOT NULL,
    "amount" DECIMAL(10,2) NOT NULL,
    "region" VARCHAR(50) NOT NULL,
    PRIMARY KEY ("id", "sale_date")
) PARTITION BY RANGE ("sale_date") (
    PARTITION "p2020" VALUES LESS THAN ('2021-01-01'),
    PARTITION "p2021" VALUES LESS THAN ('2022-01-01'),
    PARTITION "p2022" VALUES LESS THAN ('2023-01-01'),
    PARTITION "p2023" VALUES LESS THAN ('2024-01-01'),
    PARTITION "p_future" VALUES LESS THAN (MAXVALUE)
);

-- 7. 临时表转换（达梦支持临时表）
CREATE GLOBAL TEMPORARY TABLE "temp_calculations" (
    "id" INT IDENTITY(1,1) PRIMARY KEY,
    "calculation_result" DECIMAL(15,4),
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ON COMMIT PRESERVE ROWS;

-- 8. 视图转换（达梦完全支持视图）
CREATE VIEW "user_order_summary" AS
SELECT 
    u."id",
    u."username",
    u."email",
    COUNT(o."id") as "total_orders",
    SUM(o."total_amount") as "total_spent",
    MAX(o."order_date") as "last_order_date"
FROM "users" u
LEFT JOIN "orders" o ON u."id" = o."user_id"
GROUP BY u."id", u."username", u."email";

-- 9. 存储过程转换（达梦支持存储过程，语法略有不同）
CREATE OR REPLACE PROCEDURE "GetUserOrders"(IN "user_id" INT)
AS
BEGIN
    SELECT 
        o."id",
        o."order_date",
        o."status",
        o."total_amount",
        p."name" as "product_name"
    FROM "orders" o
    JOIN "products" p ON o."product_id" = p."id"
    WHERE o."user_id" = "user_id"
    ORDER BY o."order_date" DESC;
END;

-- 10. 触发器转换（达梦支持触发器）
CREATE OR REPLACE TRIGGER "update_product_stock"
    AFTER INSERT ON "orders"
    FOR EACH ROW
BEGIN
    UPDATE "products" 
    SET "stock_quantity" = "stock_quantity" - :NEW."quantity"
    WHERE "id" = :NEW."product_id";
END;

-- 11. 函数转换（达梦支持函数）
CREATE OR REPLACE FUNCTION "calculate_discount"("original_price" DECIMAL(10,2), "discount_rate" DECIMAL(3,2))
RETURNS DECIMAL(10,2)
AS
BEGIN
    DECLARE "discounted_price" DECIMAL(10,2);
    SET "discounted_price" = "original_price" * (1 - "discount_rate");
    RETURN "discounted_price";
END;

-- 12. 全文索引转换（达梦有自己的全文检索语法）
CREATE TABLE "articles" (
    "id" INT IDENTITY(1,1) PRIMARY KEY,
    "title" VARCHAR(200) NOT NULL,
    "content" CLOB NOT NULL,
    "author" VARCHAR(100),
    "published_date" DATE
);

-- 达梦全文索引创建
CREATE FULLTEXT INDEX "ft_articles_title_content" ON "articles"("title", "content");

-- 13. 生成列转换（达梦支持计算列）
CREATE TABLE "rectangles" (
    "id" INT IDENTITY(1,1) PRIMARY KEY,
    "width" DECIMAL(10,2) NOT NULL,
    "height" DECIMAL(10,2) NOT NULL,
    "area" DECIMAL(10,2) AS ("width" * "height"),
    "perimeter" DECIMAL(10,2) AS (2 * ("width" + "height"))
);

-- 14. 窗口函数相关表转换
CREATE TABLE "employee_salaries" (
    "id" INT IDENTITY(1,1) PRIMARY KEY,
    "employee_name" VARCHAR(100) NOT NULL,
    "department" VARCHAR(50) NOT NULL,
    "salary" DECIMAL(10,2) NOT NULL,
    "hire_date" DATE NOT NULL
);

-- 15. 公用表表达式相关表转换
CREATE TABLE "categories" (
    "id" INT IDENTITY(1,1) PRIMARY KEY,
    "name" VARCHAR(100) NOT NULL,
    "parent_id" INT,
    "level" INT DEFAULT 1
);

ALTER TABLE "categories" ADD CONSTRAINT "fk_categories_parent" 
    FOREIGN KEY ("parent_id") REFERENCES "categories"("id") ON DELETE CASCADE;

-- 16. 字符集转换（达梦使用UTF-8）
CREATE TABLE "multilingual_content" (
    "id" INT IDENTITY(1,1) PRIMARY KEY,
    "title_en" VARCHAR(200),
    "title_zh" VARCHAR(200),
    "content_en" CLOB,
    "content_zh" CLOB,
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 17. 空间数据类型转换（达梦支持空间数据）
CREATE TABLE "locations" (
    "id" INT IDENTITY(1,1) PRIMARY KEY,
    "name" VARCHAR(100) NOT NULL,
    "coordinates" ST_POINT NOT NULL,
    "area" ST_POLYGON
);

CREATE SPATIAL INDEX "sp_locations_coordinates" ON "locations"("coordinates");

-- 18. 表压缩转换（达梦有自己的压缩语法）
CREATE TABLE "compressed_logs" (
    "id" BIGINT IDENTITY(1,1) PRIMARY KEY,
    "log_message" CLOB NOT NULL,
    "log_level" VARCHAR(10) NOT NULL CHECK ("log_level" IN ('DEBUG', 'INFO', 'WARN', 'ERROR')),
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) COMPRESS;

CREATE INDEX "idx_compressed_logs_level_time" ON "compressed_logs" ("log_level", "created_at");

-- 19. 表加密转换（达梦支持透明数据加密）
CREATE TABLE "sensitive_data" (
    "id" INT IDENTITY(1,1) PRIMARY KEY,
    "user_id" INT NOT NULL,
    "encrypted_data" CLOB NOT NULL,
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENCRYPT;

CREATE INDEX "idx_sensitive_data_user" ON "sensitive_data" ("user_id");

-- 20. 综合示例转换
CREATE TABLE "comprehensive_example" (
    "id" BIGINT IDENTITY(1000,1) PRIMARY KEY,
    "data" VARCHAR(1000) NOT NULL,
    "status" TINYINT DEFAULT 1,
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX "idx_comprehensive_example_status" ON "comprehensive_example" ("status");
CREATE INDEX "idx_comprehensive_example_created" ON "comprehensive_example" ("created_at");

-- 表注释转换为COMMENT ON语句（达梦推荐做法）
COMMENT ON TABLE "comprehensive_example" IS '综合示例表，包含多种MySQL特性';
