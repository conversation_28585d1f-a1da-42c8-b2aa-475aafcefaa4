-- 真实世界SQL示例文件
-- 基于实际生产环境的SQL语句，用于测试转换器的完整性

-- 创建数据库
CREATE DATABASE IF NOT EXISTS ecommerce_db DEFAULT CHARSET=utf8mb4;
USE ecommerce_db;

-- 用户表
CREATE TABLE users (
    id int(11) NOT NULL AUTO_INCREMENT,
    username varchar(50) NOT NULL UNIQUE,
    email varchar(100) NOT NULL UNIQUE,
    password_hash varchar(255) NOT NULL,
    first_name varchar(50),
    last_name varchar(50),
    phone varchar(20),
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active tinyint(1) DEFAULT 1,
    PRIMARY KEY (id),
    KEY idx_username (username),
    KEY idx_email (email),
    KEY idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户信息表';

-- 商品分类表
CREATE TABLE categories (
    id int(11) NOT NULL AUTO_INCREMENT,
    name varchar(100) NOT NULL,
    description text,
    parent_id int(11) DEFAULT NULL,
    sort_order int(11) DEFAULT 0,
    is_active tinyint(1) DEFAULT 1,
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY idx_parent_id (parent_id),
    KEY idx_sort_order (sort_order),
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品分类表';

-- 商品表
CREATE TABLE products (
    id int(11) NOT NULL AUTO_INCREMENT,
    name varchar(200) NOT NULL,
    description text,
    category_id int(11) NOT NULL,
    price decimal(10,2) NOT NULL,
    stock_quantity int(11) DEFAULT 0,
    sku varchar(100) UNIQUE,
    weight decimal(8,3),
    dimensions varchar(100),
    is_active tinyint(1) DEFAULT 1,
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY idx_category_id (category_id),
    KEY idx_sku (sku),
    KEY idx_price (price),
    KEY idx_created_at (created_at),
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品信息表';

-- 订单表
CREATE TABLE orders (
    id int(11) NOT NULL AUTO_INCREMENT,
    user_id int(11) NOT NULL,
    order_number varchar(50) NOT NULL UNIQUE,
    total_amount decimal(12,2) NOT NULL,
    status enum('pending','confirmed','shipped','delivered','cancelled') DEFAULT 'pending',
    shipping_address text NOT NULL,
    billing_address text,
    payment_method varchar(50),
    payment_status enum('pending','paid','failed','refunded') DEFAULT 'pending',
    notes text,
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY idx_user_id (user_id),
    KEY idx_order_number (order_number),
    KEY idx_status (status),
    KEY idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- 订单详情表
CREATE TABLE order_items (
    id int(11) NOT NULL AUTO_INCREMENT,
    order_id int(11) NOT NULL,
    product_id int(11) NOT NULL,
    quantity int(11) NOT NULL,
    unit_price decimal(10,2) NOT NULL,
    total_price decimal(12,2) NOT NULL,
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY idx_order_id (order_id),
    KEY idx_product_id (product_id),
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单详情表';

-- 插入测试数据
INSERT INTO users (username, email, password_hash, first_name, last_name, phone) VALUES
('john_doe', '<EMAIL>', 'hashed_password_1', 'John', 'Doe', '1234567890'),
('jane_smith', '<EMAIL>', 'hashed_password_2', 'Jane', 'Smith', '0987654321'),
('admin_user', '<EMAIL>', 'hashed_password_3', 'Admin', 'User', '1111111111');

INSERT INTO categories (name, description, parent_id, sort_order) VALUES
('Electronics', 'Electronic devices and accessories', NULL, 1),
('Computers', 'Desktop and laptop computers', 1, 1),
('Mobile Phones', 'Smartphones and accessories', 1, 2),
('Clothing', 'Fashion and apparel', NULL, 2),
('Men Clothing', 'Clothing for men', 4, 1),
('Women Clothing', 'Clothing for women', 4, 2);

INSERT INTO products (name, description, category_id, price, stock_quantity, sku, weight) VALUES
('MacBook Pro 16"', 'Apple MacBook Pro with M2 chip', 2, 2499.99, 10, 'MBP-16-M2', 2.1),
('iPhone 14 Pro', 'Latest iPhone with Pro camera system', 3, 999.99, 25, 'IP14-PRO-128', 0.206),
('Samsung Galaxy S23', 'Android smartphone with advanced features', 3, 799.99, 15, 'SGS23-256', 0.168),
('Nike Air Max', 'Comfortable running shoes', 5, 129.99, 50, 'NAM-2023-42', 0.8),
('Levi\'s Jeans', 'Classic denim jeans', 5, 79.99, 30, 'LJ-501-32-34', 0.6);

-- 复杂查询示例
SELECT 
    u.username,
    u.email,
    COUNT(o.id) as order_count,
    SUM(o.total_amount) as total_spent,
    AVG(o.total_amount) as avg_order_value,
    MAX(o.created_at) as last_order_date
FROM users u
LEFT JOIN orders o ON u.id = o.user_id
WHERE u.is_active = 1
GROUP BY u.id, u.username, u.email
HAVING COUNT(o.id) > 0
ORDER BY total_spent DESC
LIMIT 10;

-- 带子查询的复杂查询
SELECT 
    p.name as product_name,
    c.name as category_name,
    p.price,
    p.stock_quantity,
    (SELECT COUNT(*) FROM order_items oi WHERE oi.product_id = p.id) as times_ordered,
    (SELECT SUM(oi.quantity) FROM order_items oi WHERE oi.product_id = p.id) as total_quantity_sold
FROM products p
JOIN categories c ON p.category_id = c.id
WHERE p.is_active = 1
AND p.price BETWEEN 100 AND 1000
ORDER BY times_ordered DESC, p.price ASC;

-- 更新语句
UPDATE products 
SET stock_quantity = stock_quantity - 1 
WHERE id IN (
    SELECT product_id 
    FROM order_items 
    WHERE order_id = 1
);

-- 删除语句
DELETE FROM order_items 
WHERE order_id IN (
    SELECT id 
    FROM orders 
    WHERE status = 'cancelled' 
    AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)
);

-- 创建视图
CREATE VIEW active_products_view AS
SELECT 
    p.id,
    p.name,
    p.price,
    p.stock_quantity,
    c.name as category_name
FROM products p
JOIN categories c ON p.category_id = c.id
WHERE p.is_active = 1 AND c.is_active = 1;

-- 创建索引
CREATE INDEX idx_products_price_stock ON products(price, stock_quantity);
CREATE INDEX idx_orders_status_created ON orders(status, created_at);

-- 存储过程示例（注释掉，因为可能不被所有数据库支持）
-- DELIMITER //
-- CREATE PROCEDURE GetUserOrders(IN user_id INT)
-- BEGIN
--     SELECT * FROM orders WHERE user_id = user_id ORDER BY created_at DESC;
-- END //
-- DELIMITER ;

-- 触发器示例（注释掉，因为可能不被所有数据库支持）
-- CREATE TRIGGER update_product_stock 
-- AFTER INSERT ON order_items
-- FOR EACH ROW
-- BEGIN
--     UPDATE products 
--     SET stock_quantity = stock_quantity - NEW.quantity 
--     WHERE id = NEW.product_id;
-- END;
