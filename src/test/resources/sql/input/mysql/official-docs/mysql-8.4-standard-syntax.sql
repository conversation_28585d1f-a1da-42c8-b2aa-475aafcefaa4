-- MySQL 8.4官方文档标准语法测试用例
-- 严格基于MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/
-- 
-- 测试原则：
-- 1. 不允许推测，必须基于官方文档
-- 2. 测试期望与官方文档不符时，修正测试用例而不是降低代码质量
-- 3. 坚持正确的实现，确保功能的准确性和完整性

-- CREATE TABLE语法测试
-- 基于: https://dev.mysql.com/doc/refman/8.4/en/create-table.html
CREATE TABLE users (
    id INT NOT NULL AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY uk_username (username),
    UNIQUE KEY uk_email (email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 数据类型测试
-- 基于: https://dev.mysql.com/doc/refman/8.4/en/data-types.html
CREATE TABLE data_types_official (
    -- 整数类型 - https://dev.mysql.com/doc/refman/8.4/en/integer-types.html
    tiny_col TINYINT,
    small_col SMALLINT,
    medium_col MEDIUMINT,
    int_col INT,
    big_col BIGINT,
    
    -- 浮点类型 - https://dev.mysql.com/doc/refman/8.4/en/floating-point-types.html
    float_col FLOAT,
    double_col DOUBLE,
    decimal_col DECIMAL(10,2),
    
    -- 字符串类型 - https://dev.mysql.com/doc/refman/8.4/en/string-types.html
    char_col CHAR(50),
    varchar_col VARCHAR(255),
    text_col TEXT,
    tinytext_col TINYTEXT,
    mediumtext_col MEDIUMTEXT,
    longtext_col LONGTEXT,
    
    -- 二进制类型 - https://dev.mysql.com/doc/refman/8.4/en/binary-varbinary.html
    binary_col BINARY(16),
    varbinary_col VARBINARY(255),
    blob_col BLOB,
    tinyblob_col TINYBLOB,
    mediumblob_col MEDIUMBLOB,
    longblob_col LONGBLOB,
    
    -- 日期时间类型 - https://dev.mysql.com/doc/refman/8.4/en/date-and-time-types.html
    date_col DATE,
    time_col TIME,
    datetime_col DATETIME,
    timestamp_col TIMESTAMP,
    year_col YEAR,
    
    -- JSON类型 - https://dev.mysql.com/doc/refman/8.4/en/json.html
    json_col JSON,
    
    PRIMARY KEY (int_col)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 约束测试
-- 基于: https://dev.mysql.com/doc/refman/8.4/en/create-table.html#create-table-constraints
CREATE TABLE constraints_official (
    id INT NOT NULL AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL,
    age INT CHECK (age >= 0 AND age <= 150),
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (id),
    UNIQUE KEY uk_email (email),
    KEY idx_name (name),
    KEY idx_status_created (status, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 外键约束测试
-- 基于: https://dev.mysql.com/doc/refman/8.4/en/create-table-foreign-keys.html
CREATE TABLE orders_official (
    order_id INT NOT NULL AUTO_INCREMENT,
    user_id INT NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    quantity INT NOT NULL DEFAULT 1,
    order_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (order_id),
    KEY fk_user_id (user_id),
    CONSTRAINT fk_orders_users FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- INSERT语句测试
-- 基于: https://dev.mysql.com/doc/refman/8.4/en/insert.html
INSERT INTO users (username, email) VALUES 
('john_doe', '<EMAIL>'),
('jane_smith', '<EMAIL>'),
('bob_wilson', '<EMAIL>');

-- 多值INSERT测试
INSERT INTO data_types_official (
    tiny_col, small_col, medium_col, int_col, big_col,
    float_col, double_col, decimal_col,
    char_col, varchar_col, text_col,
    date_col, time_col, datetime_col, timestamp_col, year_col
) VALUES 
(127, 32767, 8388607, 2147483647, 9223372036854775807,
 3.14159, 2.718281828, 12345.67,
 'CHAR50', 'Variable length string', 'Text content',
 '2024-01-15', '14:30:00', '2024-01-15 14:30:00', '2024-01-15 14:30:00', 2024),
(-128, -32768, -8388608, -2147483648, -9223372036854775808,
 -3.14159, -2.718281828, -12345.67,
 'CHAR50B', 'Another variable string', 'More text content',
 '2024-12-31', '23:59:59', '2024-12-31 23:59:59', '2024-12-31 23:59:59', 2024);

-- UPDATE语句测试
-- 基于: https://dev.mysql.com/doc/refman/8.4/en/update.html
UPDATE users SET email = '<EMAIL>' WHERE username = 'john_doe';

-- SELECT语句测试
-- 基于: https://dev.mysql.com/doc/refman/8.4/en/select.html
SELECT 
    u.id,
    u.username,
    u.email,
    u.created_at,
    COUNT(o.order_id) as order_count
FROM users u
LEFT JOIN orders_official o ON u.id = o.user_id
WHERE u.created_at >= '2024-01-01'
GROUP BY u.id, u.username, u.email, u.created_at
HAVING COUNT(o.order_id) > 0
ORDER BY u.created_at DESC, u.username ASC
LIMIT 10 OFFSET 0;

-- DELETE语句测试
-- 基于: https://dev.mysql.com/doc/refman/8.4/en/delete.html
DELETE FROM users WHERE created_at < '2023-01-01';

-- 函数测试
-- 基于: https://dev.mysql.com/doc/refman/8.4/en/functions.html
SELECT 
    -- 字符串函数 - https://dev.mysql.com/doc/refman/8.4/en/string-functions.html
    CONCAT(username, ' - ', email) as user_info,
    UPPER(username) as username_upper,
    LOWER(email) as email_lower,
    LENGTH(username) as username_length,
    SUBSTRING(email, 1, LOCATE('@', email) - 1) as email_prefix,
    
    -- 数值函数 - https://dev.mysql.com/doc/refman/8.4/en/numeric-functions.html
    ABS(-123) as abs_value,
    ROUND(3.14159, 2) as rounded_value,
    CEIL(3.14) as ceiling_value,
    FLOOR(3.99) as floor_value,
    
    -- 日期时间函数 - https://dev.mysql.com/doc/refman/8.4/en/date-and-time-functions.html
    NOW() as current_timestamp,
    CURDATE() as current_date,
    CURTIME() as current_time,
    DATE_FORMAT(created_at, '%Y-%m-%d %H:%i:%s') as formatted_date,
    YEAR(created_at) as created_year,
    MONTH(created_at) as created_month,
    DAY(created_at) as created_day,
    
    -- 条件函数 - https://dev.mysql.com/doc/refman/8.4/en/flow-control-functions.html
    IF(LENGTH(username) > 5, 'Long', 'Short') as username_category,
    IFNULL(email, 'No Email') as email_or_default,
    CASE 
        WHEN LENGTH(username) <= 5 THEN 'Short'
        WHEN LENGTH(username) <= 10 THEN 'Medium'
        ELSE 'Long'
    END as username_length_category
    
FROM users
WHERE created_at IS NOT NULL;

-- 聚合函数测试
-- 基于: https://dev.mysql.com/doc/refman/8.4/en/aggregate-functions.html
SELECT 
    COUNT(*) as total_users,
    COUNT(DISTINCT email) as unique_emails,
    MIN(created_at) as earliest_user,
    MAX(created_at) as latest_user,
    AVG(LENGTH(username)) as avg_username_length,
    SUM(LENGTH(username)) as total_username_length
FROM users;

-- 窗口函数测试（MySQL 8.0+支持）
-- 基于: https://dev.mysql.com/doc/refman/8.4/en/window-functions.html
SELECT 
    username,
    email,
    created_at,
    ROW_NUMBER() OVER (ORDER BY created_at) as row_num,
    RANK() OVER (ORDER BY LENGTH(username) DESC) as username_length_rank,
    DENSE_RANK() OVER (ORDER BY YEAR(created_at)) as year_rank,
    LAG(username, 1) OVER (ORDER BY created_at) as prev_username,
    LEAD(username, 1) OVER (ORDER BY created_at) as next_username
FROM users
ORDER BY created_at;

-- 公用表表达式(CTE)测试（MySQL 8.0+支持）
-- 基于: https://dev.mysql.com/doc/refman/8.4/en/with.html
WITH user_stats AS (
    SELECT 
        YEAR(created_at) as year,
        COUNT(*) as user_count,
        AVG(LENGTH(username)) as avg_username_length
    FROM users
    GROUP BY YEAR(created_at)
),
yearly_growth AS (
    SELECT 
        year,
        user_count,
        LAG(user_count, 1) OVER (ORDER BY year) as prev_year_count,
        user_count - LAG(user_count, 1) OVER (ORDER BY year) as growth
    FROM user_stats
)
SELECT 
    year,
    user_count,
    prev_year_count,
    COALESCE(growth, 0) as growth,
    CASE 
        WHEN prev_year_count IS NULL THEN 'N/A'
        WHEN growth > 0 THEN 'Positive'
        WHEN growth < 0 THEN 'Negative'
        ELSE 'No Change'
    END as growth_trend
FROM yearly_growth
ORDER BY year;

-- 索引提示测试
-- 基于: https://dev.mysql.com/doc/refman/8.4/en/index-hints.html
SELECT /*+ USE_INDEX(users, uk_username) */ 
    id, username, email 
FROM users 
WHERE username LIKE 'john%';

-- 事务控制语句测试
-- 基于: https://dev.mysql.com/doc/refman/8.4/en/commit.html
START TRANSACTION;
INSERT INTO users (username, email) VALUES ('test_user', '<EMAIL>');
UPDATE users SET email = '<EMAIL>' WHERE username = 'test_user';
COMMIT;

-- 锁定语句测试
-- 基于: https://dev.mysql.com/doc/refman/8.4/en/lock-tables.html
LOCK TABLES users READ;
SELECT COUNT(*) FROM users;
UNLOCK TABLES;

-- 变量设置测试
-- 基于: https://dev.mysql.com/doc/refman/8.4/en/set-variable.html
SET @user_count = (SELECT COUNT(*) FROM users);
SELECT @user_count as total_users;

-- 存储过程调用测试（如果存在）
-- 基于: https://dev.mysql.com/doc/refman/8.4/en/call.html
-- CALL get_user_statistics();

-- 注释测试
-- 基于: https://dev.mysql.com/doc/refman/8.4/en/comments.html
CREATE TABLE comments_test (
    id INT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    name VARCHAR(100) NOT NULL COMMENT '用户姓名',
    description TEXT COMMENT '详细描述',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='注释测试表';
