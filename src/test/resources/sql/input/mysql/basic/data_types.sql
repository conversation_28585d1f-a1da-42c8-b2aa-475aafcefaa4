-- 基础数据类型转换测试
-- 测试MySQL到达梦的数据类型映射

CREATE TABLE data_types_test (
    -- 整数类型
    tiny_col tinyint(3) NOT NULL,
    small_col smallint(5) DEFAULT 0,
    int_col int(11) DEFAULT NULL,
    big_col bigint(20) NOT NULL,
    
    -- 布尔类型 (MySQL特殊处理)
    bool_flag tinyint(1) DEFAULT 1,
    
    -- 浮点类型
    float_col float(7,4) DEFAULT 0.0,
    double_col double(15,8) DEFAULT NULL,
    decimal_col decimal(10,2) DEFAULT 0.00,
    
    -- 字符串类型
    char_col char(10) DEFAULT '',
    varchar_col varchar(255) NOT NULL,
    
    -- 大对象类型
    text_col text,
    longtext_col longtext,
    blob_col blob,
    longblob_col longblob,
    
    -- 日期时间类型
    date_col date DEFAULT NULL,
    datetime_col datetime DEFAULT NULL,
    timestamp_col timestamp DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (big_col)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
