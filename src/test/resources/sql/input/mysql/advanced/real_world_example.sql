DROP TABLE IF EXISTS libra_relationship_request;
-- /*!40101 SET @saved_cs_client     = @@character_set_client */;
-- /*!40101 SET character_set_client = utf8 */;
CREATE TABLE libra_relationship_request (
  requester_id bigint(20) NOT NULL,
  requestee_id bigint(20) NOT NULL,
  nemos varchar(4096) DEFAULT '',
  authority_rules varchar(4096) DEFAULT '',
  request_timestamp bigint(20) DEFAULT NULL,
  unique_tag varchar(64) NOT NULL,
  PRIMARY KEY (requester_id,requestee_id),
  UNIQUE KEY unique_tag_constraint (unique_tag),
  KEY libra_relationship_request_requestee_id_fkey (requestee_id),
  CONSTRAINT libra_relationship_request_requestee_id_fkey FOREIGN KEY (requestee_id) REFERENCES libra_user_profile (id) ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT libra_relationship_request_requester_id_fkey FOREIGN KEY (requester_id) REFERENCES libra_user_profile (id) ON DELETE CASCADE ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
-- /*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `libra_relationship_request`
--

LOCK TABLES libra_relationship_request WRITE;
UNLOCK TABLES;
