-- MySQL 8.4官方文档DDL示例
-- 基于官方文档：https://dev.mysql.com/doc/refman/8.4/en/
-- 严格遵循官方语法规范，不允许推测

-- 1. 基础CREATE TABLE语句（基于官方文档13.1.20节）
-- 官方文档：https://dev.mysql.com/doc/refman/8.4/en/create-table.html
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 2. 数据类型示例（基于官方文档11章）
-- 官方文档：https://dev.mysql.com/doc/refman/8.4/en/data-types.html
CREATE TABLE data_types_example (
    -- 数值类型（官方文档11.1节）
    tiny_int TINYINT,
    small_int SMALLINT,
    medium_int MEDIUMINT,
    int_col INT,
    big_int BIGINT,
    decimal_col DECIMAL(10,2),
    float_col FLOAT,
    double_col DOUBLE,
    
    -- 字符串类型（官方文档11.3节）
    char_col CHAR(10),
    varchar_col VARCHAR(255),
    text_col TEXT,
    longtext_col LONGTEXT,
    
    -- 日期时间类型（官方文档11.2节）
    date_col DATE,
    time_col TIME,
    datetime_col DATETIME,
    timestamp_col TIMESTAMP,
    year_col YEAR,
    
    -- JSON类型（官方文档11.5节）
    json_col JSON
) ENGINE=InnoDB;

-- 3. 约束示例（基于官方文档13.1.20.6节）
-- 官方文档：https://dev.mysql.com/doc/refman/8.4/en/create-table-check-constraints.html
CREATE TABLE products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    price DECIMAL(10,2) NOT NULL CHECK (price > 0),
    category_id INT,
    stock_quantity INT DEFAULT 0 CHECK (stock_quantity >= 0),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 外键约束（官方文档*********节）
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
) ENGINE=InnoDB;

-- 4. 索引示例（基于官方文档13.1.15节）
-- 官方文档：https://dev.mysql.com/doc/refman/8.4/en/create-index.html
CREATE TABLE orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL DEFAULT 1,
    order_date DATE NOT NULL,
    status ENUM('pending', 'processing', 'shipped', 'delivered', 'cancelled') DEFAULT 'pending',
    total_amount DECIMAL(10,2) NOT NULL,
    
    -- 复合索引
    INDEX idx_user_date (user_id, order_date),
    INDEX idx_product (product_id),
    INDEX idx_status (status),
    
    -- 唯一索引
    UNIQUE KEY uk_order_ref (user_id, product_id, order_date)
) ENGINE=InnoDB;

-- 5. ENUM和SET类型（基于官方文档11.3.5节）
-- 官方文档：https://dev.mysql.com/doc/refman/8.4/en/enum.html
CREATE TABLE user_preferences (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    theme ENUM('light', 'dark', 'auto') DEFAULT 'light',
    notifications SET('email', 'sms', 'push') DEFAULT 'email',
    language ENUM('en', 'zh', 'ja', 'ko') DEFAULT 'en'
) ENGINE=InnoDB;

-- 6. 分区表示例（基于官方文档24章）
-- 官方文档：https://dev.mysql.com/doc/refman/8.4/en/partitioning.html
CREATE TABLE sales_data (
    id INT AUTO_INCREMENT,
    sale_date DATE NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    region VARCHAR(50) NOT NULL,
    PRIMARY KEY (id, sale_date)
) ENGINE=InnoDB
PARTITION BY RANGE (YEAR(sale_date)) (
    PARTITION p2020 VALUES LESS THAN (2021),
    PARTITION p2021 VALUES LESS THAN (2022),
    PARTITION p2022 VALUES LESS THAN (2023),
    PARTITION p2023 VALUES LESS THAN (2024),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 7. 临时表示例（基于官方文档13.1.20.2节）
-- 官方文档：https://dev.mysql.com/doc/refman/8.4/en/create-temporary-table.html
CREATE TEMPORARY TABLE temp_calculations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    calculation_result DECIMAL(15,4),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=MEMORY;

-- 8. 视图示例（基于官方文档13.1.23节）
-- 官方文档：https://dev.mysql.com/doc/refman/8.4/en/create-view.html
CREATE VIEW user_order_summary AS
SELECT 
    u.id,
    u.username,
    u.email,
    COUNT(o.id) as total_orders,
    SUM(o.total_amount) as total_spent,
    MAX(o.order_date) as last_order_date
FROM users u
LEFT JOIN orders o ON u.id = o.user_id
GROUP BY u.id, u.username, u.email;

-- 9. 存储过程示例（基于官方文档25章）
-- 官方文档：https://dev.mysql.com/doc/refman/8.4/en/stored-programs-views.html
DELIMITER $$
CREATE PROCEDURE GetUserOrders(IN user_id INT)
BEGIN
    SELECT 
        o.id,
        o.order_date,
        o.status,
        o.total_amount,
        p.name as product_name
    FROM orders o
    JOIN products p ON o.product_id = p.id
    WHERE o.user_id = user_id
    ORDER BY o.order_date DESC;
END$$
DELIMITER ;

-- 10. 触发器示例（基于官方文档25.3节）
-- 官方文档：https://dev.mysql.com/doc/refman/8.4/en/triggers.html
CREATE TRIGGER update_product_stock
    AFTER INSERT ON orders
    FOR EACH ROW
BEGIN
    UPDATE products 
    SET stock_quantity = stock_quantity - NEW.quantity
    WHERE id = NEW.product_id;
END;

-- 11. 函数示例（基于官方文档25.2节）
-- 官方文档：https://dev.mysql.com/doc/refman/8.4/en/stored-routines.html
DELIMITER $$
CREATE FUNCTION calculate_discount(original_price DECIMAL(10,2), discount_rate DECIMAL(3,2))
RETURNS DECIMAL(10,2)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE discounted_price DECIMAL(10,2);
    SET discounted_price = original_price * (1 - discount_rate);
    RETURN discounted_price;
END$$
DELIMITER ;

-- 12. 全文索引示例（基于官方文档12.10节）
-- 官方文档：https://dev.mysql.com/doc/refman/8.4/en/fulltext-search.html
CREATE TABLE articles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    author VARCHAR(100),
    published_date DATE,
    
    -- 全文索引
    FULLTEXT(title, content)
) ENGINE=InnoDB;

-- 13. 生成列示例（基于官方文档13.1.20.8节）
-- 官方文档：https://dev.mysql.com/doc/refman/8.4/en/create-table-generated-columns.html
CREATE TABLE rectangles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    width DECIMAL(10,2) NOT NULL,
    height DECIMAL(10,2) NOT NULL,
    area DECIMAL(10,2) AS (width * height) STORED,
    perimeter DECIMAL(10,2) AS (2 * (width + height)) VIRTUAL
) ENGINE=InnoDB;

-- 14. 窗口函数相关表（基于官方文档12.21节）
-- 官方文档：https://dev.mysql.com/doc/refman/8.4/en/window-functions.html
CREATE TABLE employee_salaries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    employee_name VARCHAR(100) NOT NULL,
    department VARCHAR(50) NOT NULL,
    salary DECIMAL(10,2) NOT NULL,
    hire_date DATE NOT NULL
) ENGINE=InnoDB;

-- 15. 公用表表达式(CTE)相关表（基于官方文档13.2.15节）
-- 官方文档：https://dev.mysql.com/doc/refman/8.4/en/with.html
CREATE TABLE categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    parent_id INT,
    level INT DEFAULT 1,
    
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE CASCADE
) ENGINE=InnoDB;

-- 16. 字符集和排序规则示例（基于官方文档10章）
-- 官方文档：https://dev.mysql.com/doc/refman/8.4/en/charset.html
CREATE TABLE multilingual_content (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title_en VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    title_zh VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    content_en TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    content_zh TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 17. 空间数据类型示例（基于官方文档11.4节）
-- 官方文档：https://dev.mysql.com/doc/refman/8.4/en/spatial-type-overview.html
CREATE TABLE locations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    coordinates POINT NOT NULL,
    area POLYGON,
    
    SPATIAL INDEX(coordinates)
) ENGINE=InnoDB;

-- 18. 表压缩示例（基于官方文档15.9节）
-- 官方文档：https://dev.mysql.com/doc/refman/8.4/en/innodb-compression.html
CREATE TABLE compressed_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    log_message TEXT NOT NULL,
    log_level ENUM('DEBUG', 'INFO', 'WARN', 'ERROR') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_level_time (log_level, created_at)
) ENGINE=InnoDB ROW_FORMAT=COMPRESSED KEY_BLOCK_SIZE=8;

-- 19. 表加密示例（基于官方文档15.13节）
-- 官方文档：https://dev.mysql.com/doc/refman/8.4/en/innodb-data-encryption.html
CREATE TABLE sensitive_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    encrypted_data TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_user (user_id)
) ENGINE=InnoDB ENCRYPTION='Y';

-- 20. 表选项综合示例（基于官方文档13.1.20节）
-- 官方文档：https://dev.mysql.com/doc/refman/8.4/en/create-table.html
CREATE TABLE comprehensive_example (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    data VARCHAR(1000) NOT NULL,
    status TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_status (status),
    INDEX idx_created (created_at)
) ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci
  AUTO_INCREMENT=1000
  COMMENT='综合示例表，包含多种MySQL特性';
