-- MySQL到达梦数据库完整数据类型映射测试
-- 严格按照达梦官方文档标准

-- 整数类型测试表
CREATE TABLE integer_types_test (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    tiny_col TINYINT,
    tiny_bool TINYINT(1),
    small_col SMALLINT,
    medium_col MEDIUMINT,
    int_col INT,
    big_col BIGINT
);

-- 浮点类型测试表
CREATE TABLE float_types_test (
    id INT AUTO_INCREMENT PRIMARY KEY,
    float_col FLOAT,
    double_col DOUBLE,
    real_col REAL,
    decimal_col DECIMAL(10,2),
    numeric_col NUMERIC(8,3)
);

-- 字符串类型测试表
CREATE TABLE string_types_test (
    id INT AUTO_INCREMENT PRIMARY KEY,
    char_col CHAR(10),
    varchar_col VARCHAR(255),
    tiny_text TINYTEXT,
    text_col TEXT,
    medium_text MEDIUMTEXT,
    long_text LONGTEXT
);

-- 二进制类型测试表
CREATE TABLE binary_types_test (
    id INT AUTO_INCREMENT PRIMARY KEY,
    binary_col BINARY(16),
    varbinary_col VARBINARY(255),
    tiny_blob TINYBLOB,
    blob_col BLOB,
    medium_blob MEDIUMBLOB,
    long_blob LONGBLOB
);

-- 日期时间类型测试表
CREATE TABLE datetime_types_test (
    id INT AUTO_INCREMENT PRIMARY KEY,
    date_col DATE,
    time_col TIME,
    datetime_col DATETIME,
    timestamp_col TIMESTAMP,
    year_col YEAR
);

-- 特殊类型测试表
CREATE TABLE special_types_test (
    id INT AUTO_INCREMENT PRIMARY KEY,
    bit_col BIT,
    bool_col BOOLEAN,
    enum_col ENUM('active', 'inactive', 'pending'),
    set_col SET('read', 'write', 'execute', 'admin')
);

-- 复合类型测试表（模拟真实业务场景）
CREATE TABLE user_profile (
    user_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) UNIQUE,
    password_hash CHAR(64),
    is_active TINYINT(1) DEFAULT 1,
    age TINYINT UNSIGNED,
    balance DECIMAL(15,2) DEFAULT 0.00,
    profile_image LONGBLOB,
    bio TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    birth_year YEAR,
    status ENUM('active', 'suspended', 'deleted') DEFAULT 'active',
    permissions SET('read', 'write', 'admin') DEFAULT 'read'
);

-- 带约束的测试表
CREATE TABLE product_catalog (
    product_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    product_code CHAR(10) NOT NULL UNIQUE,
    product_name VARCHAR(200) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    weight FLOAT,
    is_available TINYINT(1) DEFAULT 1,
    category_id INT,
    image_data MEDIUMBLOB,
    created_date DATE,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    launch_year YEAR,
    product_type ENUM('physical', 'digital', 'service') NOT NULL,
    features SET('waterproof', 'wireless', 'rechargeable', 'portable')
);

-- 插入测试数据
INSERT INTO integer_types_test (tiny_col, tiny_bool, small_col, medium_col, int_col, big_col) 
VALUES (127, 1, 32767, 8388607, 2147483647, 9223372036854775807);

INSERT INTO float_types_test (float_col, double_col, real_col, decimal_col, numeric_col)
VALUES (3.14159, 2.718281828, 1.414213, 12345.67, 98765.432);

INSERT INTO string_types_test (char_col, varchar_col, tiny_text, text_col, medium_text, long_text)
VALUES ('CHAR10', 'Variable length string', 'Tiny text content', 'Regular text content', 'Medium text content', 'Long text content');

INSERT INTO datetime_types_test (date_col, time_col, datetime_col, timestamp_col, year_col)
VALUES ('2024-01-15', '14:30:00', '2024-01-15 14:30:00', '2024-01-15 14:30:00', 2024);

INSERT INTO special_types_test (bit_col, bool_col, enum_col, set_col)
VALUES (1, TRUE, 'active', 'read,write');

INSERT INTO user_profile (username, email, password_hash, is_active, age, balance, bio, birth_year, status, permissions)
VALUES ('testuser', '<EMAIL>', 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890', 1, 25, 1000.50, 'Test user biography', 1999, 'active', 'read,write');
