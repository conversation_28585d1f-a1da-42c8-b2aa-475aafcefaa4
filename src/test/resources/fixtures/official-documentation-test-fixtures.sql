-- 基于官方文档的测试夹具数据
-- 严格遵照 .augment/rules/rule-db.md 中的数据库规则
--
-- 测试原则：
-- 1. 不允许推测，必须基于官方文档
-- 2. 测试期望与官方文档不符时，修正测试用例而不是降低代码质量
-- 3. 坚持正确的实现，确保功能的准确性和完整性
--
-- 官方文档依据：
-- - MySQL 8.4: https://dev.mysql.com/doc/refman/8.4/en/
-- - 达梦数据库: https://eco.dameng.com/document/dm/zh-cn/sql-dev/
-- - 金仓数据库: https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/
-- - 神通数据库: shentong.md 官方文档

-- ========================================
-- MySQL 8.4官方文档标准CREATE TABLE语法
-- ========================================

-- 基于MySQL 8.4官方文档的基础CREATE TABLE语法
-- https://dev.mysql.com/doc/refman/8.4/en/create-table.html
CREATE TABLE official_users (
    id INT NOT NULL AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY uk_username (username),
    UNIQUE KEY uk_email (email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 基于MySQL 8.4官方文档的数据类型测试
-- https://dev.mysql.com/doc/refman/8.4/en/data-types.html
CREATE TABLE official_data_types (
    -- 整数类型 - https://dev.mysql.com/doc/refman/8.4/en/integer-types.html
    tiny_col TINYINT,
    small_col SMALLINT,
    medium_col MEDIUMINT,
    int_col INT,
    big_col BIGINT,
    
    -- 浮点类型 - https://dev.mysql.com/doc/refman/8.4/en/floating-point-types.html
    float_col FLOAT,
    double_col DOUBLE,
    decimal_col DECIMAL(10,2),
    
    -- 字符串类型 - https://dev.mysql.com/doc/refman/8.4/en/string-types.html
    char_col CHAR(50),
    varchar_col VARCHAR(255),
    text_col TEXT,
    
    -- 日期时间类型 - https://dev.mysql.com/doc/refman/8.4/en/date-and-time-types.html
    date_col DATE,
    time_col TIME,
    datetime_col DATETIME,
    timestamp_col TIMESTAMP,
    year_col YEAR,
    
    -- JSON类型 - https://dev.mysql.com/doc/refman/8.4/en/json.html
    json_col JSON,
    
    PRIMARY KEY (int_col)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 基于MySQL 8.4官方文档的约束测试
-- https://dev.mysql.com/doc/refman/8.4/en/create-table.html#create-table-constraints
CREATE TABLE official_constraints (
    id INT NOT NULL AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL,
    age INT CHECK (age >= 0 AND age <= 150),
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (id),
    UNIQUE KEY uk_email (email),
    KEY idx_name (name),
    KEY idx_status_created (status, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 基于MySQL 8.4官方文档的外键约束测试
-- https://dev.mysql.com/doc/refman/8.4/en/create-table-foreign-keys.html
CREATE TABLE official_orders (
    order_id INT NOT NULL AUTO_INCREMENT,
    user_id INT NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    quantity INT NOT NULL DEFAULT 1,
    order_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (order_id),
    KEY fk_user_id (user_id),
    CONSTRAINT fk_orders_users FOREIGN KEY (user_id) REFERENCES official_users (id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ========================================
-- MySQL 8.4官方文档标准DML语句
-- ========================================

-- 基于MySQL 8.4官方文档的INSERT语句
-- https://dev.mysql.com/doc/refman/8.4/en/insert.html
INSERT INTO official_users (username, email) VALUES 
('john_doe', '<EMAIL>'),
('jane_smith', '<EMAIL>'),
('bob_wilson', '<EMAIL>');

-- 基于MySQL 8.4官方文档的UPDATE语句
-- https://dev.mysql.com/doc/refman/8.4/en/update.html
UPDATE official_users SET email = '<EMAIL>' WHERE username = 'john_doe';

-- 基于MySQL 8.4官方文档的SELECT语句
-- https://dev.mysql.com/doc/refman/8.4/en/select.html
SELECT 
    u.id,
    u.username,
    u.email,
    u.created_at,
    COUNT(o.order_id) as order_count
FROM official_users u
LEFT JOIN official_orders o ON u.id = o.user_id
WHERE u.created_at >= '2024-01-01'
GROUP BY u.id, u.username, u.email, u.created_at
HAVING COUNT(o.order_id) > 0
ORDER BY u.created_at DESC, u.username ASC
LIMIT 10 OFFSET 0;

-- 基于MySQL 8.4官方文档的DELETE语句
-- https://dev.mysql.com/doc/refman/8.4/en/delete.html
DELETE FROM official_users WHERE created_at < '2023-01-01';

-- ========================================
-- MySQL 8.4官方文档标准函数测试
-- ========================================

-- 基于MySQL 8.4官方文档的字符串函数
-- https://dev.mysql.com/doc/refman/8.4/en/string-functions.html
SELECT 
    CONCAT(username, ' - ', email) as user_info,
    UPPER(username) as username_upper,
    LOWER(email) as email_lower,
    LENGTH(username) as username_length,
    SUBSTRING(email, 1, LOCATE('@', email) - 1) as email_prefix
FROM official_users;

-- 基于MySQL 8.4官方文档的数值函数
-- https://dev.mysql.com/doc/refman/8.4/en/numeric-functions.html
SELECT 
    ABS(-123) as abs_value,
    ROUND(3.14159, 2) as rounded_value,
    CEIL(3.14) as ceiling_value,
    FLOOR(3.99) as floor_value
FROM dual;

-- 基于MySQL 8.4官方文档的日期时间函数
-- https://dev.mysql.com/doc/refman/8.4/en/date-and-time-functions.html
SELECT 
    NOW() as current_timestamp,
    CURDATE() as current_date,
    CURTIME() as current_time,
    DATE_FORMAT(created_at, '%Y-%m-%d %H:%i:%s') as formatted_date,
    YEAR(created_at) as created_year,
    MONTH(created_at) as created_month,
    DAY(created_at) as created_day
FROM official_users;

-- 基于MySQL 8.4官方文档的条件函数
-- https://dev.mysql.com/doc/refman/8.4/en/flow-control-functions.html
SELECT 
    IF(LENGTH(username) > 5, 'Long', 'Short') as username_category,
    IFNULL(email, 'No Email') as email_or_default,
    CASE 
        WHEN LENGTH(username) <= 5 THEN 'Short'
        WHEN LENGTH(username) <= 10 THEN 'Medium'
        ELSE 'Long'
    END as username_length_category
FROM official_users;

-- ========================================
-- 达梦数据库官方文档预期转换示例
-- ========================================

-- 注意：以下内容仅作为参考，不作为硬编码期望值
-- 实际测试应基于达梦官方文档进行动态验证

-- 达梦数据库AUTO_INCREMENT转换示例
-- 基于达梦官方文档：AUTO_INCREMENT → IDENTITY(1,1)
-- CREATE TABLE "official_users" (
--     "id" INT NOT NULL IDENTITY(1,1),
--     "username" VARCHAR(50) NOT NULL,
--     "email" VARCHAR(100) DEFAULT NULL,
--     "created_at" TIMESTAMP DEFAULT SYSDATE,
--     "updated_at" TIMESTAMP DEFAULT SYSDATE,
--     PRIMARY KEY ("id"),
--     UNIQUE KEY "uk_username" ("username"),
--     UNIQUE KEY "uk_email" ("email")
-- ) CHARACTER SET UTF8;

-- 达梦数据库函数转换示例
-- 基于达梦官方文档：IFNULL → NVL, NOW → SYSDATE
-- SELECT NVL(email, 'No Email') as email_or_default FROM "official_users";
-- SELECT SYSDATE as current_timestamp FROM dual;

-- ========================================
-- 金仓数据库官方文档预期转换示例
-- ========================================

-- 注意：以下内容仅作为参考，不作为硬编码期望值
-- 实际测试应基于金仓官方文档进行动态验证

-- 金仓数据库MySQL兼容性示例
-- 基于金仓官方文档：良好的MySQL兼容性
-- CREATE TABLE "official_users" (
--     "id" INT NOT NULL AUTO_INCREMENT,
--     "username" VARCHAR(50) NOT NULL,
--     "email" VARCHAR(100) DEFAULT NULL,
--     "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
--     "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
--     PRIMARY KEY ("id"),
--     UNIQUE KEY "uk_username" ("username"),
--     UNIQUE KEY "uk_email" ("email")
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 金仓数据库函数转换示例
-- 基于金仓官方文档：IFNULL → COALESCE
-- SELECT COALESCE(email, 'No Email') as email_or_default FROM "official_users";

-- ========================================
-- 神通数据库官方文档预期转换示例
-- ========================================

-- 注意：以下内容仅作为参考，不作为硬编码期望值
-- 实际测试应基于神通官方文档进行动态验证

-- 神通数据库SERIAL类型转换示例
-- 基于神通官方文档：AUTO_INCREMENT → SERIAL/BIGSERIAL
-- CREATE TABLE "official_users" (
--     "id" INTEGER SERIAL PRIMARY KEY,
--     "username" VARCHAR(50) NOT NULL,
--     "email" VARCHAR(100) DEFAULT NULL,
--     "created_at" TIMESTAMP DEFAULT SYSDATE,
--     "updated_at" TIMESTAMP DEFAULT SYSDATE
-- ) CHARACTER SET UTF8;

-- 神通数据库函数转换示例
-- 基于神通官方文档：IFNULL原生支持, NOW → SYSDATE
-- SELECT IFNULL(email, 'No Email') as email_or_default FROM "official_users";
-- SELECT SYSDATE as current_timestamp FROM dual;

-- ========================================
-- 测试验证指导原则
-- ========================================

-- 1. 动态验证原则
--    - 不使用硬编码的期望值进行断言
--    - 基于官方文档规范进行动态验证
--    - 允许多种符合官方文档的转换结果

-- 2. 官方文档引用原则
--    - 每个测试用例必须包含官方文档链接
--    - 验证逻辑必须基于官方文档的明确描述
--    - 当官方文档更新时，及时更新测试用例

-- 3. 转换结果验证原则
--    - 验证语法正确性（符合目标数据库官方语法）
--    - 验证语义一致性（保持原始SQL的语义）
--    - 验证功能完整性（确保转换后功能正常）

-- 4. 错误处理验证原则
--    - 验证不支持的功能被正确标识
--    - 验证错误信息基于官方文档描述
--    - 验证警告信息提供准确的官方文档引用
