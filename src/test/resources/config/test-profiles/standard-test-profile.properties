# 标准化测试配置文件
# 遵循 .augment/rules/rule-test.md 规范

# ==================== 测试执行配置 ====================

# 测试超时设置（毫秒）
test.timeout.unit=30000
test.timeout.integration=60000
test.timeout.e2e=120000
test.timeout.performance=180000

# 测试并发设置
test.parallel.enabled=true
test.parallel.threads=4

# 测试重试设置
test.retry.enabled=true
test.retry.maxAttempts=3

# ==================== 官方文档合规性配置 ====================

# MySQL官方文档版本
mysql.official.version=8.4
mysql.official.url=https://dev.mysql.com/doc/refman/8.4/en/

# 达梦官方文档配置
dameng.official.url=https://eco.dameng.com/document/dm/zh-cn/sql-dev/
dameng.official.version=8.0

# 金仓官方文档配置
kingbase.official.url=https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/
kingbase.official.version=V8R6

# 神通官方文档配置
shentong.official.source=@shentong.md
shentong.official.version=7.0

# ==================== 动态验证配置 ====================

# 启用动态验证机制
validation.dynamic.enabled=true

# 官方文档引用验证
validation.official.reference.required=true

# 质量反馈机制
validation.feedback.enabled=true
validation.feedback.level=INFO

# ==================== 测试数据配置 ====================

# 测试SQL文件路径
test.sql.input.path=src/test/resources/sql/input
test.sql.expected.path=src/test/resources/sql/expected

# 测试数据集配置
test.dataset.basic.enabled=true
test.dataset.advanced.enabled=true
test.dataset.edge-cases.enabled=true
test.dataset.real-world.enabled=true

# ==================== 性能基准配置 ====================

# 性能基准阈值
performance.identifier.quoting.threshold=1.0
performance.datatype.mapping.threshold=1.0
performance.function.mapping.threshold=2.0
performance.sql.conversion.threshold=10.0
performance.memory.usage.threshold=10240

# 性能测试迭代次数
performance.iterations.small=1000
performance.iterations.medium=100
performance.iterations.large=10

# ==================== 合规性测试配置 ====================

# MySQL强制语法校验
compliance.mysql.syntax.strict=true
compliance.mysql.syntax.reject.non.mysql=true
compliance.mysql.syntax.accept.mysql.specific=true

# 双重测试策略
compliance.dual.testing.enabled=true
compliance.dual.testing.reject.non.mysql=true
compliance.dual.testing.accept.mysql.conversion=true

# ==================== 错误处理配置 ====================

# 测试失败处理
test.failure.continue.on.error=false
test.failure.detailed.logging=true
test.failure.stack.trace=true

# 官方文档不符处理
test.official.mismatch.action=FAIL_TEST
test.official.mismatch.logging=true

# ==================== 日志配置 ====================

# 测试日志级别
logging.level.test=INFO
logging.level.compliance=DEBUG
logging.level.performance=INFO

# 日志输出格式
logging.pattern.test=%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n
logging.pattern.compliance=%d{HH:mm:ss.SSS} [COMPLIANCE] %-5level - %msg%n

# ==================== 报告配置 ====================

# 测试报告生成
report.generation.enabled=true
report.format=HTML,XML,JSON
report.output.path=target/test-reports

# 合规性报告
report.compliance.enabled=true
report.compliance.include.official.references=true

# 性能报告
report.performance.enabled=true
report.performance.include.benchmarks=true
