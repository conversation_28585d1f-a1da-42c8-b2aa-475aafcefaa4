# 基于官方文档的测试配置文件
# 严格遵照 .augment/rules/rule-db.md 中的数据库规则
#
# 测试原则：
# 1. 不允许推测，必须基于官方文档
# 2. 测试期望与官方文档不符时，修正测试用例而不是降低代码质量
# 3. 坚持正确的实现，确保功能的准确性和完整性

# ========================================
# 官方文档URL配置
# ========================================

# MySQL 8.4官方文档
mysql.official.doc.base.url=https://dev.mysql.com/doc/refman/8.4/en/
mysql.official.doc.create.table=https://dev.mysql.com/doc/refman/8.4/en/create-table.html
mysql.official.doc.data.types=https://dev.mysql.com/doc/refman/8.4/en/data-types.html
mysql.official.doc.functions=https://dev.mysql.com/doc/refman/8.4/en/functions.html
mysql.official.doc.sql.statements=https://dev.mysql.com/doc/refman/8.4/en/sql-statements.html

# 达梦数据库官方文档
dameng.official.doc.base.url=https://eco.dameng.com/document/dm/zh-cn/sql-dev/
dameng.official.doc.data.types=https://eco.dameng.com/document/dm/zh-cn/sql-dev/dmpl-sql-datatype.html
dameng.official.doc.ddl=https://eco.dameng.com/document/dm/zh-cn/sql-dev/dmpl-sql-ddl.html
dameng.official.doc.functions=https://eco.dameng.com/document/dm/zh-cn/sql-dev/dmpl-sql-function.html

# 金仓数据库官方文档
kingbase.official.doc.base.url=https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/
kingbase.official.doc.compatibility=https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
kingbase.official.doc.data.types=https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-3.html#id13
kingbase.official.doc.sql=https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html

# 神通数据库官方文档
shentong.official.doc.file=shentong.md
shentong.official.doc.data.types=shentong.md#data-types
shentong.official.doc.functions=shentong.md#functions
shentong.official.doc.sql.syntax=shentong.md#sql-syntax

# ========================================
# 测试验证配置
# ========================================

# 基于官方文档的验证开关
test.official.doc.validation.enabled=true
test.official.doc.strict.mode=true
test.hardcoded.expectations.disabled=true

# 详细日志输出配置
test.detailed.logging.enabled=true
test.conversion.statistics.enabled=true
test.official.doc.references.required=true

# ========================================
# 数据库特性配置（基于官方文档）
# ========================================

# MySQL 8.4官方特性支持
mysql.supports.auto.increment=true
mysql.supports.engine.clause=true
mysql.supports.charset.clause=true
mysql.supports.comment.clause=true
mysql.supports.json.type=true
mysql.supports.window.functions=true
mysql.supports.cte=true

# 达梦数据库官方特性支持
dameng.supports.identity=true
dameng.supports.auto.increment=false
dameng.supports.engine.clause=false
dameng.supports.character.set=true
dameng.supports.comment.on=true
dameng.supports.clob.type=true
dameng.supports.sysdate=true

# 金仓数据库官方特性支持（基于MySQL兼容性文档）
kingbase.mysql.compatibility.high=true
kingbase.supports.auto.increment=true
kingbase.supports.serial.type=true
kingbase.supports.limit.offset=true
kingbase.supports.engine.clause=true
kingbase.supports.charset.clause=true
kingbase.supports.json.type=true

# 神通数据库官方特性支持
shentong.supports.serial.type=true
shentong.supports.bigserial.type=true
shentong.supports.auto.increment=true
shentong.supports.double.quotes=true
shentong.supports.utf8.charset=true
shentong.supports.sysdate=true

# ========================================
# 转换规则配置（基于官方文档）
# ========================================

# AUTO_INCREMENT转换规则
mysql.auto.increment.to.dameng=IDENTITY(1,1)
mysql.auto.increment.to.kingbase=AUTO_INCREMENT|SERIAL
mysql.auto.increment.to.shentong=SERIAL|BIGSERIAL|AUTO_INCREMENT

# 函数转换规则
mysql.ifnull.to.dameng=NVL
mysql.ifnull.to.kingbase=COALESCE
mysql.ifnull.to.shentong=IFNULL

mysql.now.to.dameng=SYSDATE
mysql.now.to.kingbase=NOW
mysql.now.to.shentong=SYSDATE

mysql.date.format.to.dameng=TO_CHAR
mysql.date.format.to.kingbase=TO_CHAR
mysql.date.format.to.shentong=TO_CHAR

# 数据类型转换规则
mysql.text.to.dameng=CLOB
mysql.text.to.kingbase=TEXT
mysql.text.to.shentong=TEXT

mysql.tinyint1.to.dameng=BIT|BOOLEAN
mysql.tinyint1.to.kingbase=BOOLEAN|BIT|SMALLINT|TINYINT
mysql.tinyint1.to.shentong=BOOLEAN|BIT|TINYINT

mysql.mediumint.to.dameng=INT
mysql.mediumint.to.kingbase=MEDIUMINT|INT
mysql.mediumint.to.shentong=INTEGER

# ENGINE子句处理规则
mysql.engine.dameng.action=REMOVE
mysql.engine.kingbase.action=KEEP|REMOVE
mysql.engine.shentong.action=REMOVE

# 字符集处理规则
mysql.charset.dameng.conversion=CHARACTER_SET_UTF8
mysql.charset.kingbase.conversion=KEEP
mysql.charset.shentong.conversion=CHARACTER_SET_UTF8

# ========================================
# 测试数据配置
# ========================================

# 测试SQL文件路径
test.sql.input.mysql.basic=sql/input/mysql/basic/
test.sql.input.mysql.advanced=sql/input/mysql/advanced/
test.sql.input.mysql.official=sql/input/mysql/official-docs/
test.sql.input.mysql.realworld=sql/input/mysql/real-world/

# 预期输出SQL文件路径（仅用于参考，不作为硬编码期望）
test.sql.expected.dameng=sql/expected/dameng/
test.sql.expected.kingbase=sql/expected/kingbase/
test.sql.expected.shentong=sql/expected/shentong/

# ========================================
# 验证标准配置
# ========================================

# 成功验证标记
test.success.marker=✅
test.warning.marker=⚠️
test.error.marker=❌

# 验证消息模板
test.success.template=%s正确%s（符合官方文档）
test.warning.template=%s需要验证%s（请参考官方文档）
test.error.template=%s转换失败：%s（不符合官方文档）

# 官方文档引用模板
test.doc.reference.template=基于%s官方文档：%s
test.doc.url.template=参考文档：%s

# ========================================
# 测试执行配置
# ========================================

# 测试超时配置（秒）
test.timeout.unit=60
test.timeout.integration=120
test.timeout.e2e=180
test.timeout.regression=90

# 并发测试配置
test.parallel.enabled=true
test.parallel.threads=4

# 测试重试配置
test.retry.enabled=true
test.retry.max.attempts=3

# ========================================
# 日志配置
# ========================================

# 日志级别
test.log.level=INFO
test.log.detailed.conversion=true
test.log.official.doc.references=true
test.log.statistics=true

# 日志输出格式
test.log.format.timestamp=yyyy-MM-dd HH:mm:ss
test.log.format.conversion=%s转换结果: %s
test.log.format.validation=%s验证: %s
test.log.format.statistics=转换统计 - %s: 成功=%d,失败=%d

# ========================================
# 错误处理配置
# ========================================

# 错误处理策略
test.error.handling.strict=true
test.error.fail.on.hardcoded.expectations=true
test.error.fail.on.missing.doc.reference=true

# 警告处理策略
test.warning.handling.log.only=true
test.warning.compatibility.issues.allowed=true

# ========================================
# 性能测试配置
# ========================================

# 性能基准（基于官方文档的合理期望）
test.performance.max.conversion.time.ms=5000
test.performance.max.memory.usage.mb=512
test.performance.max.statements.per.second=100

# 性能监控
test.performance.monitoring.enabled=true
test.performance.profiling.enabled=false

# ========================================
# 兼容性测试配置
# ========================================

# 兼容性验证开关
test.compatibility.mysql.to.dameng=true
test.compatibility.mysql.to.kingbase=true
test.compatibility.mysql.to.shentong=true

# 兼容性标准（基于官方文档）
test.compatibility.dameng.identity.required=true
test.compatibility.kingbase.mysql.syntax.preferred=true
test.compatibility.shentong.standard.sql.required=true

# ========================================
# 测试报告配置
# ========================================

# 报告生成配置
test.report.generation.enabled=true
test.report.format=HTML,JSON,XML
test.report.include.official.doc.references=true
test.report.include.conversion.statistics=true

# 报告输出路径
test.report.output.dir=target/test-reports/
test.report.official.compliance.file=official-documentation-compliance-report.html

# ========================================
# 环境配置
# ========================================

# 测试环境标识
test.environment=official-documentation-compliance
test.profile=strict-official-doc-validation

# 数据库连接配置（用于集成测试，如果需要）
test.db.dameng.enabled=false
test.db.kingbase.enabled=false
test.db.shentong.enabled=false

# 外部工具配置
test.external.mysql.client.enabled=false
test.external.validation.tools.enabled=false
