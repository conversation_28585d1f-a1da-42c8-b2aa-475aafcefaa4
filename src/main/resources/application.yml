server:
  port: 8080
  servlet:
    context-path: /

spring:
  application:
    name: sql-transpiler-web

  thymeleaf:
    cache: false
    encoding: UTF-8
    mode: HTML
    prefix: classpath:/templates/
    suffix: .html

  web:
    resources:
      static-locations: classpath:/static/
      cache:
        period: 0

  servlet:
    multipart:
      enabled: true
      max-file-size: 100MB
      max-request-size: 100MB
      file-size-threshold: 2KB

logging:
  level:
    com.xylink.sqltranspiler: DEBUG
    org.springframework: WARN
    org.antlr: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
