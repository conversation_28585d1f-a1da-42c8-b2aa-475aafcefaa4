[{"name": "com.xylink.sqltranspiler.Main", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "com.xylink.sqltranspiler.application.cli.SqlTranspilerCli", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true, "allDeclaredFields": true, "allPublicFields": true}, {"name": "com.xylink.sqltranspiler.infrastructure.parser.generated.MySqlLexer", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true, "allDeclaredFields": true, "allPublicFields": true}, {"name": "com.xylink.sqltranspiler.infrastructure.parser.generated.MySqlParser", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true, "allDeclaredFields": true, "allPublicFields": true}, {"name": "com.xylink.sqltranspiler.infrastructure.parser.generated.MySqlParserBaseVisitor", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "com.xylink.sqltranspiler.infrastructure.parser.generated.MySqlParserVisitor", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "org.antlr.v4.runtime.atn.ATN", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "org.antlr.v4.runtime.atn.ATNDeserializer", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "org.antlr.v4.runtime.atn.LexerATNSimulator", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "org.antlr.v4.runtime.atn.ParserATNSimulator", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "org.antlr.v4.runtime.atn.PredictionContextCache", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "org.antlr.v4.runtime.dfa.DFA", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "picocli.CommandLine", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "picocli.CommandLine$Model$CommandSpec", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "picocli.CommandLine$Model$OptionSpec", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "ch.qos.logback.classic.Logger", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "ch.qos.logback.classic.LoggerContext", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "ch.qos.logback.core.Console<PERSON>ppender", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "ch.qos.logback.classic.encoder.PatternLayoutEncoder", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "ch.qos.logback.classic.PatternLayout", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "ch.qos.logback.core.FileAppender", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}]