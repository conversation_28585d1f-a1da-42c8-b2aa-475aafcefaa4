# 严格SQL规范验证规则 - 基于官方文档
# MySQL: https://dev.mysql.com/doc/refman/8.4/en/
# 达梦: https://eco.dameng.com/document/dm/zh-cn/pm/
# 金仓: https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
# 神通: 基于神通数据库SQL参考手册

# ==================== MySQL 8.4 官方规范 ====================

# MySQL数值类型 - 严格按照官方文档
validation.mysql.types.numeric=TINYINT,SMALLINT,MEDIUMINT,INT,INTEGER,BIGINT,DECIMAL,NUMERIC,FLOAT,DOUBLE,REAL,BIT

# MySQL字符串类型
validation.mysql.types.string=CHAR,VARCHAR,BINARY,VARBINARY,TINYBLOB,BLOB,MEDIUMBLOB,LONGBLOB,TINYTEXT,TEXT,MEDIUMTEXT,LONGTEXT

# MySQL日期时间类型
validation.mysql.types.datetime=DATE,TIME,DATETIME,TIMESTAMP,YEAR

# MySQL其他类型
validation.mysql.types.boolean=BOOLEAN,BOOL
validation.mysql.types.json=JSON
validation.mysql.types.enum=ENUM,SET
validation.mysql.types.geometry=GEOMETRY,POINT,LINESTRING,POLYGON,MULTIPOINT,MULTILINESTRING,MULTIPOLYGON,GEOMETRYCOLLECTION

# MySQL数值类型默认值规范 - 不应使用引号
validation.mysql.numeric_no_quotes=TINYINT,SMALLINT,MEDIUMINT,INT,INTEGER,BIGINT,DECIMAL,NUMERIC,FLOAT,DOUBLE,REAL,BIT

# MySQL字符串类型默认值规范 - 必须使用引号
validation.mysql.string_with_quotes=CHAR,VARCHAR,TINYTEXT,TEXT,MEDIUMTEXT,LONGTEXT

# MySQL日期时间函数 - 可作为默认值
validation.mysql.datetime_functions=CURRENT_TIMESTAMP,NOW(),CURRENT_DATE,CURRENT_TIME,LOCALTIME,LOCALTIMESTAMP,UTC_TIMESTAMP,UTC_DATE,UTC_TIME

# MySQL无效的ON UPDATE函数 - 严格按照MySQL 8.4官方文档
validation.mysql.invalid_on_update=NOW(),SYSDATE(),GETDATE(),GETUTCDATE(),RAND(),UUID(),CONNECTION_ID(),CURDATE(),CURTIME(),USER(),DATABASE(),VERSION(),LOCALTIME(),LOCALTIMESTAMP(),UTC_TIMESTAMP(),UTC_DATE(),UTC_TIME()

# MySQL有效的ON UPDATE函数 - 严格按照MySQL 8.4官方文档
validation.mysql.valid_on_update=CURRENT_TIMESTAMP,CURRENT_DATE,CURRENT_TIME

# MySQL无效的DEFAULT函数 - 严格按照MySQL 8.4官方文档
validation.mysql.invalid_default_functions=RAND(),UUID(),LAST_INSERT_ID(),ROW_COUNT()

# MySQL有效的DEFAULT函数 - 严格按照MySQL 8.4官方文档
validation.mysql.valid_default_functions=CURRENT_TIMESTAMP,NOW(),CURRENT_DATE,CURRENT_TIME,LOCALTIME,LOCALTIMESTAMP,UTC_TIMESTAMP,UTC_DATE,UTC_TIME,CONNECTION_ID(),USER(),DATABASE(),VERSION()

# MySQL保留字 - 基于MySQL 8.4官方文档关键字列表
validation.mysql.reserved_words=ACCESSIBLE,ADD,ALL,ALTER,ANALYZE,AND,AS,ASC,ASENSITIVE,BEFORE,BETWEEN,BIGINT,BINARY,BLOB,BOTH,BY,CALL,CASCADE,CASE,CHANGE,CHAR,CHARACTER,CHECK,COLLATE,COLUMN,CONDITION,CONSTRAINT,CONTINUE,CONVERT,CREATE,CROSS,CUBE,CUME_DIST,CURRENT_DATE,CURRENT_TIME,CURRENT_TIMESTAMP,CURRENT_USER,CURSOR,DATABASE,DATABASES,DAY_HOUR,DAY_MICROSECOND,DAY_MINUTE,DAY_SECOND,DEC,DECIMAL,DECLARE,DEFAULT,DELAYED,DELETE,DENSE_RANK,DESC,DESCRIBE,DETERMINISTIC,DISTINCT,DISTINCTROW,DIV,DOUBLE,DROP,DUAL,EACH,ELSE,ELSEIF,EMPTY,ENCLOSED,ESCAPED,EXCEPT,EXISTS,EXIT,EXPLAIN,FALSE,FETCH,FIRST_VALUE,FLOAT,FLOAT4,FLOAT8,FOR,FORCE,FOREIGN,FROM,FULLTEXT,FUNCTION,GENERATED,GET,GRANT,GROUP,GROUPING,GROUPS,HAVING,HIGH_PRIORITY,HOUR_MICROSECOND,HOUR_MINUTE,HOUR_SECOND,IF,IGNORE,IN,INDEX,INFILE,INNER,INOUT,INSENSITIVE,INSERT,INT,INT1,INT2,INT3,INT4,INT8,INTEGER,INTERVAL,INTO,IO_AFTER_GTIDS,IO_BEFORE_GTIDS,IS,ITERATE,JOIN,JSON_TABLE,KEY,KEYS,KILL,LAG,LAST_VALUE,LATERAL,LEAD,LEADING,LEAVE,LEFT,LIKE,LIMIT,LINEAR,LINES,LOAD,LOCALTIME,LOCALTIMESTAMP,LOCK,LONG,LONGBLOB,LONGTEXT,LOOP,LOW_PRIORITY,MASTER_BIND,MASTER_SSL_VERIFY_SERVER_CERT,MATCH,MAXVALUE,MEDIUMBLOB,MEDIUMINT,MEDIUMTEXT,MIDDLEINT,MINUTE_MICROSECOND,MINUTE_SECOND,MOD,MODIFIES,NATURAL,NOT,NO_WRITE_TO_BINLOG,NTH_VALUE,NTILE,NULL,NUMERIC,OF,ON,OPTIMIZE,OPTIMIZER_COSTS,OPTION,OPTIONALLY,OR,ORDER,OUT,OUTER,OUTFILE,OVER,PARTITION,PERCENT_RANK,PRECISION,PRIMARY,PROCEDURE,PURGE,RANGE,RANK,READ,READS,READ_WRITE,REAL,RECURSIVE,REFERENCES,REGEXP,RELEASE,RENAME,REPEAT,REPLACE,REQUIRE,RESIGNAL,RESTRICT,RETURN,REVOKE,RIGHT,RLIKE,ROW,ROWS,ROW_NUMBER,SCHEMA,SCHEMAS,SECOND_MICROSECOND,SELECT,SENSITIVE,SEPARATOR,SET,SHOW,SIGNAL,SMALLINT,SPATIAL,SPECIFIC,SQL,SQLEXCEPTION,SQLSTATE,SQLWARNING,SQL_BIG_RESULT,SQL_CALC_FOUND_ROWS,SQL_SMALL_RESULT,SSL,STARTING,STORED,STRAIGHT_JOIN,SYSTEM,TABLE,TERMINATED,THEN,TINYBLOB,TINYINT,TINYTEXT,TO,TRAILING,TRIGGER,TRUE,UNDO,UNION,UNIQUE,UNLOCK,UNSIGNED,UPDATE,USAGE,USE,USING,UTC_DATE,UTC_TIME,UTC_TIMESTAMP,VALUES,VARBINARY,VARCHAR,VARCHARACTER,VARYING,VIRTUAL,WHEN,WHERE,WHILE,WINDOW,WITH,WRITE,X509,XOR,YEAR_MONTH,ZEROFILL

# MySQL字符集规范 - 基于MySQL 8.4官方文档
validation.mysql.valid_charsets=armscii8,ascii,big5,binary,cp1250,cp1251,cp1256,cp1257,cp850,cp852,cp866,cp932,dec8,eucjpms,euckr,gb18030,gb2312,gbk,geostd8,greek,hebrew,hp8,keybcs2,koi8r,koi8u,latin1,latin2,latin5,latin7,macce,macroman,sjis,swe7,tis620,ucs2,ujis,utf16,utf16le,utf32,utf8,utf8mb3,utf8mb4

# MySQL排序规则规范 - 基于MySQL 8.4官方文档
validation.mysql.valid_collations=utf8mb4_general_ci,utf8mb4_unicode_ci,utf8mb4_bin,utf8_general_ci,utf8_unicode_ci,utf8_bin,latin1_swedish_ci,latin1_general_ci,latin1_bin

# MySQL AUTO_INCREMENT规范
validation.mysql.auto_increment_types=TINYINT,SMALLINT,MEDIUMINT,INT,INTEGER,BIGINT
validation.mysql.auto_increment_requires_key=true

# MySQL数据类型长度限制 - 基于MySQL 8.4官方文档
validation.mysql.varchar_max_length=65535
validation.mysql.char_max_length=255
validation.mysql.decimal_max_precision=65
validation.mysql.decimal_max_scale=30
validation.mysql.float_max_precision=53
validation.mysql.bit_max_length=64
validation.mysql.enum_max_values=65535
validation.mysql.set_max_members=64
validation.mysql.tinyint_range=-128,127
validation.mysql.smallint_range=-32768,32767
validation.mysql.mediumint_range=-8388608,8388607
validation.mysql.int_range=-2147483648,2147483647
validation.mysql.bigint_range=-9223372036854775808,9223372036854775807

# MySQL字符串类型长度限制 - 基于MySQL 8.4官方文档
validation.mysql.tinytext_max_length=255
validation.mysql.text_max_length=65535
validation.mysql.mediumtext_max_length=16777215
validation.mysql.longtext_max_length=4294967295
validation.mysql.tinyblob_max_length=255
validation.mysql.blob_max_length=65535
validation.mysql.mediumblob_max_length=16777215
validation.mysql.longblob_max_length=4294967295

# MySQL日期时间类型范围限制 - 基于MySQL 8.4官方文档
validation.mysql.date_range=1000-01-01,9999-12-31
validation.mysql.datetime_range=1000-01-01 00:00:00,9999-12-31 23:59:59
validation.mysql.timestamp_range=1970-01-01 00:00:01,2038-01-19 03:14:07
validation.mysql.time_range=-838:59:59,838:59:59
validation.mysql.year_range=1901,2155
validation.mysql.fractional_seconds_max=6

# MySQL表和列限制 - 基于MySQL 8.4官方文档
validation.mysql.max_columns_per_table=4096
validation.mysql.innodb_max_columns_per_table=1017
validation.mysql.max_indexes_per_table=64
validation.mysql.max_key_parts=16
validation.mysql.max_key_length_dynamic=3072
validation.mysql.max_key_length_compact=767
validation.mysql.max_row_size=65535
validation.mysql.max_identifier_length=64

# MySQL InnoDB特定限制 - 基于MySQL 8.4官方文档
validation.mysql.innodb_max_tablespace_size_4k=16TB
validation.mysql.innodb_max_tablespace_size_8k=32TB
validation.mysql.innodb_max_tablespace_size_16k=64TB
validation.mysql.innodb_max_tablespace_size_32k=128TB
validation.mysql.innodb_max_tablespace_size_64k=256TB
validation.mysql.innodb_max_tablespaces=4294967296
validation.mysql.innodb_max_tables_per_shared_tablespace=4294967296

# MySQL ENUM和SET限制 - 基于MySQL 8.4官方文档
validation.mysql.enum_max_values=65535
validation.mysql.set_max_members=64

# MySQL FLOAT精度限制 - 基于MySQL 8.4官方文档
validation.mysql.float_precision_range=0,53
validation.mysql.float_4byte_precision_max=24
validation.mysql.float_8byte_precision_min=25

# MySQL JSON限制 - 基于MySQL 8.4官方文档
validation.mysql.json_max_size=max_allowed_packet

# MySQL BLOB/TEXT长度限制 - 基于MySQL 8.4官方文档
validation.mysql.tinyblob_max_length=255
validation.mysql.blob_max_length=65535
validation.mysql.mediumblob_max_length=16777215
validation.mysql.longblob_max_length=4294967295
validation.mysql.tinytext_max_length=255
validation.mysql.text_max_length=65535
validation.mysql.mediumtext_max_length=16777215
validation.mysql.longtext_max_length=4294967295

# ==================== 达梦数据库规范 ====================

# 达梦数值类型
validation.dameng.types.numeric=TINYINT,SMALLINT,INT,INTEGER,BIGINT,DECIMAL,NUMERIC,FLOAT,DOUBLE,REAL,NUMBER

# 达梦字符串类型
validation.dameng.types.string=CHAR,VARCHAR,VARCHAR2,NCHAR,NVARCHAR,NVARCHAR2,TEXT,CLOB,NCLOB

# 达梦日期时间类型
validation.dameng.types.datetime=DATE,TIME,DATETIME,TIMESTAMP

# 达梦布尔类型
validation.dameng.types.boolean=BOOLEAN,BIT

# 达梦JSON类型 - 基于达梦官方文档，完全支持JSON和JSONB数据类型
# 参考：https://eco.dameng.com/document/dm/zh-cn/pm/json.html
validation.dameng.types.json=JSON,JSONB

# 达梦IDENTITY替代AUTO_INCREMENT
validation.dameng.identity_syntax=IDENTITY(1,1)
validation.dameng.no_auto_increment=true

# 达梦保留字处理 - 使用双引号
validation.dameng.reserved_quote_char="

# 达梦字符集 - 基于达梦官方文档
validation.dameng.charset=CHARACTER SET UTF8
validation.dameng.valid_charsets=UTF8,GBK,GB18030,BIG5,ISO-8859-1,KSC5601,EUC-JP

# 达梦数据类型长度限制 - 基于达梦官方文档
validation.dameng.varchar_max_length=8188
validation.dameng.char_max_length=2000
validation.dameng.number_max_precision=38
validation.dameng.number_max_scale=127
validation.dameng.decimal_max_precision=38
validation.dameng.decimal_max_scale=127

# 达梦保留字 - 基于达梦官方文档
validation.dameng.reserved_words=ABSOLUTE,ACTION,ADD,ADMIN,AFTER,AGGREGATE,ALIAS,ALL,ALLOCATE,ALTER,AND,ANY,ARE,ARRAY,AS,ASC,ASSERTION,AT,AUTHORIZATION,BEFORE,BEGIN,BETWEEN,BINARY,BIT,BLOB,BOOLEAN,BOTH,BREADTH,BY,CALL,CASCADE,CASCADED,CASE,CAST,CATALOG,CHAR,CHARACTER,CHECK,CLASS,CLOB,CLOSE,COLLATE,COLLATION,COLUMN,COMMIT,COMPLETION,CONNECT,CONNECTION,CONSTRAINT,CONSTRAINTS,CONSTRUCTOR,CONTINUE,CORRESPONDING,CREATE,CROSS,CUBE,CURRENT,CURRENT_DATE,CURRENT_PATH,CURRENT_ROLE,CURRENT_TIME,CURRENT_TIMESTAMP,CURRENT_USER,CURSOR,CYCLE,DATA,DATE,DAY,DEALLOCATE,DEC,DECIMAL,DECLARE,DEFAULT,DEFERRABLE,DEFERRED,DELETE,DEPTH,DEREF,DESC,DESCRIBE,DESCRIPTOR,DESTROY,DESTRUCTOR,DETERMINISTIC,DIAGNOSTICS,DICTIONARY,DISCONNECT,DISTINCT,DOMAIN,DOUBLE,DROP,DYNAMIC,EACH,ELSE,END,END-EXEC,EQUALS,ESCAPE,EVERY,EXCEPT,EXCEPTION,EXEC,EXECUTE,EXTERNAL,FALSE,FETCH,FIRST,FLOAT,FOR,FOREIGN,FOUND,FROM,FULL,FUNCTION,GENERAL,GET,GLOBAL,GO,GOTO,GRANT,GROUP,GROUPING,HAVING,HOST,HOUR,IDENTITY,IGNORE,IMMEDIATE,IN,INDICATOR,INITIALIZE,INITIALLY,INNER,INOUT,INPUT,INSERT,INT,INTEGER,INTERSECT,INTERVAL,INTO,IS,ISOLATION,ITERATE,JOIN,KEY,LANGUAGE,LARGE,LAST,LATERAL,LEADING,LEFT,LESS,LEVEL,LIKE,LIMIT,LOCAL,LOCALTIME,LOCALTIMESTAMP,LOCATOR,MAP,MATCH,MINUTE,MODIFIES,MODIFY,MODULE,MONTH,NAMES,NATIONAL,NATURAL,NCHAR,NCLOB,NEW,NEXT,NO,NONE,NOT,NULL,NUMERIC,OBJECT,OF,OFF,OLD,ON,ONLY,OPEN,OPERATION,OPTION,OR,ORDER,ORDINALITY,OUT,OUTER,OUTPUT,PAD,PARAMETER,PARAMETERS,PARTIAL,PATH,POSTFIX,PRECISION,PREFIX,PREORDER,PREPARE,PRESERVE,PRIMARY,PRIOR,PRIVILEGES,PROCEDURE,PUBLIC,READ,READS,REAL,RECURSIVE,REF,REFERENCES,REFERENCING,RELATIVE,RESTRICT,RESULT,RETURN,RETURNS,REVOKE,RIGHT,ROLE,ROLLBACK,ROLLUP,ROUTINE,ROW,ROWS,SAVEPOINT,SCHEMA,SCOPE,SCROLL,SEARCH,SECOND,SECTION,SELECT,SEQUENCE,SESSION,SESSION_USER,SET,SETS,SIZE,SMALLINT,SOME,SPACE,SPECIFIC,SPECIFICTYPE,SQL,SQLEXCEPTION,SQLSTATE,SQLWARNING,START,STATE,STATEMENT,STATIC,STRUCTURE,SYSTEM_USER,TABLE,TEMPORARY,TERMINATE,THAN,THEN,TIME,TIMESTAMP,TIMEZONE_HOUR,TIMEZONE_MINUTE,TO,TRAILING,TRANSACTION,TRANSLATION,TREAT,TRIGGER,TRUE,UNDER,UNION,UNIQUE,UNKNOWN,UNNEST,UPDATE,USAGE,USER,USING,VALUE,VALUES,VARCHAR,VARIABLE,VARYING,VIEW,WHEN,WHENEVER,WHERE,WITH,WITHOUT,WORK,WRITE,YEAR,ZONE

# 达梦函数映射 - 基于达梦官方文档
validation.dameng.function_mapping.IFNULL=NVL
validation.dameng.function_mapping.DATE_FORMAT=TO_CHAR
validation.dameng.function_mapping.STR_TO_DATE=TO_DATE
validation.dameng.function_mapping.UNIX_TIMESTAMP=EXTRACT
validation.dameng.function_mapping.FROM_UNIXTIME=TO_TIMESTAMP

# ==================== 金仓数据库规范 ====================

# 金仓数值类型 - 兼容PostgreSQL，根据金仓官方文档原生支持TINYINT
validation.kingbase.types.numeric=TINYINT,SMALLINT,INT,INTEGER,BIGINT,DECIMAL,NUMERIC,REAL,DOUBLE PRECISION,SERIAL,BIGSERIAL

# 金仓字符串类型
validation.kingbase.types.string=CHAR,VARCHAR,TEXT

# 金仓日期时间类型
validation.kingbase.types.datetime=DATE,TIME,TIMESTAMP

# 金仓布尔类型
validation.kingbase.types.boolean=BOOLEAN,BOOL

# 金仓序列类型
validation.kingbase.types.serial=SERIAL,BIGSERIAL

# 金仓JSON类型 - 基于金仓官方文档，JSON类型完全支持
# 参考：https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
validation.kingbase.types.json=JSON

# 金仓PostgreSQL兼容性 - 基于金仓官方文档
validation.kingbase.postgresql_compatible=true

# 金仓数据类型长度限制 - 基于金仓官方文档
validation.kingbase.varchar_max_length=10485760
validation.kingbase.char_max_length=10485760
validation.kingbase.numeric_max_precision=1000
validation.kingbase.numeric_max_scale=1000

# 金仓保留字 - 基于金仓官方文档（PostgreSQL兼容）
validation.kingbase.reserved_words=ALL,ANALYSE,ANALYZE,AND,ANY,ARRAY,AS,ASC,ASYMMETRIC,AUTHORIZATION,BINARY,BOTH,CASE,CAST,CHECK,COLLATE,COLLATION,COLUMN,CONCURRENTLY,CONSTRAINT,CREATE,CROSS,CURRENT_CATALOG,CURRENT_DATE,CURRENT_ROLE,CURRENT_SCHEMA,CURRENT_TIME,CURRENT_TIMESTAMP,CURRENT_USER,DEFAULT,DEFERRABLE,DESC,DISTINCT,DO,ELSE,END,EXCEPT,FALSE,FETCH,FOR,FOREIGN,FREEZE,FROM,FULL,GRANT,GROUP,HAVING,ILIKE,IN,INITIALLY,INNER,INTERSECT,INTO,IS,ISNULL,JOIN,LATERAL,LEADING,LEFT,LIKE,LIMIT,LOCALTIME,LOCALTIMESTAMP,NATURAL,NOT,NOTNULL,NULL,OFFSET,ON,ONLY,OR,ORDER,OUTER,OVERLAPS,PLACING,PRIMARY,REFERENCES,RETURNING,RIGHT,SELECT,SESSION_USER,SIMILAR,SOME,SYMMETRIC,TABLE,TABLESAMPLE,THEN,TO,TRAILING,TRUE,UNION,UNIQUE,USER,USING,VARIADIC,VERBOSE,WHEN,WHERE,WINDOW,WITH

# 金仓函数映射 - 基于金仓官方文档
validation.kingbase.function_mapping.IFNULL=COALESCE
validation.kingbase.function_mapping.DATE_FORMAT=TO_CHAR
validation.kingbase.function_mapping.STR_TO_DATE=TO_DATE
validation.kingbase.function_mapping.UNIX_TIMESTAMP=EXTRACT
validation.kingbase.function_mapping.FROM_UNIXTIME=TO_TIMESTAMP
validation.kingbase.function_mapping.NOW=CURRENT_TIMESTAMP
validation.kingbase.function_mapping.CURDATE=CURRENT_DATE
validation.kingbase.function_mapping.CURTIME=CURRENT_TIME

# 金仓AUTO_INCREMENT替代方案 - 基于金仓官方文档
validation.kingbase.auto_increment_replacement=SERIAL
validation.kingbase.bigint_auto_increment_replacement=BIGSERIAL

# ==================== 神通数据库规范 ====================

# 神通数值类型 - 基于神通文档
validation.shentong.types.numeric=TINYINT,INT1,SMALLINT,INT2,INT,INTEGER,INT4,BIGINT,INT8,DECIMAL,NUMERIC,REAL,FLOAT4,DOUBLE PRECISION,FLOAT8,FLOAT,SERIAL,BIGSERIAL

# 神通字符串类型
validation.shentong.types.string=CHARACTER,CHAR,CHARACTER VARYING,VARCHAR,NVARCHAR2,TEXT,"CHAR",NAME

# 神通位串类型
validation.shentong.types.bit=BIT,BIT VARYING,VARBIT

# 神通二进制字符串类型
validation.shentong.types.binary=BINARY,VARBINARY,RAW

# 神通日期时间类型
validation.shentong.types.datetime=DATE,TIME,TIME WITH TIME ZONE

# 神通自增列类型
validation.shentong.auto_increment_types=INT,BIGINT,FLOAT
validation.shentong.auto_increment_requires_unique=true

# 神通标识符规范 - 基于神通官方文档
validation.shentong.identifier_max_length=127
validation.shentong.identifier_case_insensitive=true
validation.shentong.quoted_identifier_case_sensitive=true

# 神通数据类型长度限制 - 基于神通官方文档
validation.shentong.varchar_max_length=32767
validation.shentong.char_max_length=2000
validation.shentong.decimal_max_precision=38
validation.shentong.decimal_max_scale=127

# 神通保留字 - 基于神通官方文档
validation.shentong.reserved_words=ABSOLUTE,ACTION,ADD,ADMIN,AFTER,AGGREGATE,ALIAS,ALL,ALLOCATE,ALTER,AND,ANY,ARE,ARRAY,AS,ASC,ASSERTION,AT,AUTHORIZATION,BEFORE,BEGIN,BETWEEN,BINARY,BIT,BLOB,BOOLEAN,BOTH,BREADTH,BY,CALL,CASCADE,CASCADED,CASE,CAST,CATALOG,CHAR,CHARACTER,CHECK,CLASS,CLOB,CLOSE,COLLATE,COLLATION,COLUMN,COMMIT,COMPLETION,CONNECT,CONNECTION,CONSTRAINT,CONSTRAINTS,CONSTRUCTOR,CONTINUE,CORRESPONDING,CREATE,CROSS,CUBE,CURRENT,CURRENT_DATE,CURRENT_PATH,CURRENT_ROLE,CURRENT_TIME,CURRENT_TIMESTAMP,CURRENT_USER,CURSOR,CYCLE,DATA,DATE,DAY,DEALLOCATE,DEC,DECIMAL,DECLARE,DEFAULT,DEFERRABLE,DEFERRED,DELETE,DEPTH,DEREF,DESC,DESCRIBE,DESCRIPTOR,DESTROY,DESTRUCTOR,DETERMINISTIC,DIAGNOSTICS,DICTIONARY,DISCONNECT,DISTINCT,DOMAIN,DOUBLE,DROP,DYNAMIC,EACH,ELSE,END,EQUALS,ESCAPE,EVERY,EXCEPT,EXCEPTION,EXEC,EXECUTE,EXTERNAL,FALSE,FETCH,FIRST,FLOAT,FOR,FOREIGN,FOUND,FROM,FULL,FUNCTION,GENERAL,GET,GLOBAL,GO,GOTO,GRANT,GROUP,GROUPING,HAVING,HOST,HOUR,IDENTITY,IGNORE,IMMEDIATE,IN,INDICATOR,INITIALIZE,INITIALLY,INNER,INOUT,INPUT,INSERT,INT,INTEGER,INTERSECT,INTERVAL,INTO,IS,ISOLATION,ITERATE,JOIN,KEY,LANGUAGE,LARGE,LAST,LATERAL,LEADING,LEFT,LESS,LEVEL,LIKE,LIMIT,LOCAL,LOCALTIME,LOCALTIMESTAMP,LOCATOR,MAP,MATCH,MINUTE,MODIFIES,MODIFY,MODULE,MONTH,NAMES,NATIONAL,NATURAL,NCHAR,NCLOB,NEW,NEXT,NO,NONE,NOT,NULL,NUMERIC,OBJECT,OF,OFF,OLD,ON,ONLY,OPEN,OPERATION,OPTION,OR,ORDER,ORDINALITY,OUT,OUTER,OUTPUT,PAD,PARAMETER,PARAMETERS,PARTIAL,PATH,POSTFIX,PRECISION,PREFIX,PREORDER,PREPARE,PRESERVE,PRIMARY,PRIOR,PRIVILEGES,PROCEDURE,PUBLIC,READ,READS,REAL,RECURSIVE,REF,REFERENCES,REFERENCING,RELATIVE,RESTRICT,RESULT,RETURN,RETURNS,REVOKE,RIGHT,ROLE,ROLLBACK,ROLLUP,ROUTINE,ROW,ROWS,SAVEPOINT,SCHEMA,SCOPE,SCROLL,SEARCH,SECOND,SECTION,SELECT,SEQUENCE,SESSION,SESSION_USER,SET,SETS,SIZE,SMALLINT,SOME,SPACE,SPECIFIC,SPECIFICTYPE,SQL,SQLEXCEPTION,SQLSTATE,SQLWARNING,START,STATE,STATEMENT,STATIC,STRUCTURE,SYSTEM_USER,TABLE,TEMPORARY,TERMINATE,THAN,THEN,TIME,TIMESTAMP,TIMEZONE_HOUR,TIMEZONE_MINUTE,TO,TRAILING,TRANSACTION,TRANSLATION,TREAT,TRIGGER,TRUE,UNDER,UNION,UNIQUE,UNKNOWN,UNNEST,UPDATE,USAGE,USER,USING,VALUE,VALUES,VARCHAR,VARIABLE,VARYING,VIEW,WHEN,WHENEVER,WHERE,WITH,WITHOUT,WORK,WRITE,YEAR,ZONE

# 神通函数映射 - 基于神通官方文档
validation.shentong.function_mapping.IFNULL=NVL
validation.shentong.function_mapping.DATE_FORMAT=TO_CHAR
validation.shentong.function_mapping.STR_TO_DATE=TO_DATE
validation.shentong.function_mapping.UNIX_TIMESTAMP=EXTRACT
validation.shentong.function_mapping.FROM_UNIXTIME=TO_TIMESTAMP
validation.shentong.function_mapping.NOW=SYSDATE
validation.shentong.function_mapping.CURDATE=TRUNC
validation.shentong.function_mapping.CURTIME=TO_CHAR

# 神通AUTO_INCREMENT限制 - 基于神通官方文档
validation.shentong.auto_increment_requires_unique=true
validation.shentong.auto_increment_max_value=9223372036854775807

# ==================== MySQL 8.4 语法错误检查规则 ====================

# MySQL无效的列名模式 - 基于MySQL 8.4官方文档
validation.mysql.invalid_column_patterns=^[0-9].*,.*\\..*,.*\\s.*,.*-.*,.*#.*,.*@.*,.*\\$.*

# MySQL无效的表名模式 - 基于MySQL 8.4官方文档
validation.mysql.invalid_table_patterns=^[0-9].*,.*\\..*,.*\\s.*,.*-.*,.*#.*,.*@.*,.*\\$.*

# MySQL无效的数据类型组合 - 基于MySQL 8.4官方文档
validation.mysql.invalid_type_combinations=UNSIGNED DECIMAL,UNSIGNED FLOAT,UNSIGNED DOUBLE,ZEROFILL DECIMAL,ZEROFILL FLOAT,ZEROFILL DOUBLE

# MySQL无效的约束组合 - 基于MySQL 8.4官方文档
validation.mysql.invalid_constraint_combinations=AUTO_INCREMENT NULL,AUTO_INCREMENT DEFAULT,PRIMARY KEY NULL

# MySQL无效的字符集组合 - 基于MySQL 8.4官方文档
validation.mysql.invalid_charset_combinations=utf8mb4 latin1_swedish_ci,utf8 utf8mb4_general_ci,binary utf8_general_ci

# MySQL无效的引擎选项 - 基于MySQL 8.4官方文档
validation.mysql.invalid_engine_options=TYPE=MyISAM,TYPE=InnoDB,ENGINE=BDB,ENGINE=ISAM

# MySQL废弃的语法 - 基于MySQL 8.4官方文档
validation.mysql.deprecated_syntax=TYPE=,SERIAL DEFAULT VALUE,FLOAT4,FLOAT8,INT1,INT2,INT3,INT4,INT8

# MySQL无效的索引类型组合 - 基于MySQL 8.4官方文档
validation.mysql.invalid_index_combinations=FULLTEXT BTREE,SPATIAL HASH,UNIQUE FULLTEXT

# ==================== 通用验证规则 ====================

# 数值类型默认值不应使用引号
validation.rules.numeric_default_no_quotes=true

# 字符串类型默认值必须使用引号
validation.rules.string_default_with_quotes=true

# ON UPDATE语法验证
validation.rules.validate_on_update=true

# AUTO_INCREMENT/IDENTITY验证
validation.rules.validate_auto_increment=true

# 保留字验证
validation.rules.validate_reserved_words=true

# 保留字引用字符配置
validation.reserved_words.quote_char.mysql=`
validation.reserved_words.quote_char.dameng="
validation.reserved_words.quote_char.shentong="
validation.reserved_words.quote_char.kingbase="

# 数据类型兼容性验证
validation.rules.validate_type_compatibility=true

# 数据类型长度限制验证
validation.rules.validate_type_length_limits=true

# 字符集和排序规则验证
validation.rules.validate_charset_collation=true

# 约束组合验证
validation.rules.validate_constraint_combinations=true

# 表名和列名验证
validation.rules.validate_identifier_patterns=true

# ==================== 错误严重程度配置 ====================

# 数值类型使用引号 - 警告
validation.severity.NUMERIC_WITH_QUOTES=WARNING

# 无效数值默认值 - 错误
validation.severity.INVALID_NUMERIC_DEFAULT=ERROR

# 无效ON UPDATE语法 - 错误
validation.severity.INVALID_ON_UPDATE_SYNTAX=ERROR

# 字符串类型无引号 - 警告
validation.severity.STRING_WITHOUT_QUOTES=WARNING

# 不支持的数据类型 - 错误
validation.severity.UNSUPPORTED_DATA_TYPE=ERROR

# AUTO_INCREMENT语法错误 - 错误
validation.severity.INVALID_AUTO_INCREMENT=ERROR

# 保留字冲突 - 警告
validation.severity.RESERVED_WORD_CONFLICT=WARNING

# 数据类型长度超限 - 错误
validation.severity.TYPE_LENGTH_EXCEEDED=ERROR

# 无效的DEFAULT函数 - 错误
validation.severity.INVALID_DEFAULT_FUNCTION=ERROR

# 无效的标识符模式 - 错误
validation.severity.INVALID_IDENTIFIER_PATTERN=ERROR

# 无效的数据类型组合 - 错误
validation.severity.INVALID_TYPE_COMBINATION=ERROR

# 无效的约束组合 - 错误
validation.severity.INVALID_CONSTRAINT_COMBINATION=ERROR

# 无效的字符集组合 - 错误
validation.severity.INVALID_CHARSET_COMBINATION=ERROR

# 废弃的语法 - 警告
validation.severity.DEPRECATED_SYNTAX=WARNING

# 无效的引擎选项 - 错误
validation.severity.INVALID_ENGINE_OPTION=ERROR

# 无效的索引类型组合 - 错误
validation.severity.INVALID_INDEX_COMBINATION=ERROR

# 数值范围超限 - 错误
validation.severity.NUMERIC_RANGE_EXCEEDED=ERROR

# 日期时间范围超限 - 错误
validation.severity.DATETIME_RANGE_EXCEEDED=ERROR

# 小数精度超限 - 错误
validation.severity.DECIMAL_PRECISION_EXCEEDED=ERROR

# 小数标度超限 - 错误
validation.severity.DECIMAL_SCALE_EXCEEDED=ERROR

# ENUM值过多 - 错误
validation.severity.ENUM_VALUES_EXCEEDED=ERROR

# SET成员过多 - 错误
validation.severity.SET_MEMBERS_EXCEEDED=ERROR

# ==================== 扩展错误严重程度配置 ====================

# SQL注入安全风险 - 严重错误
validation.severity.SQL_INJECTION_RISK=CRITICAL

# 危险函数使用 - 错误
validation.severity.DANGEROUS_FUNCTION_USAGE=ERROR

# 性能问题 - 警告
validation.severity.PERFORMANCE_ISSUE=WARNING

# 兼容性问题 - 警告
validation.severity.COMPATIBILITY_ISSUE=WARNING

# 字符集不匹配 - 警告
validation.severity.CHARSET_MISMATCH=WARNING

# 排序规则不匹配 - 警告
validation.severity.COLLATION_MISMATCH=WARNING

# 存储引擎不支持 - 错误
validation.severity.UNSUPPORTED_ENGINE=ERROR

# 分区键无效 - 错误
validation.severity.INVALID_PARTITION_KEY=ERROR

# 索引类型不支持 - 错误
validation.severity.UNSUPPORTED_INDEX_TYPE=ERROR

# 触发器事件无效 - 错误
validation.severity.INVALID_TRIGGER_EVENT=ERROR

# 存储过程参数过多 - 错误
validation.severity.TOO_MANY_PROCEDURE_PARAMETERS=ERROR

# 视图列过多 - 错误
validation.severity.TOO_MANY_VIEW_COLUMNS=ERROR

# 外键操作无效 - 错误
validation.severity.INVALID_FOREIGN_KEY_ACTION=ERROR

# 事务隔离级别无效 - 错误
validation.severity.INVALID_ISOLATION_LEVEL=ERROR

# 权限类型无效 - 错误
validation.severity.INVALID_PRIVILEGE_TYPE=ERROR

# 序列属性无效 - 错误
validation.severity.INVALID_SEQUENCE_PROPERTY=ERROR

# 表空间类型无效 - 错误
validation.severity.INVALID_TABLESPACE_TYPE=ERROR

# 数据文件大小超限 - 错误
validation.severity.DATAFILE_SIZE_EXCEEDED=ERROR

# 连接数超限 - 警告
validation.severity.CONNECTION_LIMIT_EXCEEDED=WARNING

# 用户变量过多 - 警告
validation.severity.TOO_MANY_USER_VARIABLES=WARNING

# JSON深度超限 - 错误
validation.severity.JSON_DEPTH_EXCEEDED=ERROR

# 几何对象点数超限 - 错误
validation.severity.GEOMETRY_POINTS_EXCEEDED=ERROR

# CTE递归深度超限 - 错误
validation.severity.CTE_RECURSION_DEPTH_EXCEEDED=ERROR

# 窗口函数使用错误 - 错误
validation.severity.INVALID_WINDOW_FUNCTION_USAGE=ERROR

# 正则表达式模式过长 - 错误
validation.severity.REGEX_PATTERN_TOO_LONG=ERROR

# 压缩长度超限 - 错误
validation.severity.COMPRESSED_LENGTH_EXCEEDED=ERROR

# ==================== 修复建议配置 ====================

# 数值类型默认值修复
validation.fixes.numeric_remove_quotes=移除数值类型默认值的引号

# ON UPDATE修复建议
validation.fixes.on_update_current_timestamp=使用CURRENT_TIMESTAMP替代NOW()

# AUTO_INCREMENT修复建议
validation.fixes.auto_increment_to_identity=达梦数据库使用IDENTITY(1,1)替代AUTO_INCREMENT

# 字符集修复建议
validation.fixes.charset_utf8=达梦数据库使用CHARACTER SET UTF8

# ==================== 性能配置 ====================

# 验证缓存大小
validation.performance.cache_size=1000

# 最大验证时间（毫秒）
validation.performance.max_validation_time=5000

# 批量验证大小
validation.performance.batch_size=100

# ==================== 调试配置 ====================

# 启用详细日志
validation.debug.verbose_logging=false

# 输出验证统计
validation.debug.output_statistics=true

# 保存验证报告
validation.debug.save_reports=false

# ==================== 扩展MySQL 8.4限制验证 ====================

# MySQL基础限制配置 - 基于MySQL 8.4官方文档
validation.mysql.max_table_name_length=64
validation.mysql.max_column_name_length=64
validation.mysql.max_index_name_length=64
validation.mysql.max_database_name_length=64
validation.mysql.varchar_max_length=65535
validation.mysql.max_columns_per_table=4096
validation.mysql.innodb_max_columns_per_table=1017

# MySQL行大小验证错误消息 - 基于MySQL 8.4官方文档
validation.mysql.row_size_error_messages=Row size too large,maximum row size,change some columns to TEXT or BLOBs

# MySQL无效的存储引擎组合 - 基于MySQL 8.4官方文档
validation.mysql.invalid_engine_combinations=TEMPORARY MyISAM,TEMPORARY MEMORY

# MySQL无效的字符集组合 - 基于MySQL 8.4官方文档
validation.mysql.invalid_charset_combinations=BINARY utf8,BINARY utf8mb4

# MySQL分区限制 - 基于MySQL 8.4官方文档
validation.mysql.max_partitions=8192
validation.mysql.max_subpartitions=8192
validation.mysql.partition_key_max_length=1024

# MySQL触发器限制 - 基于MySQL 8.4官方文档
validation.mysql.max_triggers_per_table=6
validation.mysql.trigger_name_max_length=64

# MySQL存储过程限制 - 基于MySQL 8.4官方文档
validation.mysql.procedure_name_max_length=64
validation.mysql.function_name_max_length=64
validation.mysql.max_procedure_parameters=65535

# MySQL FULLTEXT索引限制 - 基于MySQL 8.4官方文档
validation.mysql.fulltext_min_word_length=4
validation.mysql.fulltext_max_word_length=84
validation.mysql.fulltext_supported_types=CHAR,VARCHAR,TEXT

# MySQL外键限制 - 基于MySQL 8.4官方文档
validation.mysql.max_foreign_keys_per_table=64
validation.mysql.foreign_key_name_max_length=64

# MySQL视图限制 - 基于MySQL 8.4官方文档
validation.mysql.view_name_max_length=64
validation.mysql.max_view_columns=4096

# MySQL用户变量限制 - 基于MySQL 8.4官方文档
validation.mysql.user_variable_name_max_length=64
validation.mysql.max_user_variables=65536

# ==================== 神通数据库限制验证 ====================

# 神通数据库标识符限制 - 基于神通数据库文档
validation.shentong.max_identifier_length=127
validation.shentong.reserved_prefixes=SYS_,V_SYS_
validation.shentong.invalid_identifier_chars=$,#

# 神通数据库数据类型限制 - 基于神通数据库文档
validation.shentong.char_max_length=8000
validation.shentong.varchar_max_length=8000
validation.shentong.text_max_length=16777215
validation.shentong.binary_max_length=8000
validation.shentong.varbinary_max_length=8000
validation.shentong.bit_max_length=64000
validation.shentong.decimal_max_precision=1000
validation.shentong.decimal_max_scale=1000
validation.shentong.numeric_max_precision=1000
validation.shentong.numeric_max_scale=1000

# 神通数据库特殊类型限制
validation.shentong.name_type_max_length=127
validation.shentong.char_type_max_length=1

# 神通数据库AUTO_INCREMENT限制
validation.shentong.auto_increment_supported_types=INT,BIGINT,FLOAT
validation.shentong.auto_increment_requires_unique=true

# ==================== 金仓数据库限制验证 ====================

# 金仓数据库数据类型限制 - 基于金仓官方文档
validation.kingbase.varchar_max_length=10485760
validation.kingbase.char_max_length=10485760
validation.kingbase.text_max_length=1073741823
validation.kingbase.decimal_max_precision=1000
validation.kingbase.decimal_max_scale=1000
validation.kingbase.max_identifier_length=63

# 金仓数据库MySQL兼容特性
validation.kingbase.mysql_compatible_types=TINYINT,MEDIUMINT,LONGTEXT,MEDIUMTEXT,TINYTEXT
validation.kingbase.mysql_compatible_functions=GROUP_CONCAT,FIND_IN_SET,IFNULL

# ==================== 扩展MySQL 8.4验证规则 ====================

# MySQL字符集和排序规则限制 - 基于MySQL 8.4官方文档
validation.mysql.max_charset_name_length=32
validation.mysql.max_collation_name_length=32
validation.mysql.invalid_charset_collation_combinations=utf8_bin,utf8mb3_bin

# MySQL连接和会话限制 - 基于MySQL 8.4官方文档
validation.mysql.max_connections=100000
validation.mysql.max_user_connections=4294967295
validation.mysql.max_prepared_statements=1048576

# MySQL表空间限制 - 基于MySQL 8.4官方文档
validation.mysql.max_tablespace_name_length=64
validation.mysql.max_tablespaces=64000

# MySQL复制限制 - 基于MySQL 8.4官方文档
validation.mysql.max_slave_connections=4294967295
validation.mysql.max_binlog_size=1073741824

# MySQL JSON限制 - 基于MySQL 8.4官方文档
validation.mysql.json_max_depth=100
validation.mysql.json_max_key_length=65535

# MySQL GEOMETRY限制 - 基于MySQL 8.4官方文档
validation.mysql.geometry_max_points=65535
validation.mysql.spatial_index_max_key_length=3072

# ==================== MySQL 8.4 高级语法校验规则 ====================

# MySQL存储引擎限制 - 基于MySQL 8.4官方文档
validation.mysql.supported_engines=InnoDB,MyISAM,MEMORY,CSV,ARCHIVE,BLACKHOLE,FEDERATED,NDB
validation.mysql.deprecated_engines=MyISAM,MEMORY,CSV,ARCHIVE,BLACKHOLE,FEDERATED
validation.mysql.default_engine=InnoDB

# MySQL SQL模式限制 - 基于MySQL 8.4官方文档
validation.mysql.sql_modes=ALLOW_INVALID_DATES,ANSI_QUOTES,ERROR_FOR_DIVISION_BY_ZERO,HIGH_NOT_PRECEDENCE,IGNORE_SPACE,NO_AUTO_VALUE_ON_ZERO,NO_BACKSLASH_ESCAPES,NO_DIR_IN_CREATE,NO_ENGINE_SUBSTITUTION,NO_UNSIGNED_SUBTRACTION,NO_ZERO_DATE,NO_ZERO_IN_DATE,ONLY_FULL_GROUP_BY,PAD_CHAR_TO_FULL_LENGTH,PIPES_AS_CONCAT,REAL_AS_FLOAT,STRICT_ALL_TABLES,STRICT_TRANS_TABLES,TIME_TRUNCATE_FRACTIONAL

# MySQL函数限制 - 基于MySQL 8.4官方文档
validation.mysql.max_function_name_length=64
validation.mysql.max_function_parameters=65535
validation.mysql.deprecated_functions=PASSWORD,OLD_PASSWORD,ENCODE,DECODE

# MySQL变量限制 - 基于MySQL 8.4官方文档
validation.mysql.max_variable_name_length=64
validation.mysql.max_user_variables=65536
validation.mysql.system_variable_prefixes=@@global,@@session,@@local

# MySQL事务限制 - 基于MySQL 8.4官方文档
validation.mysql.max_transaction_size=1073741824
validation.mysql.max_savepoints=65535
validation.mysql.isolation_levels=READ UNCOMMITTED,READ COMMITTED,REPEATABLE READ,SERIALIZABLE

# MySQL锁定限制 - 基于MySQL 8.4官方文档
validation.mysql.max_lock_wait_timeout=31536000
validation.mysql.max_table_locks=4294967295
validation.mysql.lock_types=READ,WRITE,READ LOCAL,LOW_PRIORITY WRITE

# MySQL分区限制 - 基于MySQL 8.4官方文档（扩展）
validation.mysql.partition_types=RANGE,LIST,HASH,KEY,LINEAR HASH,LINEAR KEY
validation.mysql.max_partition_expression_length=3072
validation.mysql.subpartition_types=HASH,KEY,LINEAR HASH,LINEAR KEY

# MySQL窗口函数限制 - 基于MySQL 8.4官方文档
validation.mysql.window_functions=ROW_NUMBER,RANK,DENSE_RANK,PERCENT_RANK,CUME_DIST,NTILE,LAG,LEAD,FIRST_VALUE,LAST_VALUE,NTH_VALUE
validation.mysql.window_frame_types=ROWS,RANGE,GROUPS
validation.mysql.window_frame_bounds=UNBOUNDED PRECEDING,CURRENT ROW,UNBOUNDED FOLLOWING

# MySQL CTE限制 - 基于MySQL 8.4官方文档
validation.mysql.max_cte_recursion_depth=1000
validation.mysql.max_cte_references=61

# MySQL正则表达式限制 - 基于MySQL 8.4官方文档
validation.mysql.regex_functions=REGEXP,RLIKE,REGEXP_LIKE,REGEXP_INSTR,REGEXP_REPLACE,REGEXP_SUBSTR
validation.mysql.regex_max_pattern_length=65535

# MySQL加密函数限制 - 基于MySQL 8.4官方文档
validation.mysql.encryption_functions=AES_ENCRYPT,AES_DECRYPT,SHA1,SHA2,MD5,ENCRYPT,RANDOM_BYTES
validation.mysql.hash_algorithms=SHA1,SHA224,SHA256,SHA384,SHA512

# MySQL压缩函数限制 - 基于MySQL 8.4官方文档
validation.mysql.compression_functions=COMPRESS,UNCOMPRESS,UNCOMPRESSED_LENGTH
validation.mysql.max_compressed_length=4294967295

# ==================== 达梦数据库扩展校验规则 ====================

# 达梦数据库版本特性 - 基于达梦官方文档
validation.dameng.supported_versions=DM8.0,DM8.1,DM8.2
validation.dameng.unicode_flag_values=0,1
validation.dameng.page_sizes=4KB,8KB,16KB,32KB

# 达梦数据库字符集扩展 - 基于达梦官方文档
validation.dameng.charset_encoding=UTF8,GBK,GB18030,BIG5,ISO-8859-1,KSC5601,EUC-JP
validation.dameng.collation_rules=BINARY,CI,CS
validation.dameng.length_semantics=BYTE,CHAR

# 达梦数据库数据类型扩展 - 基于达梦官方文档
validation.dameng.types.extended=LONGVARCHAR,LONGVARBINARY,IMAGE,BFILE
validation.dameng.types.interval=INTERVAL YEAR,INTERVAL MONTH,INTERVAL DAY,INTERVAL HOUR,INTERVAL MINUTE,INTERVAL SECOND
validation.dameng.types.array=ARRAY,VARRAY

# 达梦数据库约束扩展 - 基于达梦官方文档
validation.dameng.constraint_types=PRIMARY KEY,FOREIGN KEY,UNIQUE,CHECK,NOT NULL,DEFAULT
validation.dameng.check_constraint_operators=>,<,>=,<=,=,<>,!=,BETWEEN,IN,LIKE,IS NULL,IS NOT NULL

# 达梦数据库索引扩展 - 基于达梦官方文档
validation.dameng.index_types=BTREE,HASH,BITMAP,FUNCTION_BASED
validation.dameng.index_algorithms=BTREE,HASH
validation.dameng.max_index_columns=32
validation.dameng.max_index_key_length=8188

# 达梦数据库分区扩展 - 基于达梦官方文档
validation.dameng.partition_types=RANGE,LIST,HASH
validation.dameng.max_partitions=1024
validation.dameng.partition_key_max_columns=16

# 达梦数据库存储过程扩展 - 基于达梦官方文档
validation.dameng.procedure_max_parameters=1000
validation.dameng.procedure_max_name_length=128
validation.dameng.procedure_languages=PLSQL,JAVA,C

# 达梦数据库触发器扩展 - 基于达梦官方文档
validation.dameng.trigger_types=BEFORE,AFTER,INSTEAD OF
validation.dameng.trigger_events=INSERT,UPDATE,DELETE,DDL
validation.dameng.max_triggers_per_table=unlimited

# 达梦数据库视图扩展 - 基于达梦官方文档
validation.dameng.view_max_columns=1000
validation.dameng.materialized_view_refresh_types=COMPLETE,FAST,FORCE
validation.dameng.materialized_view_refresh_methods=ON DEMAND,ON COMMIT

# 达梦数据库序列扩展 - 基于达梦官方文档
validation.dameng.sequence_min_value=-999999999999999999999999999
validation.dameng.sequence_max_value=999999999999999999999999999
validation.dameng.sequence_cache_min=2
validation.dameng.sequence_cache_max=1000000

# 达梦数据库表空间扩展 - 基于达梦官方文档
validation.dameng.tablespace_types=NORMAL,TEMPORARY,UNDO
validation.dameng.datafile_max_size=32TB
validation.dameng.max_datafiles_per_tablespace=1000

# 达梦数据库用户权限扩展 - 基于达梦官方文档
validation.dameng.system_privileges=CREATE SESSION,CREATE TABLE,CREATE VIEW,CREATE PROCEDURE,CREATE TRIGGER,CREATE SEQUENCE,CREATE SYNONYM,CREATE USER,CREATE ROLE,CREATE TABLESPACE,ALTER USER,DROP USER,GRANT ANY PRIVILEGE,REVOKE ANY PRIVILEGE
validation.dameng.object_privileges=SELECT,INSERT,UPDATE,DELETE,ALTER,INDEX,REFERENCES,EXECUTE

# ==================== 金仓数据库扩展校验规则 ====================

# 金仓数据库版本特性 - 基于金仓官方文档
validation.kingbase.supported_versions=V8R3,V8R6,V9R1,V9R2,V9R3,V9R4
validation.kingbase.compatibility_modes=ORACLE,MYSQL,POSTGRESQL

# 金仓数据库MySQL兼容扩展 - 基于金仓官方文档
validation.kingbase.mysql_compatible_syntax=AUTO_INCREMENT,UNSIGNED,ZEROFILL,ENUM,SET,TINYINT,MEDIUMINT,LONGTEXT,MEDIUMTEXT,TINYTEXT
validation.kingbase.mysql_compatible_functions=GROUP_CONCAT,FIND_IN_SET,IFNULL,DATE_FORMAT,STR_TO_DATE,UNIX_TIMESTAMP,FROM_UNIXTIME

# 金仓数据库数据类型扩展 - 基于金仓官方文档
validation.kingbase.types.mysql_compat=TINYINT,MEDIUMINT,LONGTEXT,MEDIUMTEXT,TINYTEXT,LONGBLOB,MEDIUMBLOB,TINYBLOB
validation.kingbase.types.postgresql_compat=BYTEA,UUID,INET,CIDR,MACADDR,TSQUERY,TSVECTOR

# 金仓数据库索引扩展 - 基于金仓官方文档
validation.kingbase.index_types=BTREE,HASH,GIN,GIST,SPGIST,BRIN
validation.kingbase.index_access_methods=btree,hash,gin,gist,spgist,brin
validation.kingbase.partial_index_support=true

# 金仓数据库分区扩展 - 基于金仓官方文档
validation.kingbase.partition_types=RANGE,LIST,HASH,COMPOSITE
validation.kingbase.partition_pruning=true
validation.kingbase.partition_wise_joins=true

# 金仓数据库并发控制 - 基于金仓官方文档
validation.kingbase.isolation_levels=READ UNCOMMITTED,READ COMMITTED,REPEATABLE READ,SERIALIZABLE
validation.kingbase.lock_modes=ACCESS SHARE,ROW SHARE,ROW EXCLUSIVE,SHARE UPDATE EXCLUSIVE,SHARE,SHARE ROW EXCLUSIVE,EXCLUSIVE,ACCESS EXCLUSIVE

# 金仓数据库扩展功能 - 基于金仓官方文档
validation.kingbase.extensions=plpgsql,uuid-ossp,pgcrypto,hstore,ltree,earthdistance,cube,isn,seg
validation.kingbase.foreign_data_wrappers=file_fdw,postgres_fdw,mysql_fdw,oracle_fdw

# ==================== 神通数据库扩展校验规则 ====================

# 神通数据库版本特性 - 基于神通数据库文档
validation.shentong.supported_versions=7.0,8.0,9.0
validation.shentong.character_sets=UTF8,GBK,GB18030,BIG5,ISO-8859-1

# 神通数据库标识符扩展 - 基于神通数据库文档
validation.shentong.identifier_rules=^[A-Za-z_][A-Za-z0-9_]*$
validation.shentong.quoted_identifier_rules=^"[^"]*"$
validation.shentong.case_sensitivity=INSENSITIVE

# 神通数据库数据类型扩展 - 基于神通数据库文档
validation.shentong.types.extended=ROWID,ROWNUM,SYSATTR_ROWVERSION
validation.shentong.types.lob=CLOB,BLOB,NCLOB,BFILE
validation.shentong.types.xml=XMLTYPE

# 神通数据库伪列扩展 - 基于神通数据库文档
validation.shentong.pseudocolumns=ROWID,ROWNUM,SYSATTR_ROWVERSION
validation.shentong.rownum_restrictions=no_table_prefix,no_join_condition,no_order_by_before_rownum

# 神通数据库序列扩展 - 基于神通数据库文档
validation.shentong.sequence_properties=START WITH,INCREMENT BY,MAXVALUE,MINVALUE,CYCLE,CACHE,ORDER
validation.shentong.sequence_functions=NEXTVAL,CURRVAL

# 神通数据库分区扩展 - 基于神通数据库文档
validation.shentong.partition_methods=RANGE,LIST,HASH,COMPOSITE
validation.shentong.subpartition_methods=RANGE,LIST,HASH

# 神通数据库存储扩展 - 基于神通数据库文档
validation.shentong.storage_parameters=INITIAL,NEXT,MINEXTENTS,MAXEXTENTS,PCTINCREASE,FREELISTS,FREELIST GROUPS,BUFFER_POOL
validation.shentong.tablespace_types=PERMANENT,TEMPORARY,UNDO

# ==================== 高级SQL语法校验规则 ====================

# DDL语句校验规则 - 基于各数据库官方文档
validation.ddl.create_table_options=ENGINE,CHARSET,COLLATE,AUTO_INCREMENT,COMMENT,ROW_FORMAT,KEY_BLOCK_SIZE,COMPRESSION,ENCRYPTION
validation.ddl.alter_table_operations=ADD COLUMN,DROP COLUMN,MODIFY COLUMN,CHANGE COLUMN,ADD INDEX,DROP INDEX,ADD CONSTRAINT,DROP CONSTRAINT,RENAME TO
validation.ddl.drop_behaviors=CASCADE,RESTRICT

# DML语句校验规则 - 基于各数据库官方文档
validation.dml.insert_syntax=INSERT INTO,INSERT IGNORE,INSERT ON DUPLICATE KEY UPDATE,REPLACE INTO
validation.dml.update_syntax=UPDATE,UPDATE IGNORE,UPDATE JOIN
validation.dml.delete_syntax=DELETE,DELETE FROM,DELETE JOIN,TRUNCATE TABLE

# 查询语句校验规则 - 基于各数据库官方文档
validation.query.select_clauses=SELECT,FROM,WHERE,GROUP BY,HAVING,ORDER BY,LIMIT,OFFSET,UNION,INTERSECT,EXCEPT
validation.query.join_types=INNER JOIN,LEFT JOIN,RIGHT JOIN,FULL OUTER JOIN,CROSS JOIN,NATURAL JOIN
validation.query.subquery_types=SCALAR,ROW,TABLE,EXISTS,IN,ANY,ALL,SOME

# 函数和表达式校验规则 - 基于各数据库官方文档
validation.expressions.operators=+,-,*,/,%,=,!=,<>,<,<=,>,>=,AND,OR,NOT,LIKE,RLIKE,REGEXP,IN,BETWEEN,IS NULL,IS NOT NULL
validation.expressions.aggregate_functions=COUNT,SUM,AVG,MIN,MAX,GROUP_CONCAT,BIT_AND,BIT_OR,BIT_XOR,STDDEV,VARIANCE
validation.expressions.string_functions=CONCAT,SUBSTRING,LENGTH,CHAR_LENGTH,UPPER,LOWER,TRIM,LTRIM,RTRIM,REPLACE,LOCATE,INSTR

# 数据类型转换校验规则 - 基于各数据库官方文档
validation.conversion.cast_functions=CAST,CONVERT,BINARY
validation.conversion.implicit_conversions=NUMERIC_TO_STRING,STRING_TO_NUMERIC,DATE_TO_STRING,STRING_TO_DATE
validation.conversion.explicit_conversions=CAST_AS_CHAR,CAST_AS_SIGNED,CAST_AS_UNSIGNED,CAST_AS_DECIMAL,CAST_AS_DATE,CAST_AS_TIME,CAST_AS_DATETIME

# 约束校验规则 - 基于各数据库官方文档
validation.constraints.types=PRIMARY KEY,FOREIGN KEY,UNIQUE,CHECK,NOT NULL,DEFAULT
validation.constraints.foreign_key_actions=CASCADE,SET NULL,SET DEFAULT,RESTRICT,NO ACTION
validation.constraints.check_operators=>,<,>=,<=,=,<>,!=,BETWEEN,IN,LIKE,IS NULL,IS NOT NULL,AND,OR,NOT

# 索引校验规则 - 基于各数据库官方文档
validation.indexes.types=PRIMARY,UNIQUE,INDEX,FULLTEXT,SPATIAL
validation.indexes.algorithms=BTREE,HASH,RTREE
validation.indexes.key_block_sizes=1,2,4,8,16

# 视图校验规则 - 基于各数据库官方文档
validation.views.algorithms=UNDEFINED,MERGE,TEMPTABLE
validation.views.security=DEFINER,INVOKER
validation.views.check_options=NONE,LOCAL,CASCADED

# 存储过程和函数校验规则 - 基于各数据库官方文档
validation.procedures.characteristics=DETERMINISTIC,NOT DETERMINISTIC,CONTAINS SQL,NO SQL,READS SQL DATA,MODIFIES SQL DATA
validation.procedures.security=DEFINER,INVOKER
validation.procedures.parameter_modes=IN,OUT,INOUT

# 触发器校验规则 - 基于各数据库官方文档
validation.triggers.timing=BEFORE,AFTER
validation.triggers.events=INSERT,UPDATE,DELETE
validation.triggers.for_each=ROW,STATEMENT

# 事务校验规则 - 基于各数据库官方文档
validation.transactions.isolation_levels=READ UNCOMMITTED,READ COMMITTED,REPEATABLE READ,SERIALIZABLE
validation.transactions.access_modes=READ WRITE,READ ONLY
validation.transactions.statements=START TRANSACTION,BEGIN,COMMIT,ROLLBACK,SAVEPOINT,RELEASE SAVEPOINT,ROLLBACK TO SAVEPOINT

# 权限校验规则 - 基于各数据库官方文档
validation.privileges.system=CREATE,ALTER,DROP,SELECT,INSERT,UPDATE,DELETE,INDEX,REFERENCES,EXECUTE,GRANT OPTION
validation.privileges.object_types=TABLE,VIEW,PROCEDURE,FUNCTION,TRIGGER,SEQUENCE,SCHEMA,DATABASE
validation.privileges.grant_options=WITH GRANT OPTION,WITH ADMIN OPTION

# 字符集和排序规则校验 - 基于各数据库官方文档
validation.charset.mysql_charsets=utf8mb4,utf8mb3,utf8,latin1,ascii,binary,big5,cp1250,cp1251,cp1256,cp1257,cp850,cp852,cp866,cp932,dec8,eucjpms,euckr,gb2312,gbk,gb18030,geostd8,greek,hebrew,hp8,keybcs2,koi8r,koi8u,latin2,latin5,latin7,macce,macroman,sjis,swe7,tis620,ucs2,ujis,utf16,utf16le,utf32
validation.charset.mysql_collations=utf8mb4_general_ci,utf8mb4_unicode_ci,utf8mb4_bin,utf8mb4_0900_ai_ci,utf8_general_ci,utf8_unicode_ci,utf8_bin,latin1_swedish_ci,latin1_general_ci,latin1_bin

# SQL注入防护校验规则 - 基于安全最佳实践
validation.security.dangerous_functions=LOAD_FILE,INTO OUTFILE,INTO DUMPFILE,SYSTEM,EXEC,SHELL
# 修正：移除正常SQL语法模式，只保留真正的SQL注入模式
# DELETE FROM 和 UPDATE SET 是正常的SQL语法，不应该被标记为SQL注入
validation.security.suspicious_patterns=UNION\\s+.*\\s+SELECT,OR\\s+.*\\s*1\\s*=\\s*1,AND\\s+.*\\s*1\\s*=\\s*1
validation.security.comment_patterns=--,/\\*,\\*/,#

# 性能优化校验规则 - 基于性能最佳实践
validation.performance.inefficient_patterns=SELECT\\s+\\*,ORDER\\s+BY\\s+RAND\\(\\),LIKE\\s+'%.*%',NOT\\s+IN,OR\\s+.*\\s+OR\\s+.*\\s+OR
validation.performance.missing_indexes=WHERE\\s+.*\\s*=,WHERE\\s+.*\\s*>,WHERE\\s+.*\\s*<,JOIN\\s+.*\\s+ON\\s+.*\\s*=
# 基于MySQL 8.4官方文档的大结果集检测配置
# 参考：https://dev.mysql.com/doc/refman/8.4/en/select.html
# MySQL官方文档：LIMIT子句用于限制SELECT语句返回的行数，OFFSET指定返回第一行的偏移量
validation.performance.large_result_sets=\\bLIMIT\\s+([1-9][0-9]+)\\b,\\bOFFSET\\s+([1-9][0-9]+)\\b

# 动态阈值配置 - 基于MySQL 8.4官方文档和性能最佳实践
validation.performance.large_limit_threshold=1000
validation.performance.large_offset_threshold=1000

# 兼容性校验规则 - 基于跨数据库兼容性
validation.compatibility.mysql_specific=AUTO_INCREMENT,UNSIGNED,ZEROFILL,ENUM,SET,TINYINT,MEDIUMINT,LONGTEXT,ENGINE
validation.compatibility.oracle_specific=ROWNUM,DUAL,SYSDATE,NVL,DECODE,CONNECT BY
validation.compatibility.postgresql_specific=SERIAL,BIGSERIAL,BYTEA,UUID,ARRAY,JSONB

# 数据完整性校验规则 - 基于数据完整性最佳实践
validation.integrity.referential_actions=CASCADE,SET NULL,SET DEFAULT,RESTRICT,NO ACTION
validation.integrity.check_constraints=>,<,>=,<=,=,<>,!=,BETWEEN,IN,LIKE,IS NULL,IS NOT NULL
validation.integrity.domain_constraints=NOT NULL,UNIQUE,PRIMARY KEY,FOREIGN KEY,CHECK,DEFAULT

# ==================== 扩展修复建议配置 ====================

# SQL注入风险修复建议
validation.fix.SQL_INJECTION_RISK=使用参数化查询或预处理语句，避免直接拼接用户输入到SQL语句中

# 危险函数使用修复建议
validation.fix.DANGEROUS_FUNCTION_USAGE=避免使用LOAD_FILE、INTO OUTFILE、SYSTEM等危险函数，使用安全的替代方案

# 性能问题修复建议
validation.fix.PERFORMANCE_ISSUE=优化查询语句，避免使用SELECT *、ORDER BY RAND()、LIKE '%pattern%'等低效模式

# 兼容性问题修复建议
validation.fix.COMPATIBILITY_ISSUE=使用标准SQL语法，避免数据库特定的扩展功能，或为不同数据库提供兼容性处理

# 字符集不匹配修复建议
validation.fix.CHARSET_MISMATCH=确保表、列和连接使用一致的字符集，推荐使用UTF8MB4

# 排序规则不匹配修复建议
validation.fix.COLLATION_MISMATCH=确保比较操作使用一致的排序规则，避免隐式转换

# 存储引擎不支持修复建议
validation.fix.UNSUPPORTED_ENGINE=使用目标数据库支持的存储引擎，如InnoDB、MyISAM等

# 分区键无效修复建议
validation.fix.INVALID_PARTITION_KEY=确保分区键是表的一部分，且符合分区类型的要求

# 索引类型不支持修复建议
validation.fix.UNSUPPORTED_INDEX_TYPE=使用目标数据库支持的索引类型，如BTREE、HASH等

# 触发器事件无效修复建议
validation.fix.INVALID_TRIGGER_EVENT=使用有效的触发器事件：INSERT、UPDATE、DELETE

# 存储过程参数过多修复建议
validation.fix.TOO_MANY_PROCEDURE_PARAMETERS=减少存储过程参数数量，或将相关参数组合成复合类型

# 视图列过多修复建议
validation.fix.TOO_MANY_VIEW_COLUMNS=减少视图中的列数量，或将视图拆分为多个相关视图

# 外键操作无效修复建议
validation.fix.INVALID_FOREIGN_KEY_ACTION=使用有效的外键操作：CASCADE、SET NULL、SET DEFAULT、RESTRICT、NO ACTION

# 事务隔离级别无效修复建议
validation.fix.INVALID_ISOLATION_LEVEL=使用有效的隔离级别：READ UNCOMMITTED、READ COMMITTED、REPEATABLE READ、SERIALIZABLE

# 权限类型无效修复建议
validation.fix.INVALID_PRIVILEGE_TYPE=使用有效的权限类型：SELECT、INSERT、UPDATE、DELETE、CREATE、ALTER、DROP等

# 序列属性无效修复建议
validation.fix.INVALID_SEQUENCE_PROPERTY=检查序列属性设置：START WITH、INCREMENT BY、MAXVALUE、MINVALUE等

# 表空间类型无效修复建议
validation.fix.INVALID_TABLESPACE_TYPE=使用有效的表空间类型：NORMAL、TEMPORARY、UNDO

# 数据文件大小超限修复建议
validation.fix.DATAFILE_SIZE_EXCEEDED=减少数据文件大小或使用多个数据文件分散存储

# 连接数超限修复建议
validation.fix.CONNECTION_LIMIT_EXCEEDED=优化连接池配置，减少并发连接数或增加服务器连接限制

# 用户变量过多修复建议
validation.fix.TOO_MANY_USER_VARIABLES=减少用户变量使用，或使用临时表存储中间结果

# JSON深度超限修复建议
validation.fix.JSON_DEPTH_EXCEEDED=简化JSON结构，减少嵌套层级深度

# 几何对象点数超限修复建议
validation.fix.GEOMETRY_POINTS_EXCEEDED=减少几何对象中的点数量，或将复杂几何对象拆分

# CTE递归深度超限修复建议
validation.fix.CTE_RECURSION_DEPTH_EXCEEDED=优化递归查询逻辑，减少递归深度或使用迭代方法

# 窗口函数使用错误修复建议
validation.fix.INVALID_WINDOW_FUNCTION_USAGE=检查窗口函数语法：OVER子句、PARTITION BY、ORDER BY等

# 正则表达式模式过长修复建议
validation.fix.REGEX_PATTERN_TOO_LONG=简化正则表达式模式，或将复杂模式拆分为多个简单模式

# 压缩长度超限修复建议
validation.fix.COMPRESSED_LENGTH_EXCEEDED=减少压缩数据大小或使用分块压缩处理

# ==================== 数据库特定修复建议 ====================

# 达梦数据库特定修复建议
validation.fix.dameng.IDENTITY_INSERT_REQUIRED=在插入IDENTITY列显式值前添加SET IDENTITY_INSERT ON语句
validation.fix.dameng.CHARSET_CONVERSION=将MySQL的DEFAULT CHARSET转换为达梦的CHARACTER SET语法
validation.fix.dameng.ENGINE_REMOVAL=移除MySQL特定的ENGINE子句，达梦数据库不支持
validation.fix.dameng.COMMENT_SYNTAX=将MySQL的COMMENT子句转换为达梦的COMMENT ON语法

# 金仓数据库特定修复建议
validation.fix.kingbase.MYSQL_COMPATIBILITY=启用MySQL兼容模式或使用PostgreSQL标准语法
validation.fix.kingbase.AUTO_INCREMENT_CONVERSION=将AUTO_INCREMENT转换为SERIAL或SEQUENCE
validation.fix.kingbase.UNSIGNED_REMOVAL=移除UNSIGNED关键字，金仓数据库不支持

# 神通数据库特定修复建议
validation.fix.shentong.ROWNUM_USAGE=正确使用ROWNUM伪列，注意其限制和语法要求
validation.fix.shentong.SEQUENCE_SYNTAX=使用神通数据库的序列语法：NEXTVAL、CURRVAL
validation.fix.shentong.STORAGE_PARAMETERS=正确设置存储参数：INITIAL、NEXT、MAXEXTENTS等

# ==================== 高级校验配置 ====================

# 动态校验规则启用配置
validation.dynamic.enable_mysql_specific=true
validation.dynamic.enable_dameng_specific=true
validation.dynamic.enable_kingbase_specific=true
validation.dynamic.enable_shentong_specific=true

# 校验规则优先级配置
validation.priority.security=1
validation.priority.syntax=2
validation.priority.performance=3
validation.priority.compatibility=4

# 校验结果缓存配置
validation.cache.enable=true
validation.cache.ttl_seconds=3600
validation.cache.max_entries=10000

# 校验报告配置
validation.report.include_suggestions=true
validation.report.include_examples=true
validation.report.include_documentation_links=true
validation.report.format=JSON,XML,HTML

# 官方文档链接配置 - 基于各数据库官方文档
validation.docs.mysql=https://dev.mysql.com/doc/refman/8.4/en/
validation.docs.dameng=https://eco.dameng.com/document/dm/zh-cn/sql-dev/
validation.docs.kingbase=https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/
validation.docs.shentong=内部文档参考

# 校验规则版本信息
validation.version.mysql=8.4
validation.version.dameng=8.2
validation.version.kingbase=V9R4
validation.version.shentong=9.0
validation.rules.last_updated=2024-07-30
validation.rules.author=SQL Transpiler Team
