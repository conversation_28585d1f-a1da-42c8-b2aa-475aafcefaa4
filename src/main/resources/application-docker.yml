server:
  port: 8080
  servlet:
    context-path: /
  tomcat:
    max-threads: 200
    min-spare-threads: 10

spring:
  application:
    name: sql-transpiler-web

  thymeleaf:
    cache: true  # 生产环境启用缓存
    encoding: UTF-8
    mode: HTML
    prefix: classpath:/templates/
    suffix: .html

  web:
    resources:
      static-locations: classpath:/static/
      cache:
        period: 3600  # 1小时缓存

  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 10MB
      file-size-threshold: 2KB

# 日志配置
logging:
  level:
    com.xylink.sqltranspiler: INFO
    org.springframework: WARN
    org.antlr: WARN
    root: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /app/logs/sql-transpiler.log
    max-size: 100MB
    max-history: 30

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
  info:
    env:
      enabled: true
