<configuration>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%-5level] [%thread] %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.FileAppender">
        <file>logs/transpiler.log</file>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%-5level] [%thread] %logger{36} - %msg%n%ex</pattern>
        </encoder>
    </appender>

    <appender name="ISSUES_FILE" class="ch.qos.logback.core.FileAppender">
        <file>logs/transpiler-issues.log</file>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} - [%-5level] %msg%n%ex</pattern>
        </encoder>
    </appender>

    <!-- 详细语句跟踪日志文件 -->
    <appender name="STATEMENT_TRACKER_FILE" class="ch.qos.logback.core.FileAppender">
        <file>logs/statement-tracker.log</file>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%-5level] %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="TRANSPILER_ISSUES" level="WARN" additivity="false">
        <appender-ref ref="ISSUES_FILE" />
        <appender-ref ref="STDOUT" />
    </logger>

    <!-- 语句跟踪器专用日志配置 -->
    <logger name="STATEMENT_TRACKER" level="DEBUG" additivity="false">
        <appender-ref ref="STATEMENT_TRACKER_FILE"/>
    </logger>

    <root level="INFO">
        <appender-ref ref="STDOUT" />
        <appender-ref ref="FILE" />
    </root>

</configuration> 