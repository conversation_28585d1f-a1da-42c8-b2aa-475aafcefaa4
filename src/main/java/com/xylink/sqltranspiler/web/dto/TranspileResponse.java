package com.xylink.sqltranspiler.web.dto;

import java.util.List;
import java.util.Map;

import com.xylink.sqltranspiler.core.context.TranspilationIssue;

import lombok.Data;

/**
 * SQL转换响应DTO - 支持多数据库同时转换
 */
@Data
public class TranspileResponse {

    /**
     * 转换是否成功
     */
    private boolean success;

    /**
     * 源数据库方言
     */
    private String sourceDialect = "mysql";

    /**
     * 各数据库的转换结果
     * Key: 数据库类型 (dameng, kingbase)
     * Value: 转换结果详情
     */
    private Map<String, DatabaseResult> results;

    /**
     * 全局错误消息（当整体转换失败时）
     */
    private String errorMessage;

    /**
     * 单个数据库的转换结果
     */
    @Data
    public static class DatabaseResult {
        /**
         * 数据库名称
         */
        private String databaseName;

        /**
         * 数据库描述
         */
        private String databaseDescription;

        /**
         * 转换是否成功
         */
        private boolean success;

        /**
         * 转换后的SQL语句
         */
        private String translatedSql;

        /**
         * 成功转换的语句数量
         */
        private int successCount;

        /**
         * 失败的语句数量
         */
        private int failureCount;

        /**
         * 转换过程中的问题列表
         */
        private List<TranspilationIssue> issues;

        /**
         * 错误消息
         */
        private String errorMessage;

        /**
         * 是否应该在前端显示SQL内容
         * 当SQL行数过多时，为了页面性能，只提供下载功能而不显示内容
         */
        private boolean shouldDisplaySql = true;

        /**
         * SQL行数（用于前端显示统计信息）
         */
        private int sqlLineCount;

        /**
         * 原始文件总行数
         */
        private int originalLineCount;

        /**
         * 识别出的有效SQL语句总数
         */
        private int totalStatements;

        /**
         * 跳过的行数（空行、注释、LOCK/UNLOCK等）
         */
        private int skippedLines;

        /**
         * 跳过的内容详情列表
         */
        private List<SkippedContent> skippedContents;

        /**
         * 错误详情列表（从issues中提取的错误信息）
         */
        private List<ErrorDetail> errorDetails;
    }

    /**
     * 跳过的内容详情
     */
    @Data
    public static class SkippedContent {
        /**
         * 跳过的内容类型（如：空行、注释、LOCK语句等）
         */
        private String type;

        /**
         * 跳过的内容（截取前100个字符）
         */
        private String content;

        /**
         * 行号
         */
        private int lineNumber;

        /**
         * 跳过的原因
         */
        private String reason;

        public SkippedContent(String type, String content, int lineNumber, String reason) {
            this.type = type;
            this.content = content != null && content.length() > 100 ?
                content.substring(0, 100) + "..." : content;
            this.lineNumber = lineNumber;
            this.reason = reason;
        }
    }

    /**
     * 错误详情
     */
    @Data
    public static class ErrorDetail {
        /**
         * 错误代码
         */
        private String errorCode;

        /**
         * 错误级别
         */
        private String level;

        /**
         * 错误消息
         */
        private String message;

        /**
         * 出错的SQL语句（截取前200个字符）
         */
        private String sqlStatement;

        /**
         * 行号
         */
        private int lineNumber;

        public ErrorDetail(String errorCode, String level, String message, String sqlStatement, int lineNumber) {
            this.errorCode = errorCode;
            this.level = level;
            this.message = message;
            this.sqlStatement = sqlStatement != null && sqlStatement.length() > 200 ?
                sqlStatement.substring(0, 200) + "..." : sqlStatement;
            this.lineNumber = lineNumber;
        }
    }
}
