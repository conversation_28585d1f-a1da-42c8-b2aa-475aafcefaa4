package com.xylink.sqltranspiler.web.dto;

import org.springframework.web.multipart.MultipartFile;

import lombok.Data;

/**
 * SQL转换请求DTO
 */
@Data
public class TranspileRequest {

    /**
     * 源SQL语句
     */
    private String sql;

    /**
     * 上传的SQL文件
     */
    private MultipartFile sqlFile;

    /**
     * 源数据库方言，固定为mysql
     */
    private String sourceDialect = "mysql";

    /**
     * 是否保留注释
     */
    private boolean preserveComments = true;

    /**
     * 输入方式：text（文本输入）或 file（文件上传）
     */
    private String inputType = "text";

    /**
     * 验证SQL内容是否为空
     */
    public boolean isValidSqlInput() {
        if ("file".equals(inputType)) {
            return sqlFile != null && !sqlFile.isEmpty();
        } else {
            return sql != null && !sql.trim().isEmpty();
        }
    }

    /**
     * 获取SQL内容（从文本或文件）
     */
    public String getSqlContent() throws Exception {
        if ("file".equals(inputType) && sqlFile != null && !sqlFile.isEmpty()) {
            return new String(sqlFile.getBytes(), "UTF-8");
        }
        return sql != null ? sql : "";
    }
}
