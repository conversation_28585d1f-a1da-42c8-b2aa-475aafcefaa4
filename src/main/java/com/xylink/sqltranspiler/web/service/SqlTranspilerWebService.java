package com.xylink.sqltranspiler.web.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.xylink.sqltranspiler.Transpiler;
import com.xylink.sqltranspiler.core.context.TranspilationIssue;
import com.xylink.sqltranspiler.core.context.TranspilationResult;
import com.xylink.sqltranspiler.web.dto.DialectInfo;
import com.xylink.sqltranspiler.web.dto.TranspileRequest;
import com.xylink.sqltranspiler.web.dto.TranspileResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * SQL转换Web服务
 */
@Service
@Slf4j
public class SqlTranspilerWebService {

    private final Transpiler transpiler;

    /**
     * SQL显示行数阈值，超过此行数将不在页面显示SQL内容，只提供下载功能
     */
    private static final int SQL_DISPLAY_LINE_THRESHOLD = 200;

    public SqlTranspilerWebService() {
        this.transpiler = new Transpiler();
        // Transpiler默认已启用严格SQL验证模式
        log.info("SQL Transpiler Web Service initialized with strict validation enabled by default");
    }

    /**
     * 执行SQL转换 - 转换为所有支持的数据库
     */
    public TranspileResponse transpile(TranspileRequest request) {
        log.info("Starting SQL transpilation from MySQL to all supported databases, input type: {}",
                request.getInputType());

        TranspileResponse response = new TranspileResponse();
        response.setSourceDialect(request.getSourceDialect());

        // 获取主要的数据库类型（不包含别名）
        List<String> mainDialects = getMainDialects();
        Map<String, TranspileResponse.DatabaseResult> results = new HashMap<>();

        boolean overallSuccess = true;

        try {
            // 获取SQL内容（从文本输入或文件上传）
            String sqlContent = request.getSqlContent();

            if (sqlContent == null || sqlContent.trim().isEmpty()) {
                response.setSuccess(false);
                response.setErrorMessage("SQL内容为空");
                return response;
            }

            // 计算SQL行数
            int sqlLineCount = countLines(sqlContent);
            boolean shouldDisplaySql = sqlLineCount <= SQL_DISPLAY_LINE_THRESHOLD;

            log.info("SQL content length: {} characters, line count: {}, should display: {}",
                    sqlContent.length(), sqlLineCount, shouldDisplaySql);

            if (!shouldDisplaySql) {
                log.info("SQL file has {} lines, exceeding threshold of {}. Only download will be available.",
                        sqlLineCount, SQL_DISPLAY_LINE_THRESHOLD);
            }

            for (String dialect : mainDialects) {
                log.info("Converting to {}", dialect);

                TranspileResponse.DatabaseResult dbResult = new TranspileResponse.DatabaseResult();
                dbResult.setDatabaseName(dialect);
                dbResult.setDatabaseDescription(getDialectDescription(dialect));
                dbResult.setSqlLineCount(sqlLineCount);
                dbResult.setShouldDisplaySql(shouldDisplaySql);

                try {
                    // 执行转换
                    TranspilationResult result = transpiler.transpile(
                            sqlContent,
                            request.getSourceDialect(),
                            dialect,
                            request.isPreserveComments());

                    // 构建单个数据库的结果
                    dbResult.setSuccess(result.failureCount() == 0);
                    dbResult.setTranslatedSql(result.translatedSql());
                    dbResult.setSuccessCount(result.successCount());
                    dbResult.setFailureCount(result.failureCount());
                    dbResult.setIssues(result.issues());

                    // 计算SQL行数并决定是否显示内容
                    String translatedSql = result.translatedSql();
                    if (translatedSql != null) {
                        int lineCount = translatedSql.split("\n").length;
                        dbResult.setSqlLineCount(lineCount);
                        // 如果超过50行，则不在页面显示内容，只提供下载
                        dbResult.setShouldDisplaySql(lineCount <= 50);
                    }

                    // 设置统计信息
                    dbResult.setOriginalLineCount(sqlLineCount);
                    dbResult.setTotalStatements(result.successCount() + result.failureCount());
                    dbResult.setSkippedLines(sqlLineCount - (result.successCount() + result.failureCount()));

                    // 解析预处理日志，提取跳过的内容详情
                    List<TranspileResponse.SkippedContent> skippedContents = extractSkippedContents(result.preprocessingLogs());
                    dbResult.setSkippedContents(skippedContents);
                    log.info("Extracted {} skipped contents for {}", skippedContents.size(), dialect);

                    // 从issues中提取错误详情
                    List<TranspileResponse.ErrorDetail> errorDetails = extractErrorDetails(result.issues());
                    dbResult.setErrorDetails(errorDetails);
                    log.info("Extracted {} error details for {}", errorDetails.size(), dialect);

                    if (result.failureCount() > 0) {
                        dbResult.setErrorMessage("转换过程中发现 " + result.failureCount() + " 个错误");
                        overallSuccess = false;
                    }

                    log.info("Conversion to {} completed: success={}, successCount={}, failureCount={}",
                            dialect, dbResult.isSuccess(), dbResult.getSuccessCount(), dbResult.getFailureCount());

                } catch (Exception e) {
                    log.error("Conversion to {} failed", dialect, e);
                    dbResult.setSuccess(false);
                    dbResult.setErrorMessage("转换失败: " + e.getMessage());
                    overallSuccess = false;
                }

                results.put(dialect, dbResult);
            }

            response.setResults(results);
            response.setSuccess(overallSuccess);

        } catch (Exception e) {
            log.error("SQL transpilation failed", e);
            response.setSuccess(false);
            response.setErrorMessage("转换失败: " + e.getMessage());
        }

        return response;
    }

    /**
     * 计算文本行数
     */
    private int countLines(String text) {
        if (text == null || text.isEmpty()) {
            return 0;
        }
        return text.split("\r\n|\r|\n").length;
    }

    /**
     * 获取主要的数据库方言列表（不包含别名）
     */
    private List<String> getMainDialects() {
        return List.of("dameng", "kingbase", "shentong");
    }

    /**
     * 获取数据库方言的描述
     */
    private String getDialectDescription(String dialect) {
        switch (dialect.toLowerCase()) {
            case "dameng":
                return "达梦数据库 - 支持完整的MySQL到达梦转换";
            case "kingbase":
                return "金仓数据库 - 支持完整的MySQL到金仓转换";
            case "shentong":
                return "神通数据库 - 支持完整的MySQL到神通转换";
            default:
                return "未知数据库";
        }
    }

    /**
     * 获取支持的目标数据库方言列表
     */
    public List<DialectInfo> getSupportedDialects() {
        return getMainDialects().stream()
                .map(dialect -> new DialectInfo(dialect, getDialectDescription(dialect)))
                .collect(Collectors.toList());
    }

    /**
     * 检查是否支持指定的方言
     */
    public boolean isDialectSupported(String dialect) {
        return transpiler.isTargetDialectSupported(dialect);
    }

    /**
     * 从预处理日志中提取跳过的内容详情
     */
    private List<TranspileResponse.SkippedContent> extractSkippedContents(List<String> preprocessingLogs) {
        List<TranspileResponse.SkippedContent> skippedContents = new ArrayList<>();
        int lineNumber = 1;

        for (String logMessage : preprocessingLogs) {
            if (logMessage.startsWith("FILTERED:")) {
                // 解析过滤日志：FILTERED: reason - Original SQL: content
                String[] parts = logMessage.split(" - Original SQL: ", 2);
                if (parts.length == 2) {
                    String reason = parts[0].substring("FILTERED: ".length());
                    String content = parts[1];

                    String type = determineSkippedType(reason);
                    skippedContents.add(new TranspileResponse.SkippedContent(
                        type, content, lineNumber++, reason));
                }
            } else if (logMessage.contains("Removed MySQL conditional comment:")) {
                // 解析条件注释移除日志
                String content = extractContentFromLog(logMessage, "Removed MySQL conditional comment:");
                skippedContents.add(new TranspileResponse.SkippedContent(
                    "条件注释", content, lineNumber++, "MySQL条件注释，不支持"));
            } else if (logMessage.contains("COMMENT_REMOVAL:") &&
                       (logMessage.contains("Removed single-line comment:") ||
                        logMessage.contains("Removed multi-line comment:"))) {
                // 解析注释移除日志：COMMENT_REMOVAL: Removed single-line comment: -- content
                // 只处理具体的注释移除，不处理统计信息
                String content = extractCommentContent(logMessage);
                skippedContents.add(new TranspileResponse.SkippedContent(
                    "注释", content, lineNumber++, "注释内容已移除"));
            }
        }

        return skippedContents;
    }

    /**
     * 从issues中提取错误详情
     */
    private List<TranspileResponse.ErrorDetail> extractErrorDetails(List<TranspilationIssue> issues) {
        List<TranspileResponse.ErrorDetail> errorDetails = new ArrayList<>();

        for (TranspilationIssue issue : issues) {
            if (issue.level() == TranspilationIssue.IssueLevel.ERROR) {
                errorDetails.add(new TranspileResponse.ErrorDetail(
                    issue.issueCode(),
                    issue.level().toString(),
                    issue.message(),
                    extractSqlFromIssue(issue),
                    issue.line()
                ));
            }
        }

        return errorDetails;
    }

    /**
     * 根据跳过原因确定跳过类型
     */
    private String determineSkippedType(String reason) {
        if (reason.contains("Administrative statement")) {
            return "管理语句";
        } else if (reason.contains("SHOW statement")) {
            return "SHOW语句";
        } else if (reason.contains("SET statement")) {
            return "SET语句";
        } else {
            return "其他";
        }
    }

    /**
     * 从日志消息中提取内容
     */
    private String extractContentFromLog(String logMessage, String prefix) {
        int index = logMessage.indexOf(prefix);
        if (index >= 0) {
            return logMessage.substring(index + prefix.length()).trim();
        }
        return logMessage;
    }

    /**
     * 从注释移除日志中提取注释内容
     * 例如：COMMENT_REMOVAL: Removed single-line comment: -- 这是注释
     */
    private String extractCommentContent(String logMessage) {
        // 查找注释内容的模式
        if (logMessage.contains("Removed single-line comment:")) {
            int startIndex = logMessage.indexOf("Removed single-line comment:");
            if (startIndex != -1) {
                String content = logMessage.substring(startIndex + "Removed single-line comment:".length()).trim();
                return content.isEmpty() ? "单行注释" : content;
            }
        } else if (logMessage.contains("Removed multi-line comment:")) {
            int startIndex = logMessage.indexOf("Removed multi-line comment:");
            if (startIndex != -1) {
                String content = logMessage.substring(startIndex + "Removed multi-line comment:".length()).trim();
                return content.isEmpty() ? "多行注释" : content;
            }
        } else if (logMessage.contains("COMMENT_REMOVAL:")) {
            // 通用的注释移除日志
            return "注释内容";
        }
        return "注释";
    }

    /**
     * 从issue中提取SQL语句
     */
    private String extractSqlFromIssue(TranspilationIssue issue) {
        String message = issue.message();
        // 尝试从错误消息中提取SQL语句
        if (message.contains("Original SQL:")) {
            String[] parts = message.split("Original SQL:", 2);
            if (parts.length == 2) {
                return parts[1].trim();
            }
        }
        return null;
    }
}
