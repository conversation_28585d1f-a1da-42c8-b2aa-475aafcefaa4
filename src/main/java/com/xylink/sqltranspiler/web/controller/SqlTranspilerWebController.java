package com.xylink.sqltranspiler.web.controller;

import java.util.List;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.xylink.sqltranspiler.web.dto.DialectInfo;
import com.xylink.sqltranspiler.web.dto.TranspileRequest;
import com.xylink.sqltranspiler.web.dto.TranspileResponse;
import com.xylink.sqltranspiler.web.service.SqlTranspilerWebService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * SQL转换Web页面控制器
 */
@Controller
@RequiredArgsConstructor
@Slf4j
public class SqlTranspilerWebController {
    
    private final SqlTranspilerWebService transpilerService;
    
    /**
     * 首页
     */
    @GetMapping("/")
    public String index(Model model) {
        model.addAttribute("transpileRequest", new TranspileRequest());
        model.addAttribute("dialects", transpilerService.getSupportedDialects());
        return "index";
    }
    
    /**
     * 处理SQL转换请求
     */
    @PostMapping("/transpile")
    public String transpile(@ModelAttribute TranspileRequest request,
                           BindingResult bindingResult,
                           Model model,
                           RedirectAttributes redirectAttributes) {

        // 添加方言列表到模型
        List<DialectInfo> dialects = transpilerService.getSupportedDialects();
        model.addAttribute("dialects", dialects);

        // 手动验证SQL输入
        if (!request.isValidSqlInput()) {
            if ("file".equals(request.getInputType())) {
                model.addAttribute("errorMessage", "请选择要上传的SQL文件");
            } else {
                model.addAttribute("errorMessage", "请输入SQL语句");
            }
            model.addAttribute("transpileRequest", request);
            return "index";
        }

        // 验证文件类型（如果是文件上传）
        if ("file".equals(request.getInputType()) && request.getSqlFile() != null) {
            String filename = request.getSqlFile().getOriginalFilename();
            if (filename != null && !filename.toLowerCase().endsWith(".sql")) {
                model.addAttribute("errorMessage", "只支持.sql文件格式");
                model.addAttribute("transpileRequest", request);
                return "index";
            }
        }

        log.info("Processing transpile request from MySQL to all supported databases, input type: {}",
                request.getInputType());

        try {
            // 执行转换
            TranspileResponse response = transpilerService.transpile(request);

            // 添加结果到模型
            model.addAttribute("transpileRequest", request);
            model.addAttribute("transpileResponse", response);

            return "index";

        } catch (Exception e) {
            log.error("Error during transpilation", e);
            model.addAttribute("transpileRequest", request);
            model.addAttribute("errorMessage", "转换过程中发生错误: " + e.getMessage());
            return "index";
        }
    }
}
