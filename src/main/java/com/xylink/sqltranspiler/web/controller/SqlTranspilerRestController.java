package com.xylink.sqltranspiler.web.controller;

import com.xylink.sqltranspiler.web.dto.DialectInfo;
import com.xylink.sqltranspiler.web.dto.TranspileRequest;
import com.xylink.sqltranspiler.web.dto.TranspileResponse;
import com.xylink.sqltranspiler.web.service.SqlTranspilerWebService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * SQL转换REST API控制器
 */
@RestController
@RequestMapping("/api/transpiler")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*") // 允许跨域访问
public class SqlTranspilerRestController {
    
    private final SqlTranspilerWebService transpilerService;
    
    /**
     * 执行SQL转换 - 转换为所有支持的数据库（JSON请求）
     */
    @PostMapping("/transpile")
    public ResponseEntity<TranspileResponse> transpile(@Valid @RequestBody TranspileRequest request) {
        log.info("Received transpile request from MySQL to all supported databases");

        TranspileResponse response = transpilerService.transpile(request);

        if (response.isSuccess()) {
            return ResponseEntity.ok(response);
        } else {
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 执行SQL转换 - 支持文件上传
     */
    @PostMapping("/transpile-file")
    public ResponseEntity<TranspileResponse> transpileFile(
            @RequestParam("sqlFile") MultipartFile sqlFile,
            @RequestParam(value = "sourceDialect", defaultValue = "mysql") String sourceDialect,
            @RequestParam(value = "preserveComments", defaultValue = "true") boolean preserveComments) {

        log.info("Received file transpile request from MySQL to all supported databases, filename: {}",
                sqlFile.getOriginalFilename());

        // 验证文件
        if (sqlFile.isEmpty()) {
            TranspileResponse errorResponse = new TranspileResponse();
            errorResponse.setSuccess(false);
            errorResponse.setErrorMessage("上传的文件为空");
            return ResponseEntity.badRequest().body(errorResponse);
        }

        String filename = sqlFile.getOriginalFilename();
        if (filename != null && !filename.toLowerCase().endsWith(".sql")) {
            TranspileResponse errorResponse = new TranspileResponse();
            errorResponse.setSuccess(false);
            errorResponse.setErrorMessage("只支持.sql文件格式");
            return ResponseEntity.badRequest().body(errorResponse);
        }

        // 构建请求对象
        TranspileRequest request = new TranspileRequest();
        request.setSqlFile(sqlFile);
        request.setInputType("file");
        request.setSourceDialect(sourceDialect);
        request.setPreserveComments(preserveComments);

        TranspileResponse response = transpilerService.transpile(request);

        if (response.isSuccess()) {
            return ResponseEntity.ok(response);
        } else {
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取支持的数据库方言列表
     */
    @GetMapping("/dialects")
    public ResponseEntity<List<DialectInfo>> getSupportedDialects() {
        List<DialectInfo> dialects = transpilerService.getSupportedDialects();
        return ResponseEntity.ok(dialects);
    }
    
    /**
     * 检查是否支持指定的方言
     */
    @GetMapping("/dialects/{dialect}/supported")
    public ResponseEntity<Boolean> isDialectSupported(@PathVariable String dialect) {
        boolean supported = transpilerService.isDialectSupported(dialect);
        return ResponseEntity.ok(supported);
    }
}
