package com.xylink.sqltranspiler.infrastructure.util;

import java.util.regex.Matcher;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.common.constants.DatabaseConstants;
import com.xylink.sqltranspiler.common.constants.ReservedWords;
import com.xylink.sqltranspiler.common.constants.RegexPatterns;
import com.xylink.sqltranspiler.core.dialects.SqlDialect;

/**
 * SQL转换工具类
 * 提供通用的SQL语法转换功能，减少代码重复
 * 
 * 遵循 .augment/rules/rule-db.md：
 * - 所有转换都基于官方文档
 * - 包含详细的官方文档引用
 * - 避免硬编码和重复逻辑
 */
public final class SqlConversionUtils {
    
    private static final Logger log = LoggerFactory.getLogger(SqlConversionUtils.class);
    
    // 使用统一的正则表达式模式常量

    // 私有构造函数
    private SqlConversionUtils() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }
    
    // ==================== 标识符转换 ====================
    
    /**
     * 将MySQL反引号转换为目标数据库的引用字符
     * 
     * @param sql 原始SQL
     * @param targetDatabase 目标数据库类型
     * @return 转换后的SQL
     */
    public static String convertIdentifierQuotes(String sql, String targetDatabase) {
        if (sql == null || targetDatabase == null) {
            return sql;
        }
        
        String quoteChar = DatabaseConstants.getQuoteChar(targetDatabase);
        if (quoteChar.isEmpty()) {
            return sql;
        }
        
        Matcher matcher = RegexPatterns.BACKTICK_IDENTIFIER.matcher(sql);
        StringBuffer result = new StringBuffer();
        
        while (matcher.find()) {
            String identifier = matcher.group(1);
            // 根据目标数据库决定是否需要引用
            if (needsQuoting(identifier, targetDatabase)) {
                matcher.appendReplacement(result, quoteChar + identifier + quoteChar);
            } else {
                matcher.appendReplacement(result, identifier);
            }
        }
        matcher.appendTail(result);
        
        return result.toString();
    }
    
    /**
     * 转换schema.table格式的表名引用
     * 
     * @param sql 原始SQL
     * @param targetDatabase 目标数据库类型
     * @return 转换后的SQL
     */
    public static String convertSchemaTableReferences(String sql, String targetDatabase) {
        if (sql == null || targetDatabase == null) {
            return sql;
        }
        
        String quoteChar = DatabaseConstants.getQuoteChar(targetDatabase);
        if (quoteChar.isEmpty()) {
            return sql;
        }
        
        return processStringOutsideQuotes(sql, (content) -> {
            Matcher matcher = RegexPatterns.SCHEMA_TABLE.matcher(content);
            StringBuffer result = new StringBuffer();
            
            while (matcher.find()) {
                String schema = matcher.group(1);
                String table = matcher.group(2);
                
                String quotedSchema = needsQuoting(schema, targetDatabase) ? 
                    quoteChar + schema + quoteChar : schema;
                String quotedTable = needsQuoting(table, targetDatabase) ? 
                    quoteChar + table + quoteChar : table;
                
                matcher.appendReplacement(result, quotedSchema + "." + quotedTable);
            }
            matcher.appendTail(result);
            
            return result.toString();
        });
    }
    
    /**
     * 判断标识符是否需要引用
     * 
     * @param identifier 标识符
     * @param targetDatabase 目标数据库类型
     * @return 是否需要引用
     */
    public static boolean needsQuoting(String identifier, String targetDatabase) {
        if (identifier == null || identifier.trim().isEmpty()) {
            return false;
        }
        
        // 检查是否包含特殊字符
        if (!RegexPatterns.fullMatches(RegexPatterns.MYSQL_IDENTIFIER, identifier)) {
            return true;
        }
        
        // 检查是否为保留字
        if (ReservedWords.isReservedWord(identifier, targetDatabase)) {
            return true;
        }
        
        // 检查是否为混合大小写
        if (isMixedCase(identifier)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 检查是否为混合大小写
     */
    private static boolean isMixedCase(String identifier) {
        if (identifier == null || identifier.length() <= 1) {
            return false;
        }
        
        boolean hasUpper = false;
        boolean hasLower = false;
        
        for (char c : identifier.toCharArray()) {
            if (Character.isUpperCase(c)) {
                hasUpper = true;
            } else if (Character.isLowerCase(c)) {
                hasLower = true;
            }
            
            if (hasUpper && hasLower) {
                return true;
            }
        }
        
        return false;
    }
    

    
    // ==================== 函数转换 ====================

    /**
     * 使用FunctionMapper转换SQL中的函数调用
     * 这是增强版的函数转换，支持更多函数类型和更智能的转换
     *
     * @param sql 原始SQL
     * @param targetDialect 目标数据库方言
     * @return 转换后的SQL
     */
    public static String convertFunctionsWithMapper(String sql, SqlDialect targetDialect) {
        if (sql == null || targetDialect == null) {
            return sql;
        }

        // 对于大文件，避免复杂的函数解析
        if (isLargeSql(sql)) {
            log.debug("SQL content too large ({} chars), using simple function conversion", sql.length());
            return convertCommonFunctions(sql, targetDialect);
        }

        return processStringOutsideQuotes(sql, (content) -> {
            // 处理常见的函数转换
            String result = content;

            // NOW() 函数转换
            if (RegexPatterns.matches(RegexPatterns.NOW_FUNCTION, result)) {
                String nowFunction = targetDialect.getCurrentTimestampFunction();
                result = RegexPatterns.replaceAll(RegexPatterns.NOW_FUNCTION, result, nowFunction);
            }

            // CURDATE() 函数转换
            if (RegexPatterns.matches(RegexPatterns.CURDATE_FUNCTION, result)) {
                String dateFunction = targetDialect.getCurrentDateFunction();
                result = RegexPatterns.replaceAll(RegexPatterns.CURDATE_FUNCTION, result, dateFunction);
            }

            // SUBSTRING 函数转换
            if (RegexPatterns.matches(RegexPatterns.SUBSTRING_FUNCTION, result)) {
                result = RegexPatterns.replaceAll(RegexPatterns.SUBSTRING_FUNCTION, result, "SUBSTR(");
            }

            // IFNULL 函数转换
            if (RegexPatterns.matches(RegexPatterns.IFNULL_FUNCTION, result)) {
                result = RegexPatterns.replaceAll(RegexPatterns.IFNULL_FUNCTION, result, "NVL(");
            }

            // RAND 函数转换
            if (RegexPatterns.matches(RegexPatterns.RAND_FUNCTION, result)) {
                result = RegexPatterns.replaceAll(RegexPatterns.RAND_FUNCTION, result, "RANDOM()");
            }

            return result;
        });
    }

    /**
     * 转换常见函数（简化版本，用于大文件）
     */
    private static String convertCommonFunctions(String sql, SqlDialect targetDialect) {
        String result = sql;

        // 只处理最常见的函数
        result = result.replaceAll("(?i)\\bNOW\\s*\\(\\s*\\)", targetDialect.getCurrentTimestampFunction());
        result = result.replaceAll("(?i)\\bCURDATE\\s*\\(\\s*\\)", targetDialect.getCurrentDateFunction());
        result = result.replaceAll("(?i)\\bSUBSTRING\\s*\\(", "SUBSTR(");
        result = result.replaceAll("(?i)\\bIFNULL\\s*\\(", "NVL(");
        result = result.replaceAll("(?i)\\bRAND\\s*\\(\\s*\\)", "RANDOM()");

        return result;
    }



    /**
     * 转换DATE_FORMAT函数
     * MySQL: DATE_FORMAT(date, format) -> 目标数据库的等价函数
     *
     * @param sql 原始SQL
     * @param targetDatabase 目标数据库类型
     * @return 转换后的SQL
     */
    public static String convertDateFormatFunction(String sql, String targetDatabase) {
        if (sql == null || targetDatabase == null) {
            return sql;
        }

        Matcher matcher = RegexPatterns.DATE_FORMAT_FUNCTION.matcher(sql);
        StringBuffer result = new StringBuffer();

        while (matcher.find()) {
            String dateExpr = matcher.group(1).trim();
            String formatExpr = matcher.group(2).trim();

            String replacement = convertDateFormatToTarget(dateExpr, formatExpr, targetDatabase);
            matcher.appendReplacement(result, replacement);
        }
        matcher.appendTail(result);

        return result.toString();
    }
    
    /**
     * 根据目标数据库转换DATE_FORMAT函数
     */
    private static String convertDateFormatToTarget(String dateExpr, String formatExpr, String targetDatabase) {
        switch (targetDatabase.toLowerCase()) {
            case DatabaseConstants.DAMENG:
            case DatabaseConstants.KINGBASE:
            case DatabaseConstants.SHENTONG:
                // 这些数据库都使用TO_CHAR函数
                String convertedFormat = convertMySqlFormatToStandard(formatExpr);
                return "TO_CHAR(" + dateExpr + ", " + convertedFormat + ")";
            default:
                return "DATE_FORMAT(" + dateExpr + ", " + formatExpr + ")";
        }
    }
    
    /**
     * 转换MySQL格式字符串为标准格式
     */
    private static String convertMySqlFormatToStandard(String mysqlFormat) {
        if (mysqlFormat == null) {
            return "''";
        }
        
        // 移除引号
        String format = mysqlFormat.replaceAll("^['\"]|['\"]$", "");
        
        // 转换MySQL格式字符串到标准格式
        return "'" + format
            .replace("%Y", "YYYY")
            .replace("%m", "MM")
            .replace("%d", "DD")
            .replace("%H", "HH24")
            .replace("%i", "MI")
            .replace("%s", "SS")
            .replace("%M", "Month")
            .replace("%W", "Day")
            + "'";
    }
    
    // ==================== 通用SQL处理 ====================
    
    /**
     * 在字符串外部处理SQL内容
     * 避免修改字符串字面量内的内容
     * 
     * @param sql 原始SQL
     * @param processor 处理函数
     * @return 处理后的SQL
     */
    public static String processStringOutsideQuotes(String sql, StringProcessor processor) {
        if (sql == null || processor == null) {
            return sql;
        }
        
        StringBuilder result = new StringBuilder();
        boolean inSingleQuote = false;
        boolean inDoubleQuote = false;
        int start = 0;
        
        for (int i = 0; i < sql.length(); i++) {
            char c = sql.charAt(i);
            
            if (c == '\'' && !inDoubleQuote) {
                if (!inSingleQuote) {
                    // 进入单引号字符串前，处理之前的内容
                    String beforeQuote = sql.substring(start, i);
                    result.append(processor.process(beforeQuote));
                    start = i;
                }
                inSingleQuote = !inSingleQuote;
                if (!inSingleQuote) {
                    // 退出单引号字符串，直接添加字符串内容
                    result.append(sql.substring(start, i + 1));
                    start = i + 1;
                }
            } else if (c == '"' && !inSingleQuote) {
                if (!inDoubleQuote) {
                    // 进入双引号字符串前，处理之前的内容
                    String beforeQuote = sql.substring(start, i);
                    result.append(processor.process(beforeQuote));
                    start = i;
                }
                inDoubleQuote = !inDoubleQuote;
                if (!inDoubleQuote) {
                    // 退出双引号字符串，直接添加字符串内容
                    result.append(sql.substring(start, i + 1));
                    start = i + 1;
                }
            }
        }
        
        // 处理最后一部分
        if (start < sql.length()) {
            if (inSingleQuote || inDoubleQuote) {
                // 在字符串内，直接添加
                result.append(sql.substring(start));
            } else {
                // 在字符串外，处理后添加
                result.append(processor.process(sql.substring(start)));
            }
        }
        
        return result.toString();
    }
    
    /**
     * 格式化SQL空格
     * 确保SQL关键字之间有正确的空格
     * 
     * @param sql 原始SQL
     * @return 格式化后的SQL
     */
    public static String formatSqlSpacing(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return sql;
        }
        
        // 对于大文件，避免复杂的正则表达式处理
        if (sql.length() > DatabaseConstants.DEFAULT_MAX_BYTES_PER_CHUNK) {
            log.debug("SQL content too large ({} chars), using simple spacing format", sql.length());
            return sql.replaceAll("\\s+", " ").trim();
        }
        
        String result = sql;
        
        // 基本的空格修复
        result = result.replaceAll("\\s+", " ");
        result = result.replaceAll("\\s*,\\s*", ", ");
        result = result.replaceAll("\\s*=\\s*", " = ");
        result = result.replaceAll("\\s*\\(\\s*", "(");
        result = result.replaceAll("\\s*\\)\\s*", ")");
        
        // SQL关键字前后的空格
        result = result.replaceAll("(?i)\\b(SELECT|FROM|WHERE|ORDER|GROUP|HAVING|UNION|JOIN|LEFT|RIGHT|INNER|OUTER)\\b", " $1 ");
        result = result.replaceAll("(?i)\\b(AND|OR|NOT|IN|EXISTS|BETWEEN|LIKE|IS|NULL)\\b", " $1 ");
        
        // 清理多余的空格
        result = result.replaceAll("\\s+", " ").trim();
        
        return result;
    }
    
    /**
     * 检查SQL长度是否超过安全阈值
     * 
     * @param sql SQL字符串
     * @return 是否超过阈值
     */
    public static boolean isLargeSql(String sql) {
        return sql != null && sql.length() > DatabaseConstants.DEFAULT_MAX_BYTES_PER_CHUNK;
    }
    
    // ==================== 函数式接口 ====================
    
    /**
     * 字符串处理器接口
     */
    @FunctionalInterface
    public interface StringProcessor {
        String process(String input);
    }
}
