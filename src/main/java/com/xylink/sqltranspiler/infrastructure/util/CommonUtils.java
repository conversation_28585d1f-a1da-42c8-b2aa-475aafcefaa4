package com.xylink.sqltranspiler.infrastructure.util;

import java.util.ArrayList;
import java.util.regex.Pattern;

import org.antlr.v4.runtime.ParserRuleContext;
import org.antlr.v4.runtime.Token;
import org.antlr.v4.runtime.tree.ParseTree;
import org.antlr.v4.runtime.tree.TerminalNodeImpl;
import org.apache.commons.lang3.StringUtils;

public class CommonUtils {

    public static final Pattern KEYWORD_REGEX = Pattern.compile("'([A-Z_]+)'");

    public static void findShowStatementKeyWord(ArrayList<String> keyWords, ParseTree node) {
        if (node instanceof TerminalNodeImpl) {
            TerminalNodeImpl terminalNode = (TerminalNodeImpl) node;
            int count = terminalNode.getParent().getChildCount();
            for (int i = 0; i < count; i++) {
                ParseTree child = terminalNode.getParent().getChild(i);
                if (child instanceof TerminalNodeImpl) {
                    keyWords.add(child.getText().toUpperCase());
                }
            }
        } else {
            findShowStatementKeyWord(keyWords, node.getChild(0));
        }
    }

    public static String subsql(String sql, ParserRuleContext context) {
        String currSql = StringUtils.substring(sql, context.start.getStartIndex(), context.stop.getStopIndex() + 1);
        return cleanLastSemi(currSql);
    }

    public static String subsql(String sql, Token start, Token stop) {
        String currSql = StringUtils.substring(sql, start.getStopIndex() + 1, stop.getStopIndex() + 1);
        return cleanLastSemi(currSql);
    }

    private static String cleanLastSemi(String text) {
        if (StringUtils.endsWith(text, ";")) {
            return StringUtils.substring(text, 0, text.length() - 1);
        }

        return text;
    }

    public static String cleanQuote(String value) {
        if (StringUtils.isBlank(value)) {
            return value;
        }

        String result = value;
        if (StringUtils.startsWith(result, "'") && StringUtils.endsWith(result, "'")) {
            result = StringUtils.substring(result, 1, result.length() - 1);
        }

        if (StringUtils.startsWith(result, "\"") && StringUtils.endsWith(result, "\"")) {
            result = StringUtils.substring(result, 1, result.length() - 1);
        }

        if (StringUtils.startsWith(result, "`") && StringUtils.endsWith(result, "`")) {
            result = StringUtils.substring(result, 1, result.length() - 1);
        }

        return StringUtils.trim(result);
    }
}
