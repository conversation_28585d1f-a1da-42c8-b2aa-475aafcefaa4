package com.xylink.sqltranspiler.infrastructure.parser;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 智能SQL文件分割器
 * 
 * 解决大文件导致的内存溢出和栈溢出问题：
 * 1. 按语句边界智能分割，确保不截断完整的SQL语句
 * 2. 支持多行语句和复杂嵌套结构
 * 3. 正确处理字符串、注释中的分号
 * 4. 可配置的分块大小和策略
 */
public class SqlFileSplitter {
    
    private static final Logger log = LoggerFactory.getLogger(SqlFileSplitter.class);
    
    // 默认配置 - 使用更保守的分块大小以避免栈溢出
    private static final int DEFAULT_MAX_LINES_PER_CHUNK = 300;
    private static final int DEFAULT_MAX_STATEMENTS_PER_CHUNK = 100;
    private static final long DEFAULT_MAX_BYTES_PER_CHUNK = 256 * 1024; // 256KB
    
    // SQL语句开始的关键字列表 - 使用统一的SQL关键字管理系统
    // 避免复杂正则表达式导致 StackOverflowError，使用简单字符串检查
    private static Set<String> getSqlStatementKeywords() {
        // 使用统一的SQL关键字管理，并添加一些DDL/DML特有的关键字
        Set<String> keywords = new HashSet<>(com.xylink.sqltranspiler.common.constants.SqlKeywords.getAllKeywords());

        // 添加一些文件分割特需的关键字（这些可能不在基础SQL关键字中）
        keywords.add("USE");
        keywords.add("LOCK");
        keywords.add("UNLOCK");
        keywords.add("GRANT");
        keywords.add("REVOKE");
        keywords.add("TRUNCATE");
        keywords.add("ANALYZE");
        keywords.add("OPTIMIZE");

        return keywords;
    }
    
    private final int maxLinesPerChunk;
    private final int maxStatementsPerChunk;
    private final long maxBytesPerChunk;
    
    public SqlFileSplitter() {
        this(DEFAULT_MAX_LINES_PER_CHUNK, DEFAULT_MAX_STATEMENTS_PER_CHUNK, DEFAULT_MAX_BYTES_PER_CHUNK);
    }
    
    public SqlFileSplitter(int maxLinesPerChunk, int maxStatementsPerChunk, long maxBytesPerChunk) {
        this.maxLinesPerChunk = maxLinesPerChunk;
        this.maxStatementsPerChunk = maxStatementsPerChunk;
        this.maxBytesPerChunk = maxBytesPerChunk;
    }
    
    /**
     * 检查文件是否需要分割
     */
    public boolean needsSplitting(Path filePath) throws IOException {
        if (!Files.exists(filePath) || !Files.isRegularFile(filePath)) {
            return false;
        }
        
        long fileSize = Files.size(filePath);
        if (fileSize > maxBytesPerChunk * 2) { // 如果文件大于2倍的分块大小，就需要分割
            log.info("File {} needs splitting due to size: {} bytes", filePath.getFileName(), fileSize);
            return true;
        }
        
        long lineCount = Files.lines(filePath).count();
        if (lineCount > maxLinesPerChunk * 2) { // 如果行数大于2倍的分块行数，就需要分割
            log.info("File {} needs splitting due to line count: {} lines", filePath.getFileName(), lineCount);
            return true;
        }
        
        return false;
    }
    
    /**
     * 智能分割SQL文件
     * 
     * @param inputPath 输入文件路径
     * @param outputDir 输出目录路径
     * @return 分割后的文件列表
     */
    public List<Path> splitFile(Path inputPath, Path outputDir) throws IOException {
        log.info("Starting intelligent SQL file splitting for: {}", inputPath.getFileName());
        
        if (!needsSplitting(inputPath)) {
            log.info("File {} does not need splitting, returning original file", inputPath.getFileName());
            return List.of(inputPath);
        }
        
        Files.createDirectories(outputDir);
        
        String content = Files.readString(inputPath);
        List<SqlChunk> chunks = splitContent(content);
        
        List<Path> outputFiles = new ArrayList<>();
        String baseFileName = inputPath.getFileName().toString().replaceAll("\\.sql$", "");
        
        for (int i = 0; i < chunks.size(); i++) {
            SqlChunk chunk = chunks.get(i);
            String chunkFileName = String.format("%s_chunk_%03d.sql", baseFileName, i + 1);
            Path chunkPath = outputDir.resolve(chunkFileName);
            
            // 写入分块内容
            StringBuilder chunkContent = new StringBuilder();
            chunkContent.append("-- ========================================\n");
            chunkContent.append("-- SQL文件分块 ").append(i + 1).append("/").append(chunks.size()).append("\n");
            chunkContent.append("-- 原始文件: ").append(inputPath.getFileName()).append("\n");
            chunkContent.append("-- 语句数量: ").append(chunk.statementCount).append("\n");
            chunkContent.append("-- 行数: ").append(chunk.lineCount).append("\n");
            chunkContent.append("-- ========================================\n\n");
            chunkContent.append(chunk.content);
            
            Files.writeString(chunkPath, chunkContent.toString(), 
                StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
            
            outputFiles.add(chunkPath);
            
            log.info("Created chunk {}/{}: {} ({} statements, {} lines, {} bytes)", 
                i + 1, chunks.size(), chunkFileName, 
                chunk.statementCount, chunk.lineCount, chunk.content.length());
        }
        
        log.info("Successfully split {} into {} chunks", inputPath.getFileName(), chunks.size());
        return outputFiles;
    }
    
    /**
     * 分割SQL内容为多个块
     */
    public List<SqlChunk> splitContent(String content) {
        List<SqlChunk> chunks = new ArrayList<>();
        
        String[] lines = content.split("\\r?\\n");
        StringBuilder currentChunk = new StringBuilder();
        StringBuilder currentStatement = new StringBuilder();
        
        int currentLines = 0;
        int currentStatements = 0;
        boolean inStatement = false;
        boolean inSingleQuote = false;
        boolean inDoubleQuote = false;
        boolean inBacktick = false;
        boolean inLineComment = false;
        boolean inBlockComment = false;
        
        for (String line : lines) {
            String trimmedLine = line.trim();
            
            // 处理空行和注释行
            if (trimmedLine.isEmpty() || trimmedLine.startsWith("--")) {
                currentChunk.append(line).append("\n");
                currentLines++;
                continue;
            }
            
            // 检查是否是SQL语句的开始 - 使用简单字符串检查避免正则表达式
            if (!inStatement && isStatementStart(trimmedLine)) {
                inStatement = true;
                currentStatement = new StringBuilder();
            }
            
            if (inStatement) {
                currentStatement.append(line).append("\n");
                
                // 检查语句是否结束（简化的分号检测）
                if (isStatementEnd(line, inSingleQuote, inDoubleQuote, inBacktick, inLineComment, inBlockComment)) {
                    // 语句结束，检查是否需要创建新的分块
                    if (shouldCreateNewChunk(currentLines, currentStatements, currentChunk.length())) {
                        // 创建当前分块
                        if (currentChunk.length() > 0) {
                            chunks.add(new SqlChunk(currentChunk.toString(), currentLines, currentStatements));
                        }
                        
                        // 开始新分块
                        currentChunk = new StringBuilder();
                        currentLines = 0;
                        currentStatements = 0;
                    }
                    
                    // 将完整语句添加到当前分块
                    currentChunk.append(currentStatement);
                    currentLines += countLines(currentStatement.toString());
                    currentStatements++;
                    
                    // 重置语句状态
                    inStatement = false;
                    currentStatement = new StringBuilder();
                }
            } else {
                // 不在语句中的行，直接添加
                currentChunk.append(line).append("\n");
                currentLines++;
            }
        }
        
        // 处理最后一个未完成的语句或分块
        if (currentStatement.length() > 0) {
            currentChunk.append(currentStatement);
            currentLines += countLines(currentStatement.toString());
            currentStatements++;
        }
        
        if (currentChunk.length() > 0) {
            chunks.add(new SqlChunk(currentChunk.toString(), currentLines, currentStatements));
        }
        
        // 如果没有分块，创建一个包含所有内容的分块
        if (chunks.isEmpty()) {
            chunks.add(new SqlChunk(content, countLines(content), 1));
        }
        
        return chunks;
    }
    
    /**
     * 检查是否是SQL语句的开始 - 使用简单字符串检查避免复杂正则表达式
     */
    private boolean isStatementStart(String trimmedLine) {
        if (trimmedLine.isEmpty()) return false;

        String upperLine = trimmedLine.toUpperCase();
        for (String keyword : getSqlStatementKeywords()) {
            if (upperLine.startsWith(keyword + " ") || upperLine.equals(keyword)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 简化的语句结束检测
     */
    private boolean isStatementEnd(String line, boolean inSingleQuote, boolean inDoubleQuote,
                                  boolean inBacktick, boolean inLineComment, boolean inBlockComment) {
        String trimmed = line.trim();
        return trimmed.endsWith(";") && !inSingleQuote && !inDoubleQuote && !inBacktick && !inLineComment && !inBlockComment;
    }
    
    /**
     * 判断是否应该创建新的分块
     */
    private boolean shouldCreateNewChunk(int currentLines, int currentStatements, int currentBytes) {
        return currentLines >= maxLinesPerChunk || 
               currentStatements >= maxStatementsPerChunk || 
               currentBytes >= maxBytesPerChunk;
    }
    
    /**
     * 计算字符串中的行数
     */
    private int countLines(String text) {
        if (text == null || text.isEmpty()) {
            return 0;
        }
        return text.split("\\r?\\n").length;
    }
    
    /**
     * SQL分块数据结构
     */
    public static class SqlChunk {
        public final String content;
        public final int lineCount;
        public final int statementCount;
        
        public SqlChunk(String content, int lineCount, int statementCount) {
            this.content = content;
            this.lineCount = lineCount;
            this.statementCount = statementCount;
        }
    }
}
