package com.xylink.sqltranspiler.infrastructure.parser;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 增强的SQL语句分割器
 * 
 * 专门处理复杂SQL语句的分割，特别是包含BEGIN...END块的触发器、存储过程等
 * 
 * 基于官方文档：
 * - MySQL: https://dev.mysql.com/doc/refman/8.4/en/create-trigger.html
 * - 达梦: https://eco.dameng.com/document/dm/zh-cn/pm/
 * - 金仓: https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
 * - 神通: shentong.md
 */
public class EnhancedSqlSplitter {
    
    private static final Logger log = LoggerFactory.getLogger(EnhancedSqlSplitter.class);
    
    // 需要特殊处理的语句类型（包含BEGIN...END块）
    private static final Pattern COMPLEX_STATEMENT_PATTERN = Pattern.compile(
        "^\\s*(CREATE\\s+(TRIGGER|PROCEDURE|FUNCTION)|DELIMITER)\\s+", 
        Pattern.CASE_INSENSITIVE | Pattern.MULTILINE
    );
    
    // DELIMITER语句模式 - 修复：确保只匹配真正的DELIMITER语句，不匹配列名
    // DELIMITER语句的格式：DELIMITER 分隔符
    // 分隔符通常是 // $$ || 等特殊字符，不会是SQL数据类型
    private static final Pattern DELIMITER_PATTERN = Pattern.compile(
        "^\\s*DELIMITER\\s+([/\\$\\|\\#\\@\\&\\*\\+\\=\\-\\~\\^\\%\\!\\?]+|;)\\s*$",
        Pattern.CASE_INSENSITIVE | Pattern.MULTILINE
    );
    
    /**
     * 智能分割SQL语句
     * 
     * @param sql 要分割的SQL文本
     * @return 分割后的语句列表
     */
    public static List<String> splitSql(String sql) {
        log.debug("Starting enhanced SQL splitting for {} characters", sql.length());
        
        // 预处理：处理DELIMITER语法
        String processedSql = preprocessDelimiterSyntax(sql);
        
        // 检查是否包含复杂语句
        if (containsComplexStatements(processedSql)) {
            log.debug("Detected complex statements, using enhanced splitting logic");
            return splitComplexSql(processedSql);
        } else {
            log.debug("No complex statements detected, using semantic splitting");
            return splitBySemanticSemicolon(processedSql);
        }
    }
    
    /**
     * 预处理DELIMITER语法
     * 
     * MySQL的DELIMITER语法用于改变语句分隔符，主要用于定义包含分号的存储过程和触发器
     * 
     * @param sql 原始SQL
     * @return 处理后的SQL
     */
    private static String preprocessDelimiterSyntax(String sql) {
        log.debug("Preprocessing DELIMITER syntax");
        
        StringBuilder result = new StringBuilder();
        String[] lines = sql.split("\\r?\\n");
        String currentDelimiter = ";";
        boolean inDelimiterBlock = false;
        StringBuilder currentStatement = new StringBuilder();
        
        for (String line : lines) {
            String trimmedLine = line.trim();
            
            // 检查DELIMITER语句
            if (DELIMITER_PATTERN.matcher(trimmedLine).matches()) {
                String newDelimiter = trimmedLine.replaceAll("(?i)^\\s*DELIMITER\\s+(\\S+)\\s*$", "$1");
                
                if (!newDelimiter.equals(";")) {
                    // 开始DELIMITER块
                    currentDelimiter = newDelimiter;
                    inDelimiterBlock = true;
                    log.debug("DELIMITER changed to: {}", currentDelimiter);
                    // 不添加DELIMITER语句到结果中
                    continue;
                } else {
                    // 结束DELIMITER块
                    if (inDelimiterBlock && currentStatement.length() > 0) {
                        // 添加累积的语句
                        String stmt = currentStatement.toString().trim();
                        if (stmt.endsWith(currentDelimiter)) {
                            stmt = stmt.substring(0, stmt.length() - currentDelimiter.length()).trim();
                        }
                        if (!stmt.isEmpty()) {
                            result.append(stmt).append(";\n");
                        }
                        currentStatement = new StringBuilder();
                    }
                    currentDelimiter = ";";
                    inDelimiterBlock = false;
                    log.debug("DELIMITER reset to semicolon");
                    // 不添加DELIMITER语句到结果中
                    continue;
                }
            }
            
            if (inDelimiterBlock) {
                // 在DELIMITER块中，累积语句直到遇到自定义分隔符
                currentStatement.append(line).append("\n");
                
                if (trimmedLine.endsWith(currentDelimiter)) {
                    // 找到语句结束
                    String stmt = currentStatement.toString().trim();
                    if (stmt.endsWith(currentDelimiter)) {
                        stmt = stmt.substring(0, stmt.length() - currentDelimiter.length()).trim();
                    }
                    if (!stmt.isEmpty()) {
                        result.append(stmt).append(";\n");
                    }
                    currentStatement = new StringBuilder();
                }
            } else {
                // 正常处理
                result.append(line).append("\n");
            }
        }
        
        // 处理最后的语句
        if (currentStatement.length() > 0) {
            String stmt = currentStatement.toString().trim();
            if (stmt.endsWith(currentDelimiter)) {
                stmt = stmt.substring(0, stmt.length() - currentDelimiter.length()).trim();
            }
            if (!stmt.isEmpty()) {
                result.append(stmt).append(";\n");
            }
        }
        
        String processed = result.toString();
        log.debug("DELIMITER preprocessing completed, {} -> {} characters", sql.length(), processed.length());
        return processed;
    }
    
    /**
     * 检查是否包含复杂语句
     */
    private static boolean containsComplexStatements(String sql) {
        return COMPLEX_STATEMENT_PATTERN.matcher(sql).find();
    }
    
    /**
     * 分割包含复杂语句的SQL
     */
    private static List<String> splitComplexSql(String sql) {
        List<String> statements = new ArrayList<>();
        StringBuilder currentStatement = new StringBuilder();
        
        boolean inSingleQuote = false;
        boolean inDoubleQuote = false;
        boolean inBacktick = false;
        boolean inLineComment = false;
        boolean inBlockComment = false;
        int beginEndDepth = 0; // 跟踪BEGIN...END的嵌套深度
        
        char[] chars = sql.toCharArray();
        
        for (int i = 0; i < chars.length; i++) {
            char c = chars[i];
            char next = (i + 1 < chars.length) ? chars[i + 1] : '\0';
            
            // 处理行注释
            if (!inSingleQuote && !inDoubleQuote && !inBacktick && !inBlockComment) {
                if (c == '-' && next == '-') {
                    inLineComment = true;
                    currentStatement.append(c);
                    continue;
                }
            }
            
            if (inLineComment) {
                currentStatement.append(c);
                if (c == '\n' || c == '\r') {
                    inLineComment = false;
                }
                continue;
            }
            
            // 处理块注释
            if (!inSingleQuote && !inDoubleQuote && !inBacktick && !inLineComment) {
                if (c == '/' && next == '*') {
                    inBlockComment = true;
                    currentStatement.append(c);
                    continue;
                }
            }
            
            if (inBlockComment) {
                currentStatement.append(c);
                if (c == '*' && next == '/') {
                    currentStatement.append(next);
                    i++; // 跳过下一个字符
                    inBlockComment = false;
                }
                continue;
            }
            
            // 处理字符串
            if (!inLineComment && !inBlockComment) {
                if (c == '\'' && !inDoubleQuote && !inBacktick) {
                    inSingleQuote = !inSingleQuote;
                } else if (c == '"' && !inSingleQuote && !inBacktick) {
                    inDoubleQuote = !inDoubleQuote;
                } else if (c == '`' && !inSingleQuote && !inDoubleQuote) {
                    inBacktick = !inBacktick;
                }
            }
            
            // 处理BEGIN...END块（只在非字符串、非注释中）
            if (!inSingleQuote && !inDoubleQuote && !inBacktick && !inLineComment && !inBlockComment) {
                // 检查BEGIN关键字
                if (isWordBoundary(chars, i, "BEGIN")) {
                    beginEndDepth++;
                    log.debug("Found BEGIN, depth now: {}", beginEndDepth);
                }
                // 检查END关键字，但排除复合关键字如END IF, END WHILE, END LOOP等
                else if (isWordBoundary(chars, i, "END") && !isCompoundEndKeyword(chars, i)) {
                    beginEndDepth--;
                    log.debug("Found END, depth now: {}", beginEndDepth);
                }
            }
            
            // 处理分号
            if (c == ';' && !inSingleQuote && !inDoubleQuote && !inBacktick && !inLineComment && !inBlockComment) {
                currentStatement.append(c);
                
                // 只有在BEGIN...END深度为0时才认为语句结束
                if (beginEndDepth <= 0) {
                    String stmt = currentStatement.toString().trim();
                    if (!stmt.isEmpty()) {
                        statements.add(stmt);
                        log.debug("Added statement: {} characters", stmt.length());
                    }
                    currentStatement = new StringBuilder();
                    beginEndDepth = 0; // 重置深度
                } else {
                    log.debug("Semicolon found but still inside BEGIN...END block (depth: {})", beginEndDepth);
                }
                continue;
            }
            
            currentStatement.append(c);
        }
        
        // 添加最后一个语句
        String lastStmt = currentStatement.toString().trim();
        if (!lastStmt.isEmpty()) {
            statements.add(lastStmt);
            log.debug("Added final statement: {} characters", lastStmt.length());
        }
        
        log.debug("Enhanced SQL splitting completed: {} statements", statements.size());
        return statements;
    }
    
    /**
     * 检查指定位置是否是某个关键字的词边界匹配
     */
    private static boolean isWordBoundary(char[] chars, int pos, String keyword) {
        if (pos + keyword.length() > chars.length) {
            return false;
        }

        // 检查关键字匹配
        String found = new String(chars, pos, keyword.length());
        if (!found.equalsIgnoreCase(keyword)) {
            return false;
        }

        // 检查前边界
        if (pos > 0) {
            char prevChar = chars[pos - 1];
            if (Character.isLetterOrDigit(prevChar) || prevChar == '_') {
                return false;
            }
        }

        // 检查后边界
        int endPos = pos + keyword.length();
        if (endPos < chars.length) {
            char nextChar = chars[endPos];
            if (Character.isLetterOrDigit(nextChar) || nextChar == '_') {
                return false;
            }
        }

        return true;
    }

    /**
     * 检查END关键字后面是否跟着其他关键字，形成复合关键字
     * 如：END IF, END WHILE, END LOOP, END CASE等
     */
    private static boolean isCompoundEndKeyword(char[] chars, int endPos) {
        // 跳过END关键字
        int pos = endPos + 3; // "END".length() = 3

        // 跳过空白字符
        while (pos < chars.length && Character.isWhitespace(chars[pos])) {
            pos++;
        }

        if (pos >= chars.length) {
            return false;
        }

        // 检查常见的复合END关键字
        String[] compoundKeywords = {"IF", "WHILE", "LOOP", "CASE", "REPEAT"};

        for (String keyword : compoundKeywords) {
            if (isWordBoundary(chars, pos, keyword)) {
                return true;
            }
        }

        return false;
    }
    
    /**
     * 语义分号分割（用于简单语句）
     */
    private static List<String> splitBySemanticSemicolon(String sql) {
        List<String> statements = new ArrayList<>();
        StringBuilder currentStatement = new StringBuilder();
        
        boolean inSingleQuote = false;
        boolean inDoubleQuote = false;
        boolean inBacktick = false;
        boolean inLineComment = false;
        boolean inBlockComment = false;
        
        char[] chars = sql.toCharArray();
        
        for (int i = 0; i < chars.length; i++) {
            char c = chars[i];
            char next = (i + 1 < chars.length) ? chars[i + 1] : '\0';
            
            // 处理行注释
            if (!inSingleQuote && !inDoubleQuote && !inBacktick && !inBlockComment) {
                if (c == '-' && next == '-') {
                    inLineComment = true;
                    currentStatement.append(c);
                    continue;
                }
            }
            
            if (inLineComment) {
                currentStatement.append(c);
                if (c == '\n' || c == '\r') {
                    inLineComment = false;
                }
                continue;
            }
            
            // 处理块注释
            if (!inSingleQuote && !inDoubleQuote && !inBacktick && !inLineComment) {
                if (c == '/' && next == '*') {
                    inBlockComment = true;
                    currentStatement.append(c);
                    continue;
                }
            }
            
            if (inBlockComment) {
                currentStatement.append(c);
                if (c == '*' && next == '/') {
                    currentStatement.append(next);
                    i++; // 跳过下一个字符
                    inBlockComment = false;
                }
                continue;
            }
            
            // 处理字符串
            if (!inLineComment && !inBlockComment) {
                if (c == '\'' && !inDoubleQuote && !inBacktick) {
                    inSingleQuote = !inSingleQuote;
                } else if (c == '"' && !inSingleQuote && !inBacktick) {
                    inDoubleQuote = !inDoubleQuote;
                } else if (c == '`' && !inSingleQuote && !inDoubleQuote) {
                    inBacktick = !inBacktick;
                }
            }
            
            // 处理分号
            if (c == ';' && !inSingleQuote && !inDoubleQuote && !inBacktick && !inLineComment && !inBlockComment) {
                currentStatement.append(c);
                String stmt = currentStatement.toString().trim();
                if (!stmt.isEmpty()) {
                    statements.add(stmt);
                }
                currentStatement = new StringBuilder();
                continue;
            }
            
            currentStatement.append(c);
        }
        
        // 添加最后一个语句
        String lastStmt = currentStatement.toString().trim();
        if (!lastStmt.isEmpty()) {
            statements.add(lastStmt);
        }
        
        return statements;
    }
}
