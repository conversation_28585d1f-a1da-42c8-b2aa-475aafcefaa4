package com.xylink.sqltranspiler.infrastructure.parser.error;

import org.antlr.v4.runtime.BaseErrorListener;
import org.antlr.v4.runtime.RecognitionException;
import org.antlr.v4.runtime.Recognizer;

import com.xylink.sqltranspiler.infrastructure.parser.antlr.Origin;
import com.xylink.sqltranspiler.infrastructure.parser.antlr.ParseException;

import lombok.extern.slf4j.Slf4j;

/**
 * 增强的解析错误监听器
 * 提供更友好的错误信息和修复建议
 */
@Slf4j
public class EnhancedParseErrorListener extends BaseErrorListener {
    
    private final String originalSql;
    
    public EnhancedParseErrorListener(String originalSql) {
        this.originalSql = originalSql;
    }
    
    @Override
    public void syntaxError(
            Recognizer<?, ?> recognizer,
            Object offendingSymbol,
            int line,
            int charPositionInLine,
            String msg,
            RecognitionException e) {
        
        log.debug("Syntax error detected at line {}, position {}: {}", line, charPositionInLine, msg);
        
        // 使用增强的错误分析器分析错误
        SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(
            originalSql, msg, line, charPositionInLine);

        // 检查是否是阻塞性错误
        if (isBlockingError(errorInfo.getErrorType())) {
            // 创建增强的ParseException
            Origin position = new Origin(line, charPositionInLine);
            EnhancedParseException enhancedException = new EnhancedParseException(
                errorInfo, position, position);

            // 设置原始SQL
            enhancedException.setOriginalSql(originalSql);

            log.error("Enhanced SQL syntax error: {}", errorInfo.getShortMessage());
            log.debug("Full error details: {}", errorInfo.getFormattedMessage());

            throw enhancedException;
        } else {
            // 对于非阻塞性错误（如兼容性警告），只记录日志，不抛出异常
            log.warn("SQL compatibility warning: {}", errorInfo.getShortMessage());
            log.debug("Warning details: {}", errorInfo.getFormattedMessage());
        }
    }

    /**
     * 判断是否是阻塞性错误
     * 兼容性警告和数据类型兼容性问题不应该阻塞解析过程
     */
    private boolean isBlockingError(SqlErrorType errorType) {
        // 非阻塞性错误类型（警告）
        switch (errorType) {
            case DATA_TYPE_COMPATIBILITY:
            case FUNCTION_COMPATIBILITY:
            case MYSQL_SPECIFIC_SYNTAX:
            case SYNTAX_COMPATIBILITY:
            case CHARSET_COMPATIBILITY:
            case STORAGE_ENGINE_COMPATIBILITY:
            case MYSQL_84_DEPRECATED_FEATURE:
                return false; // 这些是警告，不阻塞解析
            default:
                return true; // 其他都是阻塞性错误
        }
    }

    /**
     * 增强的解析异常
     * 包含详细的错误分析信息
     */
    public static class EnhancedParseException extends ParseException {
        
        private final SqlErrorInfo errorInfo;
        private String originalSql;
        
        public EnhancedParseException(SqlErrorInfo errorInfo, Origin start, Origin stop) {
            super(errorInfo.getFriendlyMessage() != null ? 
                  errorInfo.getFriendlyMessage() : errorInfo.getOriginalError(), start, stop);
            this.errorInfo = errorInfo;
        }
        
        public SqlErrorInfo getErrorInfo() {
            return errorInfo;
        }
        
        public void setOriginalSql(String originalSql) {
            this.originalSql = originalSql;
        }
        
        @Override
        public String getMessage() {
            StringBuilder builder = new StringBuilder();
            
            // 添加友好的错误信息
            builder.append("\n").append("【SQL语法错误】").append("\n");
            builder.append("错误类型: ").append(errorInfo.getErrorType().getDescription()).append("\n");
            builder.append("位置: 第").append(errorInfo.getLine()).append("行第").append(errorInfo.getPosition()).append("列").append("\n");
            
            if (errorInfo.getFriendlyMessage() != null) {
                builder.append("错误: ").append(errorInfo.getFriendlyMessage()).append("\n");
            }
            
            if (errorInfo.getDetailedMessage() != null) {
                builder.append("详情: ").append(errorInfo.getDetailedMessage()).append("\n");
            }
            
            if (errorInfo.getSuggestion() != null) {
                builder.append("建议: ").append(errorInfo.getSuggestion()).append("\n");
            }
            
            // 显示错误位置
            if (originalSql != null && getStart() != null) {
                builder.append("\n== 错误的SQL ==\n");
                String[] lines = originalSql.split("\n");
                int errorLine = getStart().getLine();
                int errorPos = getStart().getStartPosition();
                
                // 显示错误行及其上下文
                int startLine = Math.max(1, errorLine - 1);
                int endLine = Math.min(lines.length, errorLine + 1);
                
                for (int i = startLine; i <= endLine; i++) {
                    if (i <= lines.length) {
                        String lineContent = lines[i - 1];
                        builder.append(String.format("%3d: %s\n", i, lineContent));
                        
                        // 在错误行下方显示指示符
                        if (i == errorLine) {
                            builder.append("     ");
                            for (int j = 0; j < errorPos; j++) {
                                builder.append("-");
                            }
                            builder.append("^^^ 错误位置\n");
                        }
                    }
                }
            }
            
            // 如果有修复建议，显示修复后的SQL
            if (errorInfo.isAutoFixable() && errorInfo.getSuggestedFix() != null) {
                builder.append("\n== 建议的修复 ==\n");
                builder.append(errorInfo.getSuggestedFix()).append("\n");
            }
            
            return builder.toString();
        }
        
        /**
         * 获取简短的错误信息（用于日志）
         */
        public String getShortMessage() {
            return errorInfo.getShortMessage();
        }
        
        /**
         * 检查是否可以自动修复
         */
        public boolean isAutoFixable() {
            return errorInfo.isAutoFixable();
        }
        
        /**
         * 获取修复后的SQL
         */
        public String getSuggestedFix() {
            return errorInfo.getSuggestedFix();
        }
    }
}
