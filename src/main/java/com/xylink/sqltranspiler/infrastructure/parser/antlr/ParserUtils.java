package com.xylink.sqltranspiler.infrastructure.parser.antlr;

import org.antlr.v4.runtime.CharStream;
import org.antlr.v4.runtime.ParserRuleContext;
import org.antlr.v4.runtime.Token;
import org.antlr.v4.runtime.misc.Interval;
import org.antlr.v4.runtime.tree.TerminalNode;

public class ParserUtils {

    /** Get the code that creates the given node. */
    public static String source(ParserRuleContext ctx) {
        CharStream stream = ctx.getStart().getInputStream();
        return stream.getText(
                Interval.of(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex()));
    }

    public static String getString(Token token) {
        return unescapeSQLString(token.getText());
    }

    public static String getString(TerminalNode token) {
        return unescapeSQLString(token.getText());
    }

    public static int getInt(Token token) {
        return Integer.parseInt(token.getText());
    }

    public static String command(ParserRuleContext ctx) {
        CharStream stream = ctx.getStart().getInputStream();
        return stream.getText(Interval.of(0, stream.size()));
    }

    public static Origin position(Token token) {
        return new Origin(token.getLine(), token.getCharPositionInLine());
    }

    private static void appendEscapedChar(StringBuilder sb, char n) {
        switch (n) {
            case '0':
                sb.append("\u0000");
            case '\'':
                sb.append("\'");
            case '"':
                sb.append("\"");
            case 'b':
                sb.append("\b");
            case 'n':
                sb.append("\n");
            case 'r':
                sb.append("\r");
            case 't':
                sb.append("\t");
            case 'Z':
                sb.append("\u001A");
            case '\\':
                sb.append("\\");
                // MySQL官方文档：https://dev.mysql.com/doc/refman/8.4/en/string-literals.html
                // "The \% and \_ sequences are used to search for literal instances of % and _
                // in pattern-matching contexts where they would otherwise be interpreted as wildcard characters"
                // MySQL官方文档：https://dev.mysql.com/doc/refman/8.4/en/string-comparison-functions.html#operator_like
                // "% matches any number of characters, even zero characters"
                // "_ matches exactly one character"
                // "To test for literal instances of a wildcard character, precede it by the escape character"
                //
                // 原因：在LIKE模式匹配中，%和_是通配符，需要转义才能匹配字面值
                // - % 匹配任意数量的字符（包括零个字符）
                // - _ 匹配恰好一个字符
                // - \% 匹配字面值 % 字符
                // - \_ 匹配字面值 _ 字符
                // 这确保了字符串字面值中的%和_被正确转义，避免在LIKE操作中被误解为通配符
            case '%':
                sb.append("\\%");
            case '_':
                sb.append("\\_");
            default:
                sb.append(n);
        }
    }

    private static String unescapeSQLString(String b) {
        Character enclosure = null;
        StringBuilder sb = new StringBuilder(b.length());

        int i = 0;
        int strLength = b.length();
        while (i < strLength) {
            char currentChar = b.charAt(i);
            if (enclosure == null) {
                if (currentChar == '\'' || currentChar == '\"') {
                    enclosure = currentChar;
                }
            } else if (enclosure == currentChar) {
                enclosure = null;
            } else if (currentChar == '\\') {

                if ((i + 6 < strLength) && b.charAt(i + 1) == 'u') {
                    // \u0000 style character literals.

                    int code = 0;
                    int base = i + 2;
                    for (int h = 0; h < 4; h++) {
                        int digit = Character.digit(b.charAt(h + base), 16);
                        code = (code << 4) + digit;
                    }
                    sb.append((char) code);
                    i += 5;
                } else if (i + 4 < strLength) {
                    // \000 style character literals.

                    char i1 = b.charAt(i + 1);
                    char i2 = b.charAt(i + 2);
                    char i3 = b.charAt(i + 3);

                    if ((i1 >= '0' && i1 <= '1') && (i2 >= '0' && i2 <= '7') && (i3 >= '0' && i3 <= '7')) {
                        char tmp = (char) ((i3 - '0') + ((i2 - '0') << 3) + ((i1 - '0') << 6));
                        sb.append(tmp);
                        i += 3;
                    } else {
                        appendEscapedChar(sb, i1);
                        i += 1;
                    }
                } else if (i + 2 < strLength) {
                    // escaped character literals.
                    char n = b.charAt(i + 1);
                    appendEscapedChar(sb, n);
                    i += 1;
                }
            } else {
                // non-escaped character literals.
                sb.append(currentChar);
            }
            i += 1;
        }
        return sb.toString();
    }
}
