package com.xylink.sqltranspiler.infrastructure.parser.error;

/**
 * 错误严重程度枚举
 */
public enum ErrorSeverity {
    
    /**
     * 信息级别 - 仅供参考
     */
    INFO("信息"),
    
    /**
     * 警告级别 - 可能有问题但不影响执行
     */
    WARNING("警告"),
    
    /**
     * 错误级别 - 阻止执行
     */
    ERROR("错误"),
    
    /**
     * 致命错误 - 严重问题
     */
    FATAL("致命错误");
    
    private final String description;
    
    ErrorSeverity(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
}
