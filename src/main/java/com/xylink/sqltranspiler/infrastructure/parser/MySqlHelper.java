package com.xylink.sqltranspiler.infrastructure.parser;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

import org.antlr.v4.runtime.CharStreams;
import org.antlr.v4.runtime.CommonTokenStream;
import org.antlr.v4.runtime.atn.PredictionMode;
import org.antlr.v4.runtime.misc.ParseCancellationException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.common.exception.SQLParserException;
import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.TableId;
import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import com.xylink.sqltranspiler.core.ast.table.ColumnRel;
import com.xylink.sqltranspiler.core.ast.table.ColumnDefType;
import com.xylink.sqltranspiler.core.ast.dml.DeleteTable;
import com.xylink.sqltranspiler.core.ast.dml.InsertTable;
import com.xylink.sqltranspiler.core.ast.dml.QueryStmt;
import com.xylink.sqltranspiler.core.ast.dml.UpdateTable;
import com.xylink.sqltranspiler.core.model.enums.TableType;
import com.xylink.sqltranspiler.core.validation.StrictSqlValidator;
import com.xylink.sqltranspiler.core.validation.StrictValidationResult;
import com.xylink.sqltranspiler.infrastructure.parser.antlr.ParseException;
import com.xylink.sqltranspiler.infrastructure.parser.error.EnhancedErrorAnalyzer;
import com.xylink.sqltranspiler.infrastructure.parser.error.EnhancedParseErrorListener;
import com.xylink.sqltranspiler.infrastructure.parser.generated.MySqlLexer;
import com.xylink.sqltranspiler.infrastructure.parser.generated.MySqlParser;
import com.xylink.sqltranspiler.infrastructure.parser.split.MySqlParserCoordinator;
import com.xylink.sqltranspiler.infrastructure.parser.split.MySqlParserFactory;
import com.xylink.sqltranspiler.infrastructure.util.CommonUtils;

public class MySqlHelper {

    private static final Logger log = LoggerFactory.getLogger(MySqlHelper.class);

    public static List<String> sqlKeywords() {
        HashSet<String> keywords = new HashSet<>();
        for (int idx = 0; idx < MySqlLexer.VOCABULARY.getMaxTokenType(); idx++) {
            String name = MySqlLexer.VOCABULARY.getLiteralName(idx);
            if (name != null) {
                Matcher matcher = CommonUtils.KEYWORD_REGEX.matcher(name);
                if (matcher.find()) {
                    keywords.add(matcher.group(1));
                }
            }
        }

        return keywords.stream().sorted().collect(Collectors.toList());
    }

    public static Statement parseStatement(String command) {
        List<Statement> statements = parseMultiStatement(command);
        if (statements.size() != 1) {
            // 提供更详细的错误信息，帮助调试
            String errorMsg = String.format("Expected exactly 1 SQL statement, but found %d statements. " +
                "This may be caused by parsing errors or unsupported SQL syntax. " +
                "Original SQL: %s", statements.size(), command.length() > 200 ?
                command.substring(0, 200) + "..." : command);
            log.error("PARSE_ERROR: {}", errorMsg);

            // 如果没有解析到任何语句，尝试创建一个合适的fallback语句
            if (statements.isEmpty()) {
                log.warn("PARSE_FALLBACK: Creating fallback statement for unparseable SQL");
                Statement fallbackStmt = createFallbackStatement(command);
                return fallbackStmt;
            }

            throw new IllegalStateException(errorMsg);
        } else {
            return statements.get(0);
        }
    }

    public static List<Statement> parseMultiStatement(String command) {
        String trimCmd = StringUtils.trim(command);

        MySqlAntlr4Visitor sqlVisitor = new MySqlAntlr4Visitor(false);
        sqlVisitor.setOriginalSql(trimCmd);  // 设置原始SQL
        innerParseStatement(trimCmd, sqlVisitor);
        List<Statement> statements = sqlVisitor.getSqlStatements();

        // 使用统一的严格验证器进行保留字验证
        if (!statements.isEmpty()) {
            StrictSqlValidator validator = new StrictSqlValidator();
            StrictValidationResult validationResult = validator.validate(trimCmd, "mysql", "mysql");

            if (validationResult.hasWarnings()) {
                log.warn("检测到 {} 个保留字使用问题:", validationResult.getWarnings().size());
                for (StrictValidationResult.ValidationIssue warning : validationResult.getWarnings()) {
                    log.warn("  - {}", warning.getMessage());
                }
            }

            if (validationResult.hasErrors()) {
                log.error("检测到 {} 个严重错误:", validationResult.getErrors().size());
                for (StrictValidationResult.ValidationIssue error : validationResult.getErrors()) {
                    log.error("  - {}", error.getMessage());
                }
            }
        }

        return statements;
    }

    /**
     * 为无法解析的SQL创建合适的fallback语句
     * 根据SQL的开头关键字判断语句类型，创建相应的Statement对象
     */
    private static Statement createFallbackStatement(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            QueryStmt fallbackStmt = new QueryStmt(new ArrayList<>());
            fallbackStmt.setSql(sql);
            return fallbackStmt;
        }

        String trimmedSql = sql.trim().toUpperCase();

        if (trimmedSql.startsWith("CREATE TABLE")) {
            // 创建一个基本的CreateTable语句
            log.info("PARSE_FALLBACK: Detected CREATE TABLE statement, creating fallback CreateTable");

            // 尝试提取表名（简单的正则匹配）
            String tableName = "unknown_table";
            try {
                java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(
                    "CREATE\\s+TABLE\\s+(?:IF\\s+NOT\\s+EXISTS\\s+)?(?:`([^`]+)`|([a-zA-Z_][a-zA-Z0-9_]*))",
                    java.util.regex.Pattern.CASE_INSENSITIVE
                );
                java.util.regex.Matcher matcher = pattern.matcher(sql);
                if (matcher.find()) {
                    tableName = matcher.group(1) != null ? matcher.group(1) : matcher.group(2);
                    log.debug("PARSE_FALLBACK: Extracted table name: {}", tableName);
                }
            } catch (Exception e) {
                log.warn("PARSE_FALLBACK: Failed to extract table name from SQL: {}", e.getMessage());
            }

            // 提取列定义
            List<ColumnRel> columnRels = extractColumnDefinitionsFromCreateTable(sql);
            log.info("PARSE_FALLBACK: Extracted {} column definitions from CREATE TABLE", columnRels.size());

            // 提取分区定义
            String partitionDefinition = extractPartitionDefinitionFromCreateTable(sql);
            if (partitionDefinition != null && !partitionDefinition.trim().isEmpty()) {
                log.info("PARSE_FALLBACK: Extracted partition definition: {}", partitionDefinition);
            }

            // 使用正确的构造函数创建CreateTable
            TableId tableId = new TableId(tableName);
            CreateTable createTable = new CreateTable(tableId, TableType.MYSQL, null, columnRels, false, null);

            // 设置原始SQL，包含分区定义
            createTable.setSql(sql);

            // 如果有分区定义，添加到CreateTable对象中
            if (partitionDefinition != null && !partitionDefinition.trim().isEmpty()) {
                createTable.setPartitionDefinition(partitionDefinition);
            }

            return createTable;
        } else if (trimmedSql.startsWith("INSERT")) {
            log.info("PARSE_FALLBACK: Detected INSERT statement, creating fallback InsertTable");
            TableId tableId = new TableId("unknown_table");
            InsertTable insertTable = new InsertTable(tableId, new QueryStmt(new ArrayList<>()));
            insertTable.setSql(sql);
            return insertTable;
        } else if (trimmedSql.startsWith("UPDATE")) {
            log.info("PARSE_FALLBACK: Detected UPDATE statement, creating fallback UpdateTable");
            TableId tableId = new TableId("unknown_table");
            UpdateTable updateTable = new UpdateTable(tableId, new ArrayList<>());
            updateTable.setSql(sql);
            return updateTable;
        } else if (trimmedSql.startsWith("DELETE")) {
            log.info("PARSE_FALLBACK: Detected DELETE statement, creating fallback DeleteTable");
            TableId tableId = new TableId("unknown_table");
            DeleteTable deleteTable = new DeleteTable(tableId);
            deleteTable.setSql(sql);
            return deleteTable;
        } else {
            // 默认创建QueryStmt
            log.info("PARSE_FALLBACK: Creating default QueryStmt for unrecognized SQL type");
            QueryStmt fallbackStmt = new QueryStmt(new ArrayList<>());
            fallbackStmt.setSql(sql);
            return fallbackStmt;
        }
    }

    public static List<String> splitSql(String command) {
        String trimCmd = StringUtils.trim(command);
        MySqlAntlr4Visitor sqlVisitor = new MySqlAntlr4Visitor(true);
        innerParseStatement(trimCmd, sqlVisitor);
        return sqlVisitor.getSplitSqls();
    }

    public static void checkSqlSyntax(String command) {
        try {
            // 使用标准的ANTLR解析器进行语法检查，支持大小写不敏感的关键字
            MySqlLexer lexer = new MySqlLexer(CharStreams.fromString(command));
            CommonTokenStream tokens = new CommonTokenStream(lexer);
            MySqlParser parser = new MySqlParser(tokens);
            parser.sqlStatements(); // 尝试解析
        } catch (Exception e) {
            throw new SQLParserException("SQL syntax error: " + e.getMessage());
        }
    }

    /**
     * Parse statements using the new modular parser system
     * This provides better performance and maintainability for large files
     */
    public static List<Statement> parseMultiStatementModular(String command) {
        String trimCmd = StringUtils.trim(command);
        try {
            // Use the new modular parser factory
            MySqlParserCoordinator parser = MySqlParserFactory.createParser(trimCmd);
            MySqlAntlr4Visitor sqlVisitor = new MySqlAntlr4Visitor(false);

            // Parse using the modular system
            sqlVisitor.visit(parser.sqlStatements());
            return sqlVisitor.getSqlStatements();

        } catch (Exception e) {
            // Fallback to original parser if modular parser fails
            return parseMultiStatement(command);
        }
    }

    /**
     * Check if SQL is valid using the modular parser
     */
    public static boolean isValidSqlModular(String command) {
        return MySqlParserFactory.isValidSQL(command);
    }

    /**
     * Get syntax errors using the modular parser
     */
    public static List<String> getSyntaxErrorsModular(String command) {
        return MySqlParserFactory.getSyntaxErrors(command);
    }

    private static void innerParseStatement(String command, MySqlAntlr4Visitor sqlVisitor) {
        try {
            // 检查是否有已知的语法问题，如果有则尝试自动修复
            String processedCommand = command;
            if (EnhancedErrorAnalyzer.hasKnownSyntaxIssues(command)) {
                String fixedCommand = EnhancedErrorAnalyzer.autoFixKnownIssues(command);
                log.warn("检测到已知的语法问题，已自动修复:");
                log.warn("原始SQL: {}", command.trim());
                log.warn("修复后SQL: {}", fixedCommand.trim());
                processedCommand = fixedCommand;
            }

            // 使用标准的ANTLR解析器而不是有问题的split解析器
            // 根据MySQL官方标准，关键字是大小写不敏感的，使用caseInsensitive选项
            MySqlLexer lexer = new MySqlLexer(CharStreams.fromString(processedCommand));
            lexer.removeErrorListeners();
            lexer.addErrorListener(new EnhancedParseErrorListener(processedCommand));

            CommonTokenStream tokenStream = new CommonTokenStream(lexer);
            MySqlParser parser = new MySqlParser(tokenStream);
            parser.removeErrorListeners();
            parser.addErrorListener(new EnhancedParseErrorListener(processedCommand));

            try {
                try {
                    // first, try parsing with potentially faster SLL mode
                    sqlVisitor.visit(parser.sqlStatements());
                } catch (ParseCancellationException e) {
                    tokenStream.seek(0); // rewind input stream
                    parser.reset();

                    // Try Again.
                    parser.getInterpreter().setPredictionMode(PredictionMode.LL);
                    sqlVisitor.visit(parser.sqlStatements());
                }
            } catch (ParseException e) {
                // 检查是否是增强的解析异常
                if (e instanceof EnhancedParseErrorListener.EnhancedParseException) {
                    EnhancedParseErrorListener.EnhancedParseException enhancedException =
                        (EnhancedParseErrorListener.EnhancedParseException) e;

                    log.error("ENHANCED_PARSE_FAILED: {}", enhancedException.getShortMessage());
                    log.debug("ENHANCED_PARSE_DETAILS: {}", enhancedException.getMessage());

                    // 如果可以自动修复，记录修复建议
                    if (enhancedException.isAutoFixable()) {
                        log.warn("PARSE_AUTO_FIX_AVAILABLE: 此错误可以自动修复");
                        if (enhancedException.getSuggestedFix() != null) {
                            log.warn("PARSE_SUGGESTED_FIX: {}", enhancedException.getSuggestedFix());
                        }
                    }

                    throw enhancedException;
                } else {
                    log.error("PARSE_FAILED: Failed to parse SQL statement. Error: {}", e.getMessage());
                    log.error("PARSE_FAILED: Original SQL: {}", command);
                    if (StringUtils.isNotBlank(e.getCommand())) {
                        throw e;
                    } else {
                        throw e.withCommand(command);
                    }
                }
            }
        } catch (Exception e) {
            if (e instanceof ParseException) {
                ParseException pe = (ParseException) e;
                log.error("PARSE_EXCEPTION: ParseException occurred. Error: {}", pe.getMessage());
                log.error("PARSE_EXCEPTION: Original SQL: {}", command);
                if (StringUtils.isNotBlank(pe.getCommand())) {
                    throw pe;
                } else {
                    throw pe.withCommand(command);
                }
            }
            log.error("PARSE_ERROR: Unexpected error during SQL parsing. Error: {}", e.getMessage());
            log.error("PARSE_ERROR: Original SQL: {}", command);
            log.error("PARSE_ERROR: Exception type: {}", e.getClass().getSimpleName());
            throw new SQLParserException(e.getMessage(), e);
        }
    }

    /**
     * 从CREATE TABLE SQL语句中提取列定义
     *
     * 根据MySQL 8.4官方文档 https://dev.mysql.com/doc/refman/8.4/en/create-table.html：
     * CREATE TABLE语法支持分区定义：
     * CREATE [TEMPORARY] TABLE [IF NOT EXISTS] tbl_name
     *     (create_definition,...)
     *     [table_options]
     *     [partition_options]
     *
     * 其中partition_options包括：
     * PARTITION BY
     *     { [LINEAR] HASH(expr)
     *     | [LINEAR] KEY [ALGORITHM={1|2}] (column_list)
     *     | RANGE{(expr) | COLUMNS(column_list)}
     *     | LIST{(expr) | COLUMNS(column_list)} }
     *     [PARTITIONS num]
     *     [SUBPARTITION BY
     *         { [LINEAR] HASH(expr)
     *         | [LINEAR] KEY [ALGORITHM={1|2}] (column_list) }
     *         [SUBPARTITIONS num]]
     *     [(partition_definition [, partition_definition] ...)]
     *
     * 此方法用于fallback模式，当ANTLR解析器无法解析包含分区定义的CREATE TABLE语句时，
     * 从原始SQL中提取基本的列定义信息。
     *
     * @param sql CREATE TABLE SQL语句
     * @return 列定义列表
     */
    private static List<ColumnRel> extractColumnDefinitionsFromCreateTable(String sql) {
        List<ColumnRel> columnRels = new ArrayList<>();

        try {
            // 使用正则表达式提取列定义部分
            // 需要处理分区表的情况，分区定义在表定义之后
            // 匹配 CREATE TABLE table_name ( ... ) [PARTITION BY ...] 中的列定义部分
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(
                "CREATE\\s+TABLE\\s+(?:IF\\s+NOT\\s+EXISTS\\s+)?(?:`[^`]+`|[a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(([^)]+(?:\\([^)]*\\)[^)]*)*)\\)(?:\\s*PARTITION\\s+BY|\\s*ENGINE|\\s*DEFAULT|\\s*;|\\s*$)",
                java.util.regex.Pattern.CASE_INSENSITIVE | java.util.regex.Pattern.DOTALL
            );

            java.util.regex.Matcher matcher = pattern.matcher(sql);
            if (matcher.find()) {
                String columnDefinitions = matcher.group(1);
                log.debug("PARSE_FALLBACK: Extracted column definitions: {}", columnDefinitions);

                // 解析列定义
                columnRels = parseColumnDefinitions(columnDefinitions);
            } else {
                log.warn("PARSE_FALLBACK: Could not extract column definitions from SQL");
                // 尝试更简单的匹配
                pattern = java.util.regex.Pattern.compile(
                    "\\(([^)]+)\\)",
                    java.util.regex.Pattern.CASE_INSENSITIVE | java.util.regex.Pattern.DOTALL
                );
                matcher = pattern.matcher(sql);
                if (matcher.find()) {
                    String columnDefinitions = matcher.group(1);
                    log.debug("PARSE_FALLBACK: Fallback extracted column definitions: {}", columnDefinitions);
                    columnRels = parseColumnDefinitions(columnDefinitions);
                }
            }
        } catch (Exception e) {
            log.warn("PARSE_FALLBACK: Failed to extract column definitions: {}", e.getMessage());
        }

        return columnRels;
    }

    /**
     * 解析列定义字符串
     *
     * 根据MySQL 8.4官方文档 https://dev.mysql.com/doc/refman/8.4/en/create-table.html，
     * 列定义的完整语法为：
     *
     * col_name column_definition
     *
     * column_definition: {
     *     data_type [NOT NULL | NULL] [DEFAULT {literal | (expr)} ]
     *       [VISIBLE | INVISIBLE]
     *       [AUTO_INCREMENT] [UNIQUE [KEY]] [PRIMARY KEY]
     *       [COMMENT 'string']
     *       [COLLATE collation_name]
     *       [COLUMN_FORMAT {FIXED | DYNAMIC | DEFAULT}]
     *       [ENGINE_ATTRIBUTE [=] 'string']
     *       [SECONDARY_ENGINE_ATTRIBUTE [=] 'string']
     *       [STORAGE {DISK | MEMORY}]
     *       [reference_definition]
     *       [check_constraint_definition]
     *   | data_type
     *       [COLLATE collation_name]
     *       [GENERATED ALWAYS] AS (expr)
     *       [VIRTUAL | STORED] [NOT NULL | NULL]
     *       [VISIBLE | INVISIBLE]
     *       [UNIQUE [KEY]] [PRIMARY KEY]
     *       [COMMENT 'string']
     *       [reference_definition]
     *       [check_constraint_definition]
     * }
     *
     * @param columnDefinitions 列定义字符串
     * @return 列定义列表
     */
    private static List<ColumnRel> parseColumnDefinitions(String columnDefinitions) {
        List<ColumnRel> columnRels = new ArrayList<>();

        try {
            // 简单的列定义解析 - 按逗号分割，但要考虑括号内的逗号
            List<String> columnDefs = splitColumnDefinitions(columnDefinitions);

            for (String columnDef : columnDefs) {
                columnDef = columnDef.trim();
                if (columnDef.isEmpty() || isConstraintDefinition(columnDef)) {
                    continue; // 跳过约束定义
                }

                ColumnRel columnRel = parseColumnDefinition(columnDef);
                if (columnRel != null) {
                    columnRels.add(columnRel);
                }
            }
        } catch (Exception e) {
            log.warn("PARSE_FALLBACK: Failed to parse column definitions: {}", e.getMessage());
        }

        return columnRels;
    }

    /**
     * 分割列定义，考虑括号内的逗号
     */
    private static List<String> splitColumnDefinitions(String columnDefinitions) {
        List<String> result = new ArrayList<>();
        StringBuilder current = new StringBuilder();
        int parenthesesLevel = 0;

        for (char c : columnDefinitions.toCharArray()) {
            if (c == '(') {
                parenthesesLevel++;
            } else if (c == ')') {
                parenthesesLevel--;
            } else if (c == ',' && parenthesesLevel == 0) {
                result.add(current.toString());
                current = new StringBuilder();
                continue;
            }
            current.append(c);
        }

        if (current.length() > 0) {
            result.add(current.toString());
        }

        return result;
    }

    /**
     * 判断是否为约束定义（PRIMARY KEY, FOREIGN KEY, INDEX等）
     */
    private static boolean isConstraintDefinition(String definition) {
        String upperDef = definition.trim().toUpperCase();
        return upperDef.startsWith("PRIMARY KEY") ||
               upperDef.startsWith("FOREIGN KEY") ||
               upperDef.startsWith("UNIQUE KEY") ||
               upperDef.startsWith("KEY ") ||
               upperDef.startsWith("INDEX ") ||
               upperDef.startsWith("CONSTRAINT ") ||
               upperDef.startsWith("CHECK ");
    }

    /**
     * 解析单个列定义
     * 根据MySQL官方文档格式解析
     */
    private static ColumnRel parseColumnDefinition(String columnDef) {
        try {
            // 使用更强大的正则表达式解析列定义
            // 格式：column_name data_type [constraints...]
            // 支持：INT AUTO_INCREMENT PRIMARY KEY, VARCHAR(100) NOT NULL, etc.
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(
                "^\\s*(?:`([^`]+)`|([a-zA-Z_][a-zA-Z0-9_]*))\\s+(\\w+(?:\\([^)]*\\))?(?:\\s+UNSIGNED)?)(.*)",
                java.util.regex.Pattern.CASE_INSENSITIVE
            );

            java.util.regex.Matcher matcher = pattern.matcher(columnDef);
            if (matcher.find()) {
                String columnName = matcher.group(1) != null ? matcher.group(1) : matcher.group(2);
                String dataType = matcher.group(3).trim();
                String constraints = matcher.group(4).trim();

                // 处理AUTO_INCREMENT - 根据MySQL官方文档，AUTO_INCREMENT应该转换为IDENTITY
                if (constraints.toUpperCase().contains("AUTO_INCREMENT")) {
                    dataType = dataType + " IDENTITY(1,1)";
                }

                // 解析约束
                boolean nullable = !constraints.toUpperCase().contains("NOT NULL");
                boolean primaryKey = constraints.toUpperCase().contains("PRIMARY KEY");
                String defaultValue = extractDefaultValue(constraints);

                log.debug("PARSE_FALLBACK: Parsed column: {} {} {}", columnName, dataType, constraints);

                return new ColumnRel(columnName, dataType, null, nullable, defaultValue, primaryKey, ColumnDefType.PHYSICAL);
            }
        } catch (Exception e) {
            log.warn("PARSE_FALLBACK: Failed to parse column definition '{}': {}", columnDef, e.getMessage());
        }

        return null;
    }

    /**
     * 从约束字符串中提取默认值
     */
    private static String extractDefaultValue(String constraints) {
        try {
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(
                "DEFAULT\\s+([^\\s]+(?:\\s+[^\\s]+)*?)(?:\\s+(?:COMMENT|AUTO_INCREMENT|PRIMARY|UNIQUE|NOT|NULL)|$)",
                java.util.regex.Pattern.CASE_INSENSITIVE
            );

            java.util.regex.Matcher matcher = pattern.matcher(constraints);
            if (matcher.find()) {
                return matcher.group(1).trim();
            }
        } catch (Exception e) {
            log.debug("PARSE_FALLBACK: Failed to extract default value from constraints: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 从CREATE TABLE语句中提取分区定义
     * 根据MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/partitioning.html
     *
     * @param sql CREATE TABLE语句
     * @return 分区定义字符串，如果没有分区则返回null
     */
    private static String extractPartitionDefinitionFromCreateTable(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return null;
        }

        try {
            // 查找PARTITION BY关键字的位置
            String upperSql = sql.toUpperCase();
            int partitionByIndex = upperSql.indexOf("PARTITION BY");

            if (partitionByIndex == -1) {
                return null; // 没有分区定义
            }

            // 从PARTITION BY开始提取到语句结束
            String partitionPart = sql.substring(partitionByIndex);

            // 移除末尾的分号和空白字符
            partitionPart = partitionPart.replaceAll(";\\s*$", "").trim();

            log.debug("PARTITION_EXTRACTION: Found partition definition: {}", partitionPart);
            return partitionPart;

        } catch (Exception e) {
            log.warn("PARTITION_EXTRACTION: Failed to extract partition definition from SQL: {}", e.getMessage());
            return null;
        }
    }
}
