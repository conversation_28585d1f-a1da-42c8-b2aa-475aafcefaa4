package com.xylink.sqltranspiler.infrastructure.parser.error;

import lombok.Data;

/**
 * SQL错误信息
 * 包含详细的错误分析和修复建议
 */
@Data
public class SqlErrorInfo {
    
    /**
     * 原始错误信息
     */
    private String originalError;
    
    /**
     * 友好的错误信息
     */
    private String friendlyMessage;
    
    /**
     * 详细的错误描述
     */
    private String detailedMessage;
    
    /**
     * 修复建议
     */
    private String suggestion;
    
    /**
     * 建议的修复后SQL
     */
    private String suggestedFix;
    
    /**
     * 错误类型
     */
    private SqlErrorType errorType;
    
    /**
     * 错误行号
     */
    private int line;
    
    /**
     * 错误位置
     */
    private int position;
    
    /**
     * 是否可以自动修复
     */
    private boolean autoFixable;

    /**
     * 检查是否可以自动修复
     */
    public boolean isAutoFixable() {
        return autoFixable || (errorType != null && errorType.isAutoFixable());
    }
    
    /**
     * 错误严重程度
     */
    private ErrorSeverity severity = ErrorSeverity.ERROR;
    
    /**
     * 获取格式化的错误信息
     */
    public String getFormattedMessage() {
        StringBuilder sb = new StringBuilder();
        
        sb.append("【SQL语法错误】").append("\n");
        sb.append("位置: 第").append(line).append("行第").append(position).append("列").append("\n");
        sb.append("错误: ").append(friendlyMessage != null ? friendlyMessage : originalError).append("\n");
        
        if (detailedMessage != null) {
            sb.append("详情: ").append(detailedMessage).append("\n");
        }
        
        if (suggestion != null) {
            sb.append("建议: ").append(suggestion).append("\n");
        }
        
        if (autoFixable && suggestedFix != null) {
            sb.append("修复后的SQL: ").append("\n").append(suggestedFix).append("\n");
        }
        
        return sb.toString();
    }
    
    /**
     * 获取简短的错误信息
     */
    public String getShortMessage() {
        return String.format("[第%d行第%d列] %s", 
            line, position, 
            friendlyMessage != null ? friendlyMessage : originalError);
    }
}
