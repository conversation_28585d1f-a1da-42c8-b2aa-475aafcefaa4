# MySQL Parser Split Architecture

This directory contains the modular MySQL parser implementation that replaces the monolithic `MySqlParser.java` file with smaller, more manageable components.

## Architecture Overview

The original ANTLR-generated `MySqlParser.java` file was extremely large (56,000+ lines) and difficult to maintain. This modular approach splits the parser into specialized components:

### Core Components

1. **MySqlBaseParser.java** - Base class containing common functionality and token definitions
2. **MySqlDdlParser.java** - Handles DDL (Data Definition Language) statements
3. **MySqlDmlParser.java** - Handles DML (Data Manipulation Language) statements  
4. **MySqlExpressionParser.java** - Handles SQL expressions and predicates
5. **MySqlParserCoordinator.java** - Main coordinator that delegates to specialized parsers
6. **MySqlParserFactory.java** - Factory for creating and managing parsers

## Benefits

### 1. **Maintainability**
- Each parser focuses on a specific domain (DDL, DML, expressions)
- Easier to understand and modify individual components
- Reduced complexity per file

### 2. **Performance**
- Smaller parsers load faster
- Better memory usage
- Reduced compilation time

### 3. **Modularity**
- Can use specific parsers for specific tasks
- Better separation of concerns
- Easier testing of individual components

### 4. **IDE Compatibility**
- Smaller files work better with IDEs
- No more Lombok StackOverflowError issues
- Better syntax highlighting and navigation

## Usage Examples

### Basic Usage

```java
// Parse any SQL using the coordinator
MySqlParserCoordinator parser = MySqlParserFactory.createParser(sql);
ParseTree tree = parser.sqlStatements();

// Or use the factory for convenience
ParseTree tree = MySqlParserFactory.parseSQL(sql);
```

### Specific Parser Usage

```java
// Parse only DDL statements
ParseTree ddlTree = MySqlParserFactory.parseDdlStatement("CREATE TABLE test (id INT)");

// Parse only DML statements  
ParseTree dmlTree = MySqlParserFactory.parseDmlStatement("SELECT * FROM users");

// Parse expressions
ParseTree exprTree = MySqlParserFactory.parseExpression("column1 = 'value'");
```

### Validation

```java
// Check if SQL is valid
boolean isValid = MySqlParserFactory.isValidSQL(sql);

// Get syntax errors
List<String> errors = MySqlParserFactory.getSyntaxErrors(sql);
```

### Integration with Existing Code

```java
// Updated MySqlHelper methods
List<Statement> statements = MySqlHelper.parseMultiStatementModular(sql);
boolean isValid = MySqlHelper.isValidSqlModular(sql);
List<String> errors = MySqlHelper.getSyntaxErrorsModular(sql);
```

## Parser Responsibilities

### MySqlDdlParser
Handles:
- CREATE DATABASE/TABLE/INDEX/VIEW
- DROP DATABASE/TABLE/INDEX/VIEW  
- ALTER TABLE/DATABASE
- TRUNCATE TABLE

### MySqlDmlParser
Handles:
- SELECT statements
- INSERT statements
- UPDATE statements
- DELETE statements
- Query specifications (FROM, WHERE, GROUP BY, ORDER BY, etc.)

### MySqlExpressionParser
Handles:
- WHERE clause expressions
- Function calls
- Arithmetic expressions
- Comparison operators
- Logical operators (AND, OR, NOT)
- Predicates (LIKE, IN, BETWEEN, IS NULL)

### MySqlParserCoordinator
- Routes statements to appropriate specialized parsers
- Manages the overall parsing flow
- Handles utility statements (SHOW, USE, SET)

## Migration Guide

### For Existing Code

1. **Replace direct MySqlParser usage:**
   ```java
   // Old way
   MySqlParser parser = new MySqlParser(tokenStream);
   
   // New way
   MySqlParserCoordinator parser = MySqlParserFactory.createParser(sql);
   ```

2. **Use factory methods for convenience:**
   ```java
   // Instead of manual parser setup
   ParseTree tree = MySqlParserFactory.parseSQL(sql);
   ```

3. **Leverage specific parsers for performance:**
   ```java
   // If you know it's a DDL statement
   ParseTree tree = MySqlParserFactory.parseDdlStatement(sql);
   ```

### For New Code

- Use `MySqlParserFactory` as the primary entry point
- Choose specific parsers when you know the statement type
- Use the coordinator for mixed SQL scripts

## Configuration

The factory supports configuration options:

```java
MySqlParserFactory.ParserConfig config = new MySqlParserFactory.ParserConfig()
    .setEnableErrorRecovery(true)
    .setMaxErrors(10);
    
MySqlParserCoordinator parser = MySqlParserFactory.createParser(sql, config);
```

## Testing

Each parser component can be tested independently:

```java
@Test
void testDdlParser() {
    MySqlDdlParser parser = MySqlParserFactory.createDdlParser("CREATE TABLE test (id INT)");
    MySqlDdlParser.CreateTableContext ctx = parser.createTable();
    assertNotNull(ctx);
}
```

## Future Enhancements

1. **Caching** - Add parser instance caching for better performance
2. **Parallel Parsing** - Parse independent statements in parallel
3. **Incremental Parsing** - Support for parsing only changed parts
4. **Custom Extensions** - Plugin system for custom SQL dialects

## Compatibility

This modular parser is designed to be fully compatible with the existing `MySqlAntlr4Visitor` and maintains the same AST structure as the original monolithic parser.
