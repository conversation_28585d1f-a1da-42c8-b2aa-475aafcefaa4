package com.xylink.sqltranspiler.infrastructure.parser.split;

import java.util.List;

import org.antlr.v4.runtime.tree.ParseTree;

/**
 * Example demonstrating how to use the split MySQL parser
 */
public class Example {
    
    public static void main(String[] args) {
        demonstrateBasicUsage();
        demonstrateSpecificParsers();
        demonstrateValidation();
        demonstrateStatementTypeDetection();
    }
    
    /**
     * Basic usage with the coordinator parser
     */
    public static void demonstrateBasicUsage() {
        System.out.println("=== Basic Usage ===");
        
        String sql = "CREATE TABLE users (id INT, name VARCHAR(50))";
        
        try {
            // Create parser using factory
            MySqlParserCoordinator parser = MySqlParserFactory.createParser(sql);
            
            // Parse the SQL
            ParseTree tree = parser.sqlStatements();
            
            System.out.println("✅ Successfully parsed: " + sql);
            System.out.println("Parse tree type: " + tree.getClass().getSimpleName());
            
        } catch (Exception e) {
            System.out.println("❌ Failed to parse: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * Using specific parsers for better performance
     */
    public static void demonstrateSpecificParsers() {
        System.out.println("=== Specific Parsers ===");
        
        // DDL Example
        String ddlSql = "CREATE TABLE products (id INT PRIMARY KEY, name VARCHAR(100))";
        try {
            ParseTree ddlTree = MySqlParserFactory.parseDdlStatement(ddlSql);
            System.out.println("✅ DDL parsed successfully: " + ddlSql);
        } catch (Exception e) {
            System.out.println("❌ DDL parsing failed: " + e.getMessage());
        }
        
        // DML Example
        String dmlSql = "SELECT * FROM users WHERE id > 10";
        try {
            ParseTree dmlTree = MySqlParserFactory.parseDmlStatement(dmlSql);
            System.out.println("✅ DML parsed successfully: " + dmlSql);
        } catch (Exception e) {
            System.out.println("❌ DML parsing failed: " + e.getMessage());
        }
        
        // Expression Example
        String expression = "name LIKE '%john%' AND age > 18";
        try {
            ParseTree exprTree = MySqlParserFactory.parseExpression(expression);
            System.out.println("✅ Expression parsed successfully: " + expression);
        } catch (Exception e) {
            System.out.println("❌ Expression parsing failed: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * SQL validation examples
     */
    public static void demonstrateValidation() {
        System.out.println("=== SQL Validation ===");
        
        String[] testSqls = {
            "CREATE TABLE test (id INT)",
            "SELECT * FROM users",
            "INVALID SQL STATEMENT",
            "INSERT INTO users VALUES (1, 'John')"
        };
        
        for (String sql : testSqls) {
            boolean isValid = MySqlParserFactory.isValidSQL(sql);
            System.out.println((isValid ? "✅" : "❌") + " " + sql);
            
            if (!isValid) {
                List<String> errors = MySqlParserFactory.getSyntaxErrors(sql);
                for (String error : errors) {
                    System.out.println("   Error: " + error);
                }
            }
        }
        
        System.out.println();
    }
    
    /**
     * Statement type detection
     */
    public static void demonstrateStatementTypeDetection() {
        System.out.println("=== Statement Type Detection ===");
        
        String[] testSqls = {
            "CREATE TABLE test (id INT)",
            "DROP TABLE test",
            "SELECT * FROM users",
            "INSERT INTO users VALUES (1, 'John')",
            "UPDATE users SET name = 'Jane' WHERE id = 1",
            "DELETE FROM users WHERE id = 1",
            "SHOW TABLES",
            "USE database_name",
            "SET @var = 1"
        };
        
        for (String sql : testSqls) {
            MySqlParserFactory.StatementType type = MySqlParserFactory.getStatementType(sql);
            System.out.println(type + ": " + sql);
        }
        
        System.out.println();
    }
    
    /**
     * Configuration example
     */
    public static void demonstrateConfiguration() {
        System.out.println("=== Parser Configuration ===");
        
        // Create custom configuration
        MySqlParserFactory.ParserConfig config = new MySqlParserFactory.ParserConfig()
            .setEnableErrorRecovery(false)
            .setMaxErrors(5);
        
        String sql = "CREATE TABLE test (id INT)";
        
        try {
            MySqlParserCoordinator parser = MySqlParserFactory.createParser(sql, config);
            ParseTree tree = parser.sqlStatements();
            System.out.println("✅ Configured parser worked successfully");
        } catch (Exception e) {
            System.out.println("❌ Configured parser failed: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * Performance comparison example
     */
    public static void demonstratePerformance() {
        System.out.println("=== Performance Comparison ===");
        
        String ddlSql = "CREATE TABLE large_table (id INT, name VARCHAR(100), email VARCHAR(255))";
        int iterations = 1000;
        
        // Test with general parser
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < iterations; i++) {
            try {
                MySqlParserFactory.parseSQL(ddlSql);
            } catch (Exception e) {
                // Ignore for performance test
            }
        }
        long generalTime = System.currentTimeMillis() - startTime;
        
        // Test with specific DDL parser
        startTime = System.currentTimeMillis();
        for (int i = 0; i < iterations; i++) {
            try {
                MySqlParserFactory.parseDdlStatement(ddlSql);
            } catch (Exception e) {
                // Ignore for performance test
            }
        }
        long specificTime = System.currentTimeMillis() - startTime;
        
        System.out.println("General parser: " + generalTime + "ms for " + iterations + " iterations");
        System.out.println("Specific DDL parser: " + specificTime + "ms for " + iterations + " iterations");
        
        if (specificTime < generalTime) {
            System.out.println("✅ Specific parser is " + ((generalTime - specificTime) * 100 / generalTime) + "% faster");
        } else {
            System.out.println("ℹ️ Performance difference is minimal for this test");
        }
        
        System.out.println();
    }
}
