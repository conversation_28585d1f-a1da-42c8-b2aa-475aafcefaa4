package com.xylink.sqltranspiler.infrastructure.parser.split;

import org.antlr.v4.runtime.BailErrorStrategy;
import org.antlr.v4.runtime.BaseErrorListener;
import org.antlr.v4.runtime.CharStream;
import org.antlr.v4.runtime.CharStreams;
import org.antlr.v4.runtime.CommonTokenStream;
import org.antlr.v4.runtime.RecognitionException;
import org.antlr.v4.runtime.Recognizer;
import org.antlr.v4.runtime.TokenStream;
import org.antlr.v4.runtime.tree.ParseTree;

import com.xylink.sqltranspiler.infrastructure.parser.generated.MySqlLexer;

/**
 * Factory class for creating and managing the split MySQL parsers
 * This provides a clean interface to use the modular parser system
 */
public class MySqlParserFactory {

    /**
     * Creates a new MySqlParserCoordinator from SQL text
     */
    public static MySqlParserCoordinator createParser(String sql) {
        CharStream charStream = CharStreams.fromString(sql);
        MySqlLexer lexer = new MySqlLexer(charStream);
        CommonTokenStream tokenStream = new CommonTokenStream(lexer);
        return new MySqlParserCoordinator(tokenStream);
    }

    /**
     * Creates a new MySqlParserCoordinator from a token stream
     */
    public static MySqlParserCoordinator createParser(TokenStream tokenStream) {
        return new MySqlParserCoordinator(tokenStream);
    }

    /**
     * Creates a DDL-specific parser
     */
    public static MySqlDdlParser createDdlParser(String sql) {
        CharStream charStream = CharStreams.fromString(sql);
        MySqlLexer lexer = new MySqlLexer(charStream);
        CommonTokenStream tokenStream = new CommonTokenStream(lexer);
        return new MySqlDdlParser(tokenStream);
    }

    /**
     * Creates a DML-specific parser
     */
    public static MySqlDmlParser createDmlParser(String sql) {
        CharStream charStream = CharStreams.fromString(sql);
        MySqlLexer lexer = new MySqlLexer(charStream);
        CommonTokenStream tokenStream = new CommonTokenStream(lexer);
        return new MySqlDmlParser(tokenStream);
    }

    /**
     * Creates an expression-specific parser
     */
    public static MySqlExpressionParser createExpressionParser(String sql) {
        CharStream charStream = CharStreams.fromString(sql);
        MySqlLexer lexer = new MySqlLexer(charStream);
        CommonTokenStream tokenStream = new CommonTokenStream(lexer);
        return new MySqlExpressionParser(tokenStream);
    }

    /**
     * Parses SQL and returns the parse tree using the modular parser
     */
    public static ParseTree parseSQL(String sql) {
        MySqlParserCoordinator parser = createParser(sql);
        return parser.sqlStatements();
    }

    /**
     * Parses a single DDL statement
     */
    public static ParseTree parseDdlStatement(String sql) {
        MySqlDdlParser parser = createDdlParser(sql);
        
        // Determine the type of DDL statement and parse accordingly
        String trimmedSql = sql.trim().toUpperCase();
        if (trimmedSql.startsWith("CREATE DATABASE")) {
            return parser.createDatabase();
        } else if (trimmedSql.startsWith("CREATE TABLE")) {
            return parser.createTable();
        } else if (trimmedSql.startsWith("DROP DATABASE")) {
            return parser.dropDatabase();
        } else if (trimmedSql.startsWith("DROP TABLE")) {
            return parser.dropTable();
        } else if (trimmedSql.startsWith("TRUNCATE")) {
            return parser.truncateTable();
        }
        
        throw new IllegalArgumentException("Unsupported DDL statement: " + sql);
    }

    /**
     * Parses a single DML statement
     */
    public static ParseTree parseDmlStatement(String sql) {
        MySqlDmlParser parser = createDmlParser(sql);
        
        // Determine the type of DML statement and parse accordingly
        String trimmedSql = sql.trim().toUpperCase();
        if (trimmedSql.startsWith("SELECT")) {
            return parser.selectStatement();
        } else if (trimmedSql.startsWith("INSERT")) {
            return parser.insertStatement();
        } else if (trimmedSql.startsWith("UPDATE")) {
            return parser.updateStatement();
        } else if (trimmedSql.startsWith("DELETE")) {
            return parser.deleteStatement();
        }
        
        throw new IllegalArgumentException("Unsupported DML statement: " + sql);
    }

    /**
     * Parses an expression
     */
    public static ParseTree parseExpression(String expression) {
        MySqlExpressionParser parser = createExpressionParser(expression);
        return parser.expression();
    }

    /**
     * Utility method to get statement type from SQL
     */
    public static StatementType getStatementType(String sql) {
        String trimmedSql = sql.trim().toUpperCase();
        
        if (trimmedSql.startsWith("CREATE")) {
            return StatementType.DDL;
        } else if (trimmedSql.startsWith("DROP")) {
            return StatementType.DDL;
        } else if (trimmedSql.startsWith("ALTER")) {
            return StatementType.DDL;
        } else if (trimmedSql.startsWith("TRUNCATE")) {
            return StatementType.DDL;
        } else if (trimmedSql.startsWith("SELECT")) {
            return StatementType.DML;
        } else if (trimmedSql.startsWith("INSERT")) {
            return StatementType.DML;
        } else if (trimmedSql.startsWith("UPDATE")) {
            return StatementType.DML;
        } else if (trimmedSql.startsWith("DELETE")) {
            return StatementType.DML;
        } else if (trimmedSql.startsWith("SHOW")) {
            return StatementType.UTILITY;
        } else if (trimmedSql.startsWith("USE")) {
            return StatementType.UTILITY;
        } else if (trimmedSql.startsWith("SET")) {
            return StatementType.UTILITY;
        }
        
        return StatementType.UNKNOWN;
    }

    /**
     * Enum for statement types
     */
    public enum StatementType {
        DDL,     // Data Definition Language
        DML,     // Data Manipulation Language
        UTILITY, // Utility statements (SHOW, USE, SET, etc.)
        UNKNOWN
    }

    /**
     * Configuration class for parser settings
     */
    public static class ParserConfig {
        private boolean enableErrorRecovery = true;
        private boolean enablePredictionMode = true;
        private int maxErrors = 10;

        public boolean isEnableErrorRecovery() {
            return enableErrorRecovery;
        }

        public ParserConfig setEnableErrorRecovery(boolean enableErrorRecovery) {
            this.enableErrorRecovery = enableErrorRecovery;
            return this;
        }

        public boolean isEnablePredictionMode() {
            return enablePredictionMode;
        }

        public ParserConfig setEnablePredictionMode(boolean enablePredictionMode) {
            this.enablePredictionMode = enablePredictionMode;
            return this;
        }

        public int getMaxErrors() {
            return maxErrors;
        }

        public ParserConfig setMaxErrors(int maxErrors) {
            this.maxErrors = maxErrors;
            return this;
        }
    }

    /**
     * Creates a parser with custom configuration
     */
    public static MySqlParserCoordinator createParser(String sql, ParserConfig config) {
        MySqlParserCoordinator parser = createParser(sql);
        
        if (!config.isEnableErrorRecovery()) {
            parser.setErrorHandler(new BailErrorStrategy());
        }
        
        return parser;
    }

    /**
     * Validates SQL syntax without building a full parse tree
     */
    public static boolean isValidSQL(String sql) {
        try {
            MySqlParserCoordinator parser = createParser(sql);
            parser.setErrorHandler(new BailErrorStrategy());
            parser.sqlStatements();
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Gets syntax errors from SQL
     */
    public static java.util.List<String> getSyntaxErrors(String sql) {
        java.util.List<String> errors = new java.util.ArrayList<>();
        
        try {
            MySqlParserCoordinator parser = createParser(sql);
            parser.removeErrorListeners();
            parser.addErrorListener(new BaseErrorListener() {
                @Override
                public void syntaxError(Recognizer<?, ?> recognizer, Object offendingSymbol,
                                      int line, int charPositionInLine, String msg,
                                      RecognitionException e) {
                    errors.add("Line " + line + ":" + charPositionInLine + " " + msg);
                }
            });
            parser.sqlStatements();
        } catch (Exception e) {
            errors.add("Parse error: " + e.getMessage());
        }
        
        return errors;
    }
}
