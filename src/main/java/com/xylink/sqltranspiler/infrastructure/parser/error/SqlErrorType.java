package com.xylink.sqltranspiler.infrastructure.parser.error;

/**
 * SQL错误类型枚举
 */
public enum SqlErrorType {
    
    /**
     * ON UPDATE 语法错误
     */
    INVALID_ON_UPDATE_SYNTAX("ON UPDATE语法错误", true),
    
    /**
     * DEFAULT 语法错误
     */
    INVALID_DEFAULT_SYNTAX("DEFAULT语法错误", true),
    
    /**
     * 缺少分号
     */
    MISSING_SEMICOLON("缺少分号", true),

    /**
     * 缺少逗号
     */
    MISSING_COMMA("缺少逗号", true),
    
    /**
     * 意外的标记
     */
    UNEXPECTED_TOKEN("意外的标记", false),

    /**
     * 缺少标记
     */
    MISSING_TOKEN("缺少标记", false),

    /**
     * 输入不匹配
     */
    MISMATCHED_INPUT("输入不匹配", false),

    /**
     * 未闭合的引号
     */
    UNCLOSED_QUOTE("未闭合的引号", false),

    /**
     * 无效的列引用
     */
    INVALID_COLUMN_REFERENCE("无效的列引用", false),

    /**
     * 无效的表引用
     */
    INVALID_TABLE_REFERENCE("无效的表引用", false),

    /**
     * 无效的标识符
     */
    INVALID_IDENTIFIER("无效的标识符", false),

    /**
     * MySQL特有语法
     */
    MYSQL_SPECIFIC_SYNTAX("MySQL特有语法", false),

    /**
     * PostgreSQL语法错误 - 本项目仅支持MySQL语法
     */
    POSTGRESQL_SYNTAX_ERROR("PostgreSQL语法错误", false),

    /**
     * Oracle语法错误 - 本项目仅支持MySQL语法
     */
    ORACLE_SYNTAX_ERROR("Oracle语法错误", false),

    /**
     * SQL Server语法错误 - 本项目仅支持MySQL语法
     */
    SQLSERVER_SYNTAX_ERROR("SQL Server语法错误", false),

    /**
     * 函数名错误
     */
    FUNCTION_NAME_ERROR("函数名错误", false),

    /**
     * 数据类型兼容性问题
     */
    DATA_TYPE_COMPATIBILITY("数据类型兼容性问题", false),

    /**
     * 函数兼容性问题
     */
    FUNCTION_COMPATIBILITY("函数兼容性问题", false),

    /**
     * 语法兼容性问题
     */
    SYNTAX_COMPATIBILITY("语法兼容性问题", false),

    /**
     * 字符集兼容性问题
     */
    CHARSET_COMPATIBILITY("字符集兼容性问题", false),

    /**
     * 关键字使用错误
     */
    INVALID_KEYWORD_USAGE("关键字使用错误", false),

    /**
     * 数据类型错误
     */
    INVALID_DATA_TYPE("数据类型错误", false),
    
    /**
     * 表达式错误
     */
    INVALID_EXPRESSION("表达式错误", false),

    /**
     * MySQL 8.4移除的功能
     */
    MYSQL_84_REMOVED_FEATURE("MySQL 8.4已移除的功能", false),

    /**
     * MySQL 8.4弃用的功能
     */
    MYSQL_84_DEPRECATED_FEATURE("MySQL 8.4弃用的功能", false),

    /**
     * 复制相关语法错误
     */
    REPLICATION_SYNTAX_ERROR("复制语法错误", false),

    /**
     * 存储引擎兼容性问题
     */
    STORAGE_ENGINE_COMPATIBILITY("存储引擎兼容性问题", false),

    /**
     * 权限和安全相关错误
     */
    PRIVILEGE_SECURITY_ERROR("权限和安全错误", false),

    /**
     * 通用语法错误
     */
    GENERIC_SYNTAX_ERROR("语法错误", false),

    /**
     * 解析器内部错误
     */
    PARSER_INTERNAL_ERROR("解析器内部错误", false);
    
    private final String description;
    private final boolean autoFixable;
    
    SqlErrorType(String description, boolean autoFixable) {
        this.description = description;
        this.autoFixable = autoFixable;
    }
    
    public String getDescription() {
        return description;
    }
    
    public boolean isAutoFixable() {
        return autoFixable;
    }
}
