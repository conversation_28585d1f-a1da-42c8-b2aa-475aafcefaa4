package com.xylink.sqltranspiler.infrastructure.parser.error;

import lombok.extern.slf4j.Slf4j;
import org.antlr.v4.runtime.*;
import org.antlr.v4.runtime.tree.*;
import com.xylink.sqltranspiler.infrastructure.parser.generated.MySqlLexer;
import com.xylink.sqltranspiler.infrastructure.parser.generated.MySqlParser;
import com.xylink.sqltranspiler.infrastructure.parser.generated.MySqlParserBaseVisitor;

import java.util.*;

/**
 * 基于ANTLR抽象语法树的MySQL函数提取器
 * 
 * 解决原有正则表达式方法的问题：
 * 1. 误将表名识别为函数名（如 t_en_role_res 被误识别为函数）
 * 2. 无法准确区分函数调用和其他语法结构
 * 
 * 基于MySQL 8.4官方语法规则，准确提取真正的函数调用
 * 
 * <AUTHOR> Transpiler Team
 */
@Slf4j
public class MySqlFunctionExtractor {

    /**
     * 基于AST准确提取SQL中的函数调用
     * 
     * @param sql SQL语句
     * @return 检测到的函数名称集合
     */
    public static Set<String> extractFunctionNames(String sql) {
        try {
            // 1. 词法分析
            MySqlLexer lexer = new MySqlLexer(CharStreams.fromString(sql));
            lexer.removeErrorListeners(); // 移除默认错误监听器，避免控制台输出
            
            // 2. 语法分析
            CommonTokenStream tokens = new CommonTokenStream(lexer);
            MySqlParser parser = new MySqlParser(tokens);
            parser.removeErrorListeners(); // 移除默认错误监听器
            
            // 3. 构建语法树
            ParseTree tree = parser.root();
            
            // 4. 使用访问者模式提取函数
            FunctionCallVisitor visitor = new FunctionCallVisitor();
            visitor.visit(tree);
            
            return visitor.getFunctionNames();
            
        } catch (Exception e) {
            log.warn("AST-based function extraction failed, falling back to empty set: {}", e.getMessage());
            return new HashSet<>();
        }
    }

    /**
     * 函数调用访问者
     * 基于MySQL语法规则准确识别函数调用节点
     */
    private static class FunctionCallVisitor extends MySqlParserBaseVisitor<Void> {
        
        private final Set<String> functionNames = new HashSet<>();
        
        public Set<String> getFunctionNames() {
            return functionNames;
        }
        
        // 标量函数调用: scalarFunctionName '(' functionArgs? ')'
        @Override
        public Void visitScalarFunctionCall(MySqlParser.ScalarFunctionCallContext ctx) {
            if (ctx.scalarFunctionName() != null) {
                String functionName = ctx.scalarFunctionName().getText();
                functionNames.add(functionName.toUpperCase());
                log.debug("Found scalar function: {}", functionName);
            }
            return super.visitScalarFunctionCall(ctx);
        }
        
        // UDF函数调用: fullId '(' functionArgs? ')'
        @Override
        public Void visitUdfFunctionCall(MySqlParser.UdfFunctionCallContext ctx) {
            if (ctx.fullId() != null) {
                String functionName = ctx.fullId().getText();
                // 只取最后一部分作为函数名（去除schema前缀）
                String[] parts = functionName.split("\\.");
                String actualFunctionName = parts[parts.length - 1];
                functionNames.add(actualFunctionName.toUpperCase());
                log.debug("Found UDF function: {}", actualFunctionName);
            }
            return super.visitUdfFunctionCall(ctx);
        }
        
        // 聚合函数调用
        @Override
        public Void visitAggregateFunctionCall(MySqlParser.AggregateFunctionCallContext ctx) {
            if (ctx.aggregateWindowedFunction() != null) {
                // 提取聚合函数名
                String functionText = ctx.aggregateWindowedFunction().getText();
                // 从聚合函数文本中提取函数名（去除参数部分）
                int openParen = functionText.indexOf('(');
                if (openParen > 0) {
                    String functionName = functionText.substring(0, openParen);
                    functionNames.add(functionName.toUpperCase());
                    log.debug("Found aggregate function: {}", functionName);
                }
            }
            return super.visitAggregateFunctionCall(ctx);
        }
        
        // 非聚合窗口函数调用
        @Override
        public Void visitNonAggregateFunctionCall(MySqlParser.NonAggregateFunctionCallContext ctx) {
            if (ctx.nonAggregateWindowedFunction() != null) {
                // 提取窗口函数名
                String functionText = ctx.nonAggregateWindowedFunction().getText();
                int openParen = functionText.indexOf('(');
                if (openParen > 0) {
                    String functionName = functionText.substring(0, openParen);
                    functionNames.add(functionName.toUpperCase());
                    log.debug("Found window function: {}", functionName);
                }
            }
            return super.visitNonAggregateFunctionCall(ctx);
        }
        
        // 特定函数调用（如CURRENT_DATE, NOW等）
        @Override
        public Void visitSimpleFunctionCall(MySqlParser.SimpleFunctionCallContext ctx) {
            String functionText = ctx.getText();
            // 移除括号
            String functionName = functionText.replaceAll("\\(\\)", "");
            functionNames.add(functionName.toUpperCase());
            log.debug("Found simple function: {}", functionName);
            return super.visitSimpleFunctionCall(ctx);
        }
        
        // 数据类型转换函数（CONVERT, CAST等）
        @Override
        public Void visitDataTypeFunctionCall(MySqlParser.DataTypeFunctionCallContext ctx) {
            String functionText = ctx.getText();
            // 提取函数名（CONVERT或CAST）
            if (functionText.toUpperCase().startsWith("CONVERT")) {
                functionNames.add("CONVERT");
                log.debug("Found data type function: CONVERT");
            } else if (functionText.toUpperCase().startsWith("CAST")) {
                functionNames.add("CAST");
                log.debug("Found data type function: CAST");
            }
            return super.visitDataTypeFunctionCall(ctx);
        }
        
        // VALUES函数调用
        @Override
        public Void visitValuesFunctionCall(MySqlParser.ValuesFunctionCallContext ctx) {
            functionNames.add("VALUES");
            log.debug("Found VALUES function");
            return super.visitValuesFunctionCall(ctx);
        }
        
        // CASE表达式函数调用
        @Override
        public Void visitCaseExpressionFunctionCall(MySqlParser.CaseExpressionFunctionCallContext ctx) {
            functionNames.add("CASE");
            log.debug("Found CASE expression function");
            return super.visitCaseExpressionFunctionCall(ctx);
        }
        
        @Override
        public Void visitCaseFunctionCall(MySqlParser.CaseFunctionCallContext ctx) {
            functionNames.add("CASE");
            log.debug("Found CASE function");
            return super.visitCaseFunctionCall(ctx);
        }
        
        // CHAR函数调用
        @Override
        public Void visitCharFunctionCall(MySqlParser.CharFunctionCallContext ctx) {
            functionNames.add("CHAR");
            log.debug("Found CHAR function");
            return super.visitCharFunctionCall(ctx);
        }
        
        // POSITION函数调用
        @Override
        public Void visitPositionFunctionCall(MySqlParser.PositionFunctionCallContext ctx) {
            functionNames.add("POSITION");
            log.debug("Found POSITION function");
            return super.visitPositionFunctionCall(ctx);
        }
        
        // SUBSTR/SUBSTRING函数调用
        @Override
        public Void visitSubstrFunctionCall(MySqlParser.SubstrFunctionCallContext ctx) {
            // 检查是SUBSTR还是SUBSTRING
            String functionText = ctx.getText().toUpperCase();
            if (functionText.startsWith("SUBSTR")) {
                functionNames.add("SUBSTR");
                log.debug("Found SUBSTR function");
            } else if (functionText.startsWith("SUBSTRING")) {
                functionNames.add("SUBSTRING");
                log.debug("Found SUBSTRING function");
            }
            return super.visitSubstrFunctionCall(ctx);
        }
        
        @Override
        public Void visitSubstrCommaFunctionCall(MySqlParser.SubstrCommaFunctionCallContext ctx) {
            String functionText = ctx.getText().toUpperCase();
            if (functionText.startsWith("SUBSTR")) {
                functionNames.add("SUBSTR");
                log.debug("Found SUBSTR function (comma variant)");
            } else if (functionText.startsWith("SUBSTRING")) {
                functionNames.add("SUBSTRING");
                log.debug("Found SUBSTRING function (comma variant)");
            }
            return super.visitSubstrCommaFunctionCall(ctx);
        }
        
        // TRIM函数调用
        @Override
        public Void visitTrimFunctionCall(MySqlParser.TrimFunctionCallContext ctx) {
            functionNames.add("TRIM");
            log.debug("Found TRIM function");
            return super.visitTrimFunctionCall(ctx);
        }
        
        // 密码函数调用
        @Override
        public Void visitPasswordFunctionCall(MySqlParser.PasswordFunctionCallContext ctx) {
            if (ctx.passwordFunctionClause() != null) {
                String functionText = ctx.passwordFunctionClause().getText().toUpperCase();
                if (functionText.startsWith("PASSWORD")) {
                    functionNames.add("PASSWORD");
                    log.debug("Found PASSWORD function");
                }
            }
            return super.visitPasswordFunctionCall(ctx);
        }
    }
}
