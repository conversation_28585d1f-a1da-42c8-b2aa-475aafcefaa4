package com.xylink.sqltranspiler.infrastructure.parser.error;

/**
 * MySQL语法违规记录
 * 根据 .augment/rules/rule-db.md 要求，记录不符合MySQL官方规范的语法问题
 * 
 * <AUTHOR> Transpiler Team
 */
public class MySqlSyntaxViolation {
    
    private final String patternId;
    private final String friendlyMessage;
    private final String detailedMessage;
    private final String suggestion;
    private final SqlErrorType errorType;
    
    /**
     * 构造函数
     * 
     * @param patternId 错误模式ID
     * @param friendlyMessage 友好的错误消息
     * @param detailedMessage 详细的错误说明（包含官方文档引用）
     * @param suggestion 修复建议
     * @param errorType 错误类型
     */
    public MySqlSyntaxViolation(String patternId, String friendlyMessage, 
                               String detailedMessage, String suggestion, 
                               SqlErrorType errorType) {
        this.patternId = patternId;
        this.friendlyMessage = friendlyMessage;
        this.detailedMessage = detailedMessage;
        this.suggestion = suggestion;
        this.errorType = errorType;
    }
    
    /**
     * 获取错误模式ID
     */
    public String getPatternId() {
        return patternId;
    }
    
    /**
     * 获取友好的错误消息
     */
    public String getFriendlyMessage() {
        return friendlyMessage;
    }
    
    /**
     * 获取详细的错误说明
     */
    public String getDetailedMessage() {
        return detailedMessage;
    }
    
    /**
     * 获取修复建议
     */
    public String getSuggestion() {
        return suggestion;
    }
    
    /**
     * 获取错误类型
     */
    public SqlErrorType getErrorType() {
        return errorType;
    }
    
    /**
     * 是否为阻塞性错误（不允许继续转换）
     */
    public boolean isBlocking() {
        // 根据 .augment/rules/rule-db.md 要求，所有非MySQL语法都应该阻塞转换
        return errorType == SqlErrorType.POSTGRESQL_SYNTAX_ERROR ||
               errorType == SqlErrorType.ORACLE_SYNTAX_ERROR ||
               errorType == SqlErrorType.SQLSERVER_SYNTAX_ERROR;
    }
    
    @Override
    public String toString() {
        return String.format("MySqlSyntaxViolation{patternId='%s', errorType=%s, message='%s'}", 
                           patternId, errorType, friendlyMessage);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        MySqlSyntaxViolation that = (MySqlSyntaxViolation) obj;
        return patternId.equals(that.patternId) && 
               errorType == that.errorType;
    }
    
    @Override
    public int hashCode() {
        return patternId.hashCode() * 31 + errorType.hashCode();
    }
}
