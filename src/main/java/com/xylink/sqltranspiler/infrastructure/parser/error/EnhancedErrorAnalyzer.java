package com.xylink.sqltranspiler.infrastructure.parser.error;

import java.util.Optional;

import lombok.extern.slf4j.Slf4j;

/**
 * 增强的SQL错误分析器
 * 作为错误分析的协调器，使用 ErrorPatternRegistry 进行模式匹配
 * 负责组装和格式化错误信息
 */
@Slf4j
public class EnhancedErrorAnalyzer {

    /**
     * 分析SQL语法错误并提供友好的错误信息
     * 使用注册表模式进行更通用的错误处理
     */
    public static SqlErrorInfo analyzeSqlError(String originalSql, String errorMessage, int line, int position) {
        log.debug("Analyzing SQL error: {} at line {}, position {}", errorMessage, line, position);

        SqlErrorInfo errorInfo = new SqlErrorInfo();
        errorInfo.setOriginalError(errorMessage);
        errorInfo.setLine(line);
        errorInfo.setPosition(position);
        errorInfo.setErrorType(SqlErrorType.GENERIC_SYNTAX_ERROR);

        // 使用注册表查找匹配的错误模式
        Optional<ErrorPatternRegistry.ErrorPattern> matchingPattern =
            ErrorPatternRegistry.findMatchingPattern(originalSql, errorMessage);

        if (matchingPattern.isPresent()) {
            ErrorPatternRegistry.ErrorPattern pattern = matchingPattern.get();

            errorInfo.setErrorType(pattern.getErrorType());
            errorInfo.setFriendlyMessage(pattern.getFriendlyMessage());
            errorInfo.setDetailedMessage(pattern.getDetailedMessage());
            errorInfo.setSuggestion(pattern.getSuggestion());
            errorInfo.setAutoFixable(pattern.isAutoFixable());

            // 如果有自动修复功能，生成修复后的SQL
            if (pattern.isAutoFixable() && pattern.getAutoFixer() != null) {
                String fixedSql = pattern.getAutoFixer().apply(originalSql);
                errorInfo.setSuggestedFix(fixedSql);
            }

            log.debug("Applied error pattern: {} for error type: {}", pattern.getId(), pattern.getErrorType());
            return errorInfo;
        }

        // 如果没有匹配的模式，使用通用错误处理
        analyzeGenericError(originalSql, errorMessage, line, position, errorInfo);
        return errorInfo;
    }
    
    /**
     * 通用错误分析
     * 当没有匹配的特定错误模式时使用
     */
    private static void analyzeGenericError(String sql, String errorMessage, int line, int position, SqlErrorInfo errorInfo) {
        errorInfo.setErrorType(SqlErrorType.GENERIC_SYNTAX_ERROR);
        errorInfo.setFriendlyMessage("SQL语法错误");
        errorInfo.setDetailedMessage("在第 " + line + " 行第 " + position + " 列发现语法错误：" + errorMessage);
        errorInfo.setSuggestion("请检查SQL语法是否符合MySQL标准");
        errorInfo.setAutoFixable(false);
    }

    /**
     * 检查SQL是否包含常见的语法问题
     * 委托给 ErrorPatternRegistry 进行检查
     */
    public static boolean hasKnownSyntaxIssues(String sql) {
        return ErrorPatternRegistry.hasKnownSyntaxIssues(sql);
    }

    /**
     * 自动修复已知的语法问题
     * 委托给 ErrorPatternRegistry 进行修复
     */
    public static String autoFixKnownIssues(String sql) {
        return ErrorPatternRegistry.autoFixKnownIssues(sql);
    }

    /**
     * 获取所有支持的错误模式数量
     * 用于统计和调试
     */
    public static int getSupportedPatternCount() {
        return ErrorPatternRegistry.getPatternCount();
    }

    /**
     * 获取所有可自动修复的错误模式数量
     * 用于统计和调试
     */
    public static int getAutoFixablePatternCount() {
        return ErrorPatternRegistry.getAutoFixablePatterns().size();
    }
}
