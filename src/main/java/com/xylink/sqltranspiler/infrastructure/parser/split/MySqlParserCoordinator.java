package com.xylink.sqltranspiler.infrastructure.parser.split;

import java.util.List;

import org.antlr.v4.runtime.NoViableAltException;
import org.antlr.v4.runtime.ParserRuleContext;
import org.antlr.v4.runtime.RecognitionException;
import org.antlr.v4.runtime.Token;
import org.antlr.v4.runtime.TokenStream;

/**
 * Main coordinator that delegates parsing to specialized parsers
 * This replaces the monolithic MySqlParser with a modular approach
 */
public class MySqlParserCoordinator extends MySqlBaseParser {

    private final MySqlDdlParser ddlParser;
    private final MySqlDmlParser dmlParser;
    private final MySqlExpressionParser expressionParser;

    public MySqlParserCoordinator(TokenStream input) {
        super(input);
        this.ddlParser = new MySqlDdlParser(input);
        this.dmlParser = new MySqlDmlParser(input);
        this.expressionParser = new MySqlExpressionParser(input);
    }

    // Main entry point context
    public static class SqlStatementsContext extends ParserRuleContext {
        public SqlStatementsContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }
        
        public List<SqlStatementContext> sqlStatement() {
            return getRuleContexts(SqlStatementContext.class);
        }
    }

    public static class SqlStatementContext extends ParserRuleContext {
        public SqlStatementContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }
        
        public DdlStatementContext ddlStatement() {
            return getRuleContext(DdlStatementContext.class, 0);
        }
        
        public DmlStatementContext dmlStatement() {
            return getRuleContext(DmlStatementContext.class, 0);
        }
        
        public UtilityStatementContext utilityStatement() {
            return getRuleContext(UtilityStatementContext.class, 0);
        }
    }

    public static class DdlStatementContext extends ParserRuleContext {
        public DdlStatementContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }
        
        public MySqlDdlParser.CreateDatabaseContext createDatabase() {
            return getRuleContext(MySqlDdlParser.CreateDatabaseContext.class, 0);
        }
        
        public MySqlDdlParser.CreateTableContext createTable() {
            return getRuleContext(MySqlDdlParser.CreateTableContext.class, 0);
        }
        
        public MySqlDdlParser.DropDatabaseContext dropDatabase() {
            return getRuleContext(MySqlDdlParser.DropDatabaseContext.class, 0);
        }
        
        public MySqlDdlParser.DropTableContext dropTable() {
            return getRuleContext(MySqlDdlParser.DropTableContext.class, 0);
        }
        
        public MySqlDdlParser.TruncateTableContext truncateTable() {
            return getRuleContext(MySqlDdlParser.TruncateTableContext.class, 0);
        }
    }

    public static class DmlStatementContext extends ParserRuleContext {
        public DmlStatementContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }
        
        public MySqlDmlParser.SelectStatementContext selectStatement() {
            return getRuleContext(MySqlDmlParser.SelectStatementContext.class, 0);
        }
        
        public MySqlDmlParser.InsertStatementContext insertStatement() {
            return getRuleContext(MySqlDmlParser.InsertStatementContext.class, 0);
        }
        
        public MySqlDmlParser.UpdateStatementContext updateStatement() {
            return getRuleContext(MySqlDmlParser.UpdateStatementContext.class, 0);
        }
        
        public MySqlDmlParser.DeleteStatementContext deleteStatement() {
            return getRuleContext(MySqlDmlParser.DeleteStatementContext.class, 0);
        }
    }

    public static class UtilityStatementContext extends ParserRuleContext {
        public UtilityStatementContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }
        
        public ShowStatementContext showStatement() {
            return getRuleContext(ShowStatementContext.class, 0);
        }
        
        public UseStatementContext useStatement() {
            return getRuleContext(UseStatementContext.class, 0);
        }
        
        public SetStatementContext setStatement() {
            return getRuleContext(SetStatementContext.class, 0);
        }
    }

    public static class ShowStatementContext extends ParserRuleContext {
        public ShowStatementContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }
    }

    public static class UseStatementContext extends ParserRuleContext {
        public UseStatementContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }
        
        public UidContext uid() {
            return getRuleContext(UidContext.class, 0);
        }
    }

    public static class SetStatementContext extends ParserRuleContext {
        public SetStatementContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }
    }

    public static class UidContext extends ParserRuleContext {
        public UidContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }
    }

    // Main parsing methods
    @Override
    public SqlStatementsContext sqlStatements() {
        SqlStatementsContext _localctx = new SqlStatementsContext(_ctx, getState());
        enterRule(_localctx, 0, 0);
        try {
            enterOuterAlt(_localctx, 1);
            {
                // Parse multiple SQL statements
                while (_input.LA(1) != Token.EOF) {
                    sqlStatement();
                }
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    public SqlStatementContext sqlStatement() {
        SqlStatementContext _localctx = new SqlStatementContext(_ctx, getState());
        enterRule(_localctx, 0, 0);
        try {
            enterOuterAlt(_localctx, 1);
            {
                // Determine statement type and delegate to appropriate parser
                int la = _input.LA(1);
                switch (la) {
                    case CREATE:
                    case DROP:
                    case ALTER:
                    case TRUNCATE:
                        ddlStatement();
                        break;
                    case SELECT:
                    case INSERT:
                    case UPDATE:
                    case DELETE:
                        dmlStatement();
                        break;
                    case SHOW:
                    case USE:
                    case SET:
                        utilityStatement();
                        break;
                    default:
                        throw new NoViableAltException(this);
                }
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    public DdlStatementContext ddlStatement() {
        DdlStatementContext _localctx = new DdlStatementContext(_ctx, getState());
        enterRule(_localctx, 0, 0);
        try {
            enterOuterAlt(_localctx, 1);
            {
                // Delegate to DDL parser
                int la = _input.LA(1);
                switch (la) {
                    case CREATE:
                        int la2 = _input.LA(2);
                        if (la2 == DATABASE) {
                            ddlParser.createDatabase();
                        } else if (la2 == TABLE) {
                            ddlParser.createTable();
                        }
                        break;
                    case DROP:
                        int la3 = _input.LA(2);
                        if (la3 == DATABASE) {
                            ddlParser.dropDatabase();
                        } else if (la3 == TABLE) {
                            ddlParser.dropTable();
                        }
                        break;
                    case TRUNCATE:
                        ddlParser.truncateTable();
                        break;
                }
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    public DmlStatementContext dmlStatement() {
        DmlStatementContext _localctx = new DmlStatementContext(_ctx, getState());
        enterRule(_localctx, 0, 0);
        try {
            enterOuterAlt(_localctx, 1);
            {
                // Delegate to DML parser
                int la = _input.LA(1);
                switch (la) {
                    case SELECT:
                        dmlParser.selectStatement();
                        break;
                    case INSERT:
                        dmlParser.insertStatement();
                        break;
                    case UPDATE:
                        dmlParser.updateStatement();
                        break;
                    case DELETE:
                        dmlParser.deleteStatement();
                        break;
                }
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    public UtilityStatementContext utilityStatement() {
        UtilityStatementContext _localctx = new UtilityStatementContext(_ctx, getState());
        enterRule(_localctx, 0, 0);
        try {
            enterOuterAlt(_localctx, 1);
            {
                // Handle utility statements
                int la = _input.LA(1);
                switch (la) {
                    case SHOW:
                        showStatement();
                        break;
                    case USE:
                        useStatement();
                        break;
                    case SET:
                        setStatement();
                        break;
                }
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    // Utility statement parsers
    public ShowStatementContext showStatement() {
        ShowStatementContext _localctx = new ShowStatementContext(_ctx, getState());
        enterRule(_localctx, 0, 0);
        try {
            enterOuterAlt(_localctx, 1);
            {
                setState(1);
                match(SHOW);
                // Additional parsing logic
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    public UseStatementContext useStatement() {
        UseStatementContext _localctx = new UseStatementContext(_ctx, getState());
        enterRule(_localctx, 0, 0);
        try {
            enterOuterAlt(_localctx, 1);
            {
                setState(1);
                match(USE);
                // Additional parsing logic
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    public SetStatementContext setStatement() {
        SetStatementContext _localctx = new SetStatementContext(_ctx, getState());
        enterRule(_localctx, 0, 0);
        try {
            enterOuterAlt(_localctx, 1);
            {
                setState(1);
                match(SET);
                // Additional parsing logic
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    // Token constants for utility statements
    public static final int TRUNCATE = 999;
    public static final int ALTER = 1000;
}
