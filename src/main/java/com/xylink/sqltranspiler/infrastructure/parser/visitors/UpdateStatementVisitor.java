package com.xylink.sqltranspiler.infrastructure.parser.visitors;

import java.util.ArrayList;
import java.util.List;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.TableId;
import com.xylink.sqltranspiler.core.ast.dml.MultiTableUpdate;
import com.xylink.sqltranspiler.core.ast.dml.UpdateTable;
import com.xylink.sqltranspiler.infrastructure.parser.generated.MySqlParser;
import com.xylink.sqltranspiler.infrastructure.parser.generated.MySqlParserBaseVisitor;
import com.xylink.sqltranspiler.infrastructure.util.CommonUtils;

/**
 * UPDATE语句专门的visitor
 * 严格遵守MySQL官方规范：https://dev.mysql.com/doc/refman/8.4/en/
 * 和达梦官方规范：https://eco.dameng.com/document/dm/zh-cn/pm/
 */
public class UpdateStatementVisitor extends MySqlParserBaseVisitor<Statement> {

    @Override
    public Statement visitSingleUpdateStatement(MySqlParser.SingleUpdateStatementContext ctx) {
        try {
            // 检查是否是多表UPDATE
            boolean isMultiTableUpdate = false;

            // 如果tableSources包含多个表或者有JOIN，则认为是多表UPDATE
            if (ctx.tableSources() != null && ctx.tableSources().tableSource() != null) {
                if (ctx.tableSources().tableSource().size() > 1) {
                    isMultiTableUpdate = true;
                } else {
                    // 检查是否有JOIN
                    MySqlParser.TableSourceContext tableSourceCtx = ctx.tableSources().tableSource().get(0);
                    if (tableSourceCtx instanceof MySqlParser.TableSourceBaseContext) {
                        MySqlParser.TableSourceBaseContext baseCtx = (MySqlParser.TableSourceBaseContext) tableSourceCtx;
                        if (baseCtx.joinPart() != null && !baseCtx.joinPart().isEmpty()) {
                            isMultiTableUpdate = true;
                        }
                    }
                }
            }

            // 如果是多表UPDATE，使用多表UPDATE处理逻辑
            if (isMultiTableUpdate) {
                return handleMultiTableUpdate(ctx);
            }

        } catch (Exception e) {
            // 检测失败时继续单表处理
        }

        // 单表UPDATE处理逻辑
        // 解析表名 - 从tableSources中获取第一个表
        TableId tableId = null;
        if (ctx.tableSources() != null && ctx.tableSources().tableSource() != null && !ctx.tableSources().tableSource().isEmpty()) {
            MySqlParser.TableSourceContext tableSourceCtx = ctx.tableSources().tableSource().get(0);
            if (tableSourceCtx instanceof MySqlParser.TableSourceBaseContext) {
                MySqlParser.TableSourceBaseContext baseCtx = (MySqlParser.TableSourceBaseContext) tableSourceCtx;
                if (baseCtx.tableSourceItem() instanceof MySqlParser.AtomTableItemContext) {
                    MySqlParser.AtomTableItemContext atomCtx = (MySqlParser.AtomTableItemContext) baseCtx.tableSourceItem();
                    tableId = parseFullId(atomCtx.tableName().fullId());
                }
            }
        }

        // 如果无法解析表名，使用默认值
        if (tableId == null) {
            tableId = new TableId("unknown_table");
        }

        // 解析SET子句
        List<UpdateTable.SetClause> setClauses = new ArrayList<>();
        if (ctx.updatedElement() != null) {
            for (MySqlParser.UpdatedElementContext updateCtx : ctx.updatedElement()) {
                UpdateTable.SetClause setClause = parseSetClause(updateCtx);
                if (setClause != null) {
                    setClauses.add(setClause);
                }
            }
        }

        // 解析WHERE子句
        String whereClause = null;
        if (ctx.WHERE() != null && ctx.expression() != null) {
            whereClause = ctx.expression().getText();
        }

        // 解析ORDER BY子句
        String orderByClause = null;
        if (ctx.orderByClause() != null) {
            orderByClause = ctx.orderByClause().getText();
        }

        // 解析LIMIT子句
        String limitClause = null;
        if (ctx.limitClause() != null) {
            limitClause = ctx.limitClause().getText();
        }

        // 解析修饰符
        boolean lowPriority = ctx.LOW_PRIORITY() != null;
        boolean ignore = ctx.IGNORE() != null;

        return new UpdateTable(tableId, setClauses, whereClause, orderByClause, limitClause, lowPriority, ignore);
    }

    @Override
    public Statement visitMultipleUpdateStatement(MySqlParser.MultipleUpdateStatementContext ctx) {
        try {
            // 解析表引用
            String tableReferences = null;
            if (ctx.tableSources() != null) {
                tableReferences = ctx.tableSources().getText();
            }

            // 解析SET子句
            List<MultiTableUpdate.SetClause> setClauses = new ArrayList<>();
            if (ctx.updatedElement() != null) {
                for (MySqlParser.UpdatedElementContext updateCtx : ctx.updatedElement()) {
                    MultiTableUpdate.SetClause setClause = parseMultiTableUpdateSetClause(updateCtx);
                    if (setClause != null) {
                        setClauses.add(setClause);
                    }
                }
            }

            // 解析WHERE子句
            String whereClause = null;
            if (ctx.WHERE() != null && ctx.expression() != null) {
                whereClause = ctx.expression().getText();
            }

            // 解析修饰符
            boolean lowPriority = ctx.LOW_PRIORITY() != null;
            boolean ignore = ctx.IGNORE() != null;

            return new MultiTableUpdate(tableReferences, setClauses, whereClause, lowPriority, ignore);

        } catch (Exception e) {
            // 解析失败时回退到简化处理
            List<UpdateTable.SetClause> setClauses = new ArrayList<>();
            if (ctx.updatedElement() != null) {
                for (MySqlParser.UpdatedElementContext updateCtx : ctx.updatedElement()) {
                    UpdateTable.SetClause setClause = parseSetClause(updateCtx);
                    if (setClause != null) {
                        setClauses.add(setClause);
                    }
                }
            }

            String whereClause = null;
            if (ctx.WHERE() != null && ctx.expression() != null) {
                whereClause = ctx.expression().getText();
            }

            boolean lowPriority = ctx.LOW_PRIORITY() != null;
            boolean ignore = ctx.IGNORE() != null;

            return new UpdateTable(new TableId("unknown_table"), setClauses, whereClause, null, null, lowPriority, ignore);
        }
    }

    /**
     * 处理多表UPDATE语句
     */
    private Statement handleMultiTableUpdate(MySqlParser.SingleUpdateStatementContext ctx) {
        try {
            // 解析表引用
            String tableReferences = null;
            if (ctx.tableSources() != null) {
                tableReferences = ctx.tableSources().getText();
            }

            // 解析SET子句
            List<MultiTableUpdate.SetClause> setClauses = new ArrayList<>();
            if (ctx.updatedElement() != null) {
                for (MySqlParser.UpdatedElementContext updateCtx : ctx.updatedElement()) {
                    MultiTableUpdate.SetClause setClause = parseMultiTableUpdateSetClause(updateCtx);
                    if (setClause != null) {
                        setClauses.add(setClause);
                    }
                }
            }

            // 解析WHERE子句
            String whereClause = null;
            if (ctx.WHERE() != null && ctx.expression() != null) {
                whereClause = ctx.expression().getText();
            }

            // 解析修饰符
            boolean lowPriority = ctx.LOW_PRIORITY() != null;
            boolean ignore = ctx.IGNORE() != null;

            return new MultiTableUpdate(tableReferences, setClauses, whereClause, lowPriority, ignore);

        } catch (Exception e) {
            // 解析失败时回退到简化处理
            return new UpdateTable(new TableId("unknown_table"), new ArrayList<>(), null, null, null, false, false);
        }
    }

    /**
     * 解析多表UPDATE语句中的SET子句
     */
    private MultiTableUpdate.SetClause parseMultiTableUpdateSetClause(MySqlParser.UpdatedElementContext ctx) {
        try {
            if (ctx.fullColumnName() != null && ctx.expression() != null) {
                // 解析列名（可能包含表名前缀）
                String fullColumnName = ctx.fullColumnName().getText();
                String value = ctx.expression().getText();

                // 尝试解析表名和列名
                TableId tableId = null;
                String columnName = fullColumnName;

                // 如果列名包含点号，说明有表名前缀
                if (fullColumnName.contains(".")) {
                    String[] parts = fullColumnName.split("\\.", 2);
                    if (parts.length == 2) {
                        tableId = new TableId(parts[0]);
                        columnName = parts[1];
                    }
                }

                return new MultiTableUpdate.SetClause(tableId, columnName, value);
            }
        } catch (Exception e) {
            // 解析失败时返回null
        }

        return null;
    }

    /**
     * 解析SET子句中的单个赋值
     */
    private UpdateTable.SetClause parseSetClause(MySqlParser.UpdatedElementContext ctx) {
        if (ctx.fullColumnName() != null && ctx.expression() != null) {
            String columnName = parseColumnName(ctx.fullColumnName());
            String value = ctx.expression().getText();
            return new UpdateTable.SetClause(columnName, value);
        }
        return null;
    }

    /**
     * 解析列名，支持简单列名和限定列名
     */
    private String parseColumnName(MySqlParser.FullColumnNameContext ctx) {
        // 根据fullColumnName的语法规则解析
        if (ctx.uid() != null) {
            // 简单列名：uid (dottedId dottedId?)?
            String columnName = CommonUtils.cleanQuote(ctx.uid().getText());

            // 如果有dottedId，说明是限定列名，我们只取最后一部分作为列名
            if (ctx.dottedId() != null && !ctx.dottedId().isEmpty()) {
                // 取最后一个dottedId作为列名
                MySqlParser.DottedIdContext lastDottedId = ctx.dottedId().get(ctx.dottedId().size() - 1);
                columnName = CommonUtils.cleanQuote(lastDottedId.getText().substring(1)); // 去掉前面的点
            }

            return columnName;
        } else if (ctx.dottedId() != null && !ctx.dottedId().isEmpty()) {
            // dottedId dottedId?
            MySqlParser.DottedIdContext lastDottedId = ctx.dottedId().get(ctx.dottedId().size() - 1);
            return CommonUtils.cleanQuote(lastDottedId.getText().substring(1)); // 去掉前面的点
        }

        throw new RuntimeException("Unable to parse column name from: " + ctx.getText());
    }

    /**
     * 解析fullId为TableId
     */
    private TableId parseFullId(MySqlParser.FullIdContext fullId) {
        List<String> texts = new ArrayList<>();
        for (int i = 0; i < fullId.getChildCount(); i++) {
            texts.add(fullId.getChild(i).getText());
        }

        List<String> parts = texts.stream()
                .filter(text -> !".".equals(text))
                .map(CommonUtils::cleanQuote)  // Clean quotes including backticks
                .collect(java.util.stream.Collectors.toList());

        if (parts.size() == 2) {
            String schemaName = parts.get(0);
            String tableName = parts.get(1);

            // 修复：如果tableName以点开头，去掉前导点
            if (tableName.startsWith(".")) {
                tableName = tableName.substring(1);
            }

            return new TableId(schemaName, tableName);
        } else if (parts.size() == 1) {
            String tableName = parts.get(0);

            // 修复：如果tableName以点开头，去掉前导点
            if (tableName.startsWith(".")) {
                tableName = tableName.substring(1);
            }

            return new TableId(tableName);
        } else {
            throw new RuntimeException("parse fullId error: " + fullId.getText());
        }
    }
}
