package com.xylink.sqltranspiler.infrastructure.parser.split;

import org.antlr.v4.runtime.Parser;
import org.antlr.v4.runtime.ParserRuleContext;
import org.antlr.v4.runtime.TokenStream;
import org.antlr.v4.runtime.atn.ATN;
import org.antlr.v4.runtime.atn.ATNDeserializer;
import org.antlr.v4.runtime.atn.ATNType;
import org.antlr.v4.runtime.atn.ParserATNSimulator;
import org.antlr.v4.runtime.atn.PredictionContextCache;
import org.antlr.v4.runtime.dfa.DFA;

/**
 * Base parser class containing common functionality and token definitions
 */
public abstract class MySqlBaseParser extends Parser {
    
    // Token constants (extracted from original MySqlParser)
    public static final int
        SPACE=1, SPEC_MYSQL_COMMENT=2, COMMENT_INPUT=3, LINE_COMMENT=4, ADD=5, 
        ALL=6, ALTER=7, ALWAYS=8, ANALYZE=9, AND=10, ARRAY=11, AS=12, ASC=13, 
        ATTRIBUTE=14, BEFORE=15, BETWEEN=16, BOTH=17, BUCKETS=18, BY=19, CALL=20, 
        CASCADE=21, CASE=22, CAST=23, CHANGE=24, CHARACTER=25, CHECK=26, COLLATE=27, 
        COLUMN=28, CONDITION=29, CONSTRAINT=30, CONTINUE=31, CONVERT=32, CREATE=33, 
        CROSS=34, CURRENT=35, CURRENT_ROLE=36, CURRENT_USER=37, CURSOR=38, DATABASE=39, 
        DATABASES=40, DECLARE=41, DEFAULT=42, DELAYED=43, DELETE=44, DESC=45, 
        DESCRIBE=46, DETERMINISTIC=47, DIAGNOSTICS=48, DISTINCT=49, DISTINCTROW=50, 
        DROP=51, EACH=52, ELSE=53, ELSEIF=54, EMPTY=55, ENCLOSED=56, ENFORCED=57, 
        ESCAPED=58, EXCEPT=59, EXISTS=60, EXIT=61, EXPLAIN=62, FALSE=63, FETCH=64, 
        FOR=65, FORCE=66, FOREIGN=67, FROM=68, FULLTEXT=69, GENERATED=70, GET=71, 
        GRANT=72, GROUP=73, HAVING=74, HIGH_PRIORITY=75, HISTOGRAM=76, IF=77, 
        IGNORE=78, IGNORED=79, IN=80, INDEX=81, INFILE=82, INNER=83, INOUT=84, 
        INSERT=85, INTERVAL=86, INTO=87, IS=88, ITERATE=89, JOIN=90, KEY=91, KEYS=92, 
        KILL=93, LATERAL=94, LEADING=95, LEAVE=96, LEFT=97, LIKE=98, LIMIT=99, 
        LINEAR=100, LINES=101, LOAD=102, LOCK=103, LOCKED=104, LOOP=105, LOW_PRIORITY=106, 
        MASTER_BIND=107, MASTER_SSL_VERIFY_SERVER_CERT=108, MATCH=109, MAXVALUE=110, 
        MINVALUE=111, MODIFIES=112, NATURAL=113, NOT=114, NO_WRITE_TO_BINLOG=115, 
        NULL_LITERAL=116, NUMBER=117, ON=118, OPTIMIZE=119, OPTION=120, OPTIONAL=121, 
        OPTIONALLY=122, OR=123, ORDER=124, OUT=125, OUTER=126, OUTFILE=127, OVER=128, 
        PARTITION=129, PRIMARY=130, PROCEDURE=131, PURGE=132, RANGE=133, READ=134, 
        READS=135, REFERENCES=136, REGEXP=137, RELEASE=138, RENAME=139, REPEAT=140, 
        REPLACE=141, REQUIRE=142, RESIGNAL=143, RESTRICT=144, RETAIN=145, RETURN=146, 
        REVOKE=147, RIGHT=148, RLIKE=149, SCHEMA=150, SCHEMAS=151, SELECT=152, 
        SET=153, SEPARATOR=154, SHOW=155, SIGNAL=156, SKIP_=157, SKIP_QUERY_REWRITE=158, 
        SPATIAL=159, SQL=160, SQLEXCEPTION=161, SQLSTATE=162, SQLWARNING=163, 
        SQL_BIG_RESULT=164, SQL_CALC_FOUND_ROWS=165, SQL_SMALL_RESULT=166, SSL=167, 
        STACKED=168, STARTING=169, STATEMENT=170, STRAIGHT_JOIN=171, TABLE=172, 
        TERMINATED=173, THEN=174, TO=175, TRAILING=176, TRIGGER=177, TRUE=178, 
        UNDO=179, UNION=180, UNIQUE=181, UNLOCK=182, UNSIGNED=183, UPDATE=184, 
        USAGE=185, USE=186, USING=187, VALUES=188, WHEN=189, WHERE=190, WHILE=191;

    protected static final DFA[] _decisionToDFA;
    protected static final PredictionContextCache _sharedContextCache = new PredictionContextCache();

    public MySqlBaseParser(TokenStream input) {
        super(input);
        _interp = new ParserATNSimulator(this, _ATN, _decisionToDFA, _sharedContextCache);
    }

    // Common utility methods
    protected boolean isValidIdentifier(String name) {
        return name != null && !name.isEmpty() && Character.isLetter(name.charAt(0));
    }

    protected String unquoteIdentifier(String identifier) {
        if (identifier == null) return null;
        if (identifier.startsWith("`") && identifier.endsWith("`")) {
            return identifier.substring(1, identifier.length() - 1);
        }
        return identifier;
    }

    // Abstract methods to be implemented by specific parsers
    public abstract ParserRuleContext sqlStatements();

    // Required by ANTLR Parser class
    @Override
    public ATN getATN() {
        return _ATN;
    }

    @Override
    public String[] getRuleNames() {
        return new String[0]; // Will be overridden by specific parsers
    }

    @Override
    public String[] getTokenNames() {
        return new String[0]; // Will be overridden by specific parsers
    }

    @Override
    public String getGrammarFileName() {
        return "MySqlParser.g4";
    }

    // Static initialization will be handled by the main parser
    static {
        _decisionToDFA = new DFA[0]; // Will be properly initialized
    }

    // Helper method to create an empty ATN
    private static ATN createEmptyATN() {
        try {
            return new ATNDeserializer().deserialize("".toCharArray());
        } catch (Exception e) {
            // Return a minimal ATN if deserialization fails
            ATN atn = new ATN(ATNType.PARSER, 0);
            return atn;
        }
    }

    // ATN will be set by the main parser - using a simple empty ATN for now
    protected static final ATN _ATN = createEmptyATN();
}
