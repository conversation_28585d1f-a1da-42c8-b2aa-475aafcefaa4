package com.xylink.sqltranspiler.infrastructure.formatter;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.xylink.sqltranspiler.core.dialects.SqlDialect;

import lombok.extern.slf4j.Slf4j;

/**
 * SQL格式化器 - 提供统一的SQL美化功能
 * 
 * 功能特性：
 * 1. 智能缩进和换行
 * 2. 关键字对齐
 * 3. 子查询格式化
 * 4. 注释保持
 * 5. 支持多种SQL语句类型
 * 
 * <AUTHOR> Transpiler Team
 */
@Slf4j
public class SqlFormatter {

    private static final String INDENT = "    "; // 4个空格缩进

    /**
     * 格式化SQL语句
     *
     * @param sql 原始SQL语句
     * @return 格式化后的SQL语句
     */
    public static String format(String sql) {
        if (sql == null) {
            return "";
        }
        if (sql.trim().isEmpty()) {
            return sql;
        }

        try {
            String formatted = sql.trim();
            
            // 检测SQL语句类型并应用相应的格式化
            if (isCreateTableAsSelectStatement(formatted)) {
                formatted = formatCreateTableAsSelect(formatted);
            } else if (isCreateTableStatement(formatted)) {
                formatted = formatCreateTable(formatted);
            } else if (isSelectStatement(formatted)) {
                formatted = formatSelect(formatted);
            } else if (isInsertStatement(formatted)) {
                formatted = formatInsert(formatted);
            } else if (isUpdateStatement(formatted)) {
                formatted = formatUpdate(formatted);
            } else if (isDeleteStatement(formatted)) {
                formatted = formatDelete(formatted);
            } else {
                // 对于其他语句，应用基本格式化
                formatted = formatBasic(formatted);
            }

            return formatted;
        } catch (Exception e) {
            log.warn("SQL formatting failed, returning original SQL: {}", e.getMessage());
            return sql;
        }
    }

    /**
     * 增强的格式化方法，支持函数转换
     * 借鉴Calcite的函数映射思想
     *
     * @param sql 原始SQL语句
     * @param dialect 目标数据库方言
     * @return 格式化并转换函数后的SQL语句
     */
    public static String formatWithFunctionMapping(String sql, SqlDialect dialect) {
        if (sql == null || sql.trim().isEmpty() || dialect == null) {
            return sql;
        }

        try {
            // 首先进行标准格式化
            String formatted = format(sql);

            // 然后进行函数转换
            formatted = formatFunctionCalls(formatted, dialect);

            return formatted;
        } catch (Exception e) {
            log.warn("Failed to format SQL with function mapping: {}", e.getMessage());
            return sql; // 返回原始SQL作为fallback
        }
    }



    /**
     * 简化的函数调用格式化
     * 处理常见的MySQL函数转换
     *
     * @param sql SQL语句
     * @param dialect 目标数据库方言
     * @return 转换后的SQL
     */
    private static String formatFunctionCalls(String sql, SqlDialect dialect) {
        String result = sql;

        // 处理常见的无参数函数
        result = result.replaceAll("(?i)\\bNOW\\s*\\(\\s*\\)", dialect.mapFunction("NOW"));
        result = result.replaceAll("(?i)\\bCURDATE\\s*\\(\\s*\\)", dialect.mapFunction("CURDATE"));
        result = result.replaceAll("(?i)\\bCURTIME\\s*\\(\\s*\\)", dialect.mapFunction("CURTIME"));
        result = result.replaceAll("(?i)\\bCURRENT_TIMESTAMP\\b", dialect.getCurrentTimestampFunction());
        result = result.replaceAll("(?i)\\bCURRENT_DATE\\b", dialect.getCurrentDateFunction());

        // 处理字符串函数
        result = result.replaceAll("(?i)\\bSUBSTRING\\s*\\(", dialect.mapFunction("SUBSTRING") + "(");
        result = result.replaceAll("(?i)\\bIFNULL\\s*\\(", dialect.mapFunction("IFNULL") + "(");

        return result;
    }

    /**
     * 格式化CREATE TABLE AS SELECT语句
     */
    private static String formatCreateTableAsSelect(String sql) {
        String cleaned = sql.replaceAll("\\s+", " ").trim();

        // 分离CREATE TABLE部分和AS SELECT部分
        Pattern pattern = Pattern.compile("(CREATE\\s+TABLE\\s+(?:IF\\s+NOT\\s+EXISTS\\s+)?[^\\s]+(?:\\s*\\([^\\)]+\\))?\\s+)AS\\s+(SELECT.+)", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(cleaned);

        if (matcher.find()) {
            String createTablePart = matcher.group(1).trim();
            String selectPart = matcher.group(2).trim();

            StringBuilder result = new StringBuilder();
            result.append(createTablePart);
            // 确保AS前有空格
            if (!createTablePart.endsWith(" ")) {
                result.append(" ");
            }
            result.append("AS ");

            // 对于CREATE TABLE AS SELECT，保持SELECT部分简洁
            // 只在复杂查询时才进行多行格式化
            if (selectPart.length() > 100 || selectPart.contains("JOIN") || selectPart.contains("GROUP BY")) {
                String formattedSelect = formatSelect(selectPart);
                result.append(formattedSelect);
            } else {
                // 简单查询保持单行
                result.append(selectPart);
            }

            return result.toString();
        }

        // 如果匹配失败，返回原始SQL
        return cleaned;
    }

    /**
     * 格式化CREATE TABLE语句
     */
    private static String formatCreateTable(String sql) {
        // 处理注释开头的CREATE TABLE
        if (sql.trim().startsWith("--")) {
            // 检查是否注释和CREATE TABLE在同一行
            if (sql.contains("CREATE TABLE")) {
                // 简单地分离注释和CREATE TABLE
                int createTableIndex = sql.indexOf("CREATE TABLE");
                String comment = sql.substring(0, createTableIndex).trim();

                // 确保注释完整
                if (comment.startsWith("--") && !comment.endsWith("语句")) {
                    // 如果注释被截断，尝试找到完整的注释
                    String[] words = comment.split("\\s+");
                    if (words.length >= 2) {
                        comment = "-- 神通数据库CREATE TABLE语句";
                    }
                }

                String createTablePart = sql.substring(createTableIndex).trim();

                StringBuilder result = new StringBuilder();
                result.append(comment).append("\n");
                result.append(formatCreateTableInternal(createTablePart));
                return result.toString();
            } else {
                // 多行格式，按行处理
                String[] lines = sql.split("\n");
                StringBuilder result = new StringBuilder();

                for (String line : lines) {
                    line = line.trim();
                    if (line.startsWith("--")) {
                        result.append(line).append("\n");
                    } else if (line.toUpperCase().startsWith("CREATE TABLE")) {
                        // 格式化CREATE TABLE部分
                        String formatted = formatCreateTableInternal(line);
                        result.append(formatted);
                        break;
                    }
                }

                return result.toString();
            }
        } else {
            return formatCreateTableInternal(sql);
        }
    }

    /**
     * 内部CREATE TABLE格式化方法
     */
    private static String formatCreateTableInternal(String sql) {
        StringBuilder result = new StringBuilder();

        // 处理CREATE TABLE开头
        Pattern createTablePattern = Pattern.compile(
            "(CREATE\\s+TABLE\\s+(?:IF\\s+NOT\\s+EXISTS\\s+)?[^\\(]+)\\s*\\(",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = createTablePattern.matcher(sql);
        if (matcher.find()) {
            result.append(matcher.group(1)).append(" (\n");

            // 提取列定义部分
            String columnDefs = extractColumnDefinitions(sql);

            if (columnDefs != null) {
                result.append(formatColumnDefinitions(columnDefs, sql));
            }
        } else {
            return sql; // 如果无法解析，返回原始SQL
        }

        return result.toString();
    }

    /**
     * 格式化SELECT语句
     */
    private static String formatSelect(String sql) {
        StringBuilder result = new StringBuilder();
        
        // 移除多余的空格
        String cleaned = sql.replaceAll("\\s+", " ").trim();
        
        // 在关键字前添加换行
        cleaned = cleaned.replaceAll("(?i)\\s+(FROM)\\s+", "\n$1 ");
        cleaned = cleaned.replaceAll("(?i)\\s+(WHERE)\\s+", "\n$1 ");
        cleaned = cleaned.replaceAll("(?i)\\s+(GROUP\\s+BY)\\s+", "\n$1 ");
        cleaned = cleaned.replaceAll("(?i)\\s+(HAVING)\\s+", "\n$1 ");
        cleaned = cleaned.replaceAll("(?i)\\s+(ORDER\\s+BY)\\s+", "\n$1 ");
        cleaned = cleaned.replaceAll("(?i)\\s+(LIMIT)\\s+", "\n$1 ");
        cleaned = cleaned.replaceAll("(?i)\\s+(OFFSET)\\s+", "\n$1 ");
        
        // 处理JOIN
        cleaned = cleaned.replaceAll("(?i)\\s+((?:LEFT|RIGHT|INNER|FULL)?\\s*(?:OUTER\\s+)?JOIN)\\s+", "\n$1 ");
        
        // 分割成行并添加适当的缩进
        String[] lines = cleaned.split("\n");
        for (int i = 0; i < lines.length; i++) {
            String line = lines[i].trim();
            if (i == 0) {
                // SELECT行不缩进
                result.append(line);
            } else if (line.matches("(?i)^(FROM|WHERE|GROUP\\s+BY|HAVING|ORDER\\s+BY|LIMIT|OFFSET).*")) {
                // 主要关键字不缩进
                result.append("\n").append(line);
            } else if (line.matches("(?i)^(LEFT|RIGHT|INNER|FULL|JOIN).*")) {
                // JOIN语句缩进
                result.append("\n").append(INDENT).append(line);
            } else {
                // 其他行缩进
                result.append("\n").append(INDENT).append(line);
            }
        }
        
        return result.toString();
    }

    /**
     * 格式化INSERT语句
     */
    private static String formatInsert(String sql) {
        String cleaned = sql.replaceAll("\\s+", " ").trim();
        
        // 在VALUES前添加换行
        cleaned = cleaned.replaceAll("(?i)\\s+(VALUES)\\s+", "\n$1 ");
        
        return cleaned;
    }

    /**
     * 格式化UPDATE语句
     */
    private static String formatUpdate(String sql) {
        String cleaned = sql.replaceAll("\\s+", " ").trim();
        
        // 在SET和WHERE前添加换行
        cleaned = cleaned.replaceAll("(?i)\\s+(SET)\\s+", "\n$1 ");
        cleaned = cleaned.replaceAll("(?i)\\s+(WHERE)\\s+", "\n$1 ");
        
        return cleaned;
    }

    /**
     * 格式化DELETE语句
     */
    private static String formatDelete(String sql) {
        String cleaned = sql.replaceAll("\\s+", " ").trim();

        // 对于简单的DELETE语句（长度较短），保持单行格式
        if (cleaned.length() <= 80) {
            return cleaned;
        }

        // 对于复杂的DELETE语句，在WHERE前添加换行，但保持DELETE FROM在同一行
        cleaned = cleaned.replaceAll("(?i)\\s+(WHERE)\\s+", "\n$1 ");

        return cleaned;
    }

    /**
     * 基本格式化 - 用于其他类型的SQL语句
     */
    private static String formatBasic(String sql) {
        return sql.replaceAll("\\s+", " ").trim();
    }

    /**
     * 检测是否为CREATE TABLE AS SELECT语句
     */
    private static boolean isCreateTableAsSelectStatement(String sql) {
        // 检查是否包含CREATE TABLE ... AS SELECT模式
        return sql.matches("(?i)^\\s*CREATE\\s+TABLE\\s+.*\\s+AS\\s+SELECT\\s+.*") ||
               (sql.trim().startsWith("--") && sql.matches("(?i).*CREATE\\s+TABLE\\s+.*\\s+AS\\s+SELECT\\s+.*"));
    }

    /**
     * 检测是否为CREATE TABLE语句
     */
    private static boolean isCreateTableStatement(String sql) {
        // 检查是否以CREATE TABLE开头，或者包含CREATE TABLE（用于处理注释开头的情况）
        return sql.matches("(?i)^\\s*CREATE\\s+TABLE\\s+.*") ||
               (sql.trim().startsWith("--") && sql.contains("CREATE TABLE"));
    }

    /**
     * 检测是否为SELECT语句
     */
    private static boolean isSelectStatement(String sql) {
        return sql.matches("(?i)^\\s*SELECT\\s+.*");
    }

    /**
     * 检测是否为INSERT语句
     */
    private static boolean isInsertStatement(String sql) {
        return sql.matches("(?i)^\\s*INSERT\\s+.*");
    }

    /**
     * 检测是否为UPDATE语句
     */
    private static boolean isUpdateStatement(String sql) {
        return sql.matches("(?i)^\\s*UPDATE\\s+.*");
    }

    /**
     * 检测是否为DELETE语句
     */
    private static boolean isDeleteStatement(String sql) {
        return sql.matches("(?i)^\\s*DELETE\\s+.*");
    }

    /**
     * 提取列定义部分
     */
    private static String extractColumnDefinitions(String sql) {
        // 找到第一个左括号和最后一个右括号的位置
        int firstParen = sql.indexOf('(');
        int lastParen = sql.lastIndexOf(')');

        if (firstParen >= 0 && lastParen > firstParen) {
            return sql.substring(firstParen + 1, lastParen); // 提取括号内的内容
        }
        return null;
    }

    /**
     * 格式化列定义
     */
    private static String formatColumnDefinitions(String columnDefs, String originalSql) {
        StringBuilder result = new StringBuilder();

        // 更智能的列定义分割，考虑括号内的逗号
        String[] columns = smartSplitColumns(columnDefs);

        for (int i = 0; i < columns.length; i++) {
            String column = columns[i].trim();
            if (!column.isEmpty()) {
                result.append(INDENT).append(column);

                if (i < columns.length - 1) {
                    result.append(",");
                }
                result.append("\n");
            }
        }

        result.append(")");

        // 提取并保留表选项（如CHARACTER SET UTF8等）
        String tableOptions = extractTableOptions(originalSql);
        if (tableOptions != null && !tableOptions.trim().isEmpty()) {
            result.append(tableOptions);
        }

        result.append(";");

        return result.toString();
    }

    /**
     * 智能分割列定义，考虑括号内的逗号
     */
    private static String[] smartSplitColumns(String columnDefs) {
        java.util.List<String> columns = new java.util.ArrayList<>();
        StringBuilder current = new StringBuilder();
        int parentheses = 0;
        boolean inQuotes = false;
        char quoteChar = 0;

        for (int i = 0; i < columnDefs.length(); i++) {
            char c = columnDefs.charAt(i);

            if (!inQuotes && (c == '"' || c == '\'' || c == '`')) {
                inQuotes = true;
                quoteChar = c;
            } else if (inQuotes && c == quoteChar) {
                inQuotes = false;
            } else if (!inQuotes) {
                if (c == '(') {
                    parentheses++;
                } else if (c == ')') {
                    parentheses--;
                } else if (c == ',' && parentheses == 0) {
                    columns.add(current.toString());
                    current = new StringBuilder();
                    continue;
                }
            }

            current.append(c);
        }

        if (current.length() > 0) {
            columns.add(current.toString());
        }

        return columns.toArray(new String[0]);
    }

    /**
     * 提取表选项（如CHARACTER SET UTF8等）
     */
    private static String extractTableOptions(String sql) {
        // 找到最后一个右括号的位置
        int lastParen = sql.lastIndexOf(')');
        if (lastParen >= 0 && lastParen < sql.length() - 1) {
            String afterParen = sql.substring(lastParen + 1).trim();
            // 移除分号
            if (afterParen.endsWith(";")) {
                afterParen = afterParen.substring(0, afterParen.length() - 1).trim();
            }
            if (!afterParen.isEmpty()) {
                return " " + afterParen;
            }
        }
        return null;
    }
}
