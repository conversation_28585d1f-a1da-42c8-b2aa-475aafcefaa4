package com.xylink.sqltranspiler;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.core.ast.DefaultStatement;
import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import com.xylink.sqltranspiler.core.context.TranspilationContext;
import com.xylink.sqltranspiler.core.context.TranspilationIssue;
import com.xylink.sqltranspiler.core.context.TranspilationResult;
import com.xylink.sqltranspiler.core.dialects.DialectRegistry;
import com.xylink.sqltranspiler.core.dialects.Generator;
import com.xylink.sqltranspiler.infrastructure.parser.EnhancedSqlSplitter;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import com.xylink.sqltranspiler.infrastructure.parser.PreprocessingResult;
import com.xylink.sqltranspiler.infrastructure.parser.Preprocessor;
import com.xylink.sqltranspiler.infrastructure.parser.SqlFileSplitter;
import com.xylink.sqltranspiler.infrastructure.parser.error.EnhancedParseErrorListener;

/**
 * 转换器顶层类，负责协调整个转换流程
 * 负责协调整个MySQL到目标方言的转换流程
 * 
 * 核心架构：解析 → 抽象 → 生成
 */
public class Transpiler {

    private static final Logger log = LoggerFactory.getLogger(Transpiler.class);
    private static final Logger issueLogger = LoggerFactory.getLogger("TRANSPILER_ISSUES");
    private static final Logger statementLogger = LoggerFactory.getLogger("STATEMENT_TRACKER");
    private final DialectRegistry dialectRegistry;
    private final com.xylink.sqltranspiler.core.validation.StrictSqlValidator strictValidator;

    public Transpiler() {
        this.dialectRegistry = new DialectRegistry();
        this.strictValidator = new com.xylink.sqltranspiler.core.validation.StrictSqlValidator();

        // 注册支持的方言
        initializeDialects();
    }

    /**
     * 设置达梦数据库的LENGTH_IN_CHAR配置
     *
     * @param lengthInChar true表示达梦环境配置了LENGTH_IN_CHAR=1（以字符为单位），
     *                     false表示LENGTH_IN_CHAR=0（以字节为单位，默认）
     */
    public void setDamengLengthInChar(boolean lengthInChar) {
        Generator damengGenerator = dialectRegistry.getGenerator("dameng");
        if (damengGenerator instanceof com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator) {
            ((com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator) damengGenerator)
                .setDamengLengthInChar(lengthInChar);
            log.info("Dameng LENGTH_IN_CHAR configuration updated: {} ({})",
                    lengthInChar, lengthInChar ? "character-based" : "byte-based");
        } else {
            log.warn("Dameng generator not found, LENGTH_IN_CHAR configuration not applied");
        }
    }

    /**
     * 获取达梦数据库的LENGTH_IN_CHAR配置
     */
    public boolean isDamengLengthInChar() {
        Generator damengGenerator = dialectRegistry.getGenerator("dameng");
        if (damengGenerator instanceof com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator) {
            return ((com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator) damengGenerator)
                .isDamengLengthInChar();
        }
        return true; // 默认值：字符模式，与MySQL保持一致
    }
    
    /**
     * 执行SQL转换 - 智能处理版本
     *
     * 核心原则：自动检测文件大小并选择最优处理策略
     * - 小文件：直接逐条处理
     * - 大文件：自动分割后逐条处理
     * - 预处理：清理注释和空行
     * - 逐条读取：按语句分割
     * - 逐条转换：每条语句单独处理
     * - 流式输出：转换完立即输出
     * - 错误隔离：单条失败不影响其他
     * - 完全透明：用户无感知
     *
     * @param sql 源SQL内容
     * @param sourceDialect 源方言 (目前支持 "mysql")
     * @param targetDialect 目标方言 ("dameng", "postgresql", "kingbase", "shentong")
     * @param preserveComments 是否保留COMMENT语句并转换为目标数据库的COMMENT ON语句
     * @return 转换结果，包含转换后的SQL和问题列表
     */
    public TranspilationResult transpile(String sql, String sourceDialect, String targetDialect, boolean preserveComments) {
        // 处理null或空SQL
        if (sql == null) {
            log.warn("Received null SQL, returning empty result");
            TranspilationContext context = new TranspilationContext("");
            return new TranspilationResult("", context.getIssues(), 0, 0);
        }

        TranspilationContext context = new TranspilationContext(sql);
        log.info("Starting transpilation process: {} -> {} (intelligent processing mode)", sourceDialect, targetDialect);
        log.info("Comment preservation: {}", preserveComments ? "enabled" : "disabled");

        try {
            // 验证源方言
            if (!"mysql".equalsIgnoreCase(sourceDialect)) {
                String errorMsg = "Unsupported source dialect: " + sourceDialect + ". Only 'mysql' is supported.";
                log.error(errorMsg);
                context.addIssue(new TranspilationIssue(
                    "ERR-001", TranspilationIssue.IssueLevel.ERROR, errorMsg, 0, 0));
                return new TranspilationResult("", context.getIssues(), 0, 1);
            }

            // 【强制性MySQL语法验证】根据 .augment/rules/rule-db.md 要求
            // 输入的SQL必须首先严格遵守MySQL官方规范，不满足MySQL规范的语句不做后续转换
            if ("mysql".equalsIgnoreCase(sourceDialect)) {
                log.info("开始强制性MySQL语法验证...");
                com.xylink.sqltranspiler.infrastructure.parser.error.MySqlSyntaxValidationResult mySqlValidation =
                    com.xylink.sqltranspiler.infrastructure.parser.error.ErrorPatternRegistry.validateMySqlSyntax(sql);

                if (!mySqlValidation.isValid()) {
                    log.error("MySQL语法验证失败，发现 {} 个违规，拒绝转换", mySqlValidation.getViolationCount());

                    // 添加所有违规作为错误
                    for (com.xylink.sqltranspiler.infrastructure.parser.error.MySqlSyntaxViolation violation : mySqlValidation.getViolations()) {
                        context.addIssue(new TranspilationIssue(
                            "MYSQL-SYNTAX-" + violation.getPatternId().toUpperCase(),
                            TranspilationIssue.IssueLevel.ERROR,
                            violation.getFriendlyMessage() + " - " + violation.getSuggestion(),
                            0, 0));
                    }

                    // 直接返回失败结果，不进行转换
                    String errorReport = mySqlValidation.generateErrorReport();
                    log.error("MySQL语法验证报告:\n{}", errorReport);
                    return new TranspilationResult("", context.getIssues(), 0, 1);
                } else {
                    log.info("MySQL语法验证通过");
                }
            }

            // 【修复StackOverflowError】对于大文件跳过严格验证，避免复杂正则表达式导致栈溢出
            if (sql.length() > 100000) { // 100KB阈值
                log.info("Large SQL detected ({} chars), skipping strict validation to avoid StackOverflowError", sql.length());
                context.addIssue(new TranspilationIssue(
                    "VAL-SKIP", TranspilationIssue.IssueLevel.INFO,
                    "Strict validation skipped for large SQL file to prevent StackOverflowError",
                    0, 0));
            } else {
                // 执行严格SQL验证
                log.info("Performing strict SQL validation before transpilation...");
                com.xylink.sqltranspiler.core.validation.StrictValidationResult validationResult =
                    strictValidator.validate(sql, sourceDialect, targetDialect);

                // 记录验证结果
                if (validationResult.hasErrors() || validationResult.hasWarnings()) {
                    log.warn("SQL validation found {} errors and {} warnings",
                        validationResult.getErrors().size(), validationResult.getWarnings().size());

                    // 输出验证报告到日志
                    String validationReport = validationResult.getConsoleOutput();
                    if (!validationReport.isEmpty()) {
                        log.warn("Validation Report:\n{}", validationReport);
                    }

                    // 将验证问题转换为转换问题
                    addValidationIssuesToContext(validationResult, context);

                    // 记录验证问题但不阻止转换，让用户看到问题并决定是否继续
                    if (validationResult.shouldBlockTranspilation()) {
                        log.warn("Validation found serious issues, but transpilation will continue. Please review the validation report.");
                    } else if (validationResult.hasErrors() || validationResult.hasWarnings()) {
                        log.info("Validation found some issues, but transpilation will continue. Please review the validation report.");
                    }
                } else {
                    log.info("SQL validation passed without issues.");
                }
            }

            // 自动检测是否需要分割处理
            if (needsChunkedProcessing(sql)) {
                log.info("Large SQL detected, using intelligent chunked processing");
                return transpileWithChunking(sql, targetDialect, context, preserveComments);
            } else {
                log.info("Regular size SQL, using direct processing");
                return transpileSingleStatement(sql, targetDialect, context, preserveComments);
            }

        } catch (Exception e) {
            String errorMsg = "A fatal error occurred during transpilation: " + e.getMessage();
            log.error(errorMsg, e);
            context.addIssue(new TranspilationIssue(
                "ERR-003", TranspilationIssue.IssueLevel.ERROR, errorMsg, 0, 0));
            return new TranspilationResult("", context.getIssues(), 0, 1);
        }
    }

    /**
     * 执行SQL转换 - 向后兼容的重载方法
     * 默认保留COMMENT语句
     *
     * @param sql 源SQL内容
     * @param sourceDialect 源方言 (目前支持 "mysql")
     * @param targetDialect 目标方言 ("dameng", "postgresql")
     * @return 转换结果，包含转换后的SQL和问题列表
     */
    public TranspilationResult transpile(String sql, String sourceDialect, String targetDialect) {
        return transpile(sql, sourceDialect, targetDialect, true);
    }
    
    /**
     * 获取支持的目标方言列表
     */
    public List<String> getSupportedTargetDialects() {
        return dialectRegistry.getSupportedDialects();
    }

    /**
     * 检查是否支持指定的目标方言
     */
    public boolean isTargetDialectSupported(String dialect) {
        return dialectRegistry.isSupported(dialect);
    }

    /**
     * 将验证问题转换为转换问题并添加到上下文中
     */
    private void addValidationIssuesToContext(com.xylink.sqltranspiler.core.validation.StrictValidationResult validationResult,
                                            TranspilationContext context) {
        // 添加错误
        for (com.xylink.sqltranspiler.core.validation.StrictValidationResult.ValidationIssue error : validationResult.getErrors()) {
            context.addIssue(new TranspilationIssue(
                "VAL-ERR", TranspilationIssue.IssueLevel.ERROR,
                "SQL Validation Error: " + error.getMessage(),
                error.getLineNumber(), error.getColumnNumber()));
        }

        // 添加警告
        for (com.xylink.sqltranspiler.core.validation.StrictValidationResult.ValidationIssue warning : validationResult.getWarnings()) {
            context.addIssue(new TranspilationIssue(
                "VAL-WARN", TranspilationIssue.IssueLevel.WARN,
                "SQL Validation Warning: " + warning.getMessage(),
                warning.getLineNumber(), warning.getColumnNumber()));
        }

        // 添加建议（作为信息级别）
        for (com.xylink.sqltranspiler.core.validation.StrictValidationResult.ValidationIssue suggestion : validationResult.getSuggestions()) {
            context.addIssue(new TranspilationIssue(
                "VAL-INFO", TranspilationIssue.IssueLevel.INFO,
                "SQL Validation Suggestion: " + suggestion.getMessage(),
                suggestion.getLineNumber(), suggestion.getColumnNumber()));
        }
    }

    /**
     * 获取方言注册表实例
     * 主要用于CLI获取方言信息
     */
    public DialectRegistry getDialectRegistry() {
        return dialectRegistry;
    }
    
    /**
     * 初始化支持的方言
     */
    private void initializeDialects() {
        dialectRegistry.registerDialects();
    }

    /**
     * 检查SQL是否需要分块处理
     *
     * 基于以下指标判断：
     * 1. 文件大小（字节数）
     * 2. 行数
     * 3. 语句数量估算
     */
    private boolean needsChunkedProcessing(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return false;
        }

        // 检查文件大小 - 超过256KB就需要分块（更保守的阈值）
        long sizeInBytes = sql.getBytes().length;
        if (sizeInBytes > 256 * 1024) {
            log.info("SQL size {} bytes exceeds threshold, needs chunking", sizeInBytes);
            return true;
        }

        // 检查行数 - 超过500行就需要分块（更保守的阈值）
        int lineCount = sql.split("\\r?\\n").length;
        if (lineCount > 500) {
            log.info("SQL line count {} exceeds threshold, needs chunking", lineCount);
            return true;
        }

        // 估算语句数量 - 简单计算分号数量（更保守的阈值）
        long statementCount = sql.chars().filter(ch -> ch == ';').count();
        if (statementCount > 200) {
            log.info("Estimated statement count {} exceeds threshold, needs chunking", statementCount);
            return true;
        }

        return false;
    }

    /**
     * 使用分块策略进行转换
     *
     * 将大SQL分割为多个小块，逐块处理，最后合并结果
     */
    private TranspilationResult transpileWithChunking(String sql, String targetDialect,
                                                     TranspilationContext context, boolean preserveComments) {
        log.info("Starting chunked transpilation process");

        try {
            // 创建分割器
            SqlFileSplitter splitter = new SqlFileSplitter();

            // 分割SQL内容
            List<SqlFileSplitter.SqlChunk> chunks = splitter.splitContent(sql);
            log.info("SQL split into {} chunks for processing", chunks.size());

            // 处理每个分块
            StringBuilder combinedResult = new StringBuilder();
            int totalSuccess = 0;
            int totalFailure = 0;

            for (int i = 0; i < chunks.size(); i++) {
                SqlFileSplitter.SqlChunk chunk = chunks.get(i);
                log.info("Processing chunk {}/{} ({} statements, {} lines)",
                    i + 1, chunks.size(), chunk.statementCount, chunk.lineCount);

                try {
                    // 为每个分块创建新的上下文
                    TranspilationContext chunkContext = new TranspilationContext(chunk.content);

                    // 处理当前分块
                    TranspilationResult chunkResult = transpileSingleStatement(
                        chunk.content, targetDialect, chunkContext, preserveComments);

                    // 合并结果
                    if (i > 0) {
                        combinedResult.append("\n");
                    }
                    combinedResult.append("-- ========== 分块 ").append(i + 1).append("/").append(chunks.size())
                                 .append(" 转换结果 ==========\n");
                    combinedResult.append(chunkResult.translatedSql());
                    if (!chunkResult.translatedSql().endsWith("\n")) {
                        combinedResult.append("\n");
                    }

                    totalSuccess += chunkResult.successCount();
                    totalFailure += chunkResult.failureCount();

                    // 合并问题列表
                    for (TranspilationIssue issue : chunkResult.issues()) {
                        context.addIssue(issue);
                    }

                    log.info("Chunk {}/{} completed: {} succeeded, {} failed",
                        i + 1, chunks.size(), chunkResult.successCount(), chunkResult.failureCount());

                } catch (Exception e) {
                    log.error("Failed to process chunk {}/{}: {}", i + 1, chunks.size(), e.getMessage(), e);
                    totalFailure++;
                    context.addIssue(new TranspilationIssue(
                        "ERR-CHUNK-" + (i + 1), TranspilationIssue.IssueLevel.ERROR,
                        "Chunk processing failed: " + e.getMessage(), 0, 0));
                }
            }

            log.info("Chunked transpilation completed: {} total succeeded, {} total failed",
                totalSuccess, totalFailure);

            return new TranspilationResult(combinedResult.toString(), context.getIssues(),
                totalSuccess, totalFailure);

        } catch (Exception e) {
            String errorMsg = "Chunked transpilation failed: " + e.getMessage();
            log.error(errorMsg, e);
            context.addIssue(new TranspilationIssue(
                "ERR-CHUNK", TranspilationIssue.IssueLevel.ERROR, errorMsg, 0, 0));
            return new TranspilationResult("", context.getIssues(), 0, 1);
        }
    }

    /**
     * 逐条处理转换方法
     *
     * 核心流程：
     * 1. 预处理：清理注释和空行
     * 2. 分割语句：按分号精确分割
     * 3. 逐条处理：每条语句单独解析和转换
     * 4. 流式输出：转换完立即输出
     *
     * 优势：
     * - 强制逐条处理，避免批量问题
     * - 最大错误隔离
     * - 内存使用最小
     * - 完全透明
     */
    private TranspilationResult transpileSingleStatement(String sql, String targetDialect, TranspilationContext context, boolean preserveComments) {
        Generator generator = dialectRegistry.getGenerator(targetDialect);

        // 配置generator的preserveComments设置（如果支持）
        if (generator instanceof com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator) {
            ((com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator) generator).setPreserveComments(preserveComments);
        }

        StringBuilder result = new StringBuilder();
        int successCount = 0;
        int failureCount = 0;

        // 详细统计信息（使用数组以便在方法中修改）
        int[] statementCounts = new int[8]; // [lock, unlock, set, create, insert, update, delete, other]

        // 收集CREATE TABLE语句用于后续外键约束生成
        List<CreateTable> createTableStatements = new ArrayList<>();

        try {
            // 1. 先进行语句分割，避免预处理阶段破坏语句边界
            log.info("Splitting SQL into individual statements before preprocessing...");
            List<String> sqlStatements = splitSqlStatementsAccurately(sql);
            log.info("Found {} statements to process individually.", sqlStatements.size());

            // 2. 对每个语句单独进行预处理
            log.info("Starting preprocessing phase for each statement...");
            List<String> preprocessedStatements = new ArrayList<>();
            for (int i = 0; i < sqlStatements.size(); i++) {
                final int statementIndex = i + 1; // 创建final变量
                String statement = sqlStatements.get(i);
                log.debug("Preprocessing statement {}/{}: {}", statementIndex, sqlStatements.size(),
                    statement.length() > 100 ? statement.substring(0, 100) + "..." : statement);

                PreprocessingResult preprocessResult = Preprocessor.preprocess(statement, preserveComments, targetDialect);
                preprocessedStatements.add(preprocessResult.cleanedSql());

                // 记录预处理日志
                preprocessResult.logs().forEach(logMessage -> log.debug("Preprocessing[{}]: {}", statementIndex, logMessage));
            }

            // 使用预处理后的语句列表
            sqlStatements = preprocessedStatements;

            // 记录原始文件统计信息
            printOriginalFileStatistics(sql, sqlStatements.size());

            // 3. 逐条处理每个语句
            for (int i = 0; i < sqlStatements.size(); i++) {
                String singleSql = sqlStatements.get(i).trim();
                if (singleSql.isEmpty()) {
                    continue;
                }

                int statementNumber = i + 1;
                String statementId = String.format("[%d/%d]", statementNumber, sqlStatements.size());

                try {
                    log.info("{} Processing single statement...", statementId);
                    log.debug("{} SQL: {}", statementId, singleSql);

                    // 单独解析这一条语句
                    Statement statement = MySqlHelper.parseStatement(singleSql);
                    String statementType = statement.getClass().getSimpleName();

                    log.debug("{} Parsed as: {}", statementId, statementType);

                    // 转换语句
                    String convertedSql = generator.generate(statement);

                    if (statement instanceof DefaultStatement) {
                        issueLogger.warn("[UNSUPPORTED] Statement type '{}' was not specifically handled. Original SQL: {}",
                            statement.getClass().getSimpleName(), singleSql);
                    }

                    if (convertedSql != null && !convertedSql.trim().isEmpty()) {
                        // 立即输出转换结果
                        result.append(convertedSql);
                        if (!convertedSql.endsWith("\n")) {
                            result.append("\n");
                        }

                        log.info("{} Single statement transpilation SUCCEEDED.", statementId);

                        // 详细的语句级别日志记录
                        logStatementConversion(statementNumber, singleSql, convertedSql, statement);

                        successCount++;

                        // 收集CREATE TABLE语句
                        if (statement instanceof CreateTable) {
                            createTableStatements.add((CreateTable) statement);
                        }

                        // 收集详细统计信息
                        collectStatementStatistics(statement, statementCounts);
                    } else {
                        log.warn("{} Statement generated empty or null result, skipping.", statementId);
                        context.addIssue(new TranspilationIssue(
                            "WARN-001", TranspilationIssue.IssueLevel.WARN,
                            "Statement " + statementNumber + " generated empty result and was skipped.",
                            0, 0));
                        // 空结果是警告，不应该计入失败数量，但要添加注释说明
                        result.append("-- [SKIPPED] Statement ").append(statementNumber)
                              .append(" generated empty result and was skipped\n");
                    }

                } catch (Exception e) {
                    // 检查是否是增强的解析异常
                    if (e instanceof EnhancedParseErrorListener.EnhancedParseException) {
                        EnhancedParseErrorListener.EnhancedParseException enhancedException =
                            (EnhancedParseErrorListener.EnhancedParseException) e;

                        log.error("{} SQL语法错误: {}", statementId, enhancedException.getShortMessage());

                        // 详细的错误信息
                        String detailedError = String.format("语句 #%d 语法错误: %s",
                            statementNumber, enhancedException.getErrorInfo().getFriendlyMessage());

                        context.addIssue(new TranspilationIssue("ERR-SYNTAX-01", TranspilationIssue.IssueLevel.ERROR,
                            detailedError, enhancedException.getErrorInfo().getLine(), enhancedException.getErrorInfo().getPosition()));

                        // 保留失败的语句作为注释，并添加错误说明
                        result.append("-- [语法错误] ").append(enhancedException.getErrorInfo().getFriendlyMessage()).append("\n");
                        if (enhancedException.getErrorInfo().getSuggestion() != null) {
                            result.append("-- [修复建议] ").append(enhancedException.getErrorInfo().getSuggestion()).append("\n");
                        }
                        String cleanedOriginalSql = removeBackticksFromErrorComment(singleSql);
                        result.append("-- [原始SQL] ").append(cleanedOriginalSql.replace("\n", "\n-- ")).append("\n\n");

                        // 如果有修复建议，也添加到注释中
                        if (enhancedException.isAutoFixable() && enhancedException.getSuggestedFix() != null) {
                            String cleanedSuggestedFix = removeBackticksFromErrorComment(enhancedException.getSuggestedFix());
                            result.append("-- [建议的修复SQL] ").append(cleanedSuggestedFix.replace("\n", "\n-- ")).append("\n\n");
                        }

                    } else {
                        log.error("{} Single statement transpilation FAILED. Reason: {}", statementId, e.getMessage(), e);

                        // 详细的失败日志记录
                        logStatementFailure(statementNumber, singleSql, e);

                        context.addIssue(new TranspilationIssue("ERR-SINGLE-01", TranspilationIssue.IssueLevel.ERROR,
                            "Failed to transpile single statement #" + statementNumber + ": " + e.getMessage(), 0, 0));

                        // 保留失败的语句作为注释，并移除反引号标识符
                        result.append("-- [CONVERSION ERROR] The following statement failed to be transpiled:\n");
                        String cleanedSql = removeBackticksFromErrorComment(singleSql);
                        result.append("-- ").append(cleanedSql.replace("\n", "\n-- ")).append("\n\n");
                    }

                    failureCount++;
                }
            }

            // 4. 生成外键约束（达梦、金仓、神通数据库都原生支持FOREIGN KEY约束）
            if ("dameng".equalsIgnoreCase(targetDialect) &&
                generator instanceof com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator) {

                log.info("Generating foreign key constraints for Dameng dialect...");
                com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator damengGenerator =
                    (com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator) generator;

                for (CreateTable createTable : createTableStatements) {
                    try {
                        String foreignKeyConstraints = damengGenerator.generateForeignKeyConstraints(createTable);

                        if (foreignKeyConstraints != null && !foreignKeyConstraints.trim().isEmpty()) {
                            result.append("\n").append(foreignKeyConstraints);
                            if (!foreignKeyConstraints.endsWith("\n")) {
                                result.append("\n");
                            }
                            log.debug("Added foreign key constraints for table {}", createTable.getTableId().getTableName());
                        }
                    } catch (Exception e) {
                        log.error("Failed to generate foreign key constraints for table {}: {}",
                            createTable.getTableId().getTableName(), e.getMessage(), e);
                        context.addIssue(new TranspilationIssue(
                            "ERR-FK-01", TranspilationIssue.IssueLevel.ERROR,
                            "Failed to generate foreign key constraints for table " + createTable.getTableId().getTableName() + ": " + e.getMessage(),
                            0, 0));
                        failureCount++;
                    }
                }
            }
            // 金仓数据库外键约束生成 - 根据金仓官方文档，金仓原生支持FOREIGN KEY约束
            else if ("kingbase".equalsIgnoreCase(targetDialect) &&
                     generator instanceof com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator) {

                log.info("Generating foreign key constraints for KingbaseES dialect...");
                com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator kingbaseGenerator =
                    (com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator) generator;

                for (CreateTable createTable : createTableStatements) {
                    try {
                        String foreignKeyConstraints = kingbaseGenerator.generateForeignKeyConstraints(createTable);

                        if (foreignKeyConstraints != null && !foreignKeyConstraints.trim().isEmpty()) {
                            result.append("\n").append(foreignKeyConstraints);
                            if (!foreignKeyConstraints.endsWith("\n")) {
                                result.append("\n");
                            }
                            log.debug("Added foreign key constraints for table {}", createTable.getTableId().getTableName());
                        }
                    } catch (Exception e) {
                        log.error("Failed to generate foreign key constraints for table {}: {}",
                            createTable.getTableId().getTableName(), e.getMessage(), e);
                        context.addIssue(new TranspilationIssue(
                            "ERR-FK-02", TranspilationIssue.IssueLevel.ERROR,
                            "Failed to generate foreign key constraints for table " + createTable.getTableId().getTableName() + ": " + e.getMessage(),
                            0, 0));
                        failureCount++;
                    }
                }
            }
            // 神通数据库外键约束生成 - 根据神通官方文档，神通原生支持FOREIGN KEY约束
            else if ("shentong".equalsIgnoreCase(targetDialect) &&
                     generator instanceof com.xylink.sqltranspiler.core.dialects.shentong.ShentongGenerator) {

                log.info("Generating foreign key constraints for Shentong dialect...");
                com.xylink.sqltranspiler.core.dialects.shentong.ShentongGenerator shentongGenerator =
                    (com.xylink.sqltranspiler.core.dialects.shentong.ShentongGenerator) generator;

                for (CreateTable createTable : createTableStatements) {
                    try {
                        String foreignKeyConstraints = shentongGenerator.generateForeignKeyConstraints(createTable);

                        if (foreignKeyConstraints != null && !foreignKeyConstraints.trim().isEmpty()) {
                            result.append("\n").append(foreignKeyConstraints);
                            if (!foreignKeyConstraints.endsWith("\n")) {
                                result.append("\n");
                            }
                            log.debug("Added foreign key constraints for table {}", createTable.getTableId().getTableName());
                        }
                    } catch (Exception e) {
                        log.error("Failed to generate foreign key constraints for table {}: {}",
                            createTable.getTableId().getTableName(), e.getMessage(), e);
                        context.addIssue(new TranspilationIssue(
                            "ERR-FK-03", TranspilationIssue.IssueLevel.ERROR,
                            "Failed to generate foreign key constraints for table " + createTable.getTableId().getTableName() + ": " + e.getMessage(),
                            0, 0));
                        failureCount++;
                    }
                }
            }

            // 打印详细统计信息
            printDetailedStatistics(sqlStatements.size(), successCount, failureCount, statementCounts, generator);

            log.info("Single-statement conversion completed. Success: {}, Failed: {}", successCount, failureCount);

            // 收集所有预处理日志
            List<String> allPreprocessingLogs = new ArrayList<>();
            allPreprocessingLogs.add("PREPROCESSING: Processed " + sqlStatements.size() + " statements individually");

            return new TranspilationResult(result.toString(), context.getIssues(), successCount, failureCount, allPreprocessingLogs);

        } catch (Exception e) {
            log.error("A fatal error occurred during single-statement processing: {}", e.getMessage(), e);
            context.addIssue(new TranspilationIssue(
                "ERR-SINGLE-02", TranspilationIssue.IssueLevel.ERROR,
                "Single-statement transpilation failed: " + e.getMessage(), 0, 0));
            return new TranspilationResult(result.toString(), context.getIssues(), successCount, failureCount + 1);
        }
    }

    /**
     * 精确分割SQL语句
     *
     * 解决问题：
     * 1. 预处理器过度处理：在分割阶段不进行预处理
     * 2. SQL分割精度：正确处理字符串、注释中的分号
     * 3. 复杂语句支持：保持多行语句的完整性
     * 4. ANTLR解析器限制：当ANTLR无法正确解析复杂SQL时，使用语义分割
     *
     * 根据MySQL 8.4官方文档，优先使用语义分号分割器，因为它能正确处理复杂的CTE语句
     */
    private List<String> splitSqlStatementsAccurately(String sql) {
        log.debug("Splitting SQL into statements: {} characters", sql.length());

        // 优先使用增强的SQL分割器（处理BEGIN...END块和DELIMITER语法）
        try {
            List<String> enhancedSplit = EnhancedSqlSplitter.splitSql(sql);
            if (enhancedSplit != null && !enhancedSplit.isEmpty()) {
                log.info("Enhanced SQL splitter found {} statements", enhancedSplit.size());
                return enhancedSplit;
            }
        } catch (Exception e) {
            log.warn("Enhanced SQL splitter failed: {}", e.getMessage());
        }

        // 首先检查SQL中的分号数量
        long semicolonCount = sql.chars().filter(ch -> ch == ';').count();

        // 如果SQL中有多个分号，直接使用简单分号分割作为主要方法
        // 根据MySQL 8.4官方文档，分号是SQL语句的标准分隔符
        if (semicolonCount > 1) {
            log.info("SQL contains {} semicolons, using simple semicolon split for reliable multi-statement processing",
                semicolonCount);

            List<String> simpleSplit = splitBySimpleSemicolon(sql);
            if (simpleSplit.size() > 1) {
                log.info("Simple semicolon split found {} statements, using it for multi-statement SQL",
                    simpleSplit.size());
                return simpleSplit;
            }

            // 如果简单分割失败，尝试语义分割器
            List<String> semanticSplit = splitBySemanticSemicolon(sql);
            log.debug("Simple split failed, semantic splitter found {} statements", semanticSplit.size());

            if (semanticSplit.size() > 1) {
                log.debug("Used semantic semicolon splitter as fallback, found {} statements",
                    semanticSplit.size());
                return semanticSplit;
            }

            // 如果两种分割都失败，尝试ANTLR分割
            try {
                List<String> antlrSplit = MySqlHelper.splitSql(sql);
                if (antlrSplit != null && !antlrSplit.isEmpty()) {
                    // 如果ANTLR只找到1个语句，但SQL中有多个分号，强制使用简单分割
                    if (antlrSplit.size() == 1 && semicolonCount > 1) {
                        log.warn("ANTLR found only {} statements but SQL contains {} semicolons. " +
                                "This indicates ANTLR cannot parse complex SQL correctly. " +
                                "Forcing simple semicolon split according to MySQL 8.4 documentation.",
                                antlrSplit.size(), semicolonCount);
                        return simpleSplit; // 强制使用简单分割
                    }
                    return antlrSplit;
                }
            } catch (Exception e) {
                log.warn("MySqlHelper.splitSql failed for multi-semicolon SQL: {}", e.getMessage());
            }

            // 最后的备用方案：返回简单分割的结果
            log.info("Using simple semicolon split as final fallback for multi-semicolon SQL");
            return simpleSplit;
        }

        // 对于单分号或无分号的SQL，尝试ANTLR分割
        try {
            List<String> antlrSplit = MySqlHelper.splitSql(sql);
            if (antlrSplit != null && !antlrSplit.isEmpty()) {
                log.debug("ANTLR found {} statements", antlrSplit.size());
                return antlrSplit;
            }
        } catch (Exception e) {
            log.warn("MySqlHelper.splitSql failed: {}", e.getMessage());
        }

        // 最后的备用方案：使用语义分割
        List<String> semanticSplit = splitBySemanticSemicolon(sql);
        log.debug("Using semantic semicolon splitter as final fallback: {} statements", semanticSplit.size());
        return semanticSplit;
    }

    /**
     * 按语义分号分割（避免字符串和注释中的分号）
     */
    private List<String> splitBySemanticSemicolon(String sql) {
        List<String> statements = new ArrayList<>();
        StringBuilder currentStatement = new StringBuilder();

        boolean inSingleQuote = false;
        boolean inDoubleQuote = false;
        boolean inBacktick = false;
        boolean inLineComment = false;
        boolean inBlockComment = false;

        char[] chars = sql.toCharArray();

        for (int i = 0; i < chars.length; i++) {
            char c = chars[i];
            char next = (i + 1 < chars.length) ? chars[i + 1] : '\0';

            // 处理行注释
            if (!inSingleQuote && !inDoubleQuote && !inBacktick && !inBlockComment) {
                if (c == '-' && next == '-') {
                    inLineComment = true;
                    currentStatement.append(c);
                    continue;
                }
            }

            if (inLineComment) {
                currentStatement.append(c);
                if (c == '\n' || c == '\r') {
                    inLineComment = false;
                }
                continue;
            }

            // 处理块注释
            if (!inSingleQuote && !inDoubleQuote && !inBacktick && !inLineComment) {
                if (c == '/' && next == '*') {
                    inBlockComment = true;
                    currentStatement.append(c);
                    continue;
                }
            }

            if (inBlockComment) {
                currentStatement.append(c);
                if (c == '*' && next == '/') {
                    currentStatement.append(next);
                    i++; // 跳过下一个字符
                    inBlockComment = false;
                }
                continue;
            }

            // 处理字符串
            if (!inLineComment && !inBlockComment) {
                if (c == '\'' && !inDoubleQuote && !inBacktick) {
                    inSingleQuote = !inSingleQuote;
                } else if (c == '"' && !inSingleQuote && !inBacktick) {
                    inDoubleQuote = !inDoubleQuote;
                } else if (c == '`' && !inSingleQuote && !inDoubleQuote) {
                    inBacktick = !inBacktick;
                }
            }

            // 处理分号
            if (c == ';' && !inSingleQuote && !inDoubleQuote && !inBacktick && !inLineComment && !inBlockComment) {
                currentStatement.append(c);
                String stmt = currentStatement.toString().trim();
                if (!stmt.isEmpty()) {
                    statements.add(stmt);
                }
                currentStatement = new StringBuilder();
                continue;
            }

            currentStatement.append(c);
        }

        // 添加最后一个语句
        String lastStmt = currentStatement.toString().trim();
        if (!lastStmt.isEmpty()) {
            statements.add(lastStmt);
        }

        log.debug("Semantic semicolon split found {} statements", statements.size());
        return statements;
    }

    /**
     * 简单的分号分割方法
     * 用于处理复杂SQL的备用分割方案
     * 根据MySQL 8.4官方文档，分号是SQL语句的标准分隔符
     */
    private List<String> splitBySimpleSemicolon(String sql) {
        List<String> statements = new ArrayList<>();

        // 按分号分割，但保留分号
        String[] parts = sql.split(";");

        for (int i = 0; i < parts.length; i++) {
            String part = parts[i].trim();
            if (!part.isEmpty()) {
                // 为非最后一个部分添加分号
                if (i < parts.length - 1) {
                    part += ";";
                }
                statements.add(part);
            }
        }

        return statements;
    }

    /**
     * 收集语句统计信息
     * statementCounts数组索引：[0]lock, [1]unlock, [2]set, [3]create, [4]insert, [5]update, [6]delete, [7]other
     */
    private void collectStatementStatistics(Statement statement, int[] statementCounts) {
        if (statement == null) {
            return;
        }

        String className = statement.getClass().getSimpleName();

        switch (className) {
            case "LockTables":
                statementCounts[0]++;
                break;
            case "UnlockTables":
                statementCounts[1]++;
                break;
            case "SetStatement":
                statementCounts[2]++;
                break;
            case "CreateTable":
                statementCounts[3]++;
                break;
            case "InsertTable":
                statementCounts[4]++;
                break;
            case "UpdateTable":
                statementCounts[5]++;
                break;
            case "DeleteTable":
                statementCounts[6]++;
                break;
            default:
                statementCounts[7]++;
                break;
        }
    }

    /**
     * 打印详细的转换统计信息
     */
    private void printDetailedStatistics(int totalStatements, int successCount, int failureCount, int[] statementCounts, Generator generator) {
        log.info("==================== DETAILED CONVERSION STATISTICS ====================");
        log.info("Total SQL statements found: {}", totalStatements);
        log.info("Successfully converted: {} ({}%)", successCount,
            totalStatements > 0 ? String.format("%.1f", (double)successCount * 100 / totalStatements) : "0.0");
        log.info("Failed to convert: {} ({}%)", failureCount,
            totalStatements > 0 ? String.format("%.1f", (double)failureCount * 100 / totalStatements) : "0.0");

        log.info("--------------------------- STATEMENT BREAKDOWN ---------------------------");
        log.info("LOCK TABLES statements: {} (converted to comments)", statementCounts[0]);
        log.info("UNLOCK TABLES statements: {} (converted to comments)", statementCounts[1]);
        log.info("SET statements: {}", statementCounts[2]);
        log.info("CREATE TABLE statements: {}", statementCounts[3]);
        log.info("INSERT statements: {}", statementCounts[4]);
        log.info("UPDATE statements: {}", statementCounts[5]);
        log.info("DELETE statements: {}", statementCounts[6]);
        log.info("Other statements: {}", statementCounts[7]);

        int transactionStatements = statementCounts[0] + statementCounts[1];
        int businessStatements = successCount - transactionStatements;

        log.info("---------------------------- SUMMARY ANALYSIS -----------------------------");
        log.info("Transaction control statements (LOCK/UNLOCK): {} (handled as comments)", transactionStatements);
        log.info("Business logic statements: {} (fully converted)", businessStatements);

        if (failureCount > 0) {
            log.warn("ATTENTION: {} statements failed conversion - check logs above for details", failureCount);
        }

        if (transactionStatements > 0) {
            log.info("NOTE: LOCK/UNLOCK TABLES are MySQL-specific and not supported in Dameng");
            log.info("      These have been converted to comments for reference");
        }

        // 显示VARCHAR长度调整统计
        if (generator instanceof com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator) {
            com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator damengGenerator =
                (com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator) generator;

            java.util.List<com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator.VarcharAdjustment> adjustments =
                damengGenerator.getVarcharAdjustments();

            if (!adjustments.isEmpty()) {
                log.info("----------------------- VARCHAR LENGTH ADJUSTMENTS ------------------------");
                log.info("Total VARCHAR columns adjusted: {}", adjustments.size());
                log.info("REASON: These adjustments prevent data truncation errors in Dameng database");

                for (com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator.VarcharAdjustment adj : adjustments) {
                    log.info("  • Table '{}', Column '{}': {} → {} characters",
                            adj.tableName, adj.columnName, adj.originalLength, adj.adjustedLength);
                    log.info("    Reason: {}", adj.reason);
                }
                log.info("------------------------------------------------------------------------");
            }
        }

        log.info("========================================================================");
    }

    /**
     * 打印原始文件统计信息
     */
    private void printOriginalFileStatistics(String originalSql, int totalStatements) {
        int originalLines = originalSql.split("\\r?\\n").length;
        int originalSemicolons = countSemicolons(originalSql);
        int originalLockTables = countPattern(originalSql, "LOCK\\s+TABLES?\\s+");
        int originalUnlockTables = countPattern(originalSql, "UNLOCK\\s+TABLES?");

        log.info("======================= ORIGINAL FILE ANALYSIS =======================");
        log.info("Original file total lines: {}", originalLines);
        log.info("Original file semicolons found: {}", originalSemicolons);
        log.info("Statements identified for processing: {}", totalStatements);
        log.info("LOCK TABLES statements found: {}", originalLockTables);
        log.info("UNLOCK TABLES statements found: {}", originalUnlockTables);

        if (originalSemicolons != totalStatements) {
            int difference = originalSemicolons - totalStatements;
            log.info("Note: {} semicolons were in comments/strings and excluded from processing", difference);
        }

        log.info("======================================================================");
    }

    /**
     * 计算字符串中分号的数量
     */
    private int countSemicolons(String text) {
        if (text == null) return 0;
        return (int) text.chars().filter(ch -> ch == ';').count();
    }

    /**
     * 计算匹配正则表达式的数量
     */
    private int countPattern(String text, String pattern) {
        if (text == null) return 0;
        java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern, java.util.regex.Pattern.CASE_INSENSITIVE);
        java.util.regex.Matcher m = p.matcher(text);
        int count = 0;
        while (m.find()) {
            count++;
        }
        return count;
    }

    /**
     * 记录成功转换的语句详细信息
     */
    private void logStatementConversion(int statementNumber, String originalSql, String convertedSql, Statement statement) {
        String statementType = statement != null ? statement.getClass().getSimpleName() : "Unknown";

        // 判断是否为不支持的语句（转换为注释）
        if (isUnsupportedStatement(statement, convertedSql)) {
            // 不支持的语句日志
            statementLogger.warn("[UNSUPPORTED] [{}] CONVERTED TO COMMENT:", statementNumber);
            statementLogger.warn("[UNSUPPORTED] ORIGINAL SQL: {}", cleanSqlForLogging(originalSql));
            statementLogger.warn("[UNSUPPORTED] REASON: {} statements are not supported in target database", statementType);
            statementLogger.warn("[UNSUPPORTED] CONVERTED TO: {}", cleanSqlForLogging(convertedSql));
        } else {
            // 成功转换的语句日志（DEBUG级别）
            statementLogger.debug("[{}] ORIGINAL: {}", statementNumber, cleanSqlForLogging(originalSql));
            statementLogger.debug("[{}] CONVERTED: {}", statementNumber, cleanSqlForLogging(convertedSql));
            statementLogger.debug("[{}] STATEMENT TYPE: {}", statementNumber, statementType);
        }
    }

    /**
     * 记录转换失败的语句详细信息
     */
    private void logStatementFailure(int statementNumber, String originalSql, Exception e) {
        statementLogger.error("[ERROR] [{}] FAILED CONVERSION:", statementNumber);
        statementLogger.error("[ERROR] ORIGINAL SQL: {}", cleanSqlForLogging(originalSql));
        statementLogger.error("[ERROR] FAILURE REASON: {}", e.getMessage());
        statementLogger.error("[ERROR] EXCEPTION TYPE: {}", e.getClass().getSimpleName());

        // 如果有更详细的错误信息，也记录下来
        if (e.getCause() != null) {
            statementLogger.error("[ERROR] ROOT CAUSE: {}", e.getCause().getMessage());
        }
    }

    /**
     * 判断是否为不支持的语句（被转换为注释）
     */
    private boolean isUnsupportedStatement(Statement statement, String convertedSql) {
        if (statement == null || convertedSql == null) {
            return false;
        }

        String statementType = statement.getClass().getSimpleName();
        return ("LockTables".equals(statementType) || "UnlockTables".equals(statementType))
               && convertedSql.trim().startsWith("--");
    }

    /**
     * 清理SQL用于日志记录（移除多余的空白和换行）
     */
    private String cleanSqlForLogging(String sql) {
        if (sql == null) {
            return "null";
        }

        // 将多行SQL压缩为单行，便于日志查看
        return sql.replaceAll("\\s+", " ").trim();
    }

    /**
     * 从错误注释中移除反引号标识符
     * 这样可以确保错误注释不包含MySQL特有的反引号语法
     */
    private String removeBackticksFromErrorComment(String sql) {
        if (sql == null) {
            return null;
        }

        // 简单地移除所有反引号，因为这只是用于错误注释
        // 不需要复杂的解析逻辑，只要确保不包含反引号即可
        return sql.replaceAll("`", "");
    }
}
