package com.xylink.sqltranspiler.application.cli;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.Callable;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.Transpiler;
import com.xylink.sqltranspiler.core.context.TranspilationIssue;
import com.xylink.sqltranspiler.core.context.TranspilationResult;

import ch.qos.logback.classic.Level;
import picocli.CommandLine;
import picocli.CommandLine.Command;
import picocli.CommandLine.Option;
import picocli.CommandLine.Parameters;

/**
 * 目标方言候选项提供器
 * 用于 picocli 自动补全功能
 */
class TargetDialectCandidates implements Iterable<String> {
    private final Transpiler transpiler = new Transpiler();

    @Override
    public java.util.Iterator<String> iterator() {
        return transpiler.getSupportedTargetDialects().iterator();
    }
}

/**
 * SQL转换工具的命令行接口
 * 使用Picocli框架提供健壮的命令行参数处理
 */
@Command(
    name = "sql-transpiler",
    description = "Convert SQL between different database dialects",
    version = "1.0.0",
    mixinStandardHelpOptions = true,
    showDefaultValues = true
)
public class SqlTranspilerCli implements Callable<Integer> {
    
    private static final Logger log = LoggerFactory.getLogger(SqlTranspilerCli.class);
    
    @Option(
        names = {"-s", "--source"}, 
        description = "Source dialect (currently only 'mysql' is supported)",
        defaultValue = "mysql"
    )
    private String sourceDialect;
    
    @Option(
        names = {"-t", "--target"},
        description = "Target dialect. Supported: dameng/dm, kingbase/kingbasees/kes",
        completionCandidates = TargetDialectCandidates.class
    )
    private String targetDialect;
    
    @Option(
        names = {"-i", "--input"},
        description = "Input SQL file or directory path. If directory, all .sql files will be processed automatically"
    )
    private String inputFile;

    @Option(
        names = {"-o", "--output"},
        description = "Output SQL file path (for single file) or directory path (for batch processing). If not specified, output to console or 'converted' subdirectory"
    )
    private String outputFile;
    
    @Option(
        names = {"-v", "--verbose"}, 
        description = "Enable verbose output (show all logs and warnings)"
    )
    private boolean verbose;
    
    @Option(
        names = {"--list-dialects"},
        description = "List all supported target dialects and exit"
    )
    private boolean listDialects;

    @Option(
        names = {"--preserve-comments", "--comments"},
        description = "Preserve MySQL COMMENT clauses and convert them to target database COMMENT ON statements (default: true)",
        defaultValue = "true"
    )
    private boolean preserveComments;

    @Option(
        names = {"--dameng-length-in-char"},
        description = "Set Dameng database LENGTH_IN_CHAR configuration. " +
                     "true: LENGTH_IN_CHAR=1 (character-based, same as MySQL), " +
                     "false: LENGTH_IN_CHAR=0 (byte-based, Dameng default). " +
                     "When false, VARCHAR lengths will be multiplied by 3 for UTF-8 safety (default: true)",
        defaultValue = "true"
    )
    private boolean damengLengthInChar;



    @Parameters(
        arity = "0..1",
        description = "SQL content to convert (if no input file specified)"
    )
    private String sqlContent;
    
    private final Transpiler transpiler;
    
    public SqlTranspilerCli() {
        this.transpiler = new Transpiler();
    }
    
    @Override
    public Integer call() throws Exception {
        String jobId = UUID.randomUUID().toString().substring(0, 8);
        
        if (verbose) {
            ch.qos.logback.classic.Logger root = (ch.qos.logback.classic.Logger)LoggerFactory.getLogger(Logger.ROOT_LOGGER_NAME);
            root.setLevel(Level.DEBUG);

            // 启用语句跟踪器的控制台输出
            ch.qos.logback.classic.Logger statementTracker = (ch.qos.logback.classic.Logger)LoggerFactory.getLogger("STATEMENT_TRACKER");
            statementTracker.setLevel(Level.DEBUG);

            log.debug("[Job:{}] Verbose mode enabled. Log level set to DEBUG.", jobId);
            log.debug("[Job:{}] Statement-level tracking enabled. Detailed logs will be written to logs/statement-tracker.log", jobId);
        }

        log.info("[Job:{}] Transpilation process started.", jobId);
        log.info("[Job:{}] Comment preservation: {}", jobId, preserveComments ? "enabled" : "disabled");

        try {
            // 处理列出方言的请求
            if (listDialects) {
                return handleListDialects();
            }

            // 验证参数
            if (!validateArguments()) {
                return 1;
            }

            // 应用达梦数据库配置
            if ("dameng".equalsIgnoreCase(targetDialect)) {
                transpiler.setDamengLengthInChar(damengLengthInChar);
                log.info("[Job:{}] Dameng LENGTH_IN_CHAR configuration: {} ({})",
                        jobId, damengLengthInChar, damengLengthInChar ? "character-based" : "byte-based");
            }

            // 统一处理逻辑：自动检测输入类型
            return handle(jobId);

        } catch (Exception e) {
            log.error("[Job:{}] An unexpected error occurred: {}", jobId, e.getMessage(), e);
            return 1;
        } finally {
            log.info("[Job:{}] Transpilation process finished.", jobId);
        }
    }
    
    private Integer handleListDialects() {
        System.out.println("Supported target dialects:");
        System.out.println();
        
        Map<String, String> dialectInfo = transpiler.getDialectRegistry().getAllDialectInfo();
        for (Map.Entry<String, String> entry : dialectInfo.entrySet()) {
            System.out.printf("  %-12s - %s%n", entry.getKey(), entry.getValue());
        }
        
        return 0;
    }
    
    private boolean validateArguments() {
        // 验证目标方言
        if (targetDialect == null) {
            log.error("Target dialect is required (use -t or --target). Use --list-dialects to see supported dialects.");
            return false;
        }

        if (!transpiler.isTargetDialectSupported(targetDialect)) {
            log.error("Unsupported target dialect '{}'. Use --list-dialects to see supported dialects.", targetDialect);
            return false;
        }

        // 验证输入
        if (inputFile == null && sqlContent == null) {
            log.error("Either --input file/directory or SQL content parameter is required.");
            return false;
        }

        if (inputFile != null && sqlContent != null) {
            log.error("Cannot specify both --input file/directory and SQL content parameter.");
            return false;
        }

        return true;
    }
    
    /**
     * 统一处理方法：自动检测输入类型并进行相应处理
     * - 如果输入是目录，进行批量处理
     * - 如果输入是文件，进行单文件处理
     * - 如果没有输入文件，处理命令行参数
     */
    private Integer handle(String jobId) throws IOException {
        // 处理命令行SQL内容
        if (inputFile == null) {
            return handleCommandLineInput(jobId);
        }

        Path inputPath = Paths.get(inputFile);

        // 自动检测输入类型
        if (Files.isDirectory(inputPath)) {
            return handleDirectoryInput(jobId, inputPath);
        } else if (Files.isRegularFile(inputPath)) {
            return handleFileInput(jobId, inputPath);
        } else {
            log.error("[Job:{}] Input path does not exist or is not accessible: {}", jobId, inputFile);
            return 1;
        }
    }

    /**
     * 处理命令行SQL输入
     */
    private Integer handleCommandLineInput(String jobId) throws IOException {
        log.info("[Job:{}] Reading SQL from command line argument.", jobId);

        TranspilationResult result = transpiler.transpile(sqlContent, sourceDialect, targetDialect, preserveComments);

        if (outputFile != null) {
            log.info("[Job:{}] Writing output to file: {}", jobId, outputFile);
            Files.writeString(Paths.get(outputFile), result.translatedSql(),
                StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
            log.info("[Job:{}] Successfully wrote to {}", jobId, outputFile);
        } else {
            System.out.println(result.translatedSql());
        }

        logIssues(jobId, result.issues());

        log.info("[Job:{}] ================ SUMMARY ================", jobId);
        log.info("[Job:{}] Input: CLI Input", jobId);
        log.info("[Job:{}] Statements Succeeded: {}", jobId, result.successCount());
        log.info("[Job:{}] Statements Failed: {}", jobId, result.failureCount());
        log.info("[Job:{}] =========================================", jobId);

        return result.failureCount() > 0 ? 1 : 0;
    }
    
    /**
     * 处理目录输入（批量处理）
     */
    private Integer handleDirectoryInput(String jobId, Path inputDir) throws IOException {
        Path outputDir = outputFile != null ? Paths.get(outputFile) : inputDir.resolve("converted");
        Files.createDirectories(outputDir);

        log.info("[Job:{}] Starting batch processing. Input directory: {}, Output directory: {}", jobId, inputDir, outputDir);

        List<Path> sqlFiles = Files.walk(inputDir)
            .filter(Files::isRegularFile)
            .filter(path -> path.toString().toLowerCase().endsWith(".sql"))
            .toList();

        if (sqlFiles.isEmpty()) {
            log.warn("[Job:{}] No .sql files found in directory: {}", jobId, inputDir);
            return 0;
        }

        log.info("[Job:{}] Found {} SQL files to process.", jobId, sqlFiles.size());

        int totalSuccess = 0;
        int totalFailure = 0;

        for (int i = 0; i < sqlFiles.size(); i++) {
            Path sqlFile = sqlFiles.get(i);
            log.info("[Job:{}] [File {}/{}] Processing: {}", jobId, i + 1, sqlFiles.size(), sqlFile.getFileName());

            try {
                String content = Files.readString(sqlFile);
                TranspilationResult result = transpiler.transpile(content, sourceDialect, targetDialect, preserveComments);

                String fileName = sqlFile.getFileName().toString();
                String outputFileName = fileName.replaceAll("\\.sql$", "_" + targetDialect + ".sql");
                Path outputPath = outputDir.resolve(outputFileName);

                Files.writeString(outputPath, result.translatedSql(), StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);

                logIssues(jobId, result.issues());

                if (result.failureCount() > 0) {
                    log.error("[Job:{}] [File {}/{}] Finished with {} errors. Output written to {}",
                        jobId, i + 1, sqlFiles.size(), result.failureCount(), outputPath);
                } else {
                    log.info("[Job:{}] [File {}/{}] Finished successfully. Output written to {}",
                        jobId, i + 1, sqlFiles.size(), outputPath);
                }

                totalSuccess += result.successCount();
                totalFailure += result.failureCount();

            } catch (Exception e) {
                log.error("[Job:{}] [File {}/{}] Failed to process file {}: {}",
                    jobId, i + 1, sqlFiles.size(), sqlFile.getFileName(), e.getMessage(), e);
                totalFailure++; // Count the whole file as a failure
            }
        }

        log.info("[Job:{}] =============== BATCH SUMMARY ===============", jobId);
        log.info("[Job:{}] Total files processed: {}", jobId, sqlFiles.size());
        log.info("[Job:{}] Total statements succeeded: {}", jobId, totalSuccess);
        log.info("[Job:{}] Total statements failed: {}", jobId, totalFailure);
        log.info("[Job:{}] ===========================================", jobId);

        return totalFailure > 0 ? 1 : 0;
    }

    /**
     * 处理文件输入（单文件处理）
     *
     * 现在分割逻辑已经集成到Transpiler内部，会自动检测大文件并进行智能分割
     */
    private Integer handleFileInput(String jobId, Path inputPath) throws IOException {
        log.info("[Job:{}] Reading SQL from file: {}", jobId, inputPath);

        String content = Files.readString(inputPath);
        TranspilationResult result = transpiler.transpile(content, sourceDialect, targetDialect, preserveComments);

        if (outputFile != null) {
            log.info("[Job:{}] Writing output to file: {}", jobId, outputFile);
            Files.writeString(Paths.get(outputFile), result.translatedSql(),
                StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
            log.info("[Job:{}] Successfully wrote to {}", jobId, outputFile);
        } else {
            System.out.println(result.translatedSql());
        }

        logIssues(jobId, result.issues());

        log.info("[Job:{}] ================ SUMMARY ================", jobId);
        log.info("[Job:{}] Input: {}", jobId, inputPath.getFileName());
        log.info("[Job:{}] Statements Succeeded: {}", jobId, result.successCount());
        log.info("[Job:{}] Statements Failed: {}", jobId, result.failureCount());
        log.info("[Job:{}] =========================================", jobId);

        return result.failureCount() > 0 ? 1 : 0;
    }


    
    private void logIssues(String jobId, List<TranspilationIssue> issues) {
        if (issues == null || issues.isEmpty()) {
            return;
        }

        long errorCount = issues.stream().filter(issue -> issue.level() == TranspilationIssue.IssueLevel.ERROR).count();
        long warnCount = issues.stream().filter(issue -> issue.level() == TranspilationIssue.IssueLevel.WARN).count();

        if (errorCount > 0) {
            log.info("[Job:{}] Encountered {} errors:", jobId, errorCount);
        }
        if (warnCount > 0) {
            log.info("[Job:{}] Encountered {} warnings:", jobId, warnCount);
        }

        for (TranspilationIssue issue : issues) {
            String message = "[Job:" + jobId + "] " + issue.message();
            switch (issue.level()) {
                case ERROR -> log.error(message);
                case WARN -> log.warn(message);
                case INFO -> log.info(message); // Should be rare from issues list now
            }
        }
    }
    
    public static void main(String[] args) {
        int exitCode = new CommandLine(new SqlTranspilerCli()).execute(args);
        System.exit(exitCode);
    }
}
