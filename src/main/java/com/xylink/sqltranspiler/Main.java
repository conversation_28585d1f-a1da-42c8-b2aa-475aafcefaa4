package com.xylink.sqltranspiler;

import com.xylink.sqltranspiler.application.cli.SqlTranspilerCli;
import org.springframework.boot.SpringApplication;
import picocli.CommandLine;

/**
 * SQL转换工具的统一入口
 *
 * 支持两种运行模式：
 * 1. CLI模式：当有命令行参数时，使用CLI模式
 * 2. Web模式：当没有命令行参数时，启动Web服务
 */
public class Main {

    public static void main(String[] args) {
        // 检查是否包含Spring Boot参数或web模式参数
        boolean isWebMode = args.length == 0 ||
                           hasSpringBootArgs(args) ||
                           hasWebModeArg(args);

        if (isWebMode) {
            // 启动Web服务
            startWebApplication(args);
        } else {
            // 使用Picocli CLI框架处理命令行参数和执行逻辑
            int exitCode = new CommandLine(new SqlTranspilerCli()).execute(args);
            System.exit(exitCode);
        }
    }

    private static boolean hasSpringBootArgs(String[] args) {
        for (String arg : args) {
            if (arg.startsWith("--server.") ||
                arg.startsWith("--spring.") ||
                arg.startsWith("--logging.")) {
                return true;
            }
        }
        return false;
    }

    private static boolean hasWebModeArg(String[] args) {
        for (String arg : args) {
            if ("--web".equals(arg)) {
                return true;
            }
        }
        return false;
    }

    private static void startWebApplication(String[] args) {
        // 过滤掉--web参数，因为Spring Boot不认识这个参数
        String[] filteredArgs = filterWebArgs(args);

        System.out.println("Starting SQL Transpiler Web Service...");
        String port = getPortFromArgs(filteredArgs);
        System.out.println("Access the web interface at: http://localhost:" + port);
        SpringApplication.run(WebApplication.class, filteredArgs);
    }

    private static String[] filterWebArgs(String[] args) {
        return java.util.Arrays.stream(args)
            .filter(arg -> !"--web".equals(arg))
            .toArray(String[]::new);
    }

    private static String getPortFromArgs(String[] args) {
        for (String arg : args) {
            if (arg.startsWith("--server.port=")) {
                return arg.substring("--server.port=".length());
            }
        }
        return "8080"; // 默认端口
    }
}