package com.xylink.sqltranspiler.core.dialects;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;

import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator;
import com.xylink.sqltranspiler.core.dialects.shentong.ShentongGenerator;

/**
 * 方言注册表
 * 负责管理所有支持的数据库方言生成器
 * 
 * 支持动态注册和获取方言生成器，便于扩展新的数据库支持
 */
public class DialectRegistry {
    
    private final Map<String, Supplier<Generator>> generators = new HashMap<>();
    private final Map<String, String> dialectDescriptions = new HashMap<>();
    private final Map<String, Generator> generatorInstances = new HashMap<>();
    
    public DialectRegistry() {
        // 构造函数中不自动注册，由外部调用registerDialects()
    }
    
    /**
     * 注册所有支持的方言
     * 这个方法包含了所有内置支持的数据库方言
     */
    public void registerDialects() {
        // 注册达梦数据库方言
        register("dameng", 
                DamengGenerator::new, 
                "DM Database - 达梦数据库，支持完整的MySQL到达梦转换");
        
        // 注册达梦的别名
        register("dm",
                DamengGenerator::new,
                "DM Database (alias) - 达梦数据库别名");

        // 注册金仓数据库方言
        register("kingbase",
                KingbaseGenerator::new,
                "KingbaseES Database - 金仓数据库，支持完整的MySQL到金仓转换");

        // 注册金仓的别名
        register("kingbasees",
                KingbaseGenerator::new,
                "KingbaseES Database (alias) - 金仓数据库别名");

        register("kes",
                KingbaseGenerator::new,
                "KingbaseES Database (short alias) - 金仓数据库简称");

        // 注册神通数据库方言
        register("shentong",
                ShentongGenerator::new,
                "Shentong Database - 神通数据库，支持完整的MySQL到神通转换");

        // 注册神通的别名
        register("st",
                ShentongGenerator::new,
                "Shentong Database (alias) - 神通数据库别名");

    }
    
    /**
     * 注册一个方言生成器
     * 
     * @param dialectName 方言名称 (不区分大小写)
     * @param generatorSupplier 生成器供应商
     * @param description 方言描述
     */
    public void register(String dialectName, Supplier<Generator> generatorSupplier, String description) {
        if (dialectName == null || dialectName.trim().isEmpty()) {
            throw new IllegalArgumentException("Dialect name cannot be null or empty");
        }
        if (generatorSupplier == null) {
            throw new IllegalArgumentException("Generator supplier cannot be null");
        }
        
        String normalizedName = dialectName.toLowerCase().trim();
        generators.put(normalizedName, generatorSupplier);
        dialectDescriptions.put(normalizedName, description != null ? description : "No description available");
    }
    
    /**
     * 获取指定方言的生成器实例
     * 
     * @param dialectName 方言名称 (不区分大小写)
     * @return 生成器实例
     * @throws UnsupportedDialectException 如果方言不支持
     */
    public Generator getGenerator(String dialectName) {
        if (dialectName == null || dialectName.trim().isEmpty()) {
            throw new IllegalArgumentException("Dialect name cannot be null or empty");
        }

        String normalizedName = dialectName.toLowerCase().trim();

        // 使用单例模式，确保每个方言只有一个Generator实例
        Generator instance = generatorInstances.get(normalizedName);
        if (instance != null) {
            return instance;
        }

        Supplier<Generator> supplier = generators.get(normalizedName);
        if (supplier == null) {
            throw new UnsupportedDialectException(
                "Unsupported dialect: '" + dialectName + "'. " +
                "Supported dialects: " + String.join(", ", getSupportedDialects()));
        }

        try {
            instance = supplier.get();
            generatorInstances.put(normalizedName, instance);
            return instance;
        } catch (Exception e) {
            throw new RuntimeException("Failed to create generator for dialect: " + dialectName, e);
        }
    }
    
    /**
     * 检查是否支持指定的方言
     * 
     * @param dialectName 方言名称 (不区分大小写)
     * @return 如果支持返回true，否则返回false
     */
    public boolean isSupported(String dialectName) {
        if (dialectName == null || dialectName.trim().isEmpty()) {
            return false;
        }
        return generators.containsKey(dialectName.toLowerCase().trim());
    }
    
    /**
     * 获取所有支持的方言名称列表
     * 
     * @return 支持的方言名称列表，按字母顺序排序
     */
    public List<String> getSupportedDialects() {
        List<String> dialects = new ArrayList<>(generators.keySet());
        Collections.sort(dialects);
        return dialects;
    }
    
    /**
     * 获取方言的描述信息
     * 
     * @param dialectName 方言名称
     * @return 方言描述，如果方言不存在返回null
     */
    public String getDialectDescription(String dialectName) {
        if (dialectName == null || dialectName.trim().isEmpty()) {
            return null;
        }
        return dialectDescriptions.get(dialectName.toLowerCase().trim());
    }
    
    /**
     * 获取所有方言的详细信息
     * 
     * @return 包含方言名称和描述的映射
     */
    public Map<String, String> getAllDialectInfo() {
        Map<String, String> info = new LinkedHashMap<>();
        List<String> sortedDialects = getSupportedDialects();
        
        for (String dialect : sortedDialects) {
            info.put(dialect, dialectDescriptions.get(dialect));
        }
        
        return info;
    }
    
    /**
     * 清除所有注册的方言
     * 主要用于测试
     */
    public void clear() {
        generators.clear();
        dialectDescriptions.clear();
    }
    
    /**
     * 获取注册的方言数量
     */
    public int size() {
        return generators.size();
    }
}
