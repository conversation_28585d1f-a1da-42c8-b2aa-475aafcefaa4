package com.xylink.sqltranspiler.core.dialects;

/**
 * SQL方言抽象接口
 * 借鉴Apache Calcite的SqlDialect设计思想，提供标准化的方言抽象
 * 
 * 设计原则：
 * 1. 保持轻量级，不引入重量级依赖
 * 2. 专注于SQL转换的核心需求
 * 3. 易于扩展新的数据库支持
 */
public interface SqlDialect {
    
    /**
     * 获取方言名称
     * @return 数据库方言名称，如 "DM", "KingbaseES", "Shentong"
     */
    String getName();
    
    /**
     * 获取数据库产品信息
     * @return 数据库产品标识
     */
    String getDatabaseProduct();
    
    // ==================== 标识符处理 ====================
    
    /**
     * 引用标识符（表名、列名等）
     * 借鉴Calcite的标识符引用规则
     * 
     * @param identifier 原始标识符
     * @return 引用后的标识符
     */
    String quoteIdentifier(String identifier);
    
    /**
     * 引用字符串字面量
     * @param literal 原始字符串
     * @return 引用后的字符串
     */
    String quoteLiteral(String literal);
    
    /**
     * 判断标识符是否需要引用
     * @param identifier 标识符
     * @return 是否需要引用
     */
    boolean requiresQuoting(String identifier);
    
    // ==================== 数据类型映射 ====================
    
    /**
     * 映射MySQL数据类型到目标数据库类型
     * 这是SQL转换的核心功能之一
     * 
     * @param mysqlType MySQL数据类型名称
     * @param length 长度参数（可选）
     * @param precision 精度参数（可选）
     * @param scale 小数位数参数（可选）
     * @return 目标数据库的数据类型
     */
    String mapDataType(String mysqlType, Integer length, Integer precision, Integer scale);
    
    /**
     * 判断是否支持指定的数据类型
     * @param dataType 数据类型
     * @return 是否支持
     */
    boolean supportsDataType(String dataType);
    
    // ==================== SQL语法支持 ====================
    
    /**
     * 是否支持LIMIT语法
     * @return 是否支持LIMIT
     */
    boolean supportsLimit();
    
    /**
     * 是否支持OFFSET语法
     * @return 是否支持OFFSET
     */
    boolean supportsOffset();
    
    /**
     * 格式化分页语句
     * 不同数据库的分页语法差异很大，这是转换的重点
     * 
     * @param limit 限制行数
     * @param offset 偏移量
     * @return 格式化后的分页语句
     */
    String formatPagination(Integer limit, Integer offset);
    
    /**
     * 是否支持AUTO_INCREMENT
     * @return 是否支持自增
     */
    boolean supportsAutoIncrement();
    
    /**
     * 获取自增语法
     * @return 自增语法字符串
     */
    String getAutoIncrementSyntax();
    
    // ==================== 函数映射 ====================
    
    /**
     * 映射MySQL函数到目标数据库函数
     * 借鉴Calcite的函数映射思路
     * 
     * @param mysqlFunction MySQL函数名
     * @param args 函数参数
     * @return 目标数据库的函数调用
     */
    String mapFunction(String mysqlFunction, String... args);
    
    /**
     * 判断是否支持指定函数
     * @param functionName 函数名
     * @return 是否支持
     */
    boolean supportsFunction(String functionName);
    
    // ==================== 约束和索引 ====================
    
    /**
     * 是否支持外键约束
     * @return 是否支持外键
     */
    boolean supportsForeignKey();
    
    /**
     * 是否支持CHECK约束
     * @return 是否支持CHECK约束
     */
    boolean supportsCheckConstraint();
    
    /**
     * 格式化主键约束
     * @param columnNames 主键列名列表
     * @return 主键约束SQL
     */
    String formatPrimaryKey(String... columnNames);
    
    /**
     * 格式化唯一约束
     * @param columnNames 唯一约束列名列表
     * @return 唯一约束SQL
     */
    String formatUniqueConstraint(String... columnNames);
    
    // ==================== 默认实现 ====================
    
    /**
     * 获取当前时间函数
     * @return 当前时间函数名
     */
    default String getCurrentTimestampFunction() {
        return "CURRENT_TIMESTAMP";
    }
    
    /**
     * 获取当前日期函数
     * @return 当前日期函数名
     */
    default String getCurrentDateFunction() {
        return "CURRENT_DATE";
    }
    
    /**
     * 获取字符串连接操作符
     * @return 连接操作符
     */
    default String getConcatOperator() {
        return "||";
    }
    
    /**
     * 是否区分大小写
     * @return 是否区分大小写
     */
    default boolean isCaseSensitive() {
        return true;
    }
}
