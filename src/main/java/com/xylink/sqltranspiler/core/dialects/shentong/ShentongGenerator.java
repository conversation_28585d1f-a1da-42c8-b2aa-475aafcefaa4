package com.xylink.sqltranspiler.core.dialects.shentong;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.core.ast.DefaultStatement;
import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.TableId;
import com.xylink.sqltranspiler.core.ast.alter.AlterSequence;
import com.xylink.sqltranspiler.core.ast.alter.AlterTable;
import com.xylink.sqltranspiler.core.ast.alter.AlterView;
import com.xylink.sqltranspiler.core.ast.common.CallStatement;
import com.xylink.sqltranspiler.core.ast.common.SetStatement;
import com.xylink.sqltranspiler.core.ast.common.ShowStatement;
import com.xylink.sqltranspiler.core.ast.common.UseStatement;
import com.xylink.sqltranspiler.core.ast.create.CreateDatabase;
import com.xylink.sqltranspiler.core.ast.create.CreateFunction;
import com.xylink.sqltranspiler.core.ast.create.CreateIndex;
import com.xylink.sqltranspiler.core.ast.create.CreateProcedure;
import com.xylink.sqltranspiler.core.ast.create.CreateSequence;
import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import com.xylink.sqltranspiler.core.ast.create.CreateTableAsSelect;
import com.xylink.sqltranspiler.core.ast.create.CreateTrigger;
import com.xylink.sqltranspiler.core.ast.create.CreateView;
import com.xylink.sqltranspiler.core.ast.dml.DeleteTable;
import com.xylink.sqltranspiler.core.ast.dml.InsertTable;
import com.xylink.sqltranspiler.core.ast.dml.MultiTableDelete;
import com.xylink.sqltranspiler.core.ast.dml.MultiTableUpdate;
import com.xylink.sqltranspiler.core.ast.dml.QueryStmt;
import com.xylink.sqltranspiler.core.ast.dml.UpdateTable;
import com.xylink.sqltranspiler.core.ast.dml.ValuesClause;
import com.xylink.sqltranspiler.core.ast.drop.DropDatabase;
import com.xylink.sqltranspiler.core.ast.drop.DropFunction;
import com.xylink.sqltranspiler.core.ast.drop.DropIndex;
import com.xylink.sqltranspiler.core.ast.drop.DropProcedure;
import com.xylink.sqltranspiler.core.ast.drop.DropSequence;
import com.xylink.sqltranspiler.core.ast.drop.DropTable;
import com.xylink.sqltranspiler.core.ast.drop.DropTrigger;
import com.xylink.sqltranspiler.core.ast.drop.DropView;
import com.xylink.sqltranspiler.core.ast.privilege.Grant;
import com.xylink.sqltranspiler.core.ast.privilege.Revoke;
import com.xylink.sqltranspiler.core.ast.table.ColumnRel;
import com.xylink.sqltranspiler.core.ast.table.TruncateTable;
import com.xylink.sqltranspiler.core.ast.transaction.BeginWork;
import com.xylink.sqltranspiler.core.ast.transaction.CommitWork;
import com.xylink.sqltranspiler.core.ast.transaction.ReleaseStatement;
import com.xylink.sqltranspiler.core.ast.transaction.RollbackStatement;
import com.xylink.sqltranspiler.core.ast.transaction.RollbackWork;
import com.xylink.sqltranspiler.core.ast.transaction.SavepointStatement;
import com.xylink.sqltranspiler.core.ast.transaction.StartTransaction;
import com.xylink.sqltranspiler.core.dialects.Generator;
import com.xylink.sqltranspiler.core.dialects.SqlDialect;
import com.xylink.sqltranspiler.core.dialects.ShentongDialect;
import com.xylink.sqltranspiler.infrastructure.formatter.SqlFormatter;

/**
 * 神通数据库SQL生成器
 *
 * 基于神通数据库官方文档实现MySQL到神通数据库的SQL转换
 *
 * 神通数据库特点：
 * - 国产化数据库管理系统，完全自主知识产权
 * - 基本支持SQL92的入门级和过渡级标准，并联系实际应用进行了大量扩展
 * - 提供ODBC、JDBC、OLEDB/ADO和.Net DataProvider等标准数据访问接口
 * - 海量数据处理和大规模并发访问能力
 *
 * 主要转换规则：
 * 1. 数据类型映射：完全支持TINYINT、SMALLINT、INT、BIGINT等，MEDIUMINT -> INT
 * 2. 自增字段：完全支持AUTO_INCREMENT语法（与MySQL兼容），支持INT、BIGINT、FLOAT类型
 * 3. 函数支持：完全支持IFNULL、CONCAT、CHAR_LENGTH等MySQL函数
 * 4. 字符集：utf8mb4 -> UTF8（注意：神通使用UTF8，不是UTF-8）
 * 5. 存储引擎：移除ENGINE子句（神通自动选择最优存储方式）
 * 6. 注释：保持MySQL COMMENT语法或转换为COMMENT ON语法
 * 7. ALTER TABLE：完全支持AUTO_INCREMENT设置，性能优于序列和SERIAL
 * 8. 序列类型：支持SERIAL和BIGSERIAL，等价于AUTO_INCREMENT
 */
public class ShentongGenerator implements Generator {
    
    private static final Logger log = LoggerFactory.getLogger(ShentongGenerator.class);

    // 神通方言实例 - 借鉴Calcite设计思想
    private final SqlDialect dialect = new ShentongDialect();
    
    @Override
    public String generate(Statement statement) {
        String result = generateInternal(statement);
        // 应用SQL格式化以提升可读性
        return SqlFormatter.format(result);
    }

    /**
     * 内部生成方法，不包含格式化
     */
    private String generateInternal(Statement statement) {
        if (statement instanceof CreateTable createTable) {
            return generateCreateTable(createTable);
        }
        if (statement instanceof CreateTableAsSelect createTableAsSelect) {
            return generateCreateTableAsSelect(createTableAsSelect);
        }
        if (statement instanceof com.xylink.sqltranspiler.core.ast.create.CreateTableLike createTableLike) {
            return generateCreateTableLike(createTableLike);
        }
        if (statement instanceof CreateIndex createIndex) {
            return generateCreateIndexStatement(createIndex);
        }
        if (statement instanceof DropTable dropTable) {
            return generateDropTable(dropTable);
        }
        if (statement instanceof TruncateTable truncateTable) {
            return generateTruncateTable(truncateTable);
        }
        if (statement instanceof CreateView createView) {
            return generateCreateView(createView);
        }
        if (statement instanceof AlterView alterView) {
            return generateAlterView(alterView);
        }
        if (statement instanceof DropView dropView) {
            return generateDropView(dropView);
        }
        if (statement instanceof CreateSequence createSequence) {
            return generateCreateSequence(createSequence);
        }
        if (statement instanceof AlterSequence alterSequence) {
            return generateAlterSequence(alterSequence);
        }
        if (statement instanceof DropSequence dropSequence) {
            return generateDropSequence(dropSequence);
        }
        if (statement instanceof DropIndex dropIndex) {
            return generateDropIndex(dropIndex);
        }
        if (statement instanceof InsertTable insertStatement) {
            return generateInsertStatement(insertStatement);
        }
        if (statement instanceof DeleteTable deleteTable) {
            return generateDeleteStatement(deleteTable);
        }
        if (statement instanceof UpdateTable updateTable) {
            return generateUpdateStatement(updateTable);
        }
        if (statement instanceof MultiTableUpdate multiTableUpdate) {
            return generateMultiTableUpdateStatement(multiTableUpdate);
        }
        if (statement instanceof MultiTableDelete multiTableDelete) {
            return generateMultiTableDeleteStatement(multiTableDelete);
        }
        if (statement instanceof QueryStmt queryStmt) {
            return generateSelectStatement(queryStmt);
        }
        if (statement instanceof AlterTable alterTable) {
            return generateAlterTable(alterTable);
        }
        if (statement instanceof CreateDatabase createDatabase) {
            return generateCreateDatabase(createDatabase);
        }
        if (statement instanceof DropDatabase dropDatabase) {
            return generateDropDatabase(dropDatabase);
        }
        if (statement instanceof UseStatement useStatement) {
            return generateUseStatement(useStatement);
        }
        if (statement instanceof ShowStatement showStatement) {
            return generateShowStatement(showStatement);
        }
        if (statement instanceof SetStatement setStatement) {
            return generateSetStatement(setStatement);
        }
        if (statement instanceof CallStatement callStatement) {
            return generateCallStatement(callStatement);
        }

        // 事务控制语句 - 根据神通官方文档，神通数据库完全支持事务控制
        if (statement instanceof BeginWork beginWork) {
            return generateBeginWork(beginWork);
        }
        if (statement instanceof StartTransaction startTransaction) {
            return generateStartTransaction(startTransaction);
        }
        if (statement instanceof CommitWork commitWork) {
            return generateCommitWork(commitWork);
        }
        if (statement instanceof RollbackWork rollbackWork) {
            return generateRollbackWork(rollbackWork);
        }
        if (statement instanceof RollbackStatement rollbackStatement) {
            return generateRollbackStatement(rollbackStatement);
        }
        if (statement instanceof SavepointStatement savepointStatement) {
            return generateSavepointStatement(savepointStatement);
        }
        if (statement instanceof ReleaseStatement releaseStatement) {
            return generateReleaseStatement(releaseStatement);
        }
        if (statement instanceof CreateFunction createFunction) {
            return generateCreateFunction(createFunction);
        }
        if (statement instanceof DropFunction dropFunction) {
            return generateDropFunction(dropFunction);
        }
        if (statement instanceof CreateProcedure createProcedure) {
            return generateCreateProcedure(createProcedure);
        }
        if (statement instanceof DropProcedure dropProcedure) {
            return generateDropProcedure(dropProcedure);
        }
        if (statement instanceof CreateTrigger createTrigger) {
            return generateCreateTrigger(createTrigger);
        }
        if (statement instanceof DropTrigger dropTrigger) {
            return generateDropTrigger(dropTrigger);
        }
        if (statement instanceof Grant grant) {
            return generateGrant(grant);
        }
        if (statement instanceof Revoke revoke) {
            return generateRevoke(revoke);
        }
        // 处理DefaultStatement类型的语句
        if (statement instanceof DefaultStatement defaultStatement) {
            return generateDefaultStatement(defaultStatement);
        }

        // 对于不支持的语句类型，记录详细信息并返回注释
        log.warn("UNSUPPORTED_STATEMENT: Statement type '{}' is not supported in Shentong database conversion. " +
                "Original SQL: {}", statement.getClass().getSimpleName(),
                statement.getSql() != null ? statement.getSql() : "SQL not available");
        return "-- Unsupported statement: " + statement.getClass().getSimpleName() +
               " -- 神通数据库不支持此语句类型";
    }

    /**
     * 处理DefaultStatement类型的语句
     * 这些语句通常是解析器无法识别的特殊语句，如REPLACE INTO、HANDLER、DO、CTE等
     * 根据神通数据库的特性，需要特殊处理
     */
    private String generateDefaultStatement(DefaultStatement defaultStatement) {
        String originalSql = defaultStatement.getSql();
        if (originalSql == null || originalSql.trim().isEmpty()) {
            log.warn("UNSUPPORTED_STATEMENT: DefaultStatement with empty SQL");
            return "-- Empty DefaultStatement";
        }

        String sql = originalSql.trim().toUpperCase();

        // 检查是否是REPLACE INTO语句
        if (sql.startsWith("REPLACE INTO")) {
            return handleReplaceIntoStatement(originalSql);
        }

        // 检查是否是LOAD DATA语句
        if (sql.startsWith("LOAD DATA")) {
            return handleLoadDataStatement(originalSql);
        }

        // 检查是否是HANDLER语句
        if (sql.startsWith("HANDLER")) {
            return handleHandlerStatement(originalSql);
        }

        // 检查是否是DO语句
        if (sql.startsWith("DO ")) {
            return handleDoStatement(originalSql);
        }

        // 检查是否是CTE (WITH语句)
        if (sql.startsWith("WITH ")) {
            return handleWithStatement(originalSql);
        }

        // 其他不支持的语句
        log.warn("UNSUPPORTED_STATEMENT: DefaultStatement type not recognized. SQL: {}", originalSql);
        return "-- Unsupported DefaultStatement: " + originalSql.substring(0, Math.min(50, originalSql.length())) + "...";
    }

    /**
     * 处理REPLACE INTO语句
     * 根据MySQL官方文档：https://dev.mysql.com/doc/refman/8.4/en/replace.html
     * REPLACE INTO是MySQL特有的语法，神通数据库不支持此语法
     * 也不支持INSERT ... ON DUPLICATE KEY UPDATE语法
     */
    private String handleReplaceIntoStatement(String originalSql) {
        log.warn("UNSUPPORTED_STATEMENT: REPLACE INTO is MySQL-specific and not supported in Shentong database. " +
                "Reference: https://dev.mysql.com/doc/refman/8.4/en/replace.html. " +
                "Shentong database only supports standard INSERT INTO ... VALUES syntax.");
        return "-- REPLACE INTO is MySQL-specific and not supported in Shentong database\n" +
               "-- Reference: https://dev.mysql.com/doc/refman/8.4/en/replace.html\n" +
               "-- Original SQL: " + originalSql + "\n" +
               "-- Please use standard INSERT INTO ... VALUES syntax;";
    }

    /**
     * 处理LOAD DATA语句
     * 根据MySQL官方文档：https://dev.mysql.com/doc/refman/8.4/en/load-data.html
     * LOAD DATA INFILE是MySQL特有的数据导入语法，神通数据库不支持此语法
     * 建议使用神通数据库的数据导入工具或INSERT语句进行数据导入
     */
    private String handleLoadDataStatement(String originalSql) {
        log.warn("UNSUPPORTED_STATEMENT: LOAD DATA INFILE is MySQL-specific and not supported in Shentong database. " +
                "Reference: https://dev.mysql.com/doc/refman/8.4/en/load-data.html. " +
                "Please use Shentong data import tools or INSERT statements for data import. SQL: {}", originalSql);
        return "-- Unsupported: LOAD DATA INFILE is MySQL-specific and not supported in Shentong database\n" +
               "-- Reference: https://dev.mysql.com/doc/refman/8.4/en/load-data.html\n" +
               "-- Please use Shentong data import tools or INSERT statements for data import\n" +
               "-- " + originalSql + ";";
    }

    /**
     * 处理HANDLER语句
     * 根据MySQL官方文档：https://dev.mysql.com/doc/refman/8.4/en/handler.html
     * 根据神通数据库官方文档，神通数据库有各种Handler机制（ImportHandler、ExportHandler等）
     * 但与MySQL的HANDLER语句可能有语法差异，需进一步验证
     */
    private String handleHandlerStatement(String originalSql) {
        log.warn("VERIFICATION_NEEDED: HANDLER statement support in Shentong database may have syntax differences from MySQL. " +
                "MySQL Reference: https://dev.mysql.com/doc/refman/8.4/en/handler.html. " +
                "Shentong official documentation shows various Handler mechanisms. SQL: {}", originalSql);
        return "-- Verification needed: HANDLER statement support in Shentong database may have syntax differences from MySQL\n" +
               "-- MySQL Reference: https://dev.mysql.com/doc/refman/8.4/en/handler.html\n" +
               "-- Shentong official documentation shows various Handler mechanisms\n" +
               "-- " + originalSql + ";";
    }

    /**
     * 处理DO语句
     * 根据MySQL官方文档：https://dev.mysql.com/doc/refman/8.4/en/do.html
     * 根据神通数据库官方文档，神通数据库在嵌入式SQL中支持DO语句的某种形式
     * 但与MySQL的DO语句可能有语法差异，需进一步验证
     */
    private String handleDoStatement(String originalSql) {
        log.warn("VERIFICATION_NEEDED: DO statement support in Shentong database may have syntax differences from MySQL. " +
                "MySQL Reference: https://dev.mysql.com/doc/refman/8.4/en/do.html. " +
                "Shentong official documentation shows DO support in embedded SQL context. SQL: {}", originalSql);
        return "-- Verification needed: DO statement support in Shentong database may have syntax differences from MySQL\n" +
               "-- MySQL Reference: https://dev.mysql.com/doc/refman/8.4/en/do.html\n" +
               "-- Shentong official documentation shows DO support in embedded SQL context\n" +
               "-- " + originalSql + ";";
    }

    /**
     * 处理WITH语句 (CTE)
     * 根据测试验证，神通数据库完全支持CTE (Common Table Expression)
     * 参考：shentong.md文档第55776行显示WITH为支持的关键字
     * 测试结果：神通数据库支持简单CTE、递归CTE、多个CTE、CTE与窗口函数组合等所有功能
     */
    private String handleWithStatement(String originalSql) {
        try {
            log.info("CONVERSION_INFO: Converting CTE statement for Shentong database. Original SQL: {}",
                    originalSql.substring(0, Math.min(100, originalSql.length())) + "...");

            // 神通数据库完全支持CTE，进行标准转换
            String convertedSql = originalSql.trim();

            // 转换反引号为双引号（神通使用双引号）
            convertedSql = convertBackticksToDoubleQuotes(convertedSql);

            // 转换MySQL函数为神通兼容函数
            convertedSql = convertSqlFunctions(convertedSql);

            // 检查是否是递归CTE
            if (convertedSql.toUpperCase().contains("WITH RECURSIVE")) {
                log.info("CONVERSION_INFO: Recursive CTE detected, Shentong database supports it natively");
            }

            // 检查是否是多个CTE
            if (countOccurrences(convertedSql.toUpperCase(), " AS (") > 1) {
                log.info("CONVERSION_INFO: Multiple CTEs detected, Shentong database supports them natively");
            }

            // 确保以分号结尾
            if (!convertedSql.endsWith(";")) {
                convertedSql += ";";
            }

            log.info("CONVERSION_SUCCESS: CTE statement converted successfully for Shentong database");
            return convertedSql;

        } catch (Exception e) {
            log.error("CONVERSION_ERROR: Failed to convert CTE statement for Shentong database. Error: {}", e.getMessage());
            return "-- CTE conversion failed: " + e.getMessage() + "\n" +
                   "-- Original SQL: " + originalSql + "\n" +
                   "-- Please check the SQL syntax;";
        }
    }

    /**
     * 辅助方法：计算字符串中子字符串的出现次数
     */
    private int countOccurrences(String text, String substring) {
        int count = 0;
        int index = 0;
        while ((index = text.indexOf(substring, index)) != -1) {
            count++;
            index += substring.length();
        }
        return count;
    }

    /**
     * 生成CREATE TABLE语句
     */
    private String generateCreateTable(CreateTable createTable) {
        StringBuilder sb = new StringBuilder();

        // 添加注释支持 - 神通数据库完全支持MySQL注释语法
        sb.append("-- 神通数据库CREATE TABLE语句\n");

        String quotedTableName = getQuotedTableName(createTable.getTableId());
        sb.append("CREATE TABLE ");

        if (createTable.isIfNotExists()) {
            sb.append("IF NOT EXISTS ");
        }

        sb.append(quotedTableName).append(" (\n");
        
        List<String> createDefinitions = createTable.getColumnRels().stream()
            .map(this::generateColumnDefinition)
            .collect(Collectors.toList());
        
        // 添加主键约束
        List<String> primaryKeyColumns = createTable.getColumnRels().stream()
            .filter(col -> col.isPrimaryKey())
            .map(col -> quote(col.getColumnName()))
            .collect(Collectors.toList());

        if (primaryKeyColumns.size() > 1) {
            createDefinitions.add("    PRIMARY KEY (" + String.join(", ", primaryKeyColumns) + ")");
        }

        // 添加表级约束（仅CHECK约束，FOREIGN KEY约束作为独立的ALTER TABLE语句处理）
        if (createTable.getProperties() != null) {
            for (Map.Entry<String, String> entry : createTable.getProperties().entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                if (key.startsWith("index_") && value.contains("CHECK") && !value.contains("FOREIGN KEY")) {
                    // 这是一个CHECK约束，添加到CREATE TABLE语句中
                    createDefinitions.add("    " + value);
                }
            }
        }

        sb.append(String.join(",\n", createDefinitions));
        sb.append("\n)");

        // 神通数据库使用CHARACTER SET UTF8
        sb.append(" CHARACTER SET UTF8");

        // 处理表级AUTO_INCREMENT设置
        if (createTable.getProperties() != null && createTable.getProperties().containsKey("auto_increment")) {
            String autoIncrementValue = createTable.getProperties().get("auto_increment");
            sb.append(" AUTO_INCREMENT = ").append(autoIncrementValue);
        }

        // 处理分区定义转换
        // 根据神通数据库官方文档，神通数据库支持分区表
        String partitionClause = convertPartitionDefinitionForShentong(createTable.getPartitionDefinition());
        if (partitionClause != null && !partitionClause.trim().isEmpty()) {
            sb.append("\n").append(partitionClause);
            log.info("PARTITION_CONVERSION: Successfully converted MySQL partition definition to Shentong format for table: {}",
                    createTable.getTableId().getTableName());
        }

        // 兼容旧的分区定义方式（从properties中读取）
        if (createTable.getProperties() != null && createTable.getProperties().containsKey("partition_definition")) {
            String legacyPartitionDefinition = createTable.getProperties().get("partition_definition");
            if (partitionClause == null || partitionClause.trim().isEmpty()) {
                sb.append("\n").append(legacyPartitionDefinition);
                log.info("CONVERSION_SUCCESS: Legacy partition definition preserved for Shentong database");
            }
        }

        sb.append(";");

        // 处理存储在properties中的索引和FOREIGN KEY语句
        StringBuilder additionalStatements = new StringBuilder();
        if (createTable.getProperties() != null) {
            for (Map.Entry<String, String> entry : createTable.getProperties().entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();

                // 处理以index_开头的语句（包括FOREIGN KEY约束和索引）
                if (key.startsWith("index_") && value != null && !value.trim().isEmpty()) {
                    additionalStatements.append("\n").append(value);
                    if (!value.trim().endsWith(";")) {
                        additionalStatements.append(";");
                    }
                }
            }
        }

        // 如果有额外的语句，添加到结果中
        if (additionalStatements.length() > 0) {
            return sb.toString() + additionalStatements.toString();
        }

        return sb.toString();
    }
    
    /**
     * 生成列定义
     */
    private String generateColumnDefinition(ColumnRel col) {
        return generateColumnDefinitionWithName(col.getColumnName(), col);
    }

    /**
     * 生成列定义（指定列名）
     */
    private String generateColumnDefinitionWithName(String columnName, ColumnRel col) {
        StringBuilder sb = new StringBuilder();
        sb.append(quote(columnName)).append(" ");

        // 数据类型转换 - 特殊处理SERIAL类型
        String originalTypeName = col.getTypeName();
        boolean isAutoIncrement = "AUTO_INCREMENT".equals(col.getExpression());

        // 检查是否是SERIAL/BIGSERIAL类型（保留对显式SERIAL类型的支持）
        if ("SERIAL".equalsIgnoreCase(originalTypeName)) {
            // 神通数据库完全支持SERIAL类型，直接使用SERIAL
            sb.append("SERIAL");
            // SERIAL类型已经包含了AUTO_INCREMENT、NOT NULL、UNIQUE等属性，不需要额外添加
        } else if ("BIGSERIAL".equalsIgnoreCase(originalTypeName)) {
            // 神通数据库完全支持BIGSERIAL类型，直接使用BIGSERIAL
            sb.append("BIGSERIAL");
            // BIGSERIAL类型已经包含了AUTO_INCREMENT、NOT NULL、UNIQUE等属性，不需要额外添加
        } else {
            // 普通数据类型转换
            String dataType = convertDataType(originalTypeName, isAutoIncrement);
            sb.append(dataType);

            // 处理自增字段 - 根据神通数据库官方文档第945-947行
            if (isAutoIncrement) {
                // 根据神通官方文档第945-947行，神通数据库完全支持AUTO_INCREMENT语法
                // 并且性能优于序列和SERIAL，所以保持AUTO_INCREMENT语法
                sb.append(" AUTO_INCREMENT");
            }
        }

        // 对于SERIAL/BIGSERIAL类型，不需要额外添加约束，因为它们已经包含了所有必要的约束
        boolean isSerialType = "SERIAL".equalsIgnoreCase(originalTypeName) ||
                              "BIGSERIAL".equalsIgnoreCase(originalTypeName);
        if (!isSerialType) {
            // NOT NULL约束
            if (!col.isNullable()) {
                sb.append(" NOT NULL");
            }

            // 默认值
            if (col.getDefaultExpr() != null && !col.getDefaultExpr().isEmpty()) {
                sb.append(" DEFAULT ").append(col.getDefaultExpr());
            }

            // UNIQUE约束
            if (col.isUnique()) {
                sb.append(" UNIQUE");
            }
        }

        // 单列主键 - 对于SERIAL类型也需要显式添加PRIMARY KEY
        if (col.isPrimaryKey()) {
            sb.append(" PRIMARY KEY");
        }

        // CHECK约束 - 根据神通官方文档，神通数据库完全支持CHECK约束
        if (col.getCheckConstraintExpression() != null && !col.getCheckConstraintExpression().trim().isEmpty()) {
            if (col.getCheckConstraintName() != null && !col.getCheckConstraintName().trim().isEmpty()) {
                // 命名CHECK约束：CONSTRAINT constraint_name CHECK (expression)
                sb.append(" CONSTRAINT ").append(col.getCheckConstraintName()).append(" CHECK (").append(col.getCheckConstraintExpression()).append(")");
            } else {
                // 匿名CHECK约束：CHECK (expression)
                sb.append(" CHECK (").append(col.getCheckConstraintExpression()).append(")");
            }
        }

        // FOREIGN KEY约束 - 根据神通官方文档，神通数据库完全支持FOREIGN KEY约束
        if (col.getReferencesTable() != null && !col.getReferencesTable().trim().isEmpty()) {
            sb.append(" REFERENCES ");
            // 使用双引号包围表名（神通数据库标准）
            sb.append("\"").append(col.getReferencesTable()).append("\"");

            // 如果指定了列名，添加列名
            if (col.getReferencesColumn() != null && !col.getReferencesColumn().trim().isEmpty()) {
                sb.append("(\"").append(col.getReferencesColumn()).append("\")");
            }
        }

        return sb.toString();
    }
    
    /**
     * 数据类型转换
     * 基于神通数据库官方文档的正确支持情况
     */
    private String convertDataType(String mysqlType) {
        return convertDataType(mysqlType, false);
    }

    /**
     * 数据类型转换（带AUTO_INCREMENT上下文）
     * 基于神通数据库官方文档的正确支持情况
     */
    private String convertDataType(String mysqlType, boolean isAutoIncrement) {
        if (mysqlType == null) return "VARCHAR(255)";

        String upperType = mysqlType.toUpperCase();

        // 根据神通数据库官方文档，神通数据库不支持SQL层面的UNSIGNED类型修饰符
        // 虽然在编程接口中支持unsigned类型，但在SQL数据类型层面不支持UNSIGNED修饰符
        if (upperType.contains("UNSIGNED")) {
            log.warn("UNSIGNED type is not supported in Shentong database SQL layer, removing UNSIGNED modifier: {}", mysqlType);
            upperType = upperType.replaceAll("\\s+UNSIGNED", "").replaceAll("UNSIGNED\\s+", "").replaceAll("UNSIGNED", "").trim();
            mysqlType = upperType; // 更新原始类型以便后续处理
        }

        // 【关键修复】处理预处理器转换的类型，恢复为神通原始类型
        // 这必须在所有其他类型检查之前进行，确保神通特有类型能够正确恢复
        if (upperType.equals("VARCHAR(127)")) {
            // 这是预处理器从NAME转换来的，恢复为NAME
            // 符合神通数据库官方文档2.9.1节：NAME类型最大长度127字符
            log.debug("Restoring VARCHAR(127) to NAME type for Shentong database (official doc 2.9.1)");
            return "NAME";
        }
        if (upperType.equals("CHAR(1)")) {
            // 这是预处理器从"CHAR"转换来的，恢复为"CHAR"
            // 符合神通数据库官方文档2.9.1节："CHAR"是特殊的固定1字符类型
            log.debug("Restoring CHAR(1) to \"CHAR\" type for Shentong database (official doc 2.9.1)");
            return "\"CHAR\"";
        }

        // NVARCHAR2类型处理：根据神通官方文档，NVARCHAR2已在预处理器中转换为VARCHAR
        // 这里不需要特殊处理，因为预处理器已经正确处理了NVARCHAR2到VARCHAR的映射

        // 整数类型 - 根据神通数据库官方文档，支持TINYINT、SMALLINT、INTEGER、BIGINT
        if (upperType.startsWith("TINYINT")) {
            if (upperType.equals("TINYINT(1)")) {
                // MySQL中TINYINT(1)通常用作布尔类型，转换为神通数据库的BIT类型
                return "BIT"; // 神通数据库BIT类型对应boolean
            }
            return "TINYINT"; // 神通数据库支持TINYINT
        }
        if (upperType.startsWith("SMALLINT")) {
            return "SMALLINT";
        }
        if (upperType.startsWith("MEDIUMINT")) {
            return "INTEGER"; // MEDIUMINT转换为INTEGER（根据官方文档第842行）
        }
        if (upperType.startsWith("INT") || upperType.startsWith("INTEGER")) {
            // 根据神通官方文档第776-782行和第846行，神通数据库同时支持INT和INTEGER
            // 根据测试要求，转换为标准的INTEGER形式
            return "INTEGER";
        }
        if (upperType.startsWith("BIGINT")) {
            return "BIGINT";
        }

        // 自增类型 - 神通数据库完全支持AUTO_INCREMENT语法，与MySQL兼容
        // 根据神通官方文档，AUTO_INCREMENT性能优于序列和SERIAL，保持原始数据类型
        if (upperType.startsWith("BIGSERIAL")) {
            return "BIGSERIAL"; // 神通数据库支持BIGSERIAL
        }
        if (upperType.startsWith("SERIAL")) {
            return "SERIAL"; // 神通数据库支持SERIAL
        }

        // 浮点类型 - 根据神通数据库官方文档第860行，支持DOUBLE PRECISION、REAL、FLOAT
        if (upperType.startsWith("FLOAT")) {
            // 根据神通官方文档第860-861行，神通数据库完全支持FLOAT类型
            // FLOAT默认精度为53，保持与MySQL的兼容性
            if (upperType.contains("(")) {
                return "FLOAT"; // 移除精度参数，使用默认精度
            } else {
                return mysqlType; // 保持原始FLOAT类型
            }
        }
        if (upperType.startsWith("REAL")) {
            return "REAL"; // 神通数据库支持REAL类型
        }
        if (upperType.startsWith("DOUBLE")) {
            return "DOUBLE PRECISION";
        }
        if (upperType.startsWith("DECIMAL") || upperType.startsWith("NUMERIC")) {
            return mysqlType; // 神通数据库支持DECIMAL和NUMERIC
        }

        // 字符串类型 - 根据神通数据库官方文档第581行和第996行
        if (upperType.startsWith("VARCHAR")) {
            // 根据神通官方文档第581行：varchar(n)是character varying(n)的别名
            // 根据神通官方文档第996行示例：**CREATETABLE** tab7_1(aINTAUTO_INCREMENT **PRIMARYKEY** , bVARCHAR( 10 ));
            // 神通数据库完全支持VARCHAR语法，保持与MySQL的兼容性
            return mysqlType;
        }
        // NVARCHAR2类型已在预处理器中处理，这里不需要额外转换
        if (upperType.startsWith("CHARACTERVARYING")) {
            // 修正CHARACTER VARYING的空格问题
            return mysqlType.replaceAll("(?i)CHARACTERVARYING", "CHARACTER VARYING");
        }
        if (upperType.startsWith("CHAR")) {
            // 根据神通官方文档示例，保持CHAR(n)形式
            // 但需要将CHARACTER VARYING转换为VARCHAR
            if (upperType.contains("CHARACTER VARYING")) {
                // CHARACTER VARYING(n) -> VARCHAR(n)
                return mysqlType.replaceAll("(?i)CHARACTER VARYING", "VARCHAR");
            } else {
                // 保持CHAR(n)和CHARACTER(n)的原始形式
                return mysqlType;
            }
        }
        if (upperType.equals("TINYTEXT") || upperType.equals("MEDIUMTEXT") || upperType.equals("LONGTEXT")) {
            return "TEXT"; // 统一使用TEXT类型，最大16777215字节
        }
        if (upperType.startsWith("TEXT")) {
            return "TEXT";
        }

        // 神通数据库特有字符串类型
        if (upperType.equals("NAME")) {
            return "NAME"; // 神通特有，最大127字符
        }
        if (upperType.equals("\"CHAR\"")) {
            return "\"CHAR\""; // 神通特有，固定1字符，超长截断
        }



        // 位串类型 - 神通数据库支持
        if (upperType.startsWith("BIT")) {
            if (upperType.startsWith("BIT VARYING") || upperType.startsWith("VARBIT")) {
                return mysqlType; // 变长位串，最大8*8000bit
            }
            return mysqlType; // 定长位串，最大8*8000bit
        }

        // 日期时间类型 - 神通数据库支持DATE、TIME、TIMESTAMP
        // 注意：TIMESTAMP必须在TIME之前检查，因为TIMESTAMP以TIME开头
        if (upperType.startsWith("DATETIME") || upperType.startsWith("TIMESTAMP")) {
            return "TIMESTAMP";
        }
        if (upperType.startsWith("DATE")) {
            return "DATE";
        }
        if (upperType.startsWith("TIME")) {
            return mysqlType; // 保持原样，支持TIME(p)精度
        }
        if (upperType.equals("YEAR") || upperType.startsWith("YEAR(")) {
            return "SMALLINT"; // 根据神通数据库官方文档，YEAR类型转换为SMALLINT更合适
        }

        // 二进制类型 - 基于神通数据库官方文档
        if (upperType.startsWith("BINARY")) {
            // 神通数据库BINARY最大8000字节
            return mysqlType; // 返回原始类型以保持精度
        }
        if (upperType.startsWith("VARBINARY")) {
            // 神通数据库VARBINARY最大8000字节
            return mysqlType; // 返回原始类型以保持精度
        }
        if (upperType.startsWith("RAW")) {
            // 神通暂时将RAW映射为VARBINARY
            return mysqlType.replaceAll("(?i)RAW", "VARBINARY");
        }
        if (upperType.equals("TINYBLOB") || upperType.equals("MEDIUMBLOB") || upperType.equals("LONGBLOB")) {
            return "BLOB"; // 统一使用BLOB类型
        }
        if (upperType.startsWith("BLOB")) {
            return "BLOB";
        }

        // 大对象类型 - 神通数据库支持
        if (upperType.equals("CLOB")) {
            return "CLOB"; // 神通数据库支持CLOB
        }

        // XML类型 - 神通数据库支持
        if (upperType.equals("XMLTYPE")) {
            return "XMLTYPE"; // 神通数据库支持XMLTYPE
        }

        // 时间间隔类型 - 神通数据库支持
        if (upperType.startsWith("INTERVAL")) {
            return mysqlType; // INTERVAL类型直接支持
        }

        // 布尔类型 - 神通数据库原生支持BOOLEAN和BOOL
        // 根据神通数据库官方文档2.9.8节：神通数据库提供标准SQL数据类型BOOLEAN和BOOL，二者无任何区别
        if (upperType.equals("BOOLEAN") || upperType.equals("BOOL")) {
            return "BOOLEAN"; // 神通数据库原生支持BOOLEAN类型，不需要转换
        }

        // ENUM类型转换 - 根据神通官方文档，ENUM类型支持有限
        if (upperType.startsWith("ENUM")) {
            log.warn("ENUM type has limited support in Shentong database, converting to VARCHAR with CHECK constraint");
            // 提取ENUM值并转换为VARCHAR
            String enumValues = extractEnumValues(mysqlType);
            if (enumValues != null && !enumValues.isEmpty()) {
                // 计算最大长度
                int maxLength = calculateMaxEnumLength(enumValues);
                return "VARCHAR(" + Math.max(maxLength, 50) + ")";
            }
            return "VARCHAR(255)"; // 默认长度
        }

        // SET类型转换 - 神通数据库不完全支持SET类型
        if (upperType.startsWith("SET")) {
            log.warn("SET type is not supported in Shentong database, converting to TEXT");
            return "TEXT";
        }

        // JSON类型转换 - 根据测试验证结果
        if (upperType.equals("JSON")) {
            // 根据ShentongJsonTest测试结果：神通数据库不支持JSON数据类型，但完全支持JSON函数
            // 将JSON数据类型转换为TEXT，同时保持JSON函数的完整支持
            log.info("CONVERSION_INFO: JSON data type is not supported in Shentong database, converting to TEXT. " +
                    "Note: JSON functions (JSON_EXTRACT, JSON_SET, etc.) are fully supported.");
            return "TEXT";
        }

        // 几何类型 - 神通数据库可能不支持
        if (upperType.startsWith("GEOMETRY") || upperType.startsWith("POINT") ||
            upperType.startsWith("LINESTRING") || upperType.startsWith("POLYGON")) {
            log.warn("Geometry types are not supported in Shentong database, converting to TEXT");
            return "TEXT";
        }

        // 其他类型保持不变
        return mysqlType;
    }

    /**
     * 提取ENUM类型的值列表
     */
    private String extractEnumValues(String enumType) {
        if (enumType == null || !enumType.toUpperCase().startsWith("ENUM")) {
            return null;
        }

        // 提取括号内的内容
        int start = enumType.indexOf('(');
        int end = enumType.lastIndexOf(')');
        if (start != -1 && end != -1 && end > start) {
            return enumType.substring(start + 1, end);
        }

        return null;
    }

    /**
     * 计算ENUM值的最大长度
     */
    private int calculateMaxEnumLength(String enumValues) {
        if (enumValues == null || enumValues.trim().isEmpty()) {
            return 50; // 默认长度
        }

        int maxLength = 0;
        // 简单解析ENUM值（假设格式为 'value1', 'value2', ...）
        String[] values = enumValues.split(",");
        for (String value : values) {
            String trimmed = value.trim();
            if (trimmed.startsWith("'") && trimmed.endsWith("'")) {
                int length = trimmed.length() - 2; // 减去引号
                maxLength = Math.max(maxLength, length);
            }
        }

        return Math.max(maxLength, 10); // 至少10个字符
    }
    
    /**
     * 生成ALTER TABLE语句
     */
    private String generateAlterTable(AlterTable alterTable) {
        StringBuilder sb = new StringBuilder();
        String quotedTableName = getQuotedTableName(alterTable.getTableId());

        // 验证表名不为空
        if (quotedTableName == null || quotedTableName.trim().isEmpty()) {
            log.error("CONVERSION_ERROR: Invalid table name in ALTER TABLE statement");
            return "-- ERROR: Invalid table name in ALTER TABLE statement";
        }

        // 处理表级AUTO_INCREMENT设置
        if (alterTable.getAutoIncrementValue() != null) {
            sb.append("ALTER TABLE ").append(quotedTableName).append(" ");
            sb.append("AUTO_INCREMENT = ").append(alterTable.getAutoIncrementValue());
            sb.append(";");

            String result = sb.toString();
            log.info("CONVERSION_SUCCESS: ALTER TABLE AUTO_INCREMENT generated. Table: {}, Value: {}, Result length: {}",
                    alterTable.getTableId().getTableName(),
                    alterTable.getAutoIncrementValue(),
                    result.length());
            return result;
        }

        // 处理单个操作的情况（使用第一个构造函数）
        if (alterTable.getActionType() != null) {
            sb.append("ALTER TABLE ").append(quotedTableName).append(" ");

            switch (alterTable.getActionType()) {
                case SET_AUTO_INCREMENT:
                    // 神通数据库支持ALTER TABLE table_name AUTO_INCREMENT = value语法
                    // 这里需要从某个地方获取value，可能需要扩展AlterTable类
                    sb.append("AUTO_INCREMENT = ").append(alterTable.getColumnName()); // 临时使用columnName存储值
                    break;
                case ADD_COLUMN:
                    sb.append("ADD COLUMN ");
                    if (alterTable.getColumnDefinition() != null) {
                        sb.append(generateColumnDefinitionWithName(alterTable.getColumnName(), alterTable.getColumnDefinition()));
                    }
                    break;
                case DROP_COLUMN:
                    sb.append("DROP COLUMN ").append(quote(alterTable.getColumnName()));
                    break;
                case ALTER_COLUMN:
                    // 神通数据库支持MODIFY COLUMN语法（兼容MySQL）
                    sb.append("MODIFY COLUMN ").append(quote(alterTable.getColumnName())).append(" ");
                    if (alterTable.getColumnDefinition() != null) {
                        String dataType = convertDataType(alterTable.getColumnDefinition().getTypeName());
                        sb.append(dataType);

                        // 处理AUTO_INCREMENT - 神通数据库完全支持AUTO_INCREMENT语法
                        if (alterTable.getColumnDefinition().getExpression() != null &&
                            alterTable.getColumnDefinition().getExpression().contains("AUTO_INCREMENT")) {
                            sb.append(" AUTO_INCREMENT");
                        }

                        if (!alterTable.getColumnDefinition().isNullable()) {
                            sb.append(" NOT NULL");
                        }
                        if (alterTable.getColumnDefinition().getDefaultExpr() != null) {
                            sb.append(" DEFAULT ").append(alterTable.getColumnDefinition().getDefaultExpr());
                        }
                    }
                    break;
                case ADD_CONSTRAINT:
                    // 处理ADD CONSTRAINT操作
                    sb.append("ADD CONSTRAINT ");
                    // alterTable.getColumnName()实际上存储的是约束名称，不是列名
                    if (alterTable.getColumnName() != null && !alterTable.getColumnName().trim().isEmpty()) {
                        sb.append(quote(alterTable.getColumnName())).append(" ");
                    }
                    // 根据约束类型生成相应的SQL
                    if (alterTable.getColumnDefinition() != null) {
                        sb.append(generateConstraintDefinition(alterTable.getColumnDefinition()));
                    }
                    break;
                case DROP_CONSTRAINT:
                    sb.append("DROP CONSTRAINT ");
                    if (alterTable.getColumnName() != null && !alterTable.getColumnName().trim().isEmpty()) {
                        sb.append(quote(alterTable.getColumnName()));
                    }
                    break;
                case SET_COLUMN_DEFAULT:
                    // 根据神通官方文档：ALTER TABLE table_name ALTER COLUMN column_name SET DEFAULT value
                    sb.append("ALTER COLUMN ").append(quote(alterTable.getColumnName())).append(" SET DEFAULT ");
                    if (alterTable.getColumnDefinition() != null && alterTable.getColumnDefinition().getDefaultExpr() != null) {
                        sb.append(alterTable.getColumnDefinition().getDefaultExpr());
                    } else {
                        sb.append("NULL");
                    }
                    break;
                case DROP_COLUMN_DRFAULT:
                    // 根据神通官方文档：ALTER TABLE table_name ALTER COLUMN column_name DROP DEFAULT
                    sb.append("ALTER COLUMN ").append(quote(alterTable.getColumnName())).append(" DROP DEFAULT");
                    break;
                default:
                    log.warn("Unsupported single ALTER TABLE operation: {}", alterTable.getActionType());
                    return "";
            }

            sb.append(";");
            return sb.toString();
        }

        // 处理多个操作的情况（使用第二个构造函数）
        if (alterTable.getSpecifications() != null) {
            for (AlterTable.AlterSpecification spec : alterTable.getSpecifications()) {
                sb.append("ALTER TABLE ").append(quotedTableName).append(" ");

                switch (spec.getActionType()) {
                    case ADD_COLUMN:
                        sb.append("ADD COLUMN ");
                        if (spec.getColumnDefinition() != null) {
                            // 对于ADD COLUMN，列名来自spec.getColumnName()
                            sb.append(generateColumnDefinitionWithName(spec.getColumnName(), spec.getColumnDefinition()));
                        }
                        break;
                    case DROP_COLUMN:
                        sb.append("DROP COLUMN ").append(quote(spec.getColumnName()));
                        break;
                    case ALTER_COLUMN:
                        // 神通数据库支持MODIFY COLUMN语法（兼容MySQL）
                        sb.append("MODIFY COLUMN ").append(quote(spec.getColumnName())).append(" ");
                        if (spec.getColumnDefinition() != null) {
                            String dataType = convertDataType(spec.getColumnDefinition().getTypeName());
                            sb.append(dataType);

                            // 处理AUTO_INCREMENT - 神通数据库完全支持AUTO_INCREMENT语法
                            if (spec.getColumnDefinition().getExpression() != null &&
                                spec.getColumnDefinition().getExpression().contains("AUTO_INCREMENT")) {
                                sb.append(" AUTO_INCREMENT");
                            }

                            if (!spec.getColumnDefinition().isNullable()) {
                                sb.append(" NOT NULL");
                            }
                            if (spec.getColumnDefinition().getDefaultExpr() != null) {
                                sb.append(" DEFAULT ").append(spec.getColumnDefinition().getDefaultExpr());
                            }
                        }
                        break;
                    case ADD_PRIMARY_KEY:
                        // 处理ADD PRIMARY KEY操作 - 神通数据库支持PRIMARY KEY
                        sb.append("ADD PRIMARY KEY ");
                        // 处理列名列表，确保有括号
                        String primaryKeyColumns = (String) spec.getValue();
                        if (primaryKeyColumns != null) {
                            // 如果列名没有括号，添加括号
                            if (!primaryKeyColumns.trim().startsWith("(")) {
                                primaryKeyColumns = "(" + primaryKeyColumns + ")";
                            }
                            String quotedColumns = convertColumnNamesToQuoted(primaryKeyColumns);
                            sb.append(quotedColumns);
                        }
                        break;
                    case DROP_PRIMARY_KEY:
                        sb.append("DROP PRIMARY KEY");
                        break;
                    case ADD_INDEX:
                        // 处理ADD INDEX操作 - 神通数据库支持INDEX
                        sb.append("ADD INDEX ");
                        String indexName = spec.getIndexName();
                        if (indexName != null && !indexName.trim().isEmpty()) {
                            sb.append(quote(indexName)).append(" ");
                        }
                        // 处理列名列表，确保有括号
                        String indexColumnNames = (String) spec.getValue();
                        if (indexColumnNames != null) {
                            // 如果列名没有括号，添加括号
                            if (!indexColumnNames.trim().startsWith("(")) {
                                indexColumnNames = "(" + indexColumnNames + ")";
                            }
                            String quotedColumns = convertColumnNamesToQuoted(indexColumnNames);
                            sb.append(quotedColumns);
                        }
                        break;
                    case ADD_UNIQUE_KEY:
                        // 处理ADD UNIQUE KEY操作 - 神通数据库支持UNIQUE索引
                        sb.append("ADD UNIQUE ");
                        String uniqueIndexName = spec.getIndexName();
                        if (uniqueIndexName != null && !uniqueIndexName.trim().isEmpty()) {
                            sb.append(quote(uniqueIndexName)).append(" ");
                        }
                        // 处理列名列表，确保有括号
                        String uniqueColumnNames = (String) spec.getValue();
                        if (uniqueColumnNames != null) {
                            // 如果列名没有括号，添加括号
                            if (!uniqueColumnNames.trim().startsWith("(")) {
                                uniqueColumnNames = "(" + uniqueColumnNames + ")";
                            }
                            String quotedColumns = convertColumnNamesToQuoted(uniqueColumnNames);
                            sb.append(quotedColumns);
                        }
                        break;
                    case RENAME:
                        // 处理RENAME TO操作 - 神通数据库支持RENAME TO语法
                        String newTableName = (String) spec.getValue();
                        if (newTableName != null && !newTableName.trim().isEmpty()) {
                            sb.append("RENAME TO ").append(quote(newTableName));
                        }
                        break;
                    case DROP_INDEX:
                        String dropIndexName = spec.getIndexName();
                        if (dropIndexName != null && !dropIndexName.trim().isEmpty()) {
                            sb.append("DROP INDEX ").append(quote(dropIndexName));
                        } else if (spec.getColumnName() != null && !spec.getColumnName().trim().isEmpty()) {
                            sb.append("DROP INDEX ").append(quote(spec.getColumnName()));
                        }
                        break;
                    case SET_AUTO_INCREMENT:
                        // 神通数据库支持ALTER TABLE table_name AUTO_INCREMENT = value语法
                        sb.append("AUTO_INCREMENT = ").append(spec.getValue());
                        break;
                    case ADD_CONSTRAINT:
                        // 处理ADD CONSTRAINT操作
                        sb.append("ADD CONSTRAINT ");
                        // spec.getColumnName()实际上存储的是约束名称，不是列名
                        if (spec.getColumnName() != null && !spec.getColumnName().trim().isEmpty()) {
                            sb.append(quote(spec.getColumnName())).append(" ");
                        }
                        // 根据约束类型生成相应的SQL
                        if (spec.getColumnDefinition() != null) {
                            sb.append(generateConstraintDefinition(spec.getColumnDefinition()));
                        }
                        break;
                    case DROP_CONSTRAINT:
                        sb.append("DROP CONSTRAINT ");
                        if (spec.getColumnName() != null && !spec.getColumnName().trim().isEmpty()) {
                            sb.append(quote(spec.getColumnName()));
                        }
                        break;
                    case SET_COLUMN_DEFAULT:
                        // 根据神通官方文档：ALTER TABLE table_name ALTER COLUMN column_name SET DEFAULT value
                        sb.append("ALTER COLUMN ").append(quote(spec.getColumnName())).append(" SET DEFAULT ");
                        if (spec.getColumnDefinition() != null && spec.getColumnDefinition().getDefaultExpr() != null) {
                            sb.append(spec.getColumnDefinition().getDefaultExpr());
                        } else if (spec.getValue() != null) {
                            sb.append(spec.getValue().toString());
                        } else {
                            sb.append("NULL");
                        }
                        break;
                    case DROP_COLUMN_DRFAULT:
                        // 根据神通官方文档：ALTER TABLE table_name ALTER COLUMN column_name DROP DEFAULT
                        sb.append("ALTER COLUMN ").append(quote(spec.getColumnName())).append(" DROP DEFAULT");
                        break;
                    default:
                        log.warn("Unsupported ALTER TABLE operation: {}", spec.getActionType());
                        continue;
                }

                sb.append(";\n");
            }
        }

        return sb.toString().trim();
    }
    
    /**
     * 生成SELECT语句
     */
    private String generateSelectStatement(QueryStmt queryStmt) {
        String originalSql = queryStmt.getSql();
        if (originalSql != null && !originalSql.trim().isEmpty()) {
            // 检查是否是REPLACE INTO语句（被错误解析为QueryStmt）
            String sqlUpper = originalSql.trim().toUpperCase();
            if (sqlUpper.startsWith("REPLACE INTO")) {
                return handleReplaceIntoStatement(originalSql);
            }

            // 检查是否是LOAD DATA语句（被错误解析为QueryStmt）
            if (sqlUpper.startsWith("LOAD DATA")) {
                return handleLoadDataStatement(originalSql);
            }

            // 转换反引号为双引号
            String convertedSql = convertBackticksToDoubleQuotes(originalSql);

            // 转换MySQL函数为神通兼容函数
            convertedSql = convertSqlFunctions(convertedSql);

            // 【新增】处理DUAL表支持 - 神通数据库重要的系统表
            // 根据神通数据库官方文档，DUAL表用于执行计算和函数测试
            convertedSql = processDualTableQueries(convertedSql);

            // 【新增】恢复神通数据库特有语法 - 将预处理器的临时转换恢复为神通原生语法
            convertedSql = restoreShentongSpecificSyntax(convertedSql);

            // 转换LIMIT OFFSET为ROWNUM分页查询
            convertedSql = convertLimitToRownum(convertedSql);

            // 确保以分号结尾
            if (!convertedSql.trim().endsWith(";")) {
                convertedSql += ";";
            }

            return convertedSql;
        }

        return "SELECT * FROM unknown_table;";
    }
    
    /**
     * 转换SQL函数
     * 基于神通数据库官方文档的函数支持情况
     * 神通数据库具有Oracle兼容性，使用Oracle风格的函数
     */
    private String convertSqlFunctions(String sql) {
        String result = sql;

        // 【神通数据库特有语法恢复】恢复预处理器转换的ROWNUM伪列
        // 根据神通数据库官方文档，ROWNUM是重要的伪列功能，用于分页和行号控制
        result = result.replaceAll("(?i)ROW_NUMBER\\(\\)\\s*OVER\\s*\\(\\s*\\)", "ROWNUM");

        // 日期时间函数转换 - 基于神通官方文档
        // 根据神通数据库官方文档：神通数据库支持NOW()、SYSDATE、CURRENT_TIMESTAMP
        // NOW()函数在恢复阶段会被转换为SYSDATE，这是神通数据库的标准做法
        // 这个转换在restoreShentongSpecificSyntax方法中处理
        // CURRENT_TIMESTAMP保持不变，神通数据库原生支持
        result = result.replaceAll("(?i)\\bCURDATE\\(\\)", "CURRENT_DATE");
        result = result.replaceAll("(?i)\\bCURTIME\\(\\)", "CURRENT_TIME");

        // 神通数据库支持的特殊日期值转换
        result = result.replaceAll("(?i)'now'", "CURRENT_TIMESTAMP");
        result = result.replaceAll("(?i)'today'", "CURRENT_DATE");
        result = result.replaceAll("(?i)'yesterday'", "(CURRENT_DATE - INTERVAL '1' DAY)");
        result = result.replaceAll("(?i)'tomorrow'", "(CURRENT_DATE + INTERVAL '1' DAY)");

        // 字符串函数转换 - 根据神通数据库官方文档和测试失败
        // IFNULL在神通数据库中直接支持，不需要转换
        // CONCAT函数也直接支持
        // 根据测试失败，LENGTH函数需要转换为CHAR_LENGTH
        result = result.replaceAll("(?i)\\bLENGTH\\(", "CHAR_LENGTH(");

        // 根据神通官方文档第16406行和第82134行，神通数据库原生支持NOW()函数，保持不变
        // result = result.replaceAll("(?i)\\bNOW\\(\\)", "CURRENT_TIMESTAMP"); // 注释掉，保持NOW()不变

        // 数学函数转换
        result = result.replaceAll("(?i)\\bRAND\\(\\)", "RANDOM()");

        // 聚合函数转换
        // GROUP_CONCAT在神通数据库中可能需要使用STRING_AGG替代
        result = convertGroupConcatFunction(result);

        // 条件函数转换
        result = convertCaseFunctions(result);

        // 【新增】分析函数转换 - 神通数据库重要的窗口函数支持
        // 根据神通数据库官方文档，支持LAG、LEAD、FIRST_VALUE、LAST_VALUE等分析函数
        result = convertAnalyticFunctions(result);

        // 【新增】JSON函数转换 - 根据ShentongJsonTest测试验证结果
        // 神通数据库完全支持JSON函数，保持所有JSON函数不变
        result = convertJsonFunctions(result);

        // 【新增】全文搜索函数转换 - 根据神通数据库官方文档
        // 神通数据库支持FULLTEXT索引和全文搜索功能
        result = convertFullTextSearchFunctions(result);

        return result;
    }

    /**
     * 转换GROUP_CONCAT函数
     * 根据神通数据库官方文档和测试要求，神通数据库完全支持GROUP_CONCAT函数
     * 保持GROUP_CONCAT函数不变，体现神通数据库对MySQL的高度兼容性
     */
    private String convertGroupConcatFunction(String sql) {
        // 根据神通数据库官方文档和测试验证，神通数据库原生支持GROUP_CONCAT函数
        // 保持GROUP_CONCAT函数不变，不需要转换为STRING_AGG
        // 这体现了神通数据库对MySQL聚合函数的完全兼容性
        return sql; // 保持不变
    }

    /**
     * 转换全文搜索函数
     *
     * 基于官方文档验证的转换规则：
     * - MySQL官方文档：https://dev.mysql.com/doc/refman/8.4/en/fulltext-search.html
     * - 神通数据库官方文档：shentong.md (第31209-31263行，第26229-26272行)
     *
     * 神通数据库支持完整的全文搜索功能：
     * - CREATE FULLTEXT INDEX：创建全文索引
     * - ALTER FULLTEXT INDEX：更新异步全文索引
     * - DROP FULLTEXT INDEX：删除全文索引
     * - OPTIMIZE FULLTEXT INDEX：优化全文索引
     * - 支持多种分词器：BasicAnalyzer、StandardAnalyzer、CJKAnalyzer、ChineseAnalyzer
     * - 支持停用词功能：USE STOPWORDS ON | OFF
     * - 支持同步/异步更新：SYNC UPDATE ON | OFF
     * - 支持多列索引：MULTICOLUMN
     *
     * 转换策略：
     * 根据神通数据库官方文档，神通数据库原生支持MATCH AGAINST语法，
     * 因此保持MATCH AGAINST语法不变，体现神通数据库对MySQL全文搜索的完全兼容性。
     */
    private String convertFullTextSearchFunctions(String sql) {
        if (sql == null) {
            return null;
        }

        String result = sql;

        // 检测MATCH AGAINST语法
        if (result.contains("MATCH") && result.contains("AGAINST")) {
            log.info("FULLTEXT_SEARCH_CONVERSION: MATCH AGAINST syntax detected. Shentong database supports it natively.");

            // 根据神通数据库官方文档，神通数据库原生支持MATCH AGAINST语法
            // 保持MATCH AGAINST语法不变，体现神通数据库对MySQL全文搜索的完全兼容性
            // 这包括：
            // 1. MATCH(column1, column2) AGAINST('search_text' IN NATURAL LANGUAGE MODE)
            // 2. MATCH(column1, column2) AGAINST('search_text' IN BOOLEAN MODE)
            // 3. MATCH(column1, column2) AGAINST('search_text' WITH QUERY EXPANSION)
            // 4. MATCH(column1, column2) AGAINST('search_text')

            log.info("FULLTEXT_SEARCH_CONVERSION: MATCH AGAINST syntax preserved for Shentong database compatibility.");
        }

        return result;
    }

    /**
     * 转换JSON函数
     * 根据ShentongJsonTest测试验证结果，神通数据库完全支持JSON函数
     * 保持所有JSON函数不变，体现神通数据库对MySQL JSON功能的完全兼容性
     */
    private String convertJsonFunctions(String sql) {
        // 根据ShentongJsonTest测试验证结果：
        // ✅ 神通数据库支持JSON函数（JSON_EXTRACT、JSON_SET、JSON_VALID等）
        // ✅ 神通数据库支持JSON聚合函数（JSON_ARRAYAGG、JSON_OBJECTAGG等）
        // ✅ 神通数据库支持JSON路径操作符（->、->>）
        // ✅ 神通数据库支持JSON修改函数（JSON_SET、JSON_REPLACE、JSON_REMOVE等）

        // 所有JSON函数保持不变，神通数据库原生支持
        String result = sql;

        // 记录JSON函数的使用情况
        if (result.matches(".*\\bJSON_[A-Z_]+\\s*\\(.*")) {
            log.info("CONVERSION_INFO: JSON functions detected and preserved. " +
                    "Shentong database fully supports MySQL JSON functions.");
        }

        // JSON路径操作符也保持不变
        if (result.contains("->") || result.contains("->>")) {
            log.info("CONVERSION_INFO: JSON path operators (->, ->>) detected and preserved. " +
                    "Shentong database fully supports JSON path expressions.");
        }

        return result; // 保持所有JSON函数和操作符不变
    }

    /**
     * 转换条件函数
     * 基于神通数据库官方文档的函数支持
     */
    private String convertCaseFunctions(String sql) {
        String result = sql;

        // 【修正】DECODE函数处理 - 神通数据库原生支持DECODE函数
        // 根据神通数据库官方文档第10313-10369行，DECODE是神通数据库原生支持的函数
        // 神通数据库完全支持DECODE函数，不需要转换为CASE WHEN
        // 保持DECODE函数不变，这是神通数据库的标准做法

        // IF函数转换为CASE WHEN
        // MySQL: IF(condition, value1, value2)
        // 神通: CASE WHEN condition THEN value1 ELSE value2 END
        java.util.regex.Pattern ifPattern = java.util.regex.Pattern.compile(
            "(?i)\\bIF\\s*\\(([^,]+),([^,]+),([^)]+)\\)",
            java.util.regex.Pattern.DOTALL
        );
        java.util.regex.Matcher ifMatcher = ifPattern.matcher(result);

        StringBuffer sb = new StringBuffer();
        while (ifMatcher.find()) {
            String condition = ifMatcher.group(1).trim();
            String value1 = ifMatcher.group(2).trim();
            String value2 = ifMatcher.group(3).trim();

            String replacement = String.format("CASE WHEN %s THEN %s ELSE %s END",
                condition, value1, value2);
            ifMatcher.appendReplacement(sb, replacement);
        }
        ifMatcher.appendTail(sb);

        return sb.toString();
    }

    // 【已删除】convertDecodeFunction方法
    // 根据神通数据库官方文档第10313-10369行，DECODE是神通数据库原生支持的函数
    // 不需要转换为CASE WHEN，应该保持DECODE函数不变

    // 【已删除】findMatchingCloseParen方法
    // 由于DECODE函数在神通数据库中原生支持，不再需要此辅助方法

    // 【已删除】buildCaseFromDecodeParams方法
    // 由于DECODE函数在神通数据库中原生支持，不再需要此方法

    // 【已删除】splitDecodeParams方法
    // 由于DECODE函数在神通数据库中原生支持，不再需要此方法

    /**
     * 转换反引号为双引号
     * 神通数据库使用双引号作为标识符引用符
     * 简化实现：只转换反引号，不自动添加引号给其他标识符
     */
    private String convertBackticksToDoubleQuotes(String sql) {
        if (sql == null) return null;

        // 将MySQL的反引号转换为神通数据库的双引号
        return sql.replaceAll("`([^`]+)`", "\"$1\"");
    }


    /**
     * 转换MySQL的LIMIT OFFSET为神通数据库的ROWNUM分页查询
     * 根据神通官方文档，使用ROWNUM实现分页
     */
    private String convertLimitToRownum(String sql) {
        if (sql == null) return null;

        log.debug("Converting LIMIT to ROWNUM for SQL: {}", sql);

        // 处理 LIMIT n OFFSET m 格式
        java.util.regex.Pattern limitOffsetPattern = java.util.regex.Pattern.compile(
            "(?i)\\s+LIMIT\\s+(\\d+)\\s+OFFSET\\s+(\\d+)\\s*;?\\s*$"
        );
        java.util.regex.Matcher limitOffsetMatcher = limitOffsetPattern.matcher(sql);

        if (limitOffsetMatcher.find()) {
            String limit = limitOffsetMatcher.group(1);
            String offset = limitOffsetMatcher.group(2);
            int limitNum = Integer.parseInt(limit);
            int offsetNum = Integer.parseInt(offset);
            int startRow = offsetNum + 1;
            int endRow = offsetNum + limitNum;

            // 构建ROWNUM分页查询
            String baseQuery = sql.substring(0, limitOffsetMatcher.start()).trim();
            if (baseQuery.endsWith(";")) {
                baseQuery = baseQuery.substring(0, baseQuery.length() - 1);
            }

            String result = String.format(
                "SELECT * FROM (\n" +
                "    SELECT ROWNUM AS ROW_NUM, A.* FROM (\n" +
                "        %s\n" +
                "    ) A\n" +
                ") B WHERE B.ROW_NUM BETWEEN %d AND %d;",
                baseQuery, startRow, endRow
            );

            log.debug("Converted LIMIT {} OFFSET {} to ROWNUM BETWEEN {} AND {}", limit, offset, startRow, endRow);
            return result;
        }

        // 处理 LIMIT n 格式（没有OFFSET）
        java.util.regex.Pattern limitOnlyPattern = java.util.regex.Pattern.compile(
            "(?i)\\s+LIMIT\\s+(\\d+)\\s*;?\\s*$"
        );
        java.util.regex.Matcher limitOnlyMatcher = limitOnlyPattern.matcher(sql);

        if (limitOnlyMatcher.find()) {
            String limit = limitOnlyMatcher.group(1);
            int limitNum = Integer.parseInt(limit);

            // 构建ROWNUM分页查询
            String baseQuery = sql.substring(0, limitOnlyMatcher.start()).trim();
            if (baseQuery.endsWith(";")) {
                baseQuery = baseQuery.substring(0, baseQuery.length() - 1);
            }

            String result = String.format(
                "SELECT * FROM (\n" +
                "    SELECT ROWNUM AS ROW_NUM, A.* FROM (\n" +
                "        %s\n" +
                "    ) A\n" +
                ") B WHERE B.ROW_NUM <= %d;",
                baseQuery, limitNum
            );

            log.debug("Converted LIMIT {} to ROWNUM <= {}", limit, limitNum);
            return result;
        }

        log.debug("No LIMIT clause found, returning original SQL");
        return sql;
    }

    /**
     * 处理DUAL表查询
     * 基于神通数据库官方文档，DUAL表是重要的系统表，用于执行计算和函数测试
     *
     * DUAL表的主要用途：
     * 1. 执行简单的计算：SELECT 1+1 FROM dual;
     * 2. 测试函数：SELECT SYSDATE FROM dual;
     * 3. 查询序列：SELECT seq.NEXTVAL FROM dual;
     * 4. 条件测试：SELECT DECODE(1, 1, 'ONE', 'OTHER') FROM dual;
     */
    private String processDualTableQueries(String sql) {
        if (sql == null) return null;

        // 神通数据库完全支持DUAL表，保持原样
        // 根据神通数据库官方文档，DUAL表是Oracle兼容的系统表
        // 神通数据库中的DUAL表功能与Oracle完全一致

        // 检查是否包含DUAL表查询
        if (sql.toUpperCase().contains("FROM DUAL") || sql.toUpperCase().contains("FROM \"DUAL\"")) {
            log.debug("Detected DUAL table query, keeping original syntax for Shentong database compatibility");

            // 神通数据库原生支持DUAL表，不需要转换
            // 只需要确保标识符引用符合神通数据库标准
            String result = sql;

            // 标准化DUAL表引用（如果需要）
            result = result.replaceAll("(?i)\\bFROM\\s+DUAL\\b", "FROM dual");
            result = result.replaceAll("(?i)\\bFROM\\s+\"DUAL\"\\b", "FROM dual");

            log.debug("Processed DUAL table query: {}", result);
            return result;
        }

        // 检查是否是没有FROM子句的SELECT语句（MySQL风格）
        // 这种情况下需要添加FROM dual来符合神通数据库语法
        if (isSelectWithoutFrom(sql)) {
            log.debug("Detected SELECT without FROM clause, adding 'FROM dual' for Shentong compatibility");
            log.debug("Original SQL before adding DUAL: {}", sql);

            String result = addDualTableToSelectWithoutFrom(sql);
            log.debug("Added DUAL table to SELECT: {}", result);
            return result;
        }

        return sql;
    }

    /**
     * 检查是否是没有FROM子句的SELECT语句
     * MySQL允许SELECT 1+1这样的语句，但神通数据库需要FROM dual
     */
    private boolean isSelectWithoutFrom(String sql) {
        if (sql == null) return false;

        String upperSql = sql.toUpperCase().trim();

        log.debug("Checking if SELECT without FROM: {}", upperSql);

        // 必须是SELECT语句
        if (!upperSql.startsWith("SELECT")) {
            log.debug("Not a SELECT statement");
            return false;
        }

        // 不能包含FROM子句（更严格的检查）
        // 需要考虑FROM前面可能是空格或换行符
        if (upperSql.contains(" FROM ") || upperSql.contains("\nFROM ") || upperSql.contains("\rFROM ")) {
            log.debug("Contains FROM clause, not a SELECT without FROM");
            return false;
        }

        // 不能包含子查询（简单检查）
        if (upperSql.contains("(SELECT")) {
            log.debug("Contains subquery, not a simple SELECT without FROM");
            return false;
        }

        // 检查是否是简单的计算或函数调用
        // 例如：SELECT 1+1; SELECT NOW(); SELECT SYSDATE;
        // 但不能包含复杂的子句如ORDER BY, GROUP BY, HAVING等
        if (upperSql.contains(" ORDER BY ") || upperSql.contains(" GROUP BY ") ||
            upperSql.contains(" HAVING ") || upperSql.contains(" WHERE ") ||
            upperSql.contains(" LIMIT ") || upperSql.contains(" OFFSET ")) {
            log.debug("Contains complex clauses, not a simple SELECT without FROM");
            return false;
        }

        java.util.regex.Pattern simpleSelectPattern = java.util.regex.Pattern.compile(
            "^SELECT\\s+[^;]+;?\\s*$",
            java.util.regex.Pattern.CASE_INSENSITIVE | java.util.regex.Pattern.DOTALL
        );

        boolean result = simpleSelectPattern.matcher(upperSql).matches();
        log.debug("Simple SELECT pattern match result: {}", result);
        return result;
    }

    /**
     * 为没有FROM子句的SELECT语句添加FROM dual
     */
    private String addDualTableToSelectWithoutFrom(String sql) {
        if (sql == null) return null;

        String trimmedSql = sql.trim();

        // 移除末尾的分号（如果有）
        boolean hasSemicolon = trimmedSql.endsWith(";");
        if (hasSemicolon) {
            trimmedSql = trimmedSql.substring(0, trimmedSql.length() - 1).trim();
        }

        // 添加FROM dual
        String result = trimmedSql + " FROM dual";

        // 恢复分号（如果原来有）
        if (hasSemicolon) {
            result += ";";
        }

        return result;
    }

    /**
     * 恢复神通数据库特有语法
     * 将预处理器中的临时转换恢复为神通数据库原生语法
     *
     * 主要恢复：
     * 1. FROM (SELECT 1) AS dual_table → FROM dual
     * 2. NOW() → SYSDATE (神通数据库标准时间函数)
     * 3. 其他预处理器转换的语法
     */
    private String restoreShentongSpecificSyntax(String sql) {
        if (sql == null) return null;

        String result = sql;

        // 恢复DUAL表语法
        // 将预处理器的 FROM (SELECT 1) AS dual_table 转换回 FROM dual
        result = result.replaceAll("(?i)\\bFROM\\s+\\(SELECT\\s+1\\)\\s+AS\\s+dual_table\\b", "FROM dual");

        log.debug("Restored Shentong DUAL table syntax: FROM (SELECT 1) AS dual_table → FROM dual");

        // 恢复NOW()函数 - 将MySQL的NOW()转换为神通数据库的SYSDATE
        // 根据神通数据库官方文档第23548行：SYSDATE - 取系统时间
        // 神通数据库标准做法是将NOW()转换为SYSDATE，这是神通数据库的原生时间函数
        result = result.replaceAll("(?i)\\bNOW\\(\\)", "SYSDATE");

        log.debug("Converted MySQL NOW() function to Shentong SYSDATE: NOW() → SYSDATE");

        // 恢复其他神通特有语法（如果需要）
        // 例如：CURDATE() → TRUNC(SYSDATE)
        result = result.replaceAll("(?i)\\bCURDATE\\(\\)\\b", "TRUNC(SYSDATE)");

        // 恢复DATE_FORMAT到TO_CHAR（如果需要）
        // 这需要更复杂的格式转换，暂时保持简单处理

        return result;
    }

    /**
     * 转换分析函数（窗口函数）
     * 基于神通数据库官方文档，神通数据库支持Oracle兼容的分析函数
     *
     * 主要转换：
     * 1. LAG/LEAD函数：访问前后行数据
     * 2. FIRST_VALUE/LAST_VALUE函数：获取窗口内的第一个/最后一个值
     * 3. NTILE函数：分组排名
     * 4. PERCENT_RANK/CUME_DIST函数：百分比排名
     */
    private String convertAnalyticFunctions(String sql) {
        if (sql == null) return null;

        String result = sql;

        // 神通数据库完全支持Oracle风格的分析函数，大部分情况下不需要转换
        // 但是可能需要处理一些MySQL特有的语法差异

        // 处理LAG函数 - 神通数据库原生支持，保持不变
        // LAG(column, offset, default) OVER (PARTITION BY ... ORDER BY ...)
        // 神通数据库完全兼容Oracle的LAG语法

        // 处理LEAD函数 - 神通数据库原生支持，保持不变
        // LEAD(column, offset, default) OVER (PARTITION BY ... ORDER BY ...)
        // 神通数据库完全兼容Oracle的LEAD语法

        // 处理FIRST_VALUE函数 - 神通数据库原生支持，保持不变
        // FIRST_VALUE(column) OVER (PARTITION BY ... ORDER BY ... ROWS/RANGE ...)
        // 神通数据库完全兼容Oracle的FIRST_VALUE语法

        // 处理LAST_VALUE函数 - 神通数据库原生支持，保持不变
        // LAST_VALUE(column) OVER (PARTITION BY ... ORDER BY ... ROWS/RANGE ...)
        // 神通数据库完全兼容Oracle的LAST_VALUE语法

        // 处理NTILE函数 - 神通数据库原生支持，保持不变
        // NTILE(n) OVER (PARTITION BY ... ORDER BY ...)
        // 神通数据库完全兼容Oracle的NTILE语法

        // 处理PERCENT_RANK函数 - 神通数据库原生支持，保持不变
        // PERCENT_RANK() OVER (PARTITION BY ... ORDER BY ...)
        // 神通数据库完全兼容Oracle的PERCENT_RANK语法

        // 处理CUME_DIST函数 - 神通数据库原生支持，保持不变
        // CUME_DIST() OVER (PARTITION BY ... ORDER BY ...)
        // 神通数据库完全兼容Oracle的CUME_DIST语法

        // 处理RANK函数 - 神通数据库原生支持，保持不变
        // RANK() OVER (PARTITION BY ... ORDER BY ...)
        // 神通数据库完全兼容Oracle的RANK语法

        // 处理DENSE_RANK函数 - 神通数据库原生支持，保持不变
        // DENSE_RANK() OVER (PARTITION BY ... ORDER BY ...)
        // 神通数据库完全兼容Oracle的DENSE_RANK语法

        // 处理ROW_NUMBER函数 - 已在其他地方处理，这里保持不变
        // ROW_NUMBER() OVER (PARTITION BY ... ORDER BY ...)
        // 神通数据库完全兼容Oracle的ROW_NUMBER语法

        // 处理MySQL特有的窗口函数语法差异（如果有）
        // 目前神通数据库的分析函数语法与Oracle高度兼容，大部分情况下不需要转换

        log.debug("Processed analytic functions for Shentong database compatibility");
        return result;
    }

    /**
     * 其他辅助方法
     */
    private String generateCreateDatabase(CreateDatabase createDatabase) {
        return "CREATE DATABASE " + quote(createDatabase.getDatabaseName()) + " CHARACTER SET UTF8;";
    }
    
    private String generateDropDatabase(DropDatabase dropDatabase) {
        StringBuilder sb = new StringBuilder("DROP DATABASE ");
        if (dropDatabase.isIfExists()) {
            sb.append("IF EXISTS ");
        }
        sb.append(quote(dropDatabase.getDatabaseName())).append(";");
        return sb.toString();
    }
    
    private String generateUseStatement(UseStatement useStatement) {
        String result = "-- USE " + quote(useStatement.getDatabaseName()) +
               "; -- 神通数据库通过连接字符串指定数据库";

        // 记录USE语句的转换日志
        log.info("CONVERSION_SUCCESS: USE statement converted to comment for Shentong database. " +
                "Database: {}, Result length: {}", useStatement.getDatabaseName(), result.length());

        return result;
    }

    /**
     * 生成SET语句
     * 根据神通数据库官方文档，处理MySQL特有的SET语句
     */
    private String generateSetStatement(SetStatement setStatement) {
        String variableName = setStatement.getVariableName();
        String value = setStatement.getValue();
        String originalSql = setStatement.getSql();

        // 转换反引号为双引号
        if (variableName != null) {
            variableName = convertBackticksToDoubleQuotes(variableName);
        }
        if (value != null) {
            value = convertBackticksToDoubleQuotes(value);
            value = convertSqlFunctions(value);
        }

        // 根据变量名称进行特殊处理
        if (variableName == null) {
            variableName = "unknown_variable";
        }

        switch (variableName.toLowerCase()) {
            case "foreign_key_checks":
                // 神通数据库中外键检查的处理方式不同，转换为注释
                log.warn("UNSUPPORTED_FEATURE: SET FOREIGN_KEY_CHECKS is not supported in Shentong database. " +
                        "Original SQL: {}. Converting to comment.", originalSql != null ? originalSql.trim() : "SET " + variableName + " = " + value);
                return "-- SET " + quote("foreign_key_checks") + " = " + value + "; -- 神通数据库中外键检查通过其他方式配置";

            case "names":
                // SET NAMES语句处理字符集转换
                String shentongCharset = convertCharsetToShentong(value);
                if (shentongCharset != null) {
                    log.debug("Converted MySQL charset '{}' to Shentong charset '{}' in SET NAMES", value, shentongCharset);
                    return "SET NAMES " + shentongCharset + ";";
                } else {
                    log.warn("Unknown charset '{}' in SET NAMES, converting to comment", value);
                    return "-- SET NAMES " + value + "; -- 神通数据库中字符集通过连接字符串指定";
                }

            case "character_set_client":
            case "character_set_connection":
            case "character_set_results":
                // 神通数据库的字符集配置方式不同，转换为注释
                log.warn("UNSUPPORTED_FEATURE: SET CHARACTER SET is not supported in Shentong database. " +
                        "Original SQL: {}. Converting to comment.", originalSql != null ? originalSql.trim() : "SET " + variableName + " = " + value);
                return "-- SET " + variableName + " = " + value + "; -- 神通数据库中字符集通过数据库创建时指定";

            case "autocommit":
                // 神通数据库支持AUTOCOMMIT设置
                if ("1".equals(value) || "ON".equalsIgnoreCase(value)) {
                    return "SET AUTOCOMMIT ON;";
                } else if ("0".equals(value) || "OFF".equalsIgnoreCase(value)) {
                    return "SET AUTOCOMMIT OFF;";
                } else {
                    return "SET AUTOCOMMIT " + value + ";";
                }

            default:
                // 其他SET语句，神通数据库基本支持MySQL兼容的SET语法
                StringBuilder sb = new StringBuilder();
                sb.append("SET ").append(variableName).append(" = ").append(value);

                // 确保以分号结尾
                if (!sb.toString().trim().endsWith(";")) {
                    sb.append(";");
                }

                return sb.toString();
        }
    }

    /**
     * 转换MySQL字符集为神通数据库字符集
     */
    private String convertCharsetToShentong(String mysqlCharset) {
        if (mysqlCharset == null) {
            return null;
        }

        String charset = mysqlCharset.toLowerCase().trim();

        // 移除引号
        if (charset.startsWith("'") && charset.endsWith("'")) {
            charset = charset.substring(1, charset.length() - 1);
        }
        if (charset.startsWith("\"") && charset.endsWith("\"")) {
            charset = charset.substring(1, charset.length() - 1);
        }

        // 根据神通数据库官方文档，神通数据库支持的字符集
        switch (charset) {
            case "utf8":
            case "utf8mb4":
                return "UTF8";
            case "latin1":
                return "ISO_8859_1";
            case "gbk":
                return "GBK";
            case "gb2312":
                return "GB2312";
            case "ascii":
                return "ASCII";
            default:
                // 未知字符集，返回null让调用者处理
                return null;
        }
    }

    /**
     * 生成CALL语句
     * 根据神通数据库官方文档第30371-30377行和第37898-37919行：
     * - CALL和EXEC功能相同，但必须选其一
     * - 推荐使用EXEC语法：EXEC procedure([args][, ...])
     * - 神通数据库同时支持CALL和EXEC语法
     */
    private String generateCallStatement(CallStatement callStatement) {
        StringBuilder sb = new StringBuilder();

        // 根据神通官方文档，推荐使用EXEC语法而不是CALL
        // 第32335行：执行存储过程: 存储过程在神通数据库里面用EXEC procedure([ args ][, ... ])的方式来执行
        sb.append("EXEC ");

        // 添加存储过程名称
        String procedureName = callStatement.getProcedureName();
        if (procedureName != null) {
            // 转换反引号为双引号
            procedureName = convertBackticksToDoubleQuotes(procedureName);
            sb.append(procedureName);
        }

        // 添加参数
        String parameters = callStatement.getParameters();
        if (parameters != null && !parameters.trim().isEmpty()) {
            // 转换反引号为双引号
            parameters = convertBackticksToDoubleQuotes(parameters);
            // 转换SQL函数
            parameters = convertSqlFunctions(parameters);
            sb.append("(").append(parameters).append(")");
        }

        // 确保以分号结尾
        if (!sb.toString().trim().endsWith(";")) {
            sb.append(";");
        }

        log.info("CONVERSION_SUCCESS: Successfully converted CALL to EXEC statement for Shentong database");
        return sb.toString();
    }

    /**
     * 生成CREATE INDEX语句
     * 根据神通官方文档第31585-31840行，神通数据库完全支持索引功能
     */
    private String generateCreateIndexStatement(CreateIndex createIndex) {
        StringBuilder sb = new StringBuilder();

        // 根据索引类型生成相应的CREATE语句
        sb.append("CREATE ");

        switch (createIndex.getIndexType()) {
            case UNIQUE:
                sb.append("UNIQUE ");
                break;
            case FULLTEXT:
                // 根据文档第31209-31263行，神通支持全文索引
                sb.append("FULLTEXT ");
                break;
            case SPATIAL:
                // 空间索引可能需要特殊处理，暂时作为普通索引
                log.warn("SPATIAL index converted to normal index for Shentong compatibility");
                break;
            case NORMAL:
            default:
                // 普通索引，不需要额外关键字
                break;
        }

        sb.append("INDEX ");

        // 索引名称 - 使用双引号包围（神通数据库标准）
        if (createIndex.getIndexName() != null && !createIndex.getIndexName().trim().isEmpty()) {
            sb.append("\"").append(createIndex.getIndexName()).append("\"");
        }

        // ON table_name - 使用getQuotedTableName方法确保schema不丢失
        sb.append(" ON ");
        if (createIndex.getTableId() != null) {
            sb.append(getQuotedTableName(createIndex.getTableId()));
        }

        // 列定义 (column1, column2, ...)
        if (createIndex.getColumns() != null && !createIndex.getColumns().isEmpty()) {
            sb.append("(");
            boolean first = true;
            for (CreateIndex.IndexColumn column : createIndex.getColumns()) {
                if (!first) {
                    sb.append(", ");
                }
                first = false;

                // 列名使用双引号包围
                sb.append("\"").append(column.getColumnName()).append("\"");

                // 列长度（如果指定）
                if (column.getLength() != null) {
                    sb.append("(").append(column.getLength()).append(")");
                }

                // 排序顺序（ASC/DESC）
                if (column.getSortOrder() != null && !column.getSortOrder().trim().isEmpty()) {
                    sb.append(" ").append(column.getSortOrder());
                }
            }
            sb.append(")");
        }

        // 算法（如果指定）- 神通支持BTREE、BITMAP等
        if (createIndex.getAlgorithm() != null && !createIndex.getAlgorithm().trim().isEmpty()) {
            String algorithm = createIndex.getAlgorithm().toUpperCase();
            if ("BTREE".equals(algorithm) || "BITMAP".equals(algorithm)) {
                sb.append(" USING ").append(algorithm);
            } else {
                log.debug("Algorithm {} may not be supported by Shentong, using default", algorithm);
            }
        }

        // 注释（如果指定）
        if (createIndex.getComment() != null && !createIndex.getComment().trim().isEmpty()) {
            // 神通数据库的索引注释语法可能需要特殊处理
            log.debug("Index comment will be handled separately: {}", createIndex.getComment());
        }

        sb.append(";");
        return sb.toString();
    }
    
    /**
     * 生成DROP TABLE语句
     * 根据神通官方文档第37680-37810行，神通数据库完全支持DROP TABLE语句
     */
    private String generateDropTable(DropTable dropTable) {
        StringBuilder sb = new StringBuilder();
        sb.append("DROP TABLE ");

        if (dropTable.isIfExists()) {
            sb.append("IF EXISTS ");
        }

        // 处理多个表名
        for (int i = 0; i < dropTable.getTableIds().size(); i++) {
            if (i > 0) {
                sb.append(", ");
            }
            TableId tableId = dropTable.getTableIds().get(i);
            sb.append(quote(tableId.getTableName()));
        }

        sb.append(";");
        return sb.toString();
    }
    
    private String generateInsertStatement(InsertTable insertTable) {
        // 检查是否有原始SQL包含不支持的语法
        String originalSql = insertTable.getSql();
        if (originalSql != null) {
            String upperSql = originalSql.toUpperCase();

            // 检查INSERT SET语法 - 根据神通数据库官方文档，不支持此MySQL特有语法
            // MySQL INSERT ... SET语法参考：https://dev.mysql.com/doc/refman/8.4/en/insert.html
            if (upperSql.contains("INSERT") && upperSql.contains("SET") &&
                !upperSql.contains("VALUES") && !upperSql.contains("SELECT")) {
                log.error("UNSUPPORTED_STATEMENT: INSERT ... SET syntax is MySQL-specific and not supported in Shentong database. " +
                         "Reference: https://dev.mysql.com/doc/refman/8.4/en/insert.html. " +
                         "Please use standard INSERT INTO ... VALUES syntax.");
                return "-- INSERT ... SET syntax is MySQL-specific and not supported in Shentong database\n" +
                       "-- Reference: https://dev.mysql.com/doc/refman/8.4/en/insert.html\n" +
                       "-- Original SQL: " + originalSql + "\n" +
                       "-- Please convert to INSERT INTO ... VALUES syntax;";
            }

            // 检查ON DUPLICATE KEY UPDATE语法 - 根据神通数据库官方文档，不支持此语法
            if (upperSql.contains("ON DUPLICATE KEY UPDATE")) {
                log.error("UNSUPPORTED_STATEMENT: INSERT ... ON DUPLICATE KEY UPDATE syntax is not supported in Shentong database. " +
                         "Please use standard INSERT INTO ... VALUES syntax.");
                return "-- INSERT ... ON DUPLICATE KEY UPDATE syntax is not supported in Shentong database\n" +
                       "-- Original SQL: " + originalSql + "\n" +
                       "-- Please use standard INSERT INTO ... VALUES syntax;";
            }
        }

        StringBuilder sb = new StringBuilder();

        // 根据神通数据库官方文档，只支持标准的INSERT INTO ... VALUES语法
        sb.append("INSERT INTO ");
        sb.append(getQuotedTableName(insertTable.getTableId()));

        // 处理列名列表
        if (insertTable.getColumns() != null && !insertTable.getColumns().isEmpty()) {
            sb.append(" (");
            List<String> quotedColumns = new ArrayList<>();
            for (String column : insertTable.getColumns()) {
                quotedColumns.add(quote(column));
            }
            sb.append(String.join(", ", quotedColumns));
            sb.append(")");
        }

        // 处理VALUES子句
        if (insertTable.getValuesClause() != null) {
            sb.append(" ");
            sb.append(generateValuesClause(insertTable.getValuesClause()));
        }

        // 处理INSERT INTO...SELECT子句
        if (insertTable.getQueryStmt() != null) {
            sb.append(" ");
            QueryStmt queryStmt = insertTable.getQueryStmt();
            String selectSql = queryStmt.getSql();
            if (selectSql != null && !selectSql.trim().isEmpty()) {
                try {
                    // 转换SELECT语句中的MySQL语法到神通语法
                    String convertedSelectSql = convertBackticksToDoubleQuotes(selectSql);
                    convertedSelectSql = convertSqlFunctions(convertedSelectSql);
                    sb.append(convertedSelectSql);
                } catch (Exception e) {
                    log.error("Failed to convert INSERT INTO...SELECT statement: {}", e.getMessage());
                    sb.append("-- Failed to convert SELECT statement: ").append(e.getMessage());
                }
            } else {
                sb.append("-- SELECT statement not available");
            }
        }

        // 确保以分号结尾
        if (!sb.toString().trim().endsWith(";")) {
            sb.append(";");
        }

        return sb.toString();
    }

    /**
     * 生成VALUES子句
     */
    private String generateValuesClause(ValuesClause valuesClause) {
        StringBuilder sb = new StringBuilder();

        // 如果有原始格式，优先使用原始格式保持一致性
        if (valuesClause.hasOriginalFormat()) {
            String originalText = valuesClause.getOriginalValuesText();
            // 转换反引号为双引号
            String convertedText = originalText.replaceAll("`([^`]+)`", "\"$1\"");
            // 确保包含VALUES关键字
            if (!convertedText.trim().toUpperCase().startsWith("VALUES")) {
                sb.append("VALUES ");
            }
            sb.append(convertedText);
        } else {
            // 构建VALUES子句
            sb.append("VALUES ");
            List<List<String>> rows = valuesClause.getRows();
            if (rows != null && !rows.isEmpty()) {
                for (int i = 0; i < rows.size(); i++) {
                    if (i > 0) {
                        sb.append(", ");
                    }
                    sb.append("(");
                    List<String> values = rows.get(i);
                    for (int j = 0; j < values.size(); j++) {
                        if (j > 0) {
                            sb.append(", ");
                        }
                        sb.append(values.get(j));
                    }
                    sb.append(")");
                }
            }
        }

        return sb.toString();
    }

    private String generateDeleteStatement(DeleteTable deleteTable) {
        StringBuilder sb = new StringBuilder();

        // 神通数据库完全支持MySQL的DELETE语法
        sb.append("DELETE FROM ");
        sb.append(getQuotedTableName(deleteTable.getTableId()));

        // 处理WHERE子句
        if (deleteTable.getWhereClause() != null) {
            sb.append(" WHERE ");
            String whereClause = deleteTable.getWhereClause();
            // 转换反引号为双引号
            String convertedWhereClause = convertBackticksToDoubleQuotes(whereClause);
            // 转换函数
            convertedWhereClause = convertSqlFunctions(convertedWhereClause);
            sb.append(convertedWhereClause);
        }

        // 处理ORDER BY子句
        if (deleteTable.getOrderByClause() != null) {
            sb.append(" ORDER BY ");
            String orderByClause = deleteTable.getOrderByClause();
            // 转换反引号为双引号
            String convertedOrderByClause = convertBackticksToDoubleQuotes(orderByClause);
            sb.append(convertedOrderByClause);
        }

        // 处理LIMIT子句
        if (deleteTable.getLimitClause() != null) {
            sb.append(" LIMIT ");
            sb.append(deleteTable.getLimitClause());
        }

        // 确保以分号结尾
        if (!sb.toString().trim().endsWith(";")) {
            sb.append(";");
        }

        return sb.toString();
    }
    
    private String generateUpdateStatement(UpdateTable updateTable) {
        StringBuilder sb = new StringBuilder();

        // 神通数据库完全支持MySQL的UPDATE语法
        sb.append("UPDATE ");
        sb.append(getQuotedTableName(updateTable.getTableId()));

        // 处理SET子句
        if (updateTable.getSetClauses() != null && !updateTable.getSetClauses().isEmpty()) {
            sb.append(" SET ");
            List<String> setExpressions = new ArrayList<>();
            for (UpdateTable.SetClause setClause : updateTable.getSetClauses()) {
                String columnName = quote(setClause.getColumnName());
                String value = setClause.getValue();
                // 转换反引号为双引号
                value = convertBackticksToDoubleQuotes(value);
                // 转换函数
                value = convertSqlFunctions(value);
                setExpressions.add(columnName + " = " + value);
            }
            sb.append(String.join(", ", setExpressions));
        }

        // 注意：根据MySQL官方文档 https://dev.mysql.com/doc/refman/8.4/en/update.html
        // MySQL标准不支持UPDATE FROM语法，该语法是SQL Server/PostgreSQL特有的
        // 标准MySQL多表UPDATE应该使用JOIN语法，由MultiTableUpdate处理
        // 这里只处理标准的单表UPDATE

        // 处理WHERE子句
        if (updateTable.getWhereClause() != null) {
            sb.append(" WHERE ");
            String whereClause = updateTable.getWhereClause();
            // 转换反引号为双引号
            String convertedWhereClause = convertBackticksToDoubleQuotes(whereClause);
            // 转换函数
            convertedWhereClause = convertSqlFunctions(convertedWhereClause);
            sb.append(convertedWhereClause);
        }

        // 确保以分号结尾
        if (!sb.toString().trim().endsWith(";")) {
            sb.append(";");
        }

        return sb.toString();
    }



    /**
     * 生成多表UPDATE语句
     * 根据神通数据库官方文档第44392行，神通数据库支持UPDATE语句中的FROM子句
     * 示例：update a set a = 1 from sys_class where oid = a;
     */
    private String generateMultiTableUpdateStatement(MultiTableUpdate multiTableUpdate) {
        StringBuilder sb = new StringBuilder();

        // 神通数据库支持UPDATE FROM语法，需要将JOIN语法转换为FROM语法
        sb.append("UPDATE ");

        // 从tableReferences中提取主表和FROM子句
        String tableReferences = multiTableUpdate.getTableReferences();
        if (tableReferences != null) {
            // 解析表引用，将JOIN语法转换为FROM语法
            String[] convertedTables = convertJoinToFromSyntax(tableReferences);
            sb.append(convertedTables[0]); // 主表

            // 处理SET子句
            if (multiTableUpdate.getSetClauses() != null && !multiTableUpdate.getSetClauses().isEmpty()) {
                sb.append(" SET ");
                List<String> setExpressions = new ArrayList<>();
                for (MultiTableUpdate.SetClause setClause : multiTableUpdate.getSetClauses()) {
                    String columnName = setClause.getColumnName();
                    String value = setClause.getValue();

                    // 转换反引号为双引号
                    value = convertBackticksToDoubleQuotes(value);
                    // 转换函数
                    value = convertSqlFunctions(value);

                    // 如果有表前缀，转换为表名（不使用别名）
                    if (setClause.getTableId() != null) {
                        String aliasOrTableName = setClause.getTableId().getTableName();
                        // 将别名转换为实际表名
                        String tableName = convertAliasToTableName(aliasOrTableName, tableReferences);
                        String tablePrefix = quote(tableName);
                        columnName = tablePrefix + "." + quote(columnName);
                    } else {
                        columnName = quote(columnName);
                    }

                    setExpressions.add(columnName + " = " + value);
                }
                sb.append(String.join(", ", setExpressions));
            }

            // 添加FROM子句（如果有其他表）
            if (convertedTables.length > 1 && convertedTables[1] != null && !convertedTables[1].trim().isEmpty()) {
                sb.append(" FROM ").append(convertedTables[1]);
            }
        } else {
            // 如果没有表引用信息，记录警告但继续处理
            log.warn("MultiTableUpdate missing table references, generating basic UPDATE");
            sb.append("unknown_table");
        }

        // 处理WHERE子句
        if (multiTableUpdate.getWhereClause() != null) {
            sb.append(" WHERE ");
            String whereClause = multiTableUpdate.getWhereClause();
            // 转换反引号为双引号
            String convertedWhereClause = convertBackticksToDoubleQuotes(whereClause);
            // 转换函数
            convertedWhereClause = convertSqlFunctions(convertedWhereClause);
            // 转换WHERE子句中的别名引用为表名引用
            convertedWhereClause = convertAliasesToTableNamesInWhereClause(convertedWhereClause, tableReferences);
            sb.append(convertedWhereClause);
        }

        // 确保以分号结尾
        if (!sb.toString().trim().endsWith(";")) {
            sb.append(";");
        }

        log.info("CONVERSION_SUCCESS: MultiTableUpdate converted to Shentong UPDATE FROM syntax");
        return sb.toString();
    }

    /**
     * 将JOIN语法转换为神通数据库支持的FROM语法
     * 根据神通数据库官方文档第44415行示例：update a set a = 1 from sys_class where oid = a;
     *
     * 神通数据库UPDATE FROM语法特点：
     * - 主表直接使用表名，不使用别名
     * - FROM子句中的表也直接使用表名，不使用别名
     *
     * 输入: "users u INNER JOIN user_profiles p ON u.id = p.user_id"
     * 输出: ["users", "user_profiles"]
     */
    private String[] convertJoinToFromSyntax(String tableReferences) {
        if (tableReferences == null || tableReferences.trim().isEmpty()) {
            return new String[]{"unknown_table", ""};
        }

        // 简化的JOIN解析 - 提取表名，忽略别名
        String cleanedReferences = convertBackticksToDoubleQuotes(tableReferences);

        // 查找JOIN关键字
        String upperReferences = cleanedReferences.toUpperCase();
        if (upperReferences.contains(" JOIN ")) {
            // 分割JOIN语句
            String[] parts = cleanedReferences.split("(?i)\\s+(INNER\\s+|LEFT\\s+|RIGHT\\s+|FULL\\s+)?JOIN\\s+");
            if (parts.length >= 2) {
                String mainTablePart = parts[0].trim();

                // 提取第二个表名（去掉ON条件）
                String secondPart = parts[1].trim();
                int onIndex = secondPart.toUpperCase().indexOf(" ON ");
                String secondTablePart = onIndex > 0 ? secondPart.substring(0, onIndex).trim() : secondPart;

                // 提取纯表名（去掉别名）
                String mainTable = extractTableNameFromAlias(mainTablePart);
                String secondTable = extractTableNameFromAlias(secondTablePart);

                return new String[]{quote(mainTable), quote(secondTable)};
            }
        }

        // 处理逗号分隔的多表语法：UPDATE table1 alias1, table2 alias2 SET ...
        if (cleanedReferences.contains(",")) {
            String[] parts = cleanedReferences.split(",", 2);
            if (parts.length == 2) {
                String mainTablePart = parts[0].trim();
                String secondTablePart = parts[1].trim();

                // 提取纯表名（去掉别名）
                String mainTable = extractTableNameFromAlias(mainTablePart);
                String secondTable = extractTableNameFromAlias(secondTablePart);

                return new String[]{quote(mainTable), quote(secondTable)};
            }
        }

        // 默认情况：提取表名（可能包含别名）
        String tableName = extractTableNameFromAlias(cleanedReferences.trim());
        return new String[]{quote(tableName), ""};
    }

    /**
     * 从表别名表达式中提取纯表名
     * 根据神通数据库官方文档，UPDATE FROM语法中应该使用表名而不是别名
     *
     * 输入示例：
     * - "users u" -> "users"
     * - "user_profiles p" -> "user_profiles"
     * - "users" -> "users"
     * - "\"users\" u" -> "users"
     */
    private String extractTableNameFromAlias(String tableExpression) {
        if (tableExpression == null || tableExpression.trim().isEmpty()) {
            return "unknown_table";
        }

        String trimmed = tableExpression.trim();

        // 移除引号
        if ((trimmed.startsWith("\"") && trimmed.endsWith("\"")) ||
            (trimmed.startsWith("`") && trimmed.endsWith("`"))) {
            trimmed = trimmed.substring(1, trimmed.length() - 1);
        }

        // 查找空格，表名在第一个空格之前
        int spaceIndex = trimmed.indexOf(' ');
        if (spaceIndex > 0) {
            String tableName = trimmed.substring(0, spaceIndex).trim();
            // 再次移除可能的引号
            if ((tableName.startsWith("\"") && tableName.endsWith("\"")) ||
                (tableName.startsWith("`") && tableName.endsWith("`"))) {
                tableName = tableName.substring(1, tableName.length() - 1);
            }
            return tableName;
        }

        // 如果没有空格，整个字符串就是表名
        return trimmed;
    }

    /**
     * 将别名转换为实际表名
     * 根据tableReferences中的映射关系，将别名转换为对应的表名
     *
     * @param aliasOrTableName 别名或表名
     * @param tableReferences 表引用字符串（包含别名映射）
     * @return 实际表名
     */
    private String convertAliasToTableName(String aliasOrTableName, String tableReferences) {
        if (aliasOrTableName == null || tableReferences == null) {
            return aliasOrTableName;
        }

        // 构建别名到表名的映射
        Map<String, String> aliasToTableMap = buildAliasToTableMap(tableReferences);

        // 查找别名对应的表名
        String tableName = aliasToTableMap.get(aliasOrTableName);
        if (tableName != null) {
            return tableName;
        }

        // 如果没有找到映射，可能本身就是表名
        return aliasOrTableName;
    }

    /**
     * 构建别名到表名的映射关系
     * 解析tableReferences字符串，提取表名和别名的对应关系
     *
     * 示例：
     * - "users u INNER JOIN user_profiles p" -> {u: users, p: user_profiles}
     * - "users u, user_profiles p" -> {u: users, p: user_profiles}
     */
    private Map<String, String> buildAliasToTableMap(String tableReferences) {
        Map<String, String> aliasToTableMap = new HashMap<>();

        if (tableReferences == null || tableReferences.trim().isEmpty()) {
            return aliasToTableMap;
        }

        String cleanedReferences = convertBackticksToDoubleQuotes(tableReferences);

        // 处理JOIN语法
        if (cleanedReferences.toUpperCase().contains(" JOIN ")) {
            String[] parts = cleanedReferences.split("(?i)\\s+(INNER\\s+|LEFT\\s+|RIGHT\\s+|FULL\\s+)?JOIN\\s+");
            for (String part : parts) {
                String trimmedPart = part.trim();
                // 移除ON条件
                int onIndex = trimmedPart.toUpperCase().indexOf(" ON ");
                if (onIndex > 0) {
                    trimmedPart = trimmedPart.substring(0, onIndex).trim();
                }

                // 解析表名和别名
                parseTableAlias(trimmedPart, aliasToTableMap);
            }
        }
        // 处理逗号分隔语法
        else if (cleanedReferences.contains(",")) {
            String[] parts = cleanedReferences.split(",");
            for (String part : parts) {
                parseTableAlias(part.trim(), aliasToTableMap);
            }
        }
        // 处理单表情况
        else {
            parseTableAlias(cleanedReferences.trim(), aliasToTableMap);
        }

        return aliasToTableMap;
    }

    /**
     * 解析单个表别名表达式
     *
     * @param tableExpression 表达式，如 "users u" 或 "user_profiles p"
     * @param aliasToTableMap 别名到表名的映射
     */
    private void parseTableAlias(String tableExpression, Map<String, String> aliasToTableMap) {
        if (tableExpression == null || tableExpression.trim().isEmpty()) {
            return;
        }

        String trimmed = tableExpression.trim();

        // 查找空格分隔的表名和别名
        int spaceIndex = trimmed.indexOf(' ');
        if (spaceIndex > 0) {
            String tableName = trimmed.substring(0, spaceIndex).trim();
            String alias = trimmed.substring(spaceIndex + 1).trim();

            // 移除引号
            tableName = removeQuotes(tableName);
            alias = removeQuotes(alias);

            aliasToTableMap.put(alias, tableName);
        }
    }

    /**
     * 移除字符串两端的引号
     */
    private String removeQuotes(String str) {
        if (str == null || str.length() < 2) {
            return str;
        }

        if ((str.startsWith("\"") && str.endsWith("\"")) ||
            (str.startsWith("`") && str.endsWith("`")) ||
            (str.startsWith("'") && str.endsWith("'"))) {
            return str.substring(1, str.length() - 1);
        }

        return str;
    }

    /**
     * 转换WHERE子句中的别名引用为表名引用
     * 根据神通数据库规范，WHERE子句中也应该使用表名而不是别名
     *
     * @param whereClause WHERE子句内容
     * @param tableReferences 表引用字符串
     * @return 转换后的WHERE子句
     */
    private String convertAliasesToTableNamesInWhereClause(String whereClause, String tableReferences) {
        if (whereClause == null || tableReferences == null) {
            return whereClause;
        }

        // 构建别名到表名的映射
        Map<String, String> aliasToTableMap = buildAliasToTableMap(tableReferences);

        String result = whereClause;

        // 替换WHERE子句中的别名引用
        for (Map.Entry<String, String> entry : aliasToTableMap.entrySet()) {
            String alias = entry.getKey();
            String tableName = entry.getValue();

            // 替换 "alias.column" 为 "tableName.column"
            String aliasPattern = "\\b" + alias + "\\.";
            String tableReplacement = quote(tableName) + ".";
            result = result.replaceAll(aliasPattern, tableReplacement);
        }

        return result;
    }

    /**
     * 生成多表DELETE语句
     * 根据神通数据库官方文档，神通数据库不支持多表DELETE语法
     * 提供替代方案建议
     */
    private String generateMultiTableDeleteStatement(MultiTableDelete multiTableDelete) {
        log.warn("UNSUPPORTED_STATEMENT: MultiTableDelete is not supported in Shentong database. " +
                "Consider using separate DELETE statements or stored procedures.");

        StringBuilder sb = new StringBuilder();
        sb.append("-- 神通数据库不支持多表DELETE语法\n");
        sb.append("-- 原始SQL: ").append(multiTableDelete.getSql() != null ? multiTableDelete.getSql() : "MultiTableDelete").append("\n");
        sb.append("-- 建议替代方案：\n");
        sb.append("-- 1. 使用多个单表DELETE语句\n");
        sb.append("-- 2. 使用存储过程实现复杂删除逻辑\n");
        sb.append("-- 3. 先SELECT出要删除的记录ID，然后分别删除\n");
        sb.append("-- 示例：\n");
        sb.append("-- DELETE FROM table1 WHERE condition;\n");
        sb.append("-- DELETE FROM table2 WHERE condition;");

        return sb.toString();
    }
    
    private String getQuotedTableName(TableId tableId) {
        if (tableId.getSchemaName() != null) {
            return quote(tableId.getSchemaName()) + "." + quote(tableId.getTableName());
        }
        return quote(tableId.getTableName());
    }
    
    private String quote(String identifier) {
        if (identifier == null) return "\"\"";

        // 清理反引号（如果存在）
        String cleanIdentifier = identifier;
        if (cleanIdentifier.startsWith("`") && cleanIdentifier.endsWith("`")) {
            cleanIdentifier = cleanIdentifier.substring(1, cleanIdentifier.length() - 1);
        }

        // 使用双引号包围标识符（神通数据库标准）
        return "\"" + cleanIdentifier.replace("\"", "\"\"") + "\"";
    }

    /**
     * 生成BEGIN语句
     * 根据神通官方文档第29958-30210行，神通数据库完全支持BEGIN语句
     */
    private String generateBeginWork(BeginWork beginWork) {
        StringBuilder sb = new StringBuilder();
        sb.append("BEGIN");

        // 添加WORK关键字（如果有的话）
        if (beginWork.isWork()) {
            sb.append(" WORK");
        }

        // 添加事务特性（如果有的话）
        if (beginWork.getCharacteristics() != null && !beginWork.getCharacteristics().trim().isEmpty()) {
            sb.append(" ").append(beginWork.getCharacteristics());
        }

        sb.append(";");
        return sb.toString();
    }

    /**
     * 生成START TRANSACTION语句
     * 根据神通官方文档，神通数据库完全支持事务控制
     */
    private String generateStartTransaction(StartTransaction startTransaction) {
        StringBuilder sb = new StringBuilder();
        sb.append("START TRANSACTION");

        // 添加事务特性（如果有的话）
        if (startTransaction.getCharacteristics() != null && !startTransaction.getCharacteristics().trim().isEmpty()) {
            sb.append(" ").append(startTransaction.getCharacteristics());
        }

        sb.append(";");
        return sb.toString();
    }

    /**
     * 生成COMMIT语句
     * 根据神通官方文档，神通数据库完全支持COMMIT语句
     */
    private String generateCommitWork(CommitWork commitWork) {
        StringBuilder sb = new StringBuilder();
        sb.append("COMMIT");

        // 添加WORK关键字（如果有的话）
        if (commitWork.isWork()) {
            sb.append(" WORK");
        }

        // 添加其他选项（如果有的话）
        if (commitWork.getOptions() != null && !commitWork.getOptions().trim().isEmpty()) {
            sb.append(" ").append(commitWork.getOptions());
        }

        sb.append(";");
        return sb.toString();
    }

    /**
     * 生成ROLLBACK WORK语句
     * 根据神通官方文档，神通数据库完全支持ROLLBACK语句
     */
    private String generateRollbackWork(RollbackWork rollbackWork) {
        StringBuilder sb = new StringBuilder();
        sb.append("ROLLBACK");

        // 添加WORK关键字（如果有的话）
        if (rollbackWork.isWork()) {
            sb.append(" WORK");
        }

        // 添加TO SAVEPOINT（如果有的话）
        if (rollbackWork.getSavepointName() != null && !rollbackWork.getSavepointName().trim().isEmpty()) {
            sb.append(" TO SAVEPOINT ").append(rollbackWork.getSavepointName());
        }

        sb.append(";");
        return sb.toString();
    }

    /**
     * 生成ROLLBACK TO SAVEPOINT语句
     * 根据神通官方文档，神通数据库支持保存点
     */
    private String generateRollbackStatement(RollbackStatement rollbackStatement) {
        StringBuilder sb = new StringBuilder();
        sb.append("ROLLBACK");

        // 添加WORK关键字（如果有的话）
        if (rollbackStatement.isWork()) {
            sb.append(" WORK");
        }

        // 添加TO SAVEPOINT（如果有的话）
        if (rollbackStatement.getSavepointName() != null && !rollbackStatement.getSavepointName().trim().isEmpty()) {
            sb.append(" TO SAVEPOINT ").append(rollbackStatement.getSavepointName());
        }

        sb.append(";");
        return sb.toString();
    }

    /**
     * 生成SAVEPOINT语句
     * 根据神通官方文档，神通数据库支持保存点
     */
    private String generateSavepointStatement(SavepointStatement savepointStatement) {
        StringBuilder sb = new StringBuilder();
        sb.append("SAVEPOINT");

        // 添加保存点名称
        if (savepointStatement.getSavepointName() != null && !savepointStatement.getSavepointName().trim().isEmpty()) {
            sb.append(" ").append(savepointStatement.getSavepointName());
        }

        sb.append(";");
        return sb.toString();
    }

    /**
     * 生成RELEASE SAVEPOINT语句
     * 根据神通官方文档，神通数据库支持保存点释放
     */
    private String generateReleaseStatement(ReleaseStatement releaseStatement) {
        StringBuilder sb = new StringBuilder();
        sb.append("RELEASE SAVEPOINT");

        // 添加保存点名称
        if (releaseStatement.getSavepointName() != null && !releaseStatement.getSavepointName().trim().isEmpty()) {
            sb.append(" ").append(releaseStatement.getSavepointName());
        }

        sb.append(";");
        return sb.toString();
    }

    /**
     * 生成约束定义
     * 根据神通数据库官方文档，支持各种约束类型
     */
    private String generateConstraintDefinition(ColumnRel columnDefinition) {
        StringBuilder sb = new StringBuilder();

        // 处理UNIQUE约束 - 优先处理，因为它可能与其他约束组合
        if (columnDefinition.isUnique()) {
            String columnName = columnDefinition.getColumnName();
            if (columnName != null && !columnName.trim().isEmpty()) {
                // 处理多列的情况
                if (columnName.contains(",")) {
                    // 多列唯一约束
                    String[] columns = columnName.split(",");
                    sb.append("UNIQUE (");
                    for (int i = 0; i < columns.length; i++) {
                        if (i > 0) sb.append(", ");
                        sb.append(quote(columns[i].trim()));
                    }
                    sb.append(")");
                } else {
                    // 单列唯一约束
                    sb.append("UNIQUE (").append(quote(columnName)).append(")");
                }
            }
        }

        // 处理CHECK约束
        else if (columnDefinition.getCheckConstraintExpression() != null && !columnDefinition.getCheckConstraintExpression().trim().isEmpty()) {
            sb.append("CHECK (").append(columnDefinition.getCheckConstraintExpression()).append(")");
        }

        // 处理FOREIGN KEY约束
        else if (columnDefinition.getReferencesTable() != null && !columnDefinition.getReferencesTable().trim().isEmpty()) {
            String localColumn = columnDefinition.getColumnName();
            if (localColumn == null || localColumn.trim().isEmpty()) {
                localColumn = "unknown_column"; // 默认列名，实际应该从解析器传入
            }
            sb.append("FOREIGN KEY (").append(quote(localColumn)).append(") ");
            sb.append("REFERENCES ").append(quote(columnDefinition.getReferencesTable()));
            if (columnDefinition.getReferencesColumn() != null && !columnDefinition.getReferencesColumn().trim().isEmpty()) {
                sb.append("(").append(quote(columnDefinition.getReferencesColumn())).append(")");
            }
        }

        // 如果没有具体的约束信息，返回一个默认的CHECK约束
        if (sb.length() == 0) {
            log.warn("No constraint information found for column: {}", columnDefinition.getColumnName());
            sb.append("CHECK (1=1)"); // 默认的总是为真的CHECK约束
        }

        return sb.toString().trim();
    }

    /**
     * 将列名列表转换为带双引号的格式
     * 例如: (col1, col2) -> ("col1", "col2")
     */
    private String convertColumnNamesToQuoted(String columnNames) {
        if (columnNames == null || columnNames.trim().isEmpty()) {
            return columnNames;
        }

        String cleaned = columnNames.trim();

        // 处理括号包围的列名列表
        if (cleaned.startsWith("(") && cleaned.endsWith(")")) {
            String inner = cleaned.substring(1, cleaned.length() - 1);
            String[] columns = inner.split(",");

            StringBuilder result = new StringBuilder("(");
            for (int i = 0; i < columns.length; i++) {
                String column = columns[i].trim();
                result.append(quote(column));
                if (i < columns.length - 1) {
                    result.append(", ");
                }
            }
            result.append(")");
            return result.toString();
        } else {
            // 单列的情况
            return quote(cleaned);
        }
    }

    /**
     * 生成DROP VIEW语句
     * 根据神通官方文档第37680-37810行，神通数据库完全支持DROP VIEW语句
     */
    private String generateDropView(DropView dropView) {
        StringBuilder sb = new StringBuilder();
        sb.append("DROP VIEW ");

        if (dropView.isIfExists()) {
            sb.append("IF EXISTS ");
        }

        // 处理多个视图名
        for (int i = 0; i < dropView.getViewIds().size(); i++) {
            if (i > 0) {
                sb.append(", ");
            }
            TableId viewId = dropView.getViewIds().get(i);
            sb.append(quote(viewId.getTableName()));
        }

        // 处理CASCADE/RESTRICT选项
        if (dropView.isCascade()) {
            sb.append(" CASCADE");
        } else if (dropView.isRestrict()) {
            sb.append(" RESTRICT");
        }

        sb.append(";");
        return sb.toString();
    }

    /**
     * 生成DROP INDEX语句
     * 根据神通官方文档第37680-37810行，神通数据库完全支持DROP INDEX语句
     */
    private String generateDropIndex(DropIndex dropIndex) {
        StringBuilder sb = new StringBuilder();
        sb.append("DROP INDEX ");

        // 添加索引名称
        if (dropIndex.getIndexName() != null) {
            sb.append(quote(dropIndex.getIndexName()));
        }

        // 添加表名（神通数据库要求指定表名）
        if (dropIndex.getTableId() != null) {
            sb.append(" ON ");
            sb.append(quote(dropIndex.getTableId().getTableName()));
        }

        // 处理ONLINE/OFFLINE选项（如果神通支持）
        if (dropIndex.isOnline()) {
            sb.append(" ONLINE");
        } else if (dropIndex.isOffline()) {
            sb.append(" OFFLINE");
        }

        // 处理ALGORITHM选项（如果神通支持）
        if (dropIndex.getAlgorithm() != null) {
            sb.append(" ALGORITHM = ").append(dropIndex.getAlgorithm());
        }

        // 处理LOCK选项（如果神通支持）
        if (dropIndex.getLockType() != null) {
            sb.append(" LOCK = ").append(dropIndex.getLockType());
        }

        sb.append(";");
        return sb.toString();
    }

    /**
     * 生成CREATE FUNCTION语句
     * 根据神通官方文档，神通数据库完全支持CREATE FUNCTION语句
     * 参考：shentong.md文档中确认支持函数功能
     */
    private String generateCreateFunction(CreateFunction createFunction) {
        // 神通数据库支持PostgreSQL兼容的函数语法
        // 语法：CREATE [OR REPLACE] FUNCTION function_name([parameters]) RETURNS datatype AS $$ ... $$ LANGUAGE plpgsql;
        String originalSql = createFunction.getSql();
        if (originalSql != null && !originalSql.trim().isEmpty()) {
            // 保持原始格式，只做必要的语法转换
            String convertedSql = originalSql;

            // 转换反引号为双引号（保持空格）
            convertedSql = convertedSql.replace("`", "\"");

            // 神通数据库使用PostgreSQL兼容语法，保持RETURNS关键字
            // 移除MySQL的DETERMINISTIC关键字（神通不需要）
            convertedSql = convertedSql.replaceAll("(?i)\\s+DETERMINISTIC\\s+", " ");
            convertedSql = convertedSql.replaceAll("(?i)\\s+DETERMINISTIC$", "");

            // 对于简单的RETURN语句，转换为神通的PostgreSQL兼容语法
            if (convertedSql.matches("(?i).*RETURNS\\s+\\w+\\s+RETURN\\s+[^;]+\\s*;?\\s*$")) {
                // 简单的RETURN语句，转换为PostgreSQL语法
                convertedSql = convertedSql.replaceAll("(?i)\\bRETURN\\s+([^;]+)\\s*;?\\s*$", "AS \\$\\$ BEGIN RETURN $1; END; \\$\\$ LANGUAGE plpgsql");
            }

            return convertedSql.endsWith(";") ? convertedSql : convertedSql + ";";
        }
        return "-- CREATE FUNCTION statement could not be converted";
    }

    /**
     * 生成DROP FUNCTION语句
     * 根据神通官方文档，神通数据库完全支持DROP FUNCTION语句
     */
    private String generateDropFunction(DropFunction dropFunction) {
        StringBuilder sb = new StringBuilder();

        // 神通数据库支持PostgreSQL兼容的DROP FUNCTION语法
        String originalSql = dropFunction.getSql();
        if (originalSql != null && !originalSql.trim().isEmpty()) {
            // 保持原始格式，只做必要的语法转换
            String convertedSql = originalSql.replace("`", "\"");
            sb.append(convertedSql);
        } else {
            // 从AST对象构建DROP FUNCTION语句
            sb.append("DROP FUNCTION ");

            if (dropFunction.isIfExists()) {
                sb.append("IF EXISTS ");
            }

            sb.append("\"").append(dropFunction.getFunctionName()).append("\"");

            if (dropFunction.getParameters() != null && !dropFunction.getParameters().trim().isEmpty()) {
                sb.append(dropFunction.getParameters());
            }

            if (dropFunction.isCascade()) {
                sb.append(" CASCADE");
            } else if (dropFunction.isRestrict()) {
                sb.append(" RESTRICT");
            }
        }

        // 确保以分号结尾
        String result = sb.toString();
        return result.endsWith(";") ? result : result + ";";
    }

    /**
     * 生成CREATE PROCEDURE语句
     * 根据神通官方文档，神通数据库完全支持CREATE PROCEDURE语句
     * 参考：shentong.md文档中确认支持存储过程功能
     */
    private String generateCreateProcedure(CreateProcedure createProcedure) {
        // 神通数据库支持PostgreSQL兼容的存储过程语法
        // 语法：CREATE [OR REPLACE] PROCEDURE procedure_name([parameters]) AS $$ ... $$ LANGUAGE plpgsql;
        String originalSql = createProcedure.getSql();
        if (originalSql != null && !originalSql.trim().isEmpty()) {
            // 保持原始格式，只做必要的语法转换
            String convertedSql = originalSql.replace("`", "\"");

            // 转换MySQL的简单语句为神通的PostgreSQL兼容语法
            if (convertedSql.matches("(?i).*PROCEDURE\\s+\\w+\\(\\)\\s+SELECT\\s+\\d+\\s*;?\\s*$")) {
                // 简单的SELECT语句，转换为PostgreSQL语法
                convertedSql = convertedSql.replaceAll("(?i)\\bSELECT\\s+(\\d+)\\s*;?\\s*$", "AS \\$\\$ BEGIN PERFORM $1; END; \\$\\$ LANGUAGE plpgsql");
            }

            return convertedSql.endsWith(";") ? convertedSql : convertedSql + ";";
        }
        return "-- CREATE PROCEDURE statement could not be converted";
    }

    /**
     * 生成DROP PROCEDURE语句
     * 根据神通官方文档，神通数据库完全支持DROP PROCEDURE语句
     */
    private String generateDropProcedure(DropProcedure dropProcedure) {
        StringBuilder sb = new StringBuilder();

        // 神通数据库支持PostgreSQL兼容的DROP PROCEDURE语法
        String originalSql = dropProcedure.getSql();
        if (originalSql != null && !originalSql.trim().isEmpty()) {
            // 保持原始格式，只做必要的语法转换
            String convertedSql = originalSql.replace("`", "\"");
            sb.append(convertedSql);
        } else {
            // 从AST对象构建DROP PROCEDURE语句
            sb.append("DROP PROCEDURE ");

            if (dropProcedure.isIfExists()) {
                sb.append("IF EXISTS ");
            }

            sb.append("\"").append(dropProcedure.getProcedureName()).append("\"");

            if (dropProcedure.getParameters() != null && !dropProcedure.getParameters().trim().isEmpty()) {
                sb.append(dropProcedure.getParameters());
            }

            if (dropProcedure.isCascade()) {
                sb.append(" CASCADE");
            } else if (dropProcedure.isRestrict()) {
                sb.append(" RESTRICT");
            }
        }

        // 确保以分号结尾
        String result = sb.toString();
        return result.endsWith(";") ? result : result + ";";
    }

    /**
     * 生成CREATE TRIGGER语句
     * 根据神通官方文档，神通数据库完全支持CREATE TRIGGER语句
     * 参考：shentong.md文档中确认支持触发器功能
     */
    private String generateCreateTrigger(CreateTrigger createTrigger) {
        // 神通数据库支持PostgreSQL兼容的触发器语法
        // 语法：CREATE TRIGGER trigger_name BEFORE|AFTER event ON table_name FOR EACH ROW EXECUTE FUNCTION trigger_function();
        String originalSql = createTrigger.getSql();
        if (originalSql != null && !originalSql.trim().isEmpty()) {
            // 保持原始格式，只做必要的语法转换
            String convertedSql = originalSql.replace("`", "\"");

            // 神通数据库使用PostgreSQL兼容的触发器语法，保持原有格式
            // 对于简单的SELECT语句，可能需要转换为触发器函数调用

            return convertedSql.endsWith(";") ? convertedSql : convertedSql + ";";
        }
        return "-- CREATE TRIGGER statement could not be converted";
    }

    /**
     * 生成DROP TRIGGER语句
     * 根据神通官方文档，神通数据库完全支持DROP TRIGGER语句
     */
    private String generateDropTrigger(DropTrigger dropTrigger) {
        StringBuilder sb = new StringBuilder();

        // 神通数据库支持PostgreSQL兼容的DROP TRIGGER语法
        String originalSql = dropTrigger.getSql();
        if (originalSql != null && !originalSql.trim().isEmpty()) {
            // 保持原始格式，只做必要的语法转换
            String convertedSql = originalSql.replace("`", "\"");
            sb.append(convertedSql);
        } else {
            // 从AST对象构建DROP TRIGGER语句
            sb.append("DROP TRIGGER ");

            if (dropTrigger.isIfExists()) {
                sb.append("IF EXISTS ");
            }

            sb.append("\"").append(dropTrigger.getTriggerName()).append("\"");

            if (dropTrigger.getTableName() != null && !dropTrigger.getTableName().trim().isEmpty()) {
                sb.append(" ON \"").append(dropTrigger.getTableName()).append("\"");
            }

            if (dropTrigger.isCascade()) {
                sb.append(" CASCADE");
            } else if (dropTrigger.isRestrict()) {
                sb.append(" RESTRICT");
            }
        }

        // 确保以分号结尾
        String result = sb.toString();
        return result.endsWith(";") ? result : result + ";";
    }

    /**
     * 生成CREATE VIEW语句
     * 根据神通官方文档，神通数据库完全支持CREATE VIEW语句
     */
    private String generateCreateView(CreateView createView) {
        StringBuilder sb = new StringBuilder();

        sb.append("CREATE ");
        if (createView.isOrReplace()) {
            sb.append("OR REPLACE ");
        }
        sb.append("VIEW ");
        sb.append(getQuotedTableName(createView.getViewId()));

        // 添加列列表（如果存在）
        if (createView.getColumnList() != null && createView.getColumnList().length > 0) {
            sb.append(" (");
            for (int i = 0; i < createView.getColumnList().length; i++) {
                if (i > 0) sb.append(", ");
                sb.append(quote(createView.getColumnList()[i]));
            }
            sb.append(")");
        }

        sb.append(" AS ");
        // 转换SELECT语句中的反引号为双引号
        String selectStatement = createView.getSelectStatement();
        selectStatement = convertBackticksToDoubleQuotes(selectStatement);
        // 转换SQL函数
        selectStatement = convertSqlFunctions(selectStatement);
        sb.append(selectStatement);

        // 神通数据库支持WITH CHECK OPTION
        if (createView.isWithCheckOption()) {
            sb.append(" WITH ");
            if (createView.isCascaded()) {
                sb.append("CASCADED ");
            } else if (createView.isLocal()) {
                sb.append("LOCAL ");
            }
            sb.append("CHECK OPTION");
        }

        sb.append(";");
        return sb.toString();
    }

    /**
     * 生成ALTER VIEW语句
     * 根据神通官方文档，神通数据库支持ALTER VIEW语句
     */
    private String generateAlterView(AlterView alterView) {
        StringBuilder sb = new StringBuilder();

        sb.append("ALTER VIEW ");
        sb.append(getQuotedTableName(alterView.getViewId()));

        // 添加列列表（如果存在）
        if (alterView.getColumnList() != null && alterView.getColumnList().length > 0) {
            sb.append(" (");
            for (int i = 0; i < alterView.getColumnList().length; i++) {
                if (i > 0) sb.append(", ");
                sb.append(quote(alterView.getColumnList()[i]));
            }
            sb.append(")");
        }

        sb.append(" AS ");
        // 转换SELECT语句中的反引号为双引号
        String selectStatement = alterView.getSelectStatement();
        selectStatement = convertBackticksToDoubleQuotes(selectStatement);
        // 转换SQL函数
        selectStatement = convertSqlFunctions(selectStatement);
        sb.append(selectStatement);

        // 神通数据库支持WITH CHECK OPTION
        if (alterView.isWithCheckOption()) {
            sb.append(" WITH ");
            if (alterView.isCascaded()) {
                sb.append("CASCADED ");
            } else if (alterView.isLocal()) {
                sb.append("LOCAL ");
            }
            sb.append("CHECK OPTION");
        }

        sb.append(";");
        return sb.toString();
    }

    /**
     * 生成TRUNCATE TABLE语句
     * 根据神通官方文档，神通数据库完全支持TRUNCATE TABLE语句
     */
    private String generateTruncateTable(TruncateTable truncateTable) {
        StringBuilder sb = new StringBuilder();
        sb.append("TRUNCATE TABLE ");
        sb.append(getQuotedTableName(truncateTable.getTableId()));
        sb.append(";");
        return sb.toString();
    }

    /**
     * 生成CREATE SEQUENCE语句
     * 根据神通官方文档，神通数据库支持序列对象
     */
    private String generateCreateSequence(CreateSequence createSequence) {
        StringBuilder sb = new StringBuilder();

        sb.append("CREATE SEQUENCE ");
        if (createSequence.isIfNotExists()) {
            sb.append("IF NOT EXISTS ");
        }
        sb.append(getQuotedTableName(createSequence.getSequenceId()));

        // 神通数据库支持PostgreSQL兼容的序列参数
        if (createSequence.getDataType() != null) {
            sb.append(" AS ").append(createSequence.getDataType());
        }

        if (createSequence.getStartWith() != null) {
            sb.append(" START WITH ").append(createSequence.getStartWith());
        }

        if (createSequence.getIncrementBy() != null) {
            sb.append(" INCREMENT BY ").append(createSequence.getIncrementBy());
        }

        if (createSequence.getMinValue() != null) {
            sb.append(" MINVALUE ").append(createSequence.getMinValue());
        }

        if (createSequence.getMaxValue() != null) {
            sb.append(" MAXVALUE ").append(createSequence.getMaxValue());
        }

        if (createSequence.getCache() != null) {
            sb.append(" CACHE ").append(createSequence.getCache());
        }

        if (createSequence.isCycle()) {
            sb.append(" CYCLE");
        }

        sb.append(";");
        return sb.toString();
    }

    /**
     * 生成ALTER SEQUENCE语句
     * 根据神通官方文档，神通数据库支持修改序列对象属性
     */
    private String generateAlterSequence(AlterSequence alterSequence) {
        StringBuilder sb = new StringBuilder();

        sb.append("ALTER SEQUENCE ");
        sb.append(getQuotedTableName(alterSequence.getSequenceId()));

        // 神通数据库支持PostgreSQL兼容的修改参数
        if (alterSequence.getRestartWith() != null) {
            sb.append(" RESTART WITH ").append(alterSequence.getRestartWith());
        }

        if (alterSequence.getIncrementBy() != null) {
            sb.append(" INCREMENT BY ").append(alterSequence.getIncrementBy());
        }

        if (alterSequence.getMinValue() != null) {
            sb.append(" MINVALUE ").append(alterSequence.getMinValue());
        }

        if (alterSequence.getMaxValue() != null) {
            sb.append(" MAXVALUE ").append(alterSequence.getMaxValue());
        }

        if (alterSequence.getCache() != null) {
            sb.append(" CACHE ").append(alterSequence.getCache());
        }

        if (alterSequence.getCycle() != null) {
            if (alterSequence.getCycle()) {
                sb.append(" CYCLE");
            } else {
                sb.append(" NO CYCLE");
            }
        }

        if (alterSequence.getOwnedBy() != null) {
            sb.append(" OWNED BY ").append(alterSequence.getOwnedBy());
        }

        sb.append(";");
        return sb.toString();
    }

    /**
     * 生成DROP SEQUENCE语句
     * 根据神通官方文档，神通数据库支持删除序列对象
     */
    private String generateDropSequence(DropSequence dropSequence) {
        StringBuilder sb = new StringBuilder();
        sb.append("DROP SEQUENCE ");

        if (dropSequence.isIfExists()) {
            sb.append("IF EXISTS ");
        }

        // 处理序列名列表
        List<String> sequenceNames = new ArrayList<>();
        for (TableId sequenceId : dropSequence.getSequenceIds()) {
            sequenceNames.add(getQuotedTableName(sequenceId));
        }
        sb.append(String.join(", ", sequenceNames));

        // 神通数据库支持CASCADE和RESTRICT选项
        if (dropSequence.isCascade()) {
            sb.append(" CASCADE");
        } else if (dropSequence.isRestrict()) {
            sb.append(" RESTRICT");
        }

        sb.append(";");
        return sb.toString();
    }

    /**
     * 生成CREATE TABLE AS SELECT语句
     * 根据神通官方文档，神通数据库支持PostgreSQL兼容的CREATE TABLE AS SELECT语句
     */
    private String generateCreateTableAsSelect(CreateTableAsSelect createTableAsSelect) {
        StringBuilder sb = new StringBuilder();

        sb.append("CREATE TABLE ");

        if (createTableAsSelect.isIfNotExists()) {
            sb.append("IF NOT EXISTS ");
        }

        sb.append(getQuotedTableName(createTableAsSelect.getTableId()));

        // 如果有列定义，添加列列表
        if (createTableAsSelect.getColumnRels() != null && !createTableAsSelect.getColumnRels().isEmpty()) {
            sb.append(" (");
            List<String> columnDefs = new ArrayList<>();
            for (ColumnRel columnRel : createTableAsSelect.getColumnRels()) {
                columnDefs.add(quote(columnRel.getColumnName()));
            }
            sb.append(String.join(", ", columnDefs));
            sb.append(")");
        }

        sb.append(" AS ");

        // 添加SELECT查询
        if (createTableAsSelect.getQueryStmt() != null) {
            String selectSql = createTableAsSelect.getQueryStmt().getSql();
            if (selectSql != null && !selectSql.trim().isEmpty()) {
                // 转换反引号为双引号
                String convertedSql = convertBackticksToDoubleQuotes(selectSql);
                // 转换SQL函数
                convertedSql = convertSqlFunctions(convertedSql);
                sb.append(convertedSql);
            } else {
                sb.append("SELECT 1 WHERE FALSE"); // 神通数据库兼容的空表创建
            }
        } else {
            sb.append("SELECT 1 WHERE FALSE"); // 神通数据库兼容的空表创建
        }

        // 确保以分号结尾
        if (!sb.toString().trim().endsWith(";")) {
            sb.append(";");
        }

        return sb.toString();
    }

    /**
     * 生成CREATE TABLE LIKE语句
     * 神通数据库不直接支持CREATE TABLE ... LIKE语法
     * 需要转换为CREATE TABLE AS SELECT * FROM source_table WHERE 1=0的形式
     * 参考神通数据库官方文档
     */
    private String generateCreateTableLike(com.xylink.sqltranspiler.core.ast.create.CreateTableLike createTableLike) {
        StringBuilder sb = new StringBuilder();

        // 神通数据库不支持CREATE TABLE ... LIKE语法
        // 转换为CREATE TABLE AS SELECT * FROM source_table WHERE 1=0
        // 这样可以复制表结构但不复制数据

        sb.append("CREATE TABLE ");

        if (createTableLike.isIfNotExists()) {
            sb.append("IF NOT EXISTS ");
        }

        String newTableName = getQuotedTableName(createTableLike.getNewTableId());
        String sourceTableName = getQuotedTableName(createTableLike.getSourceTableId());

        // 验证表名不为空
        if (newTableName == null || newTableName.trim().isEmpty() ||
            sourceTableName == null || sourceTableName.trim().isEmpty()) {
            log.error("CONVERSION_ERROR: Invalid table names in CREATE TABLE LIKE statement. New: {}, Source: {}",
                    newTableName, sourceTableName);
            return "-- ERROR: Invalid table names in CREATE TABLE LIKE statement";
        }

        sb.append(newTableName);
        sb.append(" AS SELECT * FROM ");
        sb.append(sourceTableName);
        sb.append(" WHERE 1=0");

        // 确保以分号结尾
        if (!sb.toString().trim().endsWith(";")) {
            sb.append(";");
        }

        String result = sb.toString();

        // 验证生成的SQL不为空
        if (result.trim().isEmpty()) {
            log.error("CONVERSION_ERROR: Generated empty SQL for CREATE TABLE LIKE statement");
            return "-- ERROR: Failed to generate CREATE TABLE LIKE statement";
        }

        log.info("CONVERSION_SUCCESS: MySQL CREATE TABLE LIKE converted to Shentong CREATE TABLE AS SELECT for table: {} -> {}. Result length: {}",
                createTableLike.getSourceTableId().getTableName(),
                createTableLike.getNewTableId().getTableName(),
                result.length());

        return result;
    }

    /**
     * 生成GRANT权限授予语句
     * 严格遵守MySQL官方规范：https://dev.mysql.com/doc/refman/8.4/en/grant.html
     * 和神通官方文档：神通数据库支持完整的权限管理
     */
    private String generateGrant(Grant grant) {
        StringBuilder sb = new StringBuilder();

        // 如果有原始SQL，直接转换
        if (grant.getOriginalSql() != null && !grant.getOriginalSql().trim().isEmpty()) {
            String originalSql = grant.getOriginalSql().trim();

            // 只转换反引号为双引号，不转换其他格式
            // 使用简单的正则表达式只处理反引号标识符
            String convertedSql = originalSql.replaceAll("`([^`]+)`", "\"$1\"");

            // 神通数据库支持完整的权限管理，基本兼容MySQL
            // 根据神通官方文档，GRANT语法与MySQL基本一致
            sb.append(convertedSql);

            log.info("CONVERSION_SUCCESS: Successfully converted GRANT statement for Shentong database");
        } else {
            // 构建GRANT语句
            sb.append("GRANT ");

            // 添加权限列表
            if (grant.getPrivileges() != null && !grant.getPrivileges().isEmpty()) {
                sb.append(String.join(", ", grant.getPrivileges()));
            } else {
                sb.append("ALL PRIVILEGES");
            }

            // 添加ON子句
            sb.append(" ON ");
            if (grant.getObjectType() != null && !grant.getObjectType().isEmpty()) {
                sb.append(grant.getObjectType()).append(" ");
            }
            if (grant.getPrivilegeLevel() != null && !grant.getPrivilegeLevel().isEmpty()) {
                sb.append(grant.getPrivilegeLevel());
            } else {
                sb.append("*.*");
            }

            // 添加TO子句
            sb.append(" TO ");
            if (grant.getUsers() != null && !grant.getUsers().isEmpty()) {
                sb.append(String.join(", ", grant.getUsers()));
            }

            // 添加WITH GRANT OPTION
            if (grant.isWithGrantOption()) {
                sb.append(" WITH GRANT OPTION");
            }

            log.info("CONVERSION_SUCCESS: Successfully generated GRANT statement for Shentong database");
        }

        // 确保以分号结尾
        if (!sb.toString().trim().endsWith(";")) {
            sb.append(";");
        }

        return sb.toString();
    }

    /**
     * 生成REVOKE权限回收语句
     * 严格遵守MySQL官方规范：https://dev.mysql.com/doc/refman/8.4/en/revoke.html
     * 和神通官方文档：神通数据库支持完整的权限管理
     */
    private String generateRevoke(Revoke revoke) {
        StringBuilder sb = new StringBuilder();

        // 如果有原始SQL，直接转换
        if (revoke.getOriginalSql() != null && !revoke.getOriginalSql().trim().isEmpty()) {
            String originalSql = revoke.getOriginalSql().trim();

            // 只转换反引号为双引号，不转换其他格式
            // 使用简单的正则表达式只处理反引号标识符
            String convertedSql = originalSql.replaceAll("`([^`]+)`", "\"$1\"");

            // 神通数据库支持完整的权限管理，基本兼容MySQL
            // 根据神通官方文档，REVOKE语法与MySQL基本一致
            sb.append(convertedSql);

            log.info("CONVERSION_SUCCESS: Successfully converted REVOKE statement for Shentong database");
        } else {
            // 构建REVOKE语句
            sb.append("REVOKE ");

            // 处理ALL PRIVILEGES情况
            if (revoke.isAllPrivileges()) {
                sb.append("ALL PRIVILEGES");
                if (revoke.isGrantOption()) {
                    sb.append(", GRANT OPTION");
                }
            } else {
                // 添加权限列表
                if (revoke.getPrivileges() != null && !revoke.getPrivileges().isEmpty()) {
                    sb.append(String.join(", ", revoke.getPrivileges()));
                } else {
                    sb.append("ALL PRIVILEGES");
                }
            }

            // 添加ON子句（如果不是ALL PRIVILEGES, GRANT OPTION格式）
            if (!revoke.isAllPrivileges() || !revoke.isGrantOption()) {
                sb.append(" ON ");
                if (revoke.getObjectType() != null && !revoke.getObjectType().isEmpty()) {
                    sb.append(revoke.getObjectType()).append(" ");
                }
                if (revoke.getPrivilegeLevel() != null && !revoke.getPrivilegeLevel().isEmpty()) {
                    sb.append(revoke.getPrivilegeLevel());
                } else {
                    sb.append("*.*");
                }
            }

            // 添加FROM子句
            sb.append(" FROM ");
            if (revoke.getUsers() != null && !revoke.getUsers().isEmpty()) {
                sb.append(String.join(", ", revoke.getUsers()));
            }

            log.info("CONVERSION_SUCCESS: Successfully generated REVOKE statement for Shentong database");
        }

        // 确保以分号结尾
        if (!sb.toString().trim().endsWith(";")) {
            sb.append(";");
        }

        return sb.toString();
    }

    /**
     * 生成SHOW语句
     * 根据神通数据库文档，SHOW语句可以转换为查询系统表
     */
    private String generateShowStatement(ShowStatement showStatement) {
        // 如果ShowStatement已经设置了转换后的SQL，直接使用
        if (showStatement.getSql() != null && !showStatement.getSql().trim().isEmpty()) {
            String sql = showStatement.getSql();
            // 确保以分号结尾
            if (!sql.trim().endsWith(";")) {
                sql += ";";
            }
            log.info("CONVERSION_SUCCESS: SHOW statement converted to system table query");
            return sql;
        }

        // 默认处理：保持原始SHOW语句
        StringBuilder sb = new StringBuilder();
        sb.append("SHOW");
        for (String keyword : showStatement.getKeywords()) {
            if (!"SHOW".equalsIgnoreCase(keyword)) {
                sb.append(" ").append(keyword);
            }
        }
        sb.append(";");

        log.info("CONVERSION_SUCCESS: SHOW statement preserved");
        return sb.toString();
    }

    /**
     * 生成外键约束语句
     * 根据神通官方文档，神通数据库原生支持FOREIGN KEY约束
     */
    public String generateForeignKeyConstraints(CreateTable createTable) {
        StringBuilder result = new StringBuilder();

        if (createTable.getProperties() == null) {
            return null;
        }

        String tableName = createTable.getTableId().getTableName();
        String schemaName = createTable.getTableId().getSchemaName();

        // 提取所有FOREIGN KEY约束信息
        for (Map.Entry<String, String> entry : createTable.getProperties().entrySet()) {
            if (entry.getKey().startsWith("index_")) {
                String statement = entry.getValue();
                if (statement != null && statement.toUpperCase().contains("FOREIGN KEY")) {
                    // 神通数据库原生支持FOREIGN KEY约束，语法与达梦基本兼容
                    String shentongForeignKeyStatement = convertForeignKeyStatementToShentong(statement, tableName, schemaName);
                    if (shentongForeignKeyStatement != null) {
                        if (result.length() > 0) {
                            result.append("\n\n");
                        }
                        result.append("-- 添加外键约束\n");
                        result.append(shentongForeignKeyStatement);
                        if (!shentongForeignKeyStatement.endsWith(";")) {
                            result.append(";");
                        }
                    }
                }
            }
        }

        return result.length() > 0 ? result.toString() : null;
    }

    /**
     * 将达梦格式的FOREIGN KEY约束语句转换为神通格式
     * 根据神通官方文档，神通数据库原生支持FOREIGN KEY约束
     */
    private String convertForeignKeyStatementToShentong(String damengForeignKeyStatement, String tableName, String schemaName) {
        if (damengForeignKeyStatement == null || damengForeignKeyStatement.trim().isEmpty()) {
            return null;
        }

        try {
            String statement = damengForeignKeyStatement.trim();

            // 神通数据库原生支持FOREIGN KEY约束，语法与达梦基本兼容
            // 只需要调整标识符格式（双引号转换）
            String shentongStatement = statement;

            // 转换表名格式：确保使用正确的schema.table格式
            if (schemaName != null && !schemaName.trim().isEmpty()) {
                // 替换表名引用，确保包含schema
                String quotedTableName = "\"" + tableName + "\"";
                String fullTableName = "\"" + schemaName + "\".\"" + tableName + "\"";
                shentongStatement = shentongStatement.replace("ALTER TABLE " + quotedTableName,
                                                            "ALTER TABLE " + fullTableName);
            }

            // 神通数据库支持标准的FOREIGN KEY语法，保持原有的约束定义
            return shentongStatement;

        } catch (Exception e) {
            log.warn("Failed to convert foreign key statement to Shentong format: {}", damengForeignKeyStatement, e);
        }

        return null;
    }

    /**
     * 将MySQL分区定义转换为神通数据库分区定义
     * 根据神通数据库官方文档，神通数据库支持分区表
     *
     * @param mysqlPartitionDef MySQL分区定义字符串
     * @return 神通数据库分区定义字符串，如果转换失败则返回null
     */
    private String convertPartitionDefinitionForShentong(String mysqlPartitionDef) {
        if (mysqlPartitionDef == null || mysqlPartitionDef.trim().isEmpty()) {
            return null;
        }

        try {
            String upperDef = mysqlPartitionDef.toUpperCase();
            log.debug("PARTITION_CONVERSION: Converting MySQL partition definition for Shentong: {}", mysqlPartitionDef);

            // 检测分区类型并转换
            if (upperDef.contains("PARTITION BY RANGE")) {
                return convertRangePartitionForShentong(mysqlPartitionDef);
            } else if (upperDef.contains("PARTITION BY LIST")) {
                return convertListPartitionForShentong(mysqlPartitionDef);
            } else if (upperDef.contains("PARTITION BY HASH")) {
                return convertHashPartitionForShentong(mysqlPartitionDef);
            } else {
                log.warn("PARTITION_CONVERSION: Unsupported partition type for Shentong in definition: {}", mysqlPartitionDef);
                return null;
            }

        } catch (Exception e) {
            log.error("PARTITION_CONVERSION: Failed to convert partition definition for Shentong: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 转换MySQL RANGE分区为神通RANGE分区
     * 神通数据库支持分区表，语法与MySQL基本兼容
     */
    private String convertRangePartitionForShentong(String mysqlPartitionDef) {
        try {
            StringBuilder sb = new StringBuilder();

            // 提取分区键
            String partitionKey = extractPartitionKeyForShentong(mysqlPartitionDef, "RANGE");
            if (partitionKey == null) {
                log.warn("PARTITION_CONVERSION: Cannot extract partition key from RANGE partition definition for Shentong");
                return null;
            }

            // 处理YEAR()函数 - 神通数据库支持日期函数
            if (partitionKey.toUpperCase().contains("YEAR(")) {
                java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("YEAR\\s*\\(\\s*([^)]+)\\s*\\)",
                                                                                  java.util.regex.Pattern.CASE_INSENSITIVE);
                java.util.regex.Matcher matcher = pattern.matcher(partitionKey);
                if (matcher.find()) {
                    String dateColumn = matcher.group(1).trim();
                    partitionKey = "EXTRACT(YEAR FROM " + dateColumn + ")"; // 转换为标准SQL格式
                    log.info("PARTITION_CONVERSION: Converted YEAR({}) to EXTRACT function for Shentong", dateColumn);
                }
            }

            sb.append("PARTITION BY RANGE (").append(partitionKey).append(")");

            // 提取分区定义
            String partitionList = extractPartitionListForShentong(mysqlPartitionDef);
            if (partitionList != null && !partitionList.trim().isEmpty()) {
                sb.append("\n").append(convertPartitionListForShentong(partitionList, "RANGE"));
            }

            log.info("PARTITION_CONVERSION: Successfully converted RANGE partition for Shentong");
            return sb.toString();

        } catch (Exception e) {
            log.error("PARTITION_CONVERSION: Failed to convert RANGE partition for Shentong: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 转换MySQL LIST分区为神通LIST分区
     */
    private String convertListPartitionForShentong(String mysqlPartitionDef) {
        try {
            StringBuilder sb = new StringBuilder();

            String partitionKey = extractPartitionKeyForShentong(mysqlPartitionDef, "LIST");
            if (partitionKey == null) {
                return null;
            }

            sb.append("PARTITION BY LIST (").append(partitionKey).append(")");

            String partitionList = extractPartitionListForShentong(mysqlPartitionDef);
            if (partitionList != null && !partitionList.trim().isEmpty()) {
                sb.append("\n").append(convertPartitionListForShentong(partitionList, "LIST"));
            }

            log.info("PARTITION_CONVERSION: Successfully converted LIST partition for Shentong");
            return sb.toString();

        } catch (Exception e) {
            log.error("PARTITION_CONVERSION: Failed to convert LIST partition for Shentong: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 转换MySQL HASH分区为神通HASH分区
     */
    private String convertHashPartitionForShentong(String mysqlPartitionDef) {
        try {
            StringBuilder sb = new StringBuilder();

            String partitionKey = extractPartitionKeyForShentong(mysqlPartitionDef, "HASH");
            if (partitionKey == null) {
                return null;
            }

            sb.append("PARTITION BY HASH (").append(partitionKey).append(")");

            String partitionList = extractPartitionListForShentong(mysqlPartitionDef);
            if (partitionList != null && !partitionList.trim().isEmpty()) {
                sb.append("\n").append(convertPartitionListForShentong(partitionList, "HASH"));
            }

            log.info("PARTITION_CONVERSION: Successfully converted HASH partition for Shentong");
            return sb.toString();

        } catch (Exception e) {
            log.error("PARTITION_CONVERSION: Failed to convert HASH partition for Shentong: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从分区定义中提取分区键（神通版本）
     */
    private String extractPartitionKeyForShentong(String partitionDef, String partitionType) {
        try {
            String pattern = "PARTITION\\s+BY\\s+" + partitionType + "\\s*\\(([^)]+)\\)";
            java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern, java.util.regex.Pattern.CASE_INSENSITIVE);
            java.util.regex.Matcher m = p.matcher(partitionDef);

            if (m.find()) {
                return m.group(1).trim();
            }

            return null;
        } catch (Exception e) {
            log.error("PARTITION_CONVERSION: Failed to extract partition key for Shentong: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从分区定义中提取分区列表（神通版本）
     */
    private String extractPartitionListForShentong(String partitionDef) {
        try {
            // 查找第一个左括号（分区列表开始）
            int firstParen = partitionDef.indexOf('(');
            if (firstParen == -1) return null;

            // 查找匹配的右括号后的左括号（分区列表开始）
            int parenCount = 1;
            int i = firstParen + 1;
            while (i < partitionDef.length() && parenCount > 0) {
                if (partitionDef.charAt(i) == '(') parenCount++;
                else if (partitionDef.charAt(i) == ')') parenCount--;
                i++;
            }

            // 从这里开始查找分区列表
            if (i < partitionDef.length()) {
                String remaining = partitionDef.substring(i).trim();
                if (remaining.startsWith("(")) {
                    return remaining;
                }
            }

            return null;
        } catch (Exception e) {
            log.error("PARTITION_CONVERSION: Failed to extract partition list for Shentong: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 转换分区列表定义（神通版本）
     */
    private String convertPartitionListForShentong(String partitionList, String partitionType) {
        try {
            // 基本的分区列表转换
            String converted = partitionList;

            if ("RANGE".equals(partitionType)) {
                // 将MAXVALUE转换为神通格式
                converted = converted.replaceAll("(?i)MAXVALUE", "MAXVALUE");

                // 处理日期格式，确保使用标准格式
                converted = converted.replaceAll("'(\\d{4})-(\\d{1,2})-(\\d{1,2})'", "'$1-$2-$3'");
            }

            return converted;

        } catch (Exception e) {
            log.error("PARTITION_CONVERSION: Failed to convert partition list for Shentong: {}", e.getMessage());
            return partitionList; // 返回原始定义作为fallback
        }
    }
}
