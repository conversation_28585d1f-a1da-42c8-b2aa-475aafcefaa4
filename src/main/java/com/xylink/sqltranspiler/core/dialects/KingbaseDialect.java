package com.xylink.sqltranspiler.core.dialects;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import com.xylink.sqltranspiler.common.constants.ReservedWords;

/**
 * 金仓数据库方言实现
 * 基于金仓官方文档：https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
 * 
 * 核心特性：
 * 1. 完全兼容PostgreSQL语法
 * 2. 支持双引号标识符引用
 * 3. 原生支持LIMIT/OFFSET分页
 * 4. 丰富的内置函数支持
 */
public class KingbaseDialect implements SqlDialect {
    
    // 金仓数据类型映射表 - 基于官方文档
    private static final Map<String, String> DATA_TYPE_MAPPING;
    static {
        Map<String, String> map = new HashMap<>();
        // 整数类型
        map.put("TINYINT", "SMALLINT");  // 金仓没有TINYINT，映射为SMALLINT
        map.put("SMALLINT", "SMALLINT");
        map.put("MEDIUMINT", "INTEGER");
        map.put("INT", "INTEGER");
        map.put("INTEGER", "INTEGER");
        map.put("BIGINT", "BIGINT");
        
        // 浮点类型
        map.put("FLOAT", "REAL");
        map.put("DOUBLE", "DOUBLE PRECISION");
        map.put("DECIMAL", "DECIMAL");
        map.put("NUMERIC", "NUMERIC");
        
        // 字符类型
        map.put("CHAR", "CHAR");
        map.put("VARCHAR", "VARCHAR");
        map.put("TEXT", "TEXT");
        map.put("LONGTEXT", "TEXT");
        map.put("MEDIUMTEXT", "TEXT");
        
        // 日期时间类型
        map.put("DATE", "DATE");
        map.put("TIME", "TIME");
        map.put("DATETIME", "TIMESTAMP");
        map.put("TIMESTAMP", "TIMESTAMP");
        map.put("YEAR", "INTEGER");
        
        // 二进制类型
        map.put("BINARY", "BYTEA");
        map.put("VARBINARY", "BYTEA");
        map.put("BLOB", "BYTEA");
        map.put("LONGBLOB", "BYTEA");

        // JSON类型 - 基于金仓官方文档：PostgreSQL兼容性
        // 金仓数据库完全支持PostgreSQL的JSON和JSONB类型
        map.put("JSON", "JSON");

        DATA_TYPE_MAPPING = Collections.unmodifiableMap(map);
    }
    
    // 金仓函数映射表 - 完全兼容PostgreSQL
    private static final Map<String, String> FUNCTION_MAPPING;
    static {
        Map<String, String> map = new HashMap<>();
        // 日期时间函数
        map.put("NOW", "NOW");
        map.put("CURDATE", "CURRENT_DATE");
        map.put("CURTIME", "CURRENT_TIME");
        
        // 字符串函数
        map.put("LENGTH", "LENGTH");
        map.put("CHAR_LENGTH", "CHAR_LENGTH");
        map.put("SUBSTR", "SUBSTR");
        map.put("SUBSTRING", "SUBSTRING");
        map.put("CONCAT", "CONCAT");
        map.put("UPPER", "UPPER");
        map.put("LOWER", "LOWER");
        map.put("TRIM", "TRIM");
        
        // 数学函数
        map.put("ABS", "ABS");
        map.put("CEIL", "CEIL");
        map.put("FLOOR", "FLOOR");
        map.put("ROUND", "ROUND");
        map.put("MOD", "MOD");
        map.put("RAND", "RANDOM");

        // 条件函数 - 基于PostgreSQL兼容性
        map.put("IFNULL", "COALESCE");
        map.put("NULLIF", "NULLIF");
        map.put("COALESCE", "COALESCE");

        // 扩展日期时间函数
        map.put("SYSDATE", "NOW");
        map.put("UTC_TIMESTAMP", "NOW() AT TIME ZONE 'UTC'");

        // 类型转换函数
        map.put("CAST", "CAST");
        map.put("CONVERT", "CAST");

        FUNCTION_MAPPING = Collections.unmodifiableMap(map);
    }
    
    // PostgreSQL/金仓保留字统一管理 - 使用ReservedWords类
    // 基于金仓官方文档（PostgreSQL兼容）
    // 所有保留字定义已迁移到ReservedWords类中统一管理
    
    @Override
    public String getName() {
        return "KingbaseES";
    }
    
    @Override
    public String getDatabaseProduct() {
        return "KingbaseES";
    }
    
    // ==================== 标识符处理 ====================
    
    @Override
    public String quoteIdentifier(String identifier) {
        if (identifier == null || identifier.isEmpty()) {
            return identifier;
        }
        
        // 金仓兼容PostgreSQL，使用双引号引用标识符
        if (requiresQuoting(identifier)) {
            return "\"" + identifier.replace("\"", "\"\"") + "\"";
        }
        return identifier;
    }
    
    @Override
    public String quoteLiteral(String literal) {
        if (literal == null) {
            return "NULL";
        }
        return "'" + literal.replace("'", "''") + "'";
    }
    
    @Override
    public boolean requiresQuoting(String identifier) {
        if (identifier == null || identifier.isEmpty()) {
            return false;
        }

        // 根据金仓官方文档：https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html
        // 金仓数据库应使用双引号包围schema和表名，保持PostgreSQL兼容性

        // 检查是否为保留字 - 金仓保留字需要双引号
        // 使用统一的保留字管理系统
        if (ReservedWords.isReservedWord(identifier, "kingbase")) {
            return true;
        }

        // 检查是否包含特殊字符 - 需要双引号
        if (!identifier.matches("^[a-zA-Z_][a-zA-Z0-9_]*$")) {
            return true;
        }

        // 检查是否包含大写字母 - 金仓默认转换为小写，如需保持大写需要双引号
        if (!identifier.equals(identifier.toLowerCase())) {
            return true;
        }

        return false;
    }
    
    // ==================== 数据类型映射 ====================
    
    @Override
    public String mapDataType(String mysqlType, Integer length, Integer precision, Integer scale) {
        if (mysqlType == null) {
            return "VARCHAR(255)"; // 默认类型
        }
        
        String baseType = mysqlType.toUpperCase();
        String kbType = DATA_TYPE_MAPPING.getOrDefault(baseType, baseType);
        
        // 处理精度和小数位数（优先级最高）
        // 只有当precision和scale都大于0时才添加精度信息
        if (precision != null && scale != null && precision > 0) {
            // DECIMAL(precision, scale), NUMERIC(precision, scale)
            return kbType + "(" + precision + "," + scale + ")";
        }

        // 处理长度
        if (length != null && length > 0) {
            // VARCHAR(length), CHAR(length)
            return kbType + "(" + length + ")";
        }
        
        return kbType;
    }
    
    @Override
    public boolean supportsDataType(String dataType) {
        return DATA_TYPE_MAPPING.containsKey(dataType.toUpperCase());
    }
    
    // ==================== SQL语法支持 ====================
    
    @Override
    public boolean supportsLimit() {
        return true; // 金仓原生支持LIMIT
    }
    
    @Override
    public boolean supportsOffset() {
        return true; // 金仓原生支持OFFSET
    }
    
    @Override
    public String formatPagination(Integer limit, Integer offset) {
        StringBuilder pagination = new StringBuilder();
        
        if (limit != null) {
            pagination.append("LIMIT ").append(limit);
        }
        
        if (offset != null && offset > 0) {
            pagination.append(" OFFSET ").append(offset);
        }
        
        return pagination.toString();
    }
    
    @Override
    public boolean supportsAutoIncrement() {
        // 根据金仓官方文档 https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
        // 金仓数据库支持多种自增列方式：
        // 1. 原生支持SERIAL、SMALLSERIAL、BIGSERIAL类型（序数类型）
        // 2. MySQL兼容模式下支持AUTO_INCREMENT语法
        // 参考：https://juejin.cn/post/7343137522053693467 "金仓数据库KingbaseES 兼容MYSQL自增列"
        return true;
    }

    @Override
    public String getAutoIncrementSyntax() {
        // 根据金仓官方文档，金仓数据库原生支持SERIAL类型作为自增列
        // SERIAL类型是PostgreSQL风格的自增列，底层实现是序列
        // 也支持SMALLSERIAL（对应SMALLINT）和BIGSERIAL（对应BIGINT）
        return "SERIAL";
    }
    
    // ==================== 函数映射 ====================
    
    @Override
    public String mapFunction(String mysqlFunction, String... args) {
        if (mysqlFunction == null) {
            return "";
        }
        
        String kbFunction = FUNCTION_MAPPING.get(mysqlFunction.toUpperCase());
        if (kbFunction != null) {
            if (args.length > 0) {
                return kbFunction + "(" + String.join(", ", args) + ")";
            } else {
                return kbFunction + "()";
            }
        }
        
        // 特殊处理一些MySQL特有函数
        switch (mysqlFunction.toUpperCase()) {
            case "IFNULL":
                if (args.length >= 2) {
                    return "COALESCE(" + args[0] + ", " + args[1] + ")";
                }
                break;
            case "IF":
                if (args.length >= 3) {
                    return "CASE WHEN " + args[0] + " THEN " + args[1] + " ELSE " + args[2] + " END";
                }
                break;
        }
        
        // 如果没有映射，返回原函数名
        if (args.length > 0) {
            return mysqlFunction + "(" + String.join(", ", args) + ")";
        } else {
            return mysqlFunction + "()";
        }
    }
    
    @Override
    public boolean supportsFunction(String functionName) {
        return FUNCTION_MAPPING.containsKey(functionName.toUpperCase()) ||
               "IFNULL".equals(functionName.toUpperCase()) ||
               "IF".equals(functionName.toUpperCase());
    }
    
    // ==================== 约束和索引 ====================
    
    @Override
    public boolean supportsForeignKey() {
        return true;
    }
    
    @Override
    public boolean supportsCheckConstraint() {
        return true;
    }
    
    @Override
    public String formatPrimaryKey(String... columnNames) {
        if (columnNames.length == 0) {
            return "";
        }
        return "PRIMARY KEY (" + String.join(", ", columnNames) + ")";
    }
    
    @Override
    public String formatUniqueConstraint(String... columnNames) {
        if (columnNames.length == 0) {
            return "";
        }
        return "UNIQUE (" + String.join(", ", columnNames) + ")";
    }
    
    // ==================== 金仓特有方法 ====================
    
    @Override
    public String getCurrentTimestampFunction() {
        return "NOW()";
    }
    
    @Override
    public String getCurrentDateFunction() {
        return "CURRENT_DATE";
    }
    
    @Override
    public String getConcatOperator() {
        return "||"; // PostgreSQL风格的连接符
    }
    
    @Override
    public boolean isCaseSensitive() {
        return false; // PostgreSQL/金仓默认不区分大小写（除非引用）
    }
}
