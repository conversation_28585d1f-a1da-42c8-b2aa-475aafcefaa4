package com.xylink.sqltranspiler.core.dialects;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import com.xylink.sqltranspiler.common.constants.ReservedWords;

/**
 * 达梦数据库方言实现
 * 基于达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * 
 * 核心特性：
 * 1. 支持双引号标识符引用
 * 2. 使用ROWNUM实现分页
 * 3. 支持大部分标准SQL函数
 * 4. 数据类型与Oracle相似
 */
public class DamengDialect implements SqlDialect {
    
    // 达梦数据类型映射表 - 严格基于达梦官方文档
    // 参考：https://eco.dameng.com/document/dm/zh-cn/faq/faq-sql-gramm.html "Oracle和DM数据类型对比表"
    private static final Map<String, String> DATA_TYPE_MAPPING;
    static {
        Map<String, String> map = new HashMap<>();
        // 整数类型 - 根据达梦官方文档，达梦支持完整的整数类型体系
        // 参考官方文档数据类型对比表
        map.put("TINYINT", "TINYINT");     // 达梦官方文档明确支持TINYINT
        map.put("SMALLINT", "SMALLINT");   // 达梦官方文档明确支持SMALLINT
        map.put("MEDIUMINT", "INT");       // 达梦不支持MEDIUMINT，映射为INT
        map.put("INT", "INT");             // 达梦官方文档明确支持INT
        map.put("INTEGER", "INT");         // INTEGER是INT的别名
        map.put("BIGINT", "BIGINT");       // 达梦官方文档明确支持BIGINT

        // 布尔类型 - 基于达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/dmpl-sql-datatype.html
        // BIT类型用于存储整数数据1、0或NULL，可以用来支持ODBC和JDBC的布尔数据类型
        map.put("BOOLEAN", "BIT");  // MySQL BOOLEAN映射为达梦BIT

        // 浮点类型
        map.put("FLOAT", "FLOAT");
        map.put("DOUBLE", "DOUBLE");
        map.put("DECIMAL", "DECIMAL");
        map.put("NUMERIC", "NUMERIC");

        // 字符类型
        map.put("CHAR", "CHAR");
        map.put("VARCHAR", "VARCHAR");
        map.put("TINYTEXT", "CLOB");  // 基于达梦官方DTS文档：TINYTEXT映射为CLOB
        map.put("TEXT", "CLOB");
        map.put("LONGTEXT", "CLOB");
        map.put("MEDIUMTEXT", "CLOB");

        // 日期时间类型
        map.put("DATE", "DATE");
        map.put("TIME", "TIME");
        map.put("DATETIME", "TIMESTAMP");
        map.put("TIMESTAMP", "TIMESTAMP");
        map.put("YEAR", "INT");

        // 二进制类型
        map.put("BINARY", "BINARY");
        map.put("VARBINARY", "VARBINARY");
        map.put("TINYBLOB", "BLOB");  // 达梦数据库将TINYBLOB映射为BLOB
        map.put("BLOB", "BLOB");
        map.put("MEDIUMBLOB", "BLOB");  // 达梦数据库将MEDIUMBLOB映射为BLOB
        map.put("LONGBLOB", "BLOB");

        // JSON类型 - 基于达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/pm/json.html
        // 达梦数据库完全支持JSON数据类型
        map.put("JSON", "JSON");

        // ENUM和SET类型 - 达梦数据库不支持，转换为VARCHAR
        // 根据达梦官方文档，达梦数据库不支持MySQL的ENUM和SET类型
        map.put("ENUM", "VARCHAR");
        map.put("SET", "VARCHAR");

        DATA_TYPE_MAPPING = Collections.unmodifiableMap(map);
    }
    
    // 达梦函数映射表
    private static final Map<String, String> FUNCTION_MAPPING;
    static {
        Map<String, String> map = new HashMap<>();
        // 日期时间函数
        map.put("NOW", "SYSDATE");
        map.put("CURDATE", "TRUNC(SYSDATE)");
        map.put("CURTIME", "TO_CHAR(SYSDATE, 'HH24:MI:SS')");

        // 字符串函数
        map.put("LENGTH", "LENGTH");
        map.put("CHAR_LENGTH", "LENGTH");
        map.put("SUBSTR", "SUBSTR");
        map.put("SUBSTRING", "SUBSTR");
        map.put("CONCAT", "CONCAT");
        map.put("UPPER", "UPPER");
        map.put("LOWER", "LOWER");
        map.put("TRIM", "TRIM");

        // 数学函数
        map.put("ABS", "ABS");
        map.put("CEIL", "CEIL");
        map.put("FLOOR", "FLOOR");
        map.put("ROUND", "ROUND");
        map.put("MOD", "MOD");
        map.put("RAND", "RANDOM");

        // 条件函数 - 基于达梦官方文档
        map.put("IFNULL", "NVL");
        map.put("NULLIF", "NULLIF");
        map.put("COALESCE", "COALESCE");

        // 扩展日期时间函数
        map.put("SYSDATE", "SYSDATE");
        map.put("UTC_TIMESTAMP", "SYS_EXTRACT_UTC(SYSTIMESTAMP)");

        FUNCTION_MAPPING = Collections.unmodifiableMap(map);
    }
    
    // 达梦保留字统一管理 - 使用ReservedWords类
    // 基于达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/faq/faq-errorcode.html
    // 官方文档明确提到的保留关键字：DOMAIN,verify,reference,offset,TYPE
    // 所有保留字定义已迁移到ReservedWords类中统一管理
    
    @Override
    public String getName() {
        return "DM";
    }
    
    @Override
    public String getDatabaseProduct() {
        return "DM Database";
    }
    
    // ==================== 标识符处理 ====================
    
    @Override
    public String quoteIdentifier(String identifier) {
        if (identifier == null || identifier.isEmpty()) {
            return identifier;
        }
        
        // 根据达梦官方文档，普通标识符不需要双引号，只有特殊情况才需要
        if (requiresQuoting(identifier)) {
            return "\"" + identifier.replace("\"", "\"\"") + "\"";
        }
        return identifier;
    }
    
    @Override
    public String quoteLiteral(String literal) {
        if (literal == null) {
            return "NULL";
        }
        return "'" + literal.replace("'", "''") + "'";
    }
    
    @Override
    public boolean requiresQuoting(String identifier) {
        if (identifier == null || identifier.isEmpty()) {
            return false;
        }

        // 根据达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
        // 达梦数据库标识符引用规则：
        // 1. 普通标识符（字母开头，只包含字母、数字、下划线）不需要引用
        // 2. 保留字需要双引号
        // 3. 包含特殊字符的标识符需要双引号
        // 4. 以数字开头的标识符需要双引号
        // 5. 需要保持大小写敏感的标识符需要双引号

        // 检查是否为保留字 - 达梦保留字需要双引号
        // 使用统一的保留字管理系统
        if (ReservedWords.isReservedWord(identifier, "dameng")) {
            return true;
        }

        // 检查是否包含特殊字符 - 需要双引号
        if (!identifier.matches("^[a-zA-Z_][a-zA-Z0-9_]*$")) {
            return true;
        }

        // 检查是否以数字开头 - 需要双引号
        if (Character.isDigit(identifier.charAt(0))) {
            return true;
        }

        // 对于普通标识符（如users、id、name等），达梦数据库不需要双引号
        // 只有在需要保持特定大小写或包含特殊字符时才需要双引号
        // 这里我们采用保守策略：只对明确需要引用的情况加引号

        return false;
    }
    
    // ==================== 数据类型映射 ====================
    
    @Override
    public String mapDataType(String mysqlType, Integer length, Integer precision, Integer scale) {
        if (mysqlType == null) {
            return "VARCHAR(255)"; // 默认类型
        }

        String baseType = mysqlType.toUpperCase();

        // 特殊处理：ENUM和SET类型
        // 根据达梦官方文档，达梦数据库不支持ENUM和SET类型，需要转换为VARCHAR
        if (baseType.startsWith("ENUM(")) {
            // ENUM类型转换为VARCHAR，计算合适的长度
            int enumLength = calculateEnumLength(mysqlType);
            return "VARCHAR(" + enumLength + ")";
        }

        if (baseType.startsWith("SET(")) {
            // SET类型转换为VARCHAR，计算合适的长度
            int setLength = calculateSetLength(mysqlType);
            return "VARCHAR(" + setLength + ")";
        }

        // 特殊处理：TINYINT(1) 在MySQL中通常用作布尔类型
        // 由于解析问题，mysqlType可能包含完整的类型定义如"TINYINT(1)"
        // 基于达梦官方文档，BIT类型用于支持布尔数据类型
        if (baseType.equals("TINYINT(1)")) {
            return "BIT";  // TINYINT(1) 映射为 BIT
        }

        // 标准的TINYINT(1)处理
        if ("TINYINT".equals(baseType) && length != null && length == 1) {
            return "BIT";  // TINYINT(1) 映射为 BIT
        }

        String dmType = DATA_TYPE_MAPPING.getOrDefault(baseType, baseType);

        // 处理精度和小数位数（优先级最高）
        // 只有当precision和scale都大于0时才添加精度信息
        if (precision != null && scale != null && precision > 0) {
            // DECIMAL(precision, scale), NUMERIC(precision, scale)
            return dmType + "(" + precision + "," + scale + ")";
        }

        // 处理长度
        if (length != null && length > 0) {
            // 检查VARCHAR长度限制 - 根据达梦官方文档，VARCHAR最大长度为32767字节
            if ("VARCHAR".equals(dmType) && length > 32767) {
                // 超过VARCHAR限制，转换为CLOB
                return "CLOB";
            }
            // VARCHAR(length), CHAR(length)
            return dmType + "(" + length + ")";
        }

        return dmType;
    }
    
    @Override
    public boolean supportsDataType(String dataType) {
        return DATA_TYPE_MAPPING.containsKey(dataType.toUpperCase());
    }

    /**
     * 计算ENUM类型转换为VARCHAR时的合适长度
     * 根据ENUM值列表中最长的值确定VARCHAR长度
     */
    private int calculateEnumLength(String enumType) {
        try {
            // 提取ENUM值列表：ENUM('value1', 'value2', ...)
            int start = enumType.indexOf('(');
            int end = enumType.lastIndexOf(')');
            if (start == -1 || end == -1 || end <= start) {
                return 50; // 默认长度
            }

            String valueList = enumType.substring(start + 1, end);
            String[] values = valueList.split(",");
            int maxLength = 0;

            for (String value : values) {
                // 移除引号和空格
                String cleanValue = value.trim().replaceAll("^['\"]|['\"]$", "");
                maxLength = Math.max(maxLength, cleanValue.length());
            }

            // 确保最小长度为10，最大长度为255
            return Math.max(10, Math.min(maxLength + 10, 255));
        } catch (Exception e) {
            // 解析失败时使用默认长度
            return 50;
        }
    }

    /**
     * 计算SET类型转换为VARCHAR时的合适长度
     * SET类型可以包含多个值的组合，需要更大的长度
     */
    private int calculateSetLength(String setType) {
        try {
            // 提取SET值列表：SET('value1', 'value2', ...)
            int start = setType.indexOf('(');
            int end = setType.lastIndexOf(')');
            if (start == -1 || end == -1 || end <= start) {
                return 100; // 默认长度
            }

            String valueList = setType.substring(start + 1, end);
            String[] values = valueList.split(",");
            int totalLength = 0;

            for (String value : values) {
                // 移除引号和空格
                String cleanValue = value.trim().replaceAll("^['\"]|['\"]$", "");
                totalLength += cleanValue.length() + 1; // +1 for comma separator
            }

            // SET类型可以包含所有值的组合，所以需要更大的长度
            // 确保最小长度为20，最大长度为500
            return Math.max(20, Math.min(totalLength * 2, 500));
        } catch (Exception e) {
            // 解析失败时使用默认长度
            return 100;
        }
    }
    
    // ==================== SQL语法支持 ====================
    
    @Override
    public boolean supportsLimit() {
        return true; // 达梦原生支持LIMIT语法，根据官方文档
    }

    @Override
    public boolean supportsOffset() {
        return true; // 达梦原生支持OFFSET语法，根据官方文档
    }
    
    @Override
    public String formatPagination(Integer limit, Integer offset) {
        // 达梦数据库原生支持LIMIT OFFSET语法
        // 参考：https://eco.dameng.com/document/dm/zh-cn/sql-dev/practice-single-table.html#2-14-如何限制返回的行数
        // 达梦官方文档明确显示：方法二：使用 LIMIT 子句返回两行。示例语句如下所示：SELECT * FROM dmhr.employee LIMIT 2;

        // 根据SQL标准，OFFSET必须与LIMIT一起使用
        if (limit == null) {
            return "";  // 没有LIMIT就不能有OFFSET
        }

        StringBuilder pagination = new StringBuilder();
        pagination.append("LIMIT ").append(limit);

        if (offset != null && offset > 0) {
            pagination.append(" OFFSET ").append(offset);
        }

        return pagination.toString();
    }
    
    @Override
    public boolean supportsAutoIncrement() {
        // 根据达梦官方文档 https://eco.dameng.com/document/dm/zh-cn/faq/faq-sql-gramm.html
        // "不支持auto_increment，使用identity代替，从1开始，每次增1"
        // 达梦数据库支持自增列，但使用IDENTITY语法而不是AUTO_INCREMENT
        return true;
    }

    @Override
    public String getAutoIncrementSyntax() {
        // 根据达梦官方文档示例：
        // CREATE TABLE "TAB_12" ("ID" INT IDENTITY (1, 1) NOT NULL, ...)
        // IDENTITY(1,1) 表示从1开始，每次增1
        return "IDENTITY(1,1)";
    }
    
    // ==================== 函数映射 ====================
    
    @Override
    public String mapFunction(String mysqlFunction, String... args) {
        if (mysqlFunction == null) {
            return "";
        }
        
        String dmFunction = FUNCTION_MAPPING.get(mysqlFunction.toUpperCase());
        if (dmFunction != null) {
            if (args.length > 0) {
                return dmFunction + "(" + String.join(", ", args) + ")";
            } else {
                // 对于无参数函数，检查是否需要括号
                if (needsParentheses(mysqlFunction)) {
                    return dmFunction + "()";
                } else {
                    return dmFunction;
                }
            }
        }
        
        // 如果没有映射，返回原函数名
        if (args.length > 0) {
            return mysqlFunction + "(" + String.join(", ", args) + ")";
        } else {
            return mysqlFunction;
        }
    }
    
    /**
     * 检查函数是否需要括号
     * 根据达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/practice-date.html
     * SYSDATE是无参数函数，不需要括号
     * 某些函数即使无参数也需要括号
     */
    private boolean needsParentheses(String functionName) {
        String upperFunction = functionName.toUpperCase();
        // 根据达梦官方文档，这些函数即使无参数也需要括号
        return "RAND".equals(upperFunction) ||
               "RANDOM".equals(upperFunction);
        // 注意：SYSDATE、NOW、CURDATE、CURTIME在达梦中不需要括号
        // 参考达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/practice-date.html
    }

    @Override
    public boolean supportsFunction(String functionName) {
        return FUNCTION_MAPPING.containsKey(functionName.toUpperCase());
    }
    
    // ==================== 约束和索引 ====================
    
    @Override
    public boolean supportsForeignKey() {
        return true;
    }
    
    @Override
    public boolean supportsCheckConstraint() {
        return true;
    }
    
    @Override
    public String formatPrimaryKey(String... columnNames) {
        if (columnNames.length == 0) {
            return "";
        }
        return "PRIMARY KEY (" + String.join(", ", columnNames) + ")";
    }
    
    @Override
    public String formatUniqueConstraint(String... columnNames) {
        if (columnNames.length == 0) {
            return "";
        }
        return "UNIQUE (" + String.join(", ", columnNames) + ")";
    }
    
    // ==================== 达梦特有方法 ====================
    
    @Override
    public String getCurrentTimestampFunction() {
        return "SYSDATE";
    }
    
    @Override
    public String getCurrentDateFunction() {
        return "TRUNC(SYSDATE)";
    }
    
    @Override
    public String getConcatOperator() {
        return "||"; // 达梦支持Oracle风格的连接符
    }
    
    @Override
    public boolean isCaseSensitive() {
        return false; // 达梦默认不区分大小写
    }
}
