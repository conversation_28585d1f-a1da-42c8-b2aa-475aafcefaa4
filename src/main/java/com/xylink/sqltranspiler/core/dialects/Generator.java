package com.xylink.sqltranspiler.core.dialects;

import com.xylink.sqltranspiler.core.ast.Statement;

/**
 * Interface for generating SQL for a specific dialect from the AST.
 */
public interface Generator {
    /**
     * Generates a SQL script from an AST statement.
     *
     * @param statement The AST statement.
     * @return The result of the transpilation.
     */
    String generate(Statement statement);
}