package com.xylink.sqltranspiler.core.model.enums;

import java.io.Serializable;

public enum AlterActionType implements Serializable {
    SET_PROPS,
    SET_SERDE,
    TOUCH_TABLE,
    ALTER_COLUMN,
    ALTER_VIEW_QUERY,
    ADD_UNIQUE_KEY,
    ADD_PRIMARY_KEY,
    ADD_INDEX,
    ADD_PARTITION,
    DROP_INDEX,
    ADD_COLUMN,
    SET_COLUMN_DEFAULT,
    DROP_COLUMN,
    DROP_COLUMN_DRFAULT,
    DROP_PARTITION,
    DROP_PRIMARY_KEY,
    RENAME,
    RENAME_PARTITION,
    DETACH_PARTITION,
    ATTACH_PARTITION,
    TRUNCATE_PARTITION,
    REFRESH_MV,

    // Iceberg SQL Extensions
    CREATE_TAG,
    CREATE_BRANCH,
    DROP_TAG,
    DROP_<PERSON>ANCH,
    ADD_PARTITION_FIELD,
    DROP_PARTITION_FIELD,
    REPLACE_PARTITION_FIELD,
    SET_WRITE_DISTRIBUTION_AND_ORDERING,
    SET_IDENTIFIER_FIELDS,
    DROP_IDENTIFIER_FIELDS,

    // delta
    ADD_CONSTRAINT,
    DROP_CONSTRAINT,
    DROP_FEATURE,
    CLUSTER_BY,
    SYNC_IDENTITY,

    // MySQL specific
    SET_AUTO_INCREMENT,
    UNKOWN
}
