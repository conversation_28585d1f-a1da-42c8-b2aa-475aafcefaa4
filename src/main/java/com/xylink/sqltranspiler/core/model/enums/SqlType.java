package com.xylink.sqltranspiler.core.model.enums;

public enum SqlType {
    DML("Data Manipulation Language"),
    DDL("Data Definition Language"),
    DQL("Data Query Language"),
    DCL("Data Control Language"),
    TCL("Transaction Control Language");

    private final String desc;

    SqlType(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }
}
