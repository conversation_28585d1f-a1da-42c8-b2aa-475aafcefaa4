package com.xylink.sqltranspiler.core.validation;

import java.util.*;

/**
 * 表结构信息，用于VARCHAR长度验证
 */
public class TableSchema {
    
    private final String tableName;
    private final Map<String, VarcharColumn> varcharColumns;
    private final long createdTime;
    private volatile long lastAccessTime;
    
    public TableSchema(String tableName, Map<String, VarcharColumn> varcharColumns) {
        this.tableName = tableName;
        this.varcharColumns = Collections.unmodifiableMap(new HashMap<>(varcharColumns));
        this.createdTime = System.currentTimeMillis();
        this.lastAccessTime = createdTime;
    }
    
    /**
     * VARCHAR列信息
     */
    public static class VarcharColumn {
        private final String columnName;
        private final int maxLength;
        private final int position; // 在INSERT语句中的位置（从0开始）
        private final boolean nullable;
        
        public VarcharColumn(String columnName, int maxLength, int position, boolean nullable) {
            this.columnName = columnName;
            this.maxLength = maxLength;
            this.position = position;
            this.nullable = nullable;
        }
        
        public VarcharColumn(String columnName, int maxLength, int position) {
            this(columnName, maxLength, position, true);
        }
        
        // Getter方法
        public String getColumnName() {
            return columnName;
        }
        
        public int getMaxLength() {
            return maxLength;
        }
        
        public int getPosition() {
            return position;
        }
        
        public boolean isNullable() {
            return nullable;
        }
        
        @Override
        public String toString() {
            return String.format("%s VARCHAR(%d) pos=%d nullable=%s", 
                columnName, maxLength, position, nullable);
        }
    }
    
    // Getter方法
    public String getTableName() {
        return tableName;
    }
    
    public Map<String, VarcharColumn> getVarcharColumns() {
        updateLastAccessTime();
        return varcharColumns;
    }
    
    public long getCreatedTime() {
        return createdTime;
    }
    
    public long getLastAccessTime() {
        return lastAccessTime;
    }
    
    /**
     * 获取指定列的VARCHAR信息
     */
    public VarcharColumn getVarcharColumn(String columnName) {
        updateLastAccessTime();
        return varcharColumns.get(columnName);
    }
    
    /**
     * 检查是否包含指定的VARCHAR列
     */
    public boolean hasVarcharColumn(String columnName) {
        return varcharColumns.containsKey(columnName);
    }
    
    /**
     * 获取VARCHAR列数量
     */
    public int getVarcharColumnCount() {
        return varcharColumns.size();
    }
    
    /**
     * 检查表是否有VARCHAR列
     */
    public boolean hasVarcharColumns() {
        return !varcharColumns.isEmpty();
    }
    
    /**
     * 更新最后访问时间
     */
    private void updateLastAccessTime() {
        this.lastAccessTime = System.currentTimeMillis();
    }
    
    /**
     * 获取表的年龄（毫秒）
     */
    public long getAgeMs() {
        return System.currentTimeMillis() - createdTime;
    }
    
    /**
     * 获取自上次访问以来的时间（毫秒）
     */
    public long getTimeSinceLastAccessMs() {
        return System.currentTimeMillis() - lastAccessTime;
    }
    
    @Override
    public String toString() {
        return String.format("TableSchema{name='%s', varcharColumns=%d, age=%dms}", 
            tableName, varcharColumns.size(), getAgeMs());
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        TableSchema that = (TableSchema) obj;
        return tableName.equals(that.tableName);
    }
    
    @Override
    public int hashCode() {
        return tableName.hashCode();
    }
}
