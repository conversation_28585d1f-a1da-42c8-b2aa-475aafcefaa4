package com.xylink.sqltranspiler.core.validation;

import com.xylink.sqltranspiler.core.ast.Statement;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 批量验证器 - 验证INSERT/UPDATE语句中的VARCHAR长度冲突
 *
 * 使用委托模式，内部使用StagedVarcharValidator的单个语句验证功能
 */
public class BatchValidator {

    private static final Logger log = LoggerFactory.getLogger(BatchValidator.class);

    private final Map<String, TableSchema> schemas;
    private final VarcharValidationConfig config;
    private final List<ValidationResult> results = new ArrayList<>();
    private int totalValidations = 0;
    private int statementLineNumber = 0;

    // 委托给StagedVarcharValidator进行实际验证
    private final StagedVarcharValidator delegateValidator;

    public BatchValidator(Map<String, TableSchema> schemas, VarcharValidationConfig config) {
        this.schemas = schemas != null ? schemas : new HashMap<>();
        this.config = config != null ? config : VarcharValidationConfig.defaultConfig();

        // 创建并配置委托验证器
        this.delegateValidator = new StagedVarcharValidator();
        this.delegateValidator.configure(this.config);
    }

    public BatchValidator(Map<String, TableSchema> schemas) {
        this(schemas, VarcharValidationConfig.defaultConfig());
    }
    
    /**
     * 验证单个语句
     * 使用委托模式调用StagedVarcharValidator的单个语句验证功能
     */
    public void validateStatement(Statement statement) {
        if (statement == null) {
            return;
        }

        statementLineNumber++;
        totalValidations++;

        try {
            // 使用委托验证器进行实际验证
            ValidationResult result = delegateValidator.validateSingleStatement(statement, schemas);

            if (result != null) {
                // 更新行号信息
                ValidationResult updatedResult = new ValidationResult(
                    result.getTableName(),
                    result.getStatementText(),
                    result.getConflicts(),
                    statementLineNumber,
                    result.getValidationTime()
                );
                results.add(updatedResult);

                log.debug("Validated statement at line {}: {} conflicts found",
                    statementLineNumber, result.getConflictCount());
            } else {
                log.debug("Statement at line {} does not require VARCHAR validation or has no conflicts",
                    statementLineNumber);
            }

        } catch (Exception e) {
            log.error("Failed to validate statement at line {}", statementLineNumber, e);
        }
    }

    /**
     * 批量验证语句列表
     */
    public void validateStatements(List<Statement> statements) {
        if (statements == null) {
            return;
        }

        for (Statement statement : statements) {
            validateStatement(statement);
        }
    }
    
    /**
     * 获取验证摘要
     */
    public ValidationSummary getSummary(long validationTimeMs) {
        List<VarcharConflict> allConflicts = results.stream()
            .flatMap(result -> result.getConflicts().stream())
            .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
            
        return new ValidationSummary(allConflicts, totalValidations, validationTimeMs, config.isEnabled());
    }
    
    /**
     * 获取所有验证结果
     */
    public List<ValidationResult> getResults() {
        return Collections.unmodifiableList(results);
    }
    
    /**
     * 重置验证器状态
     */
    public void reset() {
        results.clear();
        totalValidations = 0;
        statementLineNumber = 0;
    }

    /**
     * 获取委托验证器（用于测试或高级用法）
     */
    public StagedVarcharValidator getDelegateValidator() {
        return delegateValidator;
    }

    /**
     * 获取表结构信息（用于调试）
     */
    public Map<String, TableSchema> getSchemas() {
        return Collections.unmodifiableMap(schemas);
    }
}
