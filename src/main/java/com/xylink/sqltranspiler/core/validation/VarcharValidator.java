package com.xylink.sqltranspiler.core.validation;

import com.xylink.sqltranspiler.core.ast.Statement;
import java.util.List;

/**
 * VARCHAR字段长度验证器接口
 * 
 * 负责检测SQL语句中VARCHAR字段的实际数据长度是否超过定义长度
 */
public interface VarcharValidator {
    
    /**
     * 验证SQL语句列表中的VARCHAR长度冲突
     * 
     * @param statements SQL语句列表
     * @return 验证结果摘要
     */
    ValidationSummary validate(List<Statement> statements);
    
    /**
     * 配置验证器
     * 
     * @param config 验证配置
     */
    void configure(VarcharValidationConfig config);
    
    /**
     * 检查验证器是否已启用
     * 
     * @return 如果启用返回true
     */
    boolean isEnabled();
}
