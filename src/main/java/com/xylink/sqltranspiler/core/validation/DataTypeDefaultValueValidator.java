package com.xylink.sqltranspiler.core.validation;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import com.xylink.sqltranspiler.core.ast.table.ColumnRel;

/**
 * 数据类型与默认值匹配校验器
 * 
 * 根据MySQL、达梦、金仓、神通数据库官方文档，校验数据类型与默认值的匹配性：
 * 1. 数值类型的默认值不应该使用字符串格式（如 bigint default '0'）
 * 2. 字符串类型的默认值应该使用引号包围
 * 3. 日期时间类型的默认值应该符合相应格式
 * 4. 布尔类型的默认值应该是合法的布尔值
 */
public class DataTypeDefaultValueValidator {
    
    private static final Logger log = LoggerFactory.getLogger(DataTypeDefaultValueValidator.class);
    
    /**
     * 数值类型模式
     */
    private static final Pattern NUMERIC_TYPES = Pattern.compile(
        "(?i)\\b(TINYINT|SMALLINT|MEDIUMINT|INT|INTEGER|BIGINT|DECIMAL|NUMERIC|FLOAT|DOUBLE|REAL)\\b"
    );
    
    /**
     * 字符串类型模式
     */
    private static final Pattern STRING_TYPES = Pattern.compile(
        "(?i)\\b(CHAR|VARCHAR|NVARCHAR2|TEXT|TINYTEXT|MEDIUMTEXT|LONGTEXT|CLOB)\\b"
    );
    
    /**
     * 日期时间类型模式
     */
    private static final Pattern DATETIME_TYPES = Pattern.compile(
        "(?i)\\b(DATE|TIME|DATETIME|TIMESTAMP)\\b"
    );
    
    /**
     * 布尔类型模式
     */
    private static final Pattern BOOLEAN_TYPES = Pattern.compile(
        "(?i)\\b(BOOLEAN|BOOL|BIT)\\b"
    );
    
    /**
     * 校验CREATE TABLE语句中的数据类型与默认值匹配性
     */
    public List<DataTypeDefaultValueIssue> validate(CreateTable createTable) {
        List<DataTypeDefaultValueIssue> issues = new ArrayList<>();

        if (createTable == null || createTable.getColumnRels() == null) {
            return issues;
        }

        String tableName = createTable.getTableId().getTableName();

        for (ColumnRel column : createTable.getColumnRels()) {
            if (column.getDefaultExpr() != null && !column.getDefaultExpr().trim().isEmpty()) {
                DataTypeDefaultValueIssue issue = validateColumnDefault(tableName, column);
                if (issue != null) {
                    issues.add(issue);
                }
            }
        }

        return issues;
    }
    
    /**
     * 校验单个列的数据类型与默认值匹配性
     */
    private DataTypeDefaultValueIssue validateColumnDefault(String tableName, ColumnRel column) {
        String columnName = column.getColumnName();
        String dataType = column.getTypeName();
        String defaultValue = column.getDefaultExpr();
        
        if (dataType == null || defaultValue == null) {
            return null;
        }
        
        // 跳过特殊函数和表达式
        if (isSpecialFunction(defaultValue)) {
            return null;
        }
        
        String upperDataType = dataType.toUpperCase();
        String trimmedDefault = defaultValue.trim();
        
        // 校验数值类型
        if (NUMERIC_TYPES.matcher(upperDataType).find()) {
            return validateNumericDefault(tableName, columnName, dataType, trimmedDefault);
        }
        
        // 校验字符串类型
        if (STRING_TYPES.matcher(upperDataType).find()) {
            return validateStringDefault(tableName, columnName, dataType, trimmedDefault);
        }
        
        // 校验日期时间类型
        if (DATETIME_TYPES.matcher(upperDataType).find()) {
            return validateDateTimeDefault(tableName, columnName, dataType, trimmedDefault);
        }
        
        // 校验布尔类型
        if (BOOLEAN_TYPES.matcher(upperDataType).find()) {
            return validateBooleanDefault(tableName, columnName, dataType, trimmedDefault);
        }
        
        return null;
    }
    
    /**
     * 校验数值类型的默认值
     */
    private DataTypeDefaultValueIssue validateNumericDefault(String tableName, String columnName, 
                                                           String dataType, String defaultValue) {
        // 检查是否使用了引号包围的数值（这是错误的）
        if ((defaultValue.startsWith("'") && defaultValue.endsWith("'")) ||
            (defaultValue.startsWith("\"") && defaultValue.endsWith("\""))) {
            
            String quotedValue = defaultValue.substring(1, defaultValue.length() - 1);
            
            // 检查引号内是否是有效数值
            if (isValidNumeric(quotedValue)) {
                return new DataTypeDefaultValueIssue(
                    tableName, columnName, dataType, defaultValue,
                    DataTypeDefaultValueIssue.IssueType.QUOTED_NUMERIC_DEFAULT,
                    String.format("数值类型 %s 的默认值不应该使用引号包围。应该是: %s", dataType, quotedValue),
                    quotedValue
                );
            } else {
                return new DataTypeDefaultValueIssue(
                    tableName, columnName, dataType, defaultValue,
                    DataTypeDefaultValueIssue.IssueType.INVALID_NUMERIC_DEFAULT,
                    String.format("数值类型 %s 的默认值 '%s' 不是有效的数值", dataType, quotedValue),
                    "0"
                );
            }
        }
        
        // 检查是否是有效的数值格式
        if (!isValidNumeric(defaultValue) && !"NULL".equalsIgnoreCase(defaultValue)) {
            return new DataTypeDefaultValueIssue(
                tableName, columnName, dataType, defaultValue,
                DataTypeDefaultValueIssue.IssueType.INVALID_NUMERIC_DEFAULT,
                String.format("数值类型 %s 的默认值 '%s' 不是有效的数值格式", dataType, defaultValue),
                "0"
            );
        }
        
        return null;
    }
    
    /**
     * 校验字符串类型的默认值
     */
    private DataTypeDefaultValueIssue validateStringDefault(String tableName, String columnName, 
                                                          String dataType, String defaultValue) {
        // 字符串类型的默认值应该使用引号包围（除了NULL）
        if (!"NULL".equalsIgnoreCase(defaultValue) && 
            !defaultValue.startsWith("'") && !defaultValue.startsWith("\"")) {
            
            return new DataTypeDefaultValueIssue(
                tableName, columnName, dataType, defaultValue,
                DataTypeDefaultValueIssue.IssueType.UNQUOTED_STRING_DEFAULT,
                String.format("字符串类型 %s 的默认值应该使用引号包围", dataType),
                "'" + defaultValue + "'"
            );
        }
        
        return null;
    }
    
    /**
     * 校验日期时间类型的默认值
     */
    private DataTypeDefaultValueIssue validateDateTimeDefault(String tableName, String columnName, 
                                                            String dataType, String defaultValue) {
        // 检查无效的日期时间值
        if (defaultValue.contains("0000-00-00")) {
            return new DataTypeDefaultValueIssue(
                tableName, columnName, dataType, defaultValue,
                DataTypeDefaultValueIssue.IssueType.INVALID_DATETIME_DEFAULT,
                String.format("日期时间类型 %s 的默认值包含无效的日期 '0000-00-00'", dataType),
                "'1900-01-01'"
            );
        }
        
        return null;
    }
    
    /**
     * 校验布尔类型的默认值
     */
    private DataTypeDefaultValueIssue validateBooleanDefault(String tableName, String columnName, 
                                                           String dataType, String defaultValue) {
        String upperDefault = defaultValue.toUpperCase();
        
        // 检查是否是有效的布尔值
        if (!"TRUE".equals(upperDefault) && !"FALSE".equals(upperDefault) && 
            !"1".equals(defaultValue) && !"0".equals(defaultValue) && 
            !"NULL".equals(upperDefault)) {
            
            return new DataTypeDefaultValueIssue(
                tableName, columnName, dataType, defaultValue,
                DataTypeDefaultValueIssue.IssueType.INVALID_BOOLEAN_DEFAULT,
                String.format("布尔类型 %s 的默认值 '%s' 不是有效的布尔值", dataType, defaultValue),
                "0"
            );
        }
        
        return null;
    }
    
    /**
     * 检查是否是特殊函数或表达式
     */
    private boolean isSpecialFunction(String defaultValue) {
        String upper = defaultValue.toUpperCase();
        return upper.contains("CURRENT_TIMESTAMP") ||
               upper.contains("NOW()") ||
               upper.contains("SYSDATE") ||
               upper.contains("CURRENT_DATE") ||
               upper.contains("CURRENT_TIME") ||
               upper.contains("AUTO_INCREMENT") ||
               upper.contains("CURRENT_USER") ||
               upper.contains("USER()") ||
               upper.contains("CONNECTION_ID()") ||
               upper.contains("UUID()") ||
               upper.contains("RAND()");
    }
    
    /**
     * 检查是否是有效的数值
     */
    private boolean isValidNumeric(String value) {
        if (value == null || value.trim().isEmpty()) {
            return false;
        }
        
        try {
            // 尝试解析为数值
            if (value.contains(".")) {
                Double.parseDouble(value);
            } else {
                Long.parseLong(value);
            }
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
}
