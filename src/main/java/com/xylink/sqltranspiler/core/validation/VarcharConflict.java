package com.xylink.sqltranspiler.core.validation;

/**
 * VARCHAR长度冲突信息
 */
public class VarcharConflict {
    
    private final String tableName;
    private final String columnName;
    private final int definedLength;
    private final int actualLength;
    private final String actualValue;
    private final String statementText;
    private final int statementLineNumber;
    
    public VarcharConflict(String tableName, String columnName, int definedLength, 
                          int actualLength, String actualValue, String statementText, 
                          int statementLineNumber) {
        this.tableName = tableName;
        this.columnName = columnName;
        this.definedLength = definedLength;
        this.actualLength = actualLength;
        this.actualValue = actualValue;
        this.statementText = statementText;
        this.statementLineNumber = statementLineNumber;
    }
    
    // Getter方法
    public String getTableName() {
        return tableName;
    }
    
    public String getColumnName() {
        return columnName;
    }
    
    public int getDefinedLength() {
        return definedLength;
    }
    
    public int getActualLength() {
        return actualLength;
    }
    
    public String getActualValue() {
        return actualValue;
    }
    
    public String getStatementText() {
        return statementText;
    }
    
    public int getStatementLineNumber() {
        return statementLineNumber;
    }
    
    /**
     * 获取冲突的详细描述
     */
    public String getDescription() {
        return String.format(
            "Table '%s', Column '%s': data length %d exceeds defined length %d. " +
            "Data: '%s' (line %d)",
            tableName, columnName, actualLength, definedLength, 
            truncateForDisplay(actualValue, 50), statementLineNumber
        );
    }
    
    /**
     * 获取简短描述
     */
    public String getShortDescription() {
        return String.format(
            "%s.%s: %d > %d", 
            tableName, columnName, actualLength, definedLength
        );
    }
    
    /**
     * 获取建议的解决方案
     */
    public String getSuggestion() {
        int suggestedLength = Math.max(definedLength * 2, actualLength + 50);
        return String.format(
            "Consider increasing VARCHAR length from %d to %d or more",
            definedLength, suggestedLength
        );
    }
    
    /**
     * 截断字符串用于显示
     */
    private String truncateForDisplay(String value, int maxLength) {
        if (value == null) {
            return "null";
        }
        if (value.length() <= maxLength) {
            return value;
        }
        return value.substring(0, maxLength - 3) + "...";
    }
    
    @Override
    public String toString() {
        return getDescription();
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        VarcharConflict that = (VarcharConflict) obj;
        return definedLength == that.definedLength &&
               actualLength == that.actualLength &&
               statementLineNumber == that.statementLineNumber &&
               tableName.equals(that.tableName) &&
               columnName.equals(that.columnName) &&
               actualValue.equals(that.actualValue);
    }
    
    @Override
    public int hashCode() {
        int result = tableName.hashCode();
        result = 31 * result + columnName.hashCode();
        result = 31 * result + definedLength;
        result = 31 * result + actualLength;
        result = 31 * result + actualValue.hashCode();
        result = 31 * result + statementLineNumber;
        return result;
    }
}
