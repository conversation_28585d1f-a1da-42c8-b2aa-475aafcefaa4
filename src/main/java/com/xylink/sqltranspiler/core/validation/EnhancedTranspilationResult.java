package com.xylink.sqltranspiler.core.validation;

import java.util.Collections;
import java.util.List;

/**
 * 增强的转换结果，包含VARCHAR验证信息
 */
public class EnhancedTranspilationResult {
    
    private final String translatedSql;
    private final ValidationSummary validationSummary;
    private final List<String> warnings;
    private final ConversionStatistics statistics;
    private final boolean success;
    private final String errorMessage;
    
    public EnhancedTranspilationResult(String translatedSql, ValidationSummary validationSummary, 
                                     List<String> warnings, ConversionStatistics statistics, 
                                     boolean success, String errorMessage) {
        this.translatedSql = translatedSql;
        this.validationSummary = validationSummary != null ? validationSummary : ValidationSummary.empty();
        this.warnings = warnings != null ? List.copyOf(warnings) : Collections.emptyList();
        this.statistics = statistics != null ? statistics : new ConversionStatistics();
        this.success = success;
        this.errorMessage = errorMessage;
    }
    
    /**
     * 创建成功的转换结果
     */
    public static EnhancedTranspilationResult success(String translatedSql, ValidationSummary validationSummary) {
        return new EnhancedTranspilationResult(translatedSql, validationSummary, 
                                             Collections.emptyList(), new ConversionStatistics(), 
                                             true, null);
    }
    
    /**
     * 创建成功的转换结果（带警告）
     */
    public static EnhancedTranspilationResult successWithWarnings(String translatedSql, 
                                                                ValidationSummary validationSummary, 
                                                                List<String> warnings) {
        return new EnhancedTranspilationResult(translatedSql, validationSummary, warnings, 
                                             new ConversionStatistics(), true, null);
    }
    
    /**
     * 创建失败的转换结果
     */
    public static EnhancedTranspilationResult failure(String errorMessage) {
        return new EnhancedTranspilationResult(null, ValidationSummary.empty(), 
                                             Collections.emptyList(), new ConversionStatistics(), 
                                             false, errorMessage);
    }
    
    /**
     * 创建失败的转换结果（带验证信息）
     */
    public static EnhancedTranspilationResult failure(String errorMessage, ValidationSummary validationSummary) {
        return new EnhancedTranspilationResult(null, validationSummary, 
                                             Collections.emptyList(), new ConversionStatistics(), 
                                             false, errorMessage);
    }
    
    // Getter方法
    public String getTranslatedSql() {
        return translatedSql;
    }
    
    public ValidationSummary getValidationSummary() {
        return validationSummary;
    }
    
    public List<String> getWarnings() {
        return warnings;
    }
    
    public ConversionStatistics getStatistics() {
        return statistics;
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    /**
     * 是否有警告
     */
    public boolean hasWarnings() {
        return !warnings.isEmpty();
    }
    
    /**
     * 是否有VARCHAR验证冲突
     */
    public boolean hasVarcharConflicts() {
        return validationSummary.hasConflicts();
    }
    
    /**
     * 获取详细报告
     */
    public String getDetailedReport() {
        StringBuilder sb = new StringBuilder();
        
        sb.append("=== Transpilation Result ===\n");
        sb.append("Success: ").append(success).append("\n");
        
        if (!success && errorMessage != null) {
            sb.append("Error: ").append(errorMessage).append("\n");
        }
        
        if (hasWarnings()) {
            sb.append("Warnings (").append(warnings.size()).append("):\n");
            for (String warning : warnings) {
                sb.append("  - ").append(warning).append("\n");
            }
        }
        
        sb.append("VARCHAR Validation: ").append(validationSummary.getBriefReport()).append("\n");
        
        if (hasVarcharConflicts()) {
            sb.append("VARCHAR Conflicts: ").append(validationSummary.getConflictCount()).append("\n");
        }
        
        sb.append("Statistics: ").append(statistics.toString()).append("\n");
        sb.append("============================");
        
        return sb.toString();
    }
    
    /**
     * 获取简要报告
     */
    public String getBriefReport() {
        if (!success) {
            return String.format("FAILED: %s", errorMessage != null ? errorMessage : "Unknown error");
        }
        
        StringBuilder sb = new StringBuilder("SUCCESS");
        
        if (hasWarnings()) {
            sb.append(" (").append(warnings.size()).append(" warnings)");
        }
        
        if (hasVarcharConflicts()) {
            sb.append(" (").append(validationSummary.getConflictCount()).append(" VARCHAR conflicts)");
        }
        
        return sb.toString();
    }
    
    @Override
    public String toString() {
        return getBriefReport();
    }
    
    /**
     * 转换统计信息
     */
    public static class ConversionStatistics {
        private final int totalStatements;
        private final int successfulConversions;
        private final int failedConversions;
        private final long conversionTimeMs;
        
        public ConversionStatistics() {
            this(0, 0, 0, 0);
        }
        
        public ConversionStatistics(int totalStatements, int successfulConversions, 
                                  int failedConversions, long conversionTimeMs) {
            this.totalStatements = totalStatements;
            this.successfulConversions = successfulConversions;
            this.failedConversions = failedConversions;
            this.conversionTimeMs = conversionTimeMs;
        }
        
        // Getter方法
        public int getTotalStatements() { return totalStatements; }
        public int getSuccessfulConversions() { return successfulConversions; }
        public int getFailedConversions() { return failedConversions; }
        public long getConversionTimeMs() { return conversionTimeMs; }
        
        @Override
        public String toString() {
            return String.format("ConversionStatistics{total=%d, success=%d, failed=%d, time=%dms}",
                totalStatements, successfulConversions, failedConversions, conversionTimeMs);
        }
    }
}
