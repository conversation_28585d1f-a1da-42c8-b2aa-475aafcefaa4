package com.xylink.sqltranspiler.core.validation;

import java.util.List;

/**
 * 数据长度冲突异常
 * 
 * 当VARCHAR字段的实际数据长度超过定义长度时抛出
 */
public class DataLengthConflictException extends RuntimeException {
    
    private final List<VarcharConflict> conflicts;
    private final String tableName;
    
    public DataLengthConflictException(String message) {
        super(message);
        this.conflicts = null;
        this.tableName = null;
    }
    
    public DataLengthConflictException(String message, Throwable cause) {
        super(message, cause);
        this.conflicts = null;
        this.tableName = null;
    }
    
    public DataLengthConflictException(String tableName, List<VarcharConflict> conflicts) {
        super(buildMessage(tableName, conflicts));
        this.tableName = tableName;
        this.conflicts = conflicts;
    }
    
    public DataLengthConflictException(String tableName, List<VarcharConflict> conflicts, Throwable cause) {
        super(buildMessage(tableName, conflicts), cause);
        this.tableName = tableName;
        this.conflicts = conflicts;
    }
    
    /**
     * 构建错误消息
     */
    private static String buildMessage(String tableName, List<VarcharConflict> conflicts) {
        if (conflicts == null || conflicts.isEmpty()) {
            return String.format("VARCHAR length conflicts detected in table '%s'", tableName);
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append(String.format("VARCHAR length conflicts detected in table '%s':\n", tableName));
        
        for (VarcharConflict conflict : conflicts) {
            sb.append("  - ").append(conflict.getShortDescription()).append("\n");
        }
        
        sb.append("\nSuggested solutions:\n");
        sb.append("1. Increase VARCHAR length in the CREATE TABLE statement\n");
        sb.append("2. Truncate the data to fit the defined length\n");
        sb.append("3. Use TEXT/CLOB type for unlimited length\n");
        
        return sb.toString();
    }
    
    /**
     * 获取冲突列表
     */
    public List<VarcharConflict> getConflicts() {
        return conflicts;
    }
    
    /**
     * 获取表名
     */
    public String getTableName() {
        return tableName;
    }
    
    /**
     * 是否有冲突详情
     */
    public boolean hasConflictDetails() {
        return conflicts != null && !conflicts.isEmpty();
    }
    
    /**
     * 获取冲突数量
     */
    public int getConflictCount() {
        return conflicts != null ? conflicts.size() : 0;
    }
}
