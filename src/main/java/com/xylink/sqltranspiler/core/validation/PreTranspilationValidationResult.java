package com.xylink.sqltranspiler.core.validation;

import java.util.Collections;
import java.util.List;

/**
 * 转换前验证结果
 * 
 * 封装SQL规范验证的结果，包括错误、警告和修复建议
 */
public class PreTranspilationValidationResult {
    
    /**
     * 验证状态枚举
     */
    public enum Status {
        SUCCESS("成功"),
        WARNING("警告"),
        FAILURE("失败");
        
        private final String description;
        
        Status(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    private final Status status;
    private final String message;
    private final List<String> errors;
    private final List<String> warnings;
    private final List<String> suggestions;
    
    private PreTranspilationValidationResult(Status status, String message, 
                                           List<String> errors, List<String> warnings, 
                                           List<String> suggestions) {
        this.status = status;
        this.message = message;
        this.errors = errors != null ? List.copyOf(errors) : Collections.emptyList();
        this.warnings = warnings != null ? List.copyOf(warnings) : Collections.emptyList();
        this.suggestions = suggestions != null ? List.copyOf(suggestions) : Collections.emptyList();
    }
    
    /**
     * 创建成功结果
     */
    public static PreTranspilationValidationResult success(String message) {
        return new PreTranspilationValidationResult(Status.SUCCESS, message, 
                                                   Collections.emptyList(), 
                                                   Collections.emptyList(), 
                                                   Collections.emptyList());
    }
    
    /**
     * 创建警告结果
     */
    public static PreTranspilationValidationResult warning(String message, List<String> warnings) {
        return new PreTranspilationValidationResult(Status.WARNING, message, 
                                                   Collections.emptyList(), 
                                                   warnings, 
                                                   Collections.emptyList());
    }
    
    /**
     * 创建警告结果（带建议）
     */
    public static PreTranspilationValidationResult warning(String message, List<String> warnings, 
                                                         List<String> suggestions) {
        return new PreTranspilationValidationResult(Status.WARNING, message, 
                                                   Collections.emptyList(), 
                                                   warnings, 
                                                   suggestions);
    }
    
    /**
     * 创建失败结果
     */
    public static PreTranspilationValidationResult failure(String message, List<String> errors) {
        return new PreTranspilationValidationResult(Status.FAILURE, message, 
                                                   errors, 
                                                   Collections.emptyList(), 
                                                   Collections.emptyList());
    }
    
    /**
     * 创建失败结果（完整版本）
     */
    public static PreTranspilationValidationResult failure(String message, List<String> errors, 
                                                         List<String> warnings, List<String> suggestions) {
        return new PreTranspilationValidationResult(Status.FAILURE, message, 
                                                   errors, warnings, suggestions);
    }
    
    // Getter方法
    public Status getStatus() {
        return status;
    }
    
    public String getMessage() {
        return message;
    }
    
    public List<String> getErrors() {
        return errors;
    }
    
    public List<String> getWarnings() {
        return warnings;
    }
    
    public List<String> getSuggestions() {
        return suggestions;
    }
    
    /**
     * 是否验证成功
     */
    public boolean isSuccess() {
        return status == Status.SUCCESS;
    }
    
    /**
     * 是否有警告
     */
    public boolean hasWarnings() {
        return !warnings.isEmpty();
    }
    
    /**
     * 是否有错误
     */
    public boolean hasErrors() {
        return !errors.isEmpty();
    }
    
    /**
     * 是否有修复建议
     */
    public boolean hasSuggestions() {
        return !suggestions.isEmpty();
    }
    
    /**
     * 是否验证失败
     */
    public boolean isFailure() {
        return status == Status.FAILURE;
    }
    
    /**
     * 获取问题总数
     */
    public int getTotalIssueCount() {
        return errors.size() + warnings.size();
    }
    
    /**
     * 获取简要描述
     */
    public String getBriefDescription() {
        switch (status) {
            case SUCCESS:
                return "验证通过";
            case WARNING:
                return String.format("验证通过但有 %d 个警告", warnings.size());
            case FAILURE:
                return String.format("验证失败：%d 个错误，%d 个警告", errors.size(), warnings.size());
            default:
                return "未知状态";
        }
    }
    
    /**
     * 获取详细描述
     */
    public String getDetailedDescription() {
        StringBuilder sb = new StringBuilder();
        sb.append("验证状态: ").append(status.getDescription()).append("\n");
        sb.append("消息: ").append(message).append("\n");
        
        if (hasErrors()) {
            sb.append("\n错误列表:\n");
            for (int i = 0; i < errors.size(); i++) {
                sb.append(String.format("  %d. %s\n", i + 1, errors.get(i)));
            }
        }
        
        if (hasWarnings()) {
            sb.append("\n警告列表:\n");
            for (int i = 0; i < warnings.size(); i++) {
                sb.append(String.format("  %d. %s\n", i + 1, warnings.get(i)));
            }
        }
        
        if (hasSuggestions()) {
            sb.append("\n修复建议:\n");
            for (int i = 0; i < suggestions.size(); i++) {
                sb.append(String.format("  %d. %s\n", i + 1, suggestions.get(i)));
            }
        }
        
        return sb.toString();
    }
    
    /**
     * 获取格式化的控制台输出
     */
    public String getConsoleOutput() {
        StringBuilder sb = new StringBuilder();
        
        // 状态行
        String statusLine = String.format("[%s] %s", status.name(), message);
        sb.append(statusLine).append("\n");
        
        // 错误信息
        if (hasErrors()) {
            sb.append("\n❌ 错误 (").append(errors.size()).append("):\n");
            for (String error : errors) {
                sb.append("   ").append(error).append("\n");
            }
        }
        
        // 警告信息
        if (hasWarnings()) {
            sb.append("\n⚠️  警告 (").append(warnings.size()).append("):\n");
            for (String warning : warnings) {
                sb.append("   ").append(warning).append("\n");
            }
        }
        
        // 修复建议
        if (hasSuggestions()) {
            sb.append("\n💡 修复建议 (").append(suggestions.size()).append("):\n");
            for (String suggestion : suggestions) {
                sb.append("   ").append(suggestion).append("\n");
            }
        }
        
        return sb.toString();
    }
    
    @Override
    public String toString() {
        return getBriefDescription();
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        PreTranspilationValidationResult that = (PreTranspilationValidationResult) obj;
        return status == that.status &&
               message.equals(that.message) &&
               errors.equals(that.errors) &&
               warnings.equals(that.warnings) &&
               suggestions.equals(that.suggestions);
    }
    
    @Override
    public int hashCode() {
        int result = status.hashCode();
        result = 31 * result + message.hashCode();
        result = 31 * result + errors.hashCode();
        result = 31 * result + warnings.hashCode();
        result = 31 * result + suggestions.hashCode();
        return result;
    }
}
