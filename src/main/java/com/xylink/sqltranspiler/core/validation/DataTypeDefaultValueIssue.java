package com.xylink.sqltranspiler.core.validation;

/**
 * 数据类型与默认值不匹配的问题
 */
public class DataTypeDefaultValueIssue {
    
    /**
     * 问题类型枚举
     */
    public enum IssueType {
        /**
         * 数值类型使用了引号包围的默认值
         */
        QUOTED_NUMERIC_DEFAULT,
        
        /**
         * 无效的数值默认值
         */
        INVALID_NUMERIC_DEFAULT,
        
        /**
         * 字符串类型未使用引号包围的默认值
         */
        UNQUOTED_STRING_DEFAULT,
        
        /**
         * 无效的日期时间默认值
         */
        INVALID_DATETIME_DEFAULT,
        
        /**
         * 无效的布尔默认值
         */
        INVALID_BOOLEAN_DEFAULT
    }
    
    private final String tableName;
    private final String columnName;
    private final String dataType;
    private final String originalDefaultValue;
    private final IssueType issueType;
    private final String description;
    private final String suggestedFix;
    private final int lineNumber;

    public DataTypeDefaultValueIssue(String tableName, String columnName, String dataType,
                                   String originalDefaultValue, IssueType issueType,
                                   String description, String suggestedFix) {
        this(tableName, columnName, dataType, originalDefaultValue, issueType, description, suggestedFix, 0);
    }

    public DataTypeDefaultValueIssue(String tableName, String columnName, String dataType,
                                   String originalDefaultValue, IssueType issueType,
                                   String description, String suggestedFix, int lineNumber) {
        this.tableName = tableName;
        this.columnName = columnName;
        this.dataType = dataType;
        this.originalDefaultValue = originalDefaultValue;
        this.issueType = issueType;
        this.description = description;
        this.suggestedFix = suggestedFix;
        this.lineNumber = lineNumber;
    }
    
    public String getTableName() {
        return tableName;
    }
    
    public String getColumnName() {
        return columnName;
    }
    
    public String getDataType() {
        return dataType;
    }
    
    public String getOriginalDefaultValue() {
        return originalDefaultValue;
    }
    
    public IssueType getIssueType() {
        return issueType;
    }
    
    public String getDescription() {
        return description;
    }
    
    public String getSuggestedFix() {
        return suggestedFix;
    }

    public int getLineNumber() {
        return lineNumber;
    }

    /**
     * 获取建议的值（不包含DEFAULT关键字）
     */
    public String getSuggestedValue() {
        return suggestedFix;
    }

    /**
     * 获取消息（兼容PreTranspilationValidator）
     */
    public String getMessage() {
        return description;
    }
    
    /**
     * 获取问题的严重程度
     */
    public Severity getSeverity() {
        switch (issueType) {
            case QUOTED_NUMERIC_DEFAULT:
                return Severity.ERROR;
            case INVALID_NUMERIC_DEFAULT:
            case INVALID_DATETIME_DEFAULT:
            case INVALID_BOOLEAN_DEFAULT:
                return Severity.ERROR;
            case UNQUOTED_STRING_DEFAULT:
                return Severity.WARNING;
            default:
                return Severity.INFO;
        }
    }
    
    /**
     * 严重程度枚举
     */
    public enum Severity {
        ERROR, WARNING, INFO
    }
    
    @Override
    public String toString() {
        if (lineNumber > 0) {
            return String.format("[%s] Line %d - 表 %s.%s (%s): %s | 原值: %s | 建议: %s",
                getSeverity(),
                lineNumber,
                tableName,
                columnName,
                dataType,
                description,
                originalDefaultValue,
                suggestedFix
            );
        } else {
            return String.format("[%s] 表 %s.%s (%s): %s | 原值: %s | 建议: %s",
                getSeverity(),
                tableName,
                columnName,
                dataType,
                description,
                originalDefaultValue,
                suggestedFix
            );
        }
    }
    
    /**
     * 获取格式化的报告信息
     */
    public String getFormattedReport() {
        StringBuilder sb = new StringBuilder();
        sb.append("数据类型默认值问题:\n");
        sb.append("  表名: ").append(tableName).append("\n");
        sb.append("  列名: ").append(columnName).append("\n");
        sb.append("  数据类型: ").append(dataType).append("\n");
        sb.append("  原始默认值: ").append(originalDefaultValue).append("\n");
        sb.append("  问题类型: ").append(issueType).append("\n");
        sb.append("  问题描述: ").append(description).append("\n");
        sb.append("  建议修复: ").append(suggestedFix).append("\n");
        sb.append("  严重程度: ").append(getSeverity()).append("\n");
        return sb.toString();
    }
    
    /**
     * 检查是否是关键错误（需要立即修复）
     */
    public boolean isCritical() {
        return getSeverity() == Severity.ERROR;
    }
    
    /**
     * 获取修复后的SQL片段
     */
    public String getFixedDefaultClause() {
        if (suggestedFix == null || suggestedFix.trim().isEmpty()) {
            return "";
        }
        
        if ("NULL".equalsIgnoreCase(suggestedFix)) {
            return "DEFAULT NULL";
        } else {
            return "DEFAULT " + suggestedFix;
        }
    }
}
