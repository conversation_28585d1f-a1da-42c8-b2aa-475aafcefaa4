package com.xylink.sqltranspiler.core.validation;

import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 严格的SQL验证器 - 基于官方文档规范
 * 
 * 严格按照以下官方文档进行验证：
 * - MySQL: https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦: https://eco.dameng.com/document/dm/zh-cn/pm/
 * - 金仓: https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
 * - 神通: 基于神通数据库SQL参考手册
 */
public class StrictSqlValidator {
    
    private final Properties config;
    private final Map<String, Set<String>> typesByDatabase;
    private final Pattern createTablePattern;
    private final Pattern alterTableAddColumnPattern;
    private final Pattern alterTableModifyColumnPattern;
    private final Pattern alterTableChangeColumnPattern;
    private final Pattern createIndexPattern;
    private final Pattern createViewPattern;
    private final Pattern createProcedurePattern;
    private final Pattern createFunctionPattern;
    private final Pattern createTriggerPattern;

    public StrictSqlValidator() {
        this.config = loadStrictConfig();
        this.typesByDatabase = initializeTypesByDatabase();

        // 【修复StackOverflowError】使用更安全的正则表达式，避免过度回溯
        // 限制匹配长度，避免在大文件中导致栈溢出
        this.createTablePattern = Pattern.compile(
            "CREATE\\s+TABLE\\s+(?:IF\\s+NOT\\s+EXISTS\\s+)?([^\\s(]{1,200})\\s*\\(([^;]{1,5000})\\)\\s*(?:ENGINE|DEFAULT|COMMENT|;|$)",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
        );
        this.alterTableAddColumnPattern = Pattern.compile(
            "ALTER\\s+TABLE\\s+([^\\s]{1,200})\\s+ADD\\s+(?:COLUMN\\s+)?(.{1,1000})(?:;|$)",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
        );
        this.alterTableModifyColumnPattern = Pattern.compile(
            "ALTER\\s+TABLE\\s+([^\\s]{1,200})\\s+MODIFY\\s+(?:COLUMN\\s+)?(.{1,1000})(?:;|$)",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
        );
        this.alterTableChangeColumnPattern = Pattern.compile(
            "ALTER\\s+TABLE\\s+([^\\s]{1,200})\\s+CHANGE\\s+(?:COLUMN\\s+)?([^\\s]{1,200})\\s+(.{1,1000})(?:;|$)",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
        );
        this.createIndexPattern = Pattern.compile(
            "CREATE\\s+(?:UNIQUE\\s+)?INDEX\\s+([^\\s]{1,200})\\s+ON\\s+([^\\s]{1,200})\\s*\\((.{1,1000})\\)(?:;|$)",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
        );
        this.createViewPattern = Pattern.compile(
            "CREATE\\s+(?:OR\\s+REPLACE\\s+)?VIEW\\s+([^\\s]{1,200})(?:\\s*\\((.{1,1000})\\))?\\s+AS\\s+(.{1,5000})(?:;|$)",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
        );
        this.createProcedurePattern = Pattern.compile(
            "CREATE\\s+(?:DEFINER\\s*=\\s*[^\\s]+\\s+)?PROCEDURE\\s+([^\\s(]{1,200})\\s*\\(([^)]{0,2000})\\)(.{1,10000})(?:;|$)",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
        );
        this.createFunctionPattern = Pattern.compile(
            "CREATE\\s+(?:DEFINER\\s*=\\s*[^\\s]+\\s+)?FUNCTION\\s+([^\\s(]{1,200})\\s*\\(([^)]{0,2000})\\)\\s+RETURNS\\s+([^\\s]{1,200})(.{1,10000})(?:;|$)",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
        );
        this.createTriggerPattern = Pattern.compile(
            "CREATE\\s+(?:DEFINER\\s*=\\s*[^\\s]+\\s+)?TRIGGER\\s+([^\\s]{1,200})\\s+(BEFORE|AFTER)\\s+(INSERT|UPDATE|DELETE)\\s+ON\\s+([^\\s]{1,200})(.{1,10000})(?:;|$)",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
        );
    }
    
    /**
     * 加载严格验证配置
     */
    private Properties loadStrictConfig() {
        Properties props = new Properties();
        try (InputStream is = getClass().getClassLoader().getResourceAsStream("strict-validation-rules.properties")) {
            if (is != null) {
                // 使用UTF-8编码读取配置文件，避免中文乱码
                try (java.io.InputStreamReader reader = new java.io.InputStreamReader(is, java.nio.charset.StandardCharsets.UTF_8)) {
                    props.load(reader);
                }
            }
        } catch (IOException e) {
            throw new RuntimeException("无法加载严格验证配置文件", e);
        }
        return props;
    }
    
    /**
     * 初始化各数据库的类型映射
     */
    private Map<String, Set<String>> initializeTypesByDatabase() {
        Map<String, Set<String>> types = new HashMap<>();
        
        // MySQL类型
        Set<String> mysqlTypes = new HashSet<>();
        addTypesToSet(mysqlTypes, "validation.mysql.types.numeric");
        addTypesToSet(mysqlTypes, "validation.mysql.types.string");
        addTypesToSet(mysqlTypes, "validation.mysql.types.datetime");
        addTypesToSet(mysqlTypes, "validation.mysql.types.boolean");
        addTypesToSet(mysqlTypes, "validation.mysql.types.json");
        addTypesToSet(mysqlTypes, "validation.mysql.types.enum");
        addTypesToSet(mysqlTypes, "validation.mysql.types.geometry");
        types.put("mysql", mysqlTypes);
        
        // 达梦类型
        Set<String> damengTypes = new HashSet<>();
        addTypesToSet(damengTypes, "validation.dameng.types.numeric");
        addTypesToSet(damengTypes, "validation.dameng.types.string");
        addTypesToSet(damengTypes, "validation.dameng.types.datetime");
        addTypesToSet(damengTypes, "validation.dameng.types.boolean");
        addTypesToSet(damengTypes, "validation.dameng.types.json");
        types.put("dameng", damengTypes);
        
        // 金仓类型
        Set<String> kingbaseTypes = new HashSet<>();
        addTypesToSet(kingbaseTypes, "validation.kingbase.types.numeric");
        addTypesToSet(kingbaseTypes, "validation.kingbase.types.string");
        addTypesToSet(kingbaseTypes, "validation.kingbase.types.datetime");
        addTypesToSet(kingbaseTypes, "validation.kingbase.types.boolean");
        addTypesToSet(kingbaseTypes, "validation.kingbase.types.serial");
        addTypesToSet(kingbaseTypes, "validation.kingbase.types.json");
        types.put("kingbase", kingbaseTypes);
        
        // 神通类型
        Set<String> shentongTypes = new HashSet<>();
        addTypesToSet(shentongTypes, "validation.shentong.types.numeric");
        addTypesToSet(shentongTypes, "validation.shentong.types.string");
        addTypesToSet(shentongTypes, "validation.shentong.types.bit");
        addTypesToSet(shentongTypes, "validation.shentong.types.binary");
        addTypesToSet(shentongTypes, "validation.shentong.types.datetime");
        types.put("shentong", shentongTypes);
        
        return types;
    }
    
    /**
     * 添加类型到集合
     */
    private void addTypesToSet(Set<String> typeSet, String configKey) {
        String typesStr = config.getProperty(configKey, "");
        if (!typesStr.isEmpty()) {
            String[] types = typesStr.split(",");
            for (String type : types) {
                typeSet.add(type.trim().toUpperCase());
            }
        }
    }
    
    /**
     * 验证SQL语句
     */
    public StrictValidationResult validate(String sql, String sourceDatabase, String targetDatabase) {
        StrictValidationResult result = new StrictValidationResult();

        if (sql == null || sql.trim().isEmpty()) {
            return result;
        }

        // 验证CREATE TABLE语句
        validateCreateTableStatements(sql, sourceDatabase, targetDatabase, result);

        // 验证ALTER TABLE语句
        validateAlterTableStatements(sql, sourceDatabase, targetDatabase, result);

        // 验证ALTER TABLE MODIFY语句
        validateAlterTableModifyStatements(sql, sourceDatabase, targetDatabase, result);

        // 验证ALTER TABLE CHANGE语句
        validateAlterTableChangeStatements(sql, sourceDatabase, targetDatabase, result);

        // 验证CREATE INDEX语句
        validateCreateIndexStatements(sql, sourceDatabase, targetDatabase, result);

        // 验证CREATE VIEW语句
        validateCreateViewStatements(sql, sourceDatabase, targetDatabase, result);

        // 验证CREATE PROCEDURE语句
        validateCreateProcedureStatements(sql, sourceDatabase, targetDatabase, result);

        // 验证CREATE FUNCTION语句
        validateCreateFunctionStatements(sql, sourceDatabase, targetDatabase, result);

        // 验证CREATE TRIGGER语句
        validateCreateTriggerStatements(sql, sourceDatabase, targetDatabase, result);

        // 验证字符集和排序规则
        validateCharsetAndCollation(sql, sourceDatabase, targetDatabase, result);

        // 验证JSON相关限制
        validateJsonLimits(sql, sourceDatabase, targetDatabase, result);

        // 验证GEOMETRY相关限制
        validateGeometryLimits(sql, sourceDatabase, targetDatabase, result);

        // 验证保留字使用
        validateReservedWords(sql, sourceDatabase, targetDatabase, result);

        // 验证窗口函数语法 - 基于MySQL 8.4官方文档
        validateWindowFunctions(sql, sourceDatabase, targetDatabase, result);

        // 验证CTE递归深度 - 基于MySQL 8.4官方文档
        validateCTERecursion(sql, sourceDatabase, targetDatabase, result);

        // 验证达梦数据库IDENTITY列处理 - 基于达梦官方文档
        validateDamengIdentityInsert(sql, sourceDatabase, targetDatabase, result);

        // 验证神通数据库ROWNUM使用 - 基于神通官方文档
        validateShentongRownum(sql, sourceDatabase, targetDatabase, result);

        // 验证SQL注入安全 - 基于安全最佳实践
        validateSQLSecurity(sql, sourceDatabase, targetDatabase, result);

        // 验证性能优化 - 基于性能最佳实践
        validatePerformanceIssues(sql, sourceDatabase, targetDatabase, result);

        // 验证兼容性问题 - 基于跨数据库兼容性
        validateCompatibilityIssues(sql, sourceDatabase, targetDatabase, result);

        return result;
    }

    /**
     * 验证达梦数据库IDENTITY列INSERT语句 - 基于达梦官方文档
     * 根据达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
     * 当INSERT语句中包含IDENTITY列的显式值时，需要SET IDENTITY_INSERT ON
     */
    private void validateDamengIdentityInsert(String sql, String sourceDb, String targetDb, StrictValidationResult result) {
        if (!"dameng".equalsIgnoreCase(targetDb)) {
            return;
        }

        // 检查INSERT语句中是否包含IDENTITY列的显式值
        Pattern insertPattern = Pattern.compile(
            "INSERT\\s+INTO\\s+([^\\s(]+)\\s*\\(([^)]+)\\)\\s*VALUES",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = insertPattern.matcher(sql);
        while (matcher.find()) {
            String tableName = matcher.group(1).replaceAll("[`\"']", "");
            String columnList = matcher.group(2);

            // 检查是否包含可能的IDENTITY列（通常是id列）
            if (columnList.toLowerCase().contains("id")) {
                result.addSuggestion(String.format(
                    "达梦数据库IDENTITY列处理: 表 %s 的INSERT语句包含ID列，" +
                    "根据达梦官方文档，插入IDENTITY列显式值时需要添加 SET IDENTITY_INSERT %s ON; 语句。" +
                    "参考：https://eco.dameng.com/document/dm/zh-cn/sql-dev/",
                    tableName, tableName
                ));
            }
        }

        // 检查没有列列表的INSERT语句（可能包含IDENTITY列）
        Pattern insertAllPattern = Pattern.compile(
            "INSERT\\s+INTO\\s+([^\\s(]+)\\s+VALUES",
            Pattern.CASE_INSENSITIVE
        );

        Matcher allMatcher = insertAllPattern.matcher(sql);
        while (allMatcher.find()) {
            String tableName = allMatcher.group(1).replaceAll("[`\"']", "");
            result.addSuggestion(String.format(
                "达梦数据库IDENTITY列处理: 表 %s 的INSERT语句未指定列列表，" +
                "如果表包含IDENTITY列，根据达梦官方文档需要添加 SET IDENTITY_INSERT %s ON; 语句。" +
                "参考：https://eco.dameng.com/document/dm/zh-cn/sql-dev/",
                tableName, tableName
            ));
        }
    }

    /**
     * 验证神通数据库ROWNUM使用 - 基于神通官方文档
     * 根据神通数据库官方文档，ROWNUM不能在ORDER BY子句之后使用
     */
    private void validateShentongRownum(String sql, String sourceDb, String targetDb, StrictValidationResult result) {
        if (!"shentong".equalsIgnoreCase(targetDb)) {
            return;
        }

        // 检查ORDER BY之后使用ROWNUM的情况
        Pattern orderByRownumPattern = Pattern.compile(
            "ORDER\\s+BY\\s+[^)]*?\\)\\s*[^)]*?ROWNUM",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
        );

        if (orderByRownumPattern.matcher(sql).find()) {
            result.addError(
                "神通数据库语法错误: ROWNUM不能在ORDER BY子句之后使用。" +
                "根据神通官方文档，应该使用子查询先排序，再在外层查询中使用ROWNUM限制行数。"
            );
            result.addSuggestion(
                "修复建议: 使用子查询结构 SELECT * FROM (SELECT ... ORDER BY ...) WHERE ROWNUM <= N"
            );
        }

        // 检查简单的ORDER BY ... ROWNUM模式
        Pattern simpleOrderByRownumPattern = Pattern.compile(
            "ORDER\\s+BY\\s+[^\\s]+.*?ROWNUM",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
        );

        if (simpleOrderByRownumPattern.matcher(sql).find()) {
            result.addError(
                "神通数据库语法错误: ROWNUM不能在ORDER BY子句之后使用。" +
                "根据神通官方文档，ROWNUM是在排序之前分配的，因此ORDER BY ROWNUM没有意义。"
            );
        }
    }

    /**
     * 验证CREATE TABLE语句
     */
    private void validateCreateTableStatements(String sql, String sourceDb, String targetDb, StrictValidationResult result) {
        // 使用更智能的方法来解析CREATE TABLE语句，正确处理嵌套括号
        Pattern tableStartPattern = Pattern.compile(
            "CREATE\\s+TABLE\\s+(?:IF\\s+NOT\\s+EXISTS\\s+)?([^\\s(]+)\\s*\\(",
            Pattern.CASE_INSENSITIVE
        );

        Matcher startMatcher = tableStartPattern.matcher(sql);

        while (startMatcher.find()) {
            String tableName = startMatcher.group(1);
            int startPos = startMatcher.end() - 1; // 包含开始的 '('

            // 找到匹配的结束括号
            int endPos = findMatchingClosingParen(sql, startPos);
            if (endPos == -1) {
                continue; // 没有找到匹配的括号，跳过
            }

            String columnDefinitions = sql.substring(startPos + 1, endPos); // 不包含括号
            validateTableDefinition(tableName, columnDefinitions, sourceDb, targetDb, result);
        }
    }

    /**
     * 找到匹配的结束括号位置
     */
    private int findMatchingClosingParen(String sql, int startPos) {
        int parenCount = 0;
        boolean inQuotes = false;
        char quoteChar = 0;

        for (int i = startPos; i < sql.length(); i++) {
            char c = sql.charAt(i);

            // 处理引号
            if ((c == '\'' || c == '"' || c == '`') && !inQuotes) {
                inQuotes = true;
                quoteChar = c;
            } else if (c == quoteChar && inQuotes) {
                inQuotes = false;
                quoteChar = 0;
            }

            // 只在非引号内处理括号
            if (!inQuotes) {
                if (c == '(') {
                    parenCount++;
                } else if (c == ')') {
                    parenCount--;
                    if (parenCount == 0) {
                        return i; // 找到匹配的结束括号
                    }
                }
            }
        }

        return -1; // 没有找到匹配的括号
    }

    /**
     * 验证ALTER TABLE语句
     */
    private void validateAlterTableStatements(String sql, String sourceDb, String targetDb, StrictValidationResult result) {
        Matcher alterMatcher = alterTableAddColumnPattern.matcher(sql);

        while (alterMatcher.find()) {
            String tableName = alterMatcher.group(1);
            String columnDefinition = alterMatcher.group(2);

            // 移除表名前缀（如果存在）
            if (tableName.contains(".")) {
                tableName = tableName.substring(tableName.lastIndexOf(".") + 1);
            }

            validateColumnDefinition(tableName, columnDefinition.trim(), sourceDb, targetDb, result);
        }
    }
    
    /**
     * 验证表定义 - 改进的列分割逻辑
     */
    private void validateTableDefinition(String tableName, String columnDefs, String sourceDb, String targetDb, StrictValidationResult result) {
        // 改进的列分割逻辑 - 考虑括号内的逗号
        String[] columns = splitColumnDefinitions(columnDefs);

        // 验证表列数限制
        validateTableColumnCount(tableName, columns, sourceDb, targetDb, result);

        for (String column : columns) {
            column = column.trim();
            if (column.isEmpty()) {
                continue;
            }

            // 跳过约束定义
            if (column.toUpperCase().matches("^(PRIMARY\\s+KEY|KEY|INDEX|UNIQUE|CONSTRAINT|FOREIGN\\s+KEY).*")) {
                continue;
            }

            validateColumnDefinition(tableName, column, sourceDb, targetDb, result);
        }

        // 验证目标数据库特有的表级别限制
        validateTableLevelLimits(tableName, sourceDb, targetDb, result);
    }

    /**
     * 智能分割列定义 - 考虑括号内的逗号
     */
    private String[] splitColumnDefinitions(String columnDefs) {
        java.util.List<String> columns = new java.util.ArrayList<>();
        StringBuilder currentColumn = new StringBuilder();
        int parenthesesLevel = 0;
        boolean inQuotes = false;
        char quoteChar = 0;

        for (int i = 0; i < columnDefs.length(); i++) {
            char c = columnDefs.charAt(i);

            // 处理引号
            if ((c == '\'' || c == '"' || c == '`') && !inQuotes) {
                inQuotes = true;
                quoteChar = c;
                currentColumn.append(c);
            } else if (c == quoteChar && inQuotes) {
                inQuotes = false;
                quoteChar = 0;
                currentColumn.append(c);
            } else if (inQuotes) {
                currentColumn.append(c);
            } else if (c == '(') {
                parenthesesLevel++;
                currentColumn.append(c);
            } else if (c == ')') {
                parenthesesLevel--;
                currentColumn.append(c);
            } else if (c == ',' && parenthesesLevel == 0) {
                // 只有在括号外的逗号才分割
                columns.add(currentColumn.toString().trim());
                currentColumn = new StringBuilder();
            } else {
                currentColumn.append(c);
            }
        }

        // 添加最后一列
        if (currentColumn.length() > 0) {
            columns.add(currentColumn.toString().trim());
        }

        return columns.toArray(new String[0]);
    }
    
    /**
     * 验证列定义 - 改进的解析逻辑
     */
    private void validateColumnDefinition(String tableName, String columnDef, String sourceDb, String targetDb, StrictValidationResult result) {
        // 改进的列定义解析 - 更精确地处理复杂的列定义
        String cleanColumnDef = columnDef.trim();

        // 跳过约束定义
        if (cleanColumnDef.toUpperCase().matches("^(PRIMARY\\s+KEY|KEY|INDEX|UNIQUE|CONSTRAINT|FOREIGN\\s+KEY).*")) {
            return;
        }

        // 跳过表级约束（如 PRIMARY KEY (id)）
        if (cleanColumnDef.toUpperCase().matches("^PRIMARY\\s+KEY\\s*\\(.*\\).*")) {
            return;
        }

        // 使用更精确的正则表达式解析列定义
        Pattern improvedColumnPattern = Pattern.compile(
            "^([`\"']?\\w+[`\"']?)\\s+(\\w+(?:\\([^)]*\\))?)\\s*(.*?)$",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
        );

        Matcher columnMatcher = improvedColumnPattern.matcher(cleanColumnDef);

        if (!columnMatcher.find()) {
            // 如果无法解析，记录调试信息
            System.out.println("DEBUG: Failed to parse column definition: " + cleanColumnDef);
            result.addStatistic("unparsed_columns",
                result.getStatistics().getOrDefault("unparsed_columns", 0) + " " + cleanColumnDef);
            return;
        }

        String columnName = columnMatcher.group(1).replaceAll("[`\"']", ""); // 移除引号
        String dataType = columnMatcher.group(2);
        String attributes = columnMatcher.group(3);

        // 组合完整的数据类型和修饰符
        String dataTypeAndModifiers = dataType + (attributes != null && !attributes.trim().isEmpty() ? " " + attributes : "");

        // 提取基本数据类型
        String baseDataType = extractDataTypeFromDefinition(dataType);

        // 验证数据类型
        validateDataType(tableName, columnName, baseDataType, sourceDb, targetDb, result);

        // 验证列名是否为保留字
        validateColumnName(tableName, columnName, sourceDb, targetDb, result);

        // 验证数据类型长度限制
        validateDataTypeLength(tableName, columnName, dataType, sourceDb, targetDb, result);

        // 验证默认值和其他属性
        if (attributes != null) {
            validateDefaultValue(tableName, columnName, baseDataType, attributes, sourceDb, targetDb, result);
            validateOnUpdateClause(tableName, columnName, attributes, sourceDb, targetDb, result);
            validateAutoIncrement(tableName, columnName, baseDataType, attributes, sourceDb, targetDb, result);
            validateConstraintCombinations(tableName, columnName, baseDataType, attributes, sourceDb, targetDb, result);
        }

        // 验证数据类型长度限制
        validateDataTypeLengthLimits(tableName, columnName, baseDataType, sourceDb, targetDb, result);

        // 验证标识符模式
        validateIdentifierPattern(tableName, columnName, sourceDb, targetDb, result);

        // 检查数据类型定义中的AUTO_INCREMENT
        if (dataTypeAndModifiers.toUpperCase().contains("AUTO_INCREMENT")) {
            validateAutoIncrement(tableName, columnName, baseDataType, dataTypeAndModifiers, sourceDb, targetDb, result);
        }

        // 验证BLOB/TEXT长度限制
        validateBlobTextLimits(tableName, columnName, dataType, sourceDb, targetDb, result);

        // 验证神通数据库特有限制
        if ("shentong".equalsIgnoreCase(targetDb)) {
            validateShentongSpecificLimits(tableName, columnName, baseDataType, dataTypeAndModifiers, sourceDb, targetDb, result);
        }

        // 验证金仓数据库特有限制
        if ("kingbase".equalsIgnoreCase(targetDb)) {
            validateKingbaseSpecificLimits(tableName, columnName, baseDataType, dataTypeAndModifiers, sourceDb, targetDb, result);
        }
    }

    /**
     * 从完整的数据类型定义中提取基本类型
     */
    private String extractDataTypeFromDefinition(String dataTypeAndModifiers) {
        // 移除修饰符，提取完整的数据类型（包括长度）
        String cleaned = dataTypeAndModifiers.trim().toUpperCase();

        // 移除常见修饰符
        cleaned = cleaned.replaceAll("\\s+(UNSIGNED|ZEROFILL|NOT\\s+NULL|NULL|AUTO_INCREMENT).*", "");

        // 提取完整的类型名（包含长度）
        Pattern typePattern = Pattern.compile("^(\\w+(?:\\([^)]*\\))?)");
        Matcher typeMatcher = typePattern.matcher(cleaned);

        if (typeMatcher.find()) {
            return typeMatcher.group(1);
        }

        return cleaned.split("\\s+")[0];
    }
    
    /**
     * 验证列名是否为保留字 - 基于官方文档
     */
    private void validateColumnName(String tableName, String columnName, String sourceDb, String targetDb, StrictValidationResult result) {
        // 验证源数据库保留字
        String sourceReservedWords = config.getProperty("validation." + sourceDb.toLowerCase() + ".reserved_words", "");
        if (containsReservedWord(sourceReservedWords, columnName)) {
            result.addWarning(String.format(
                "表 %s 列名 %s: 在%s数据库中是保留字，建议使用引号包围或重命名",
                tableName, columnName, sourceDb
            ));
        }

        // 验证目标数据库保留字
        String targetReservedWords = config.getProperty("validation." + targetDb.toLowerCase() + ".reserved_words", "");
        if (containsReservedWord(targetReservedWords, columnName)) {
            result.addWarning(String.format(
                "表 %s 列名 %s: 在%s数据库中是保留字，转换时需要特殊处理",
                tableName, columnName, targetDb
            ));

            // 提供引号处理建议
            String quoteChar = config.getProperty("validation." + targetDb.toLowerCase() + ".reserved_quote_char", "\"");
            result.addSuggestion(String.format(
                "表 %s 列名 %s: 建议在%s中使用 %s%s%s",
                tableName, columnName, targetDb, quoteChar, columnName, quoteChar
            ));
        }
    }

    /**
     * 验证数据类型长度限制 - 基于官方文档
     */
    private void validateDataTypeLength(String tableName, String columnName, String dataType, String sourceDb, String targetDb, StrictValidationResult result) {
        String upperDataType = dataType.toUpperCase();

        // 提取长度信息
        if (upperDataType.startsWith("VARCHAR")) {
            validateVarcharLength(tableName, columnName, dataType, sourceDb, targetDb, result);
        } else if (upperDataType.startsWith("CHAR")) {
            validateCharLength(tableName, columnName, dataType, sourceDb, targetDb, result);
        } else if (upperDataType.startsWith("DECIMAL") || upperDataType.startsWith("NUMERIC")) {
            validateNumericPrecision(tableName, columnName, dataType, sourceDb, targetDb, result);
        }
    }

    /**
     * 验证VARCHAR长度限制
     */
    private void validateVarcharLength(String tableName, String columnName, String dataType, String sourceDb, String targetDb, StrictValidationResult result) {
        // 提取长度
        Pattern lengthPattern = Pattern.compile("VARCHAR\\s*\\(\\s*(\\d+)\\s*\\)", Pattern.CASE_INSENSITIVE);
        Matcher matcher = lengthPattern.matcher(dataType);

        if (matcher.find()) {
            int length = Integer.parseInt(matcher.group(1));

            // 检查目标数据库的VARCHAR长度限制
            String configKey = "validation." + targetDb.toLowerCase() + ".varchar_max_length";
            String maxLengthStr = config.getProperty(configKey, "");

            if (!maxLengthStr.isEmpty()) {
                int maxLength = Integer.parseInt(maxLengthStr);

                if (length > maxLength) {
                    result.addError(String.format(
                        "表 %s 列 %s: VARCHAR长度 %d 超过%s数据库的最大限制 %d",
                        tableName, columnName, length, targetDb, maxLength
                    ));
                    result.addSuggestion(String.format(
                        "表 %s 列 %s: 建议将VARCHAR(%d)改为VARCHAR(%d)或使用TEXT类型",
                        tableName, columnName, length, maxLength
                    ));
                }
            }
        }
    }

    /**
     * 验证CHAR长度限制
     */
    private void validateCharLength(String tableName, String columnName, String dataType, String sourceDb, String targetDb, StrictValidationResult result) {
        // 提取长度
        Pattern lengthPattern = Pattern.compile("CHAR\\s*\\(\\s*(\\d+)\\s*\\)", Pattern.CASE_INSENSITIVE);
        Matcher matcher = lengthPattern.matcher(dataType);

        if (matcher.find()) {
            int length = Integer.parseInt(matcher.group(1));

            // 检查目标数据库的CHAR长度限制
            String maxLengthStr = config.getProperty("validation." + targetDb.toLowerCase() + ".char_max_length", "");
            if (!maxLengthStr.isEmpty()) {
                int maxLength = Integer.parseInt(maxLengthStr);
                if (length > maxLength) {
                    result.addError(String.format(
                        "表 %s 列 %s: CHAR长度 %d 超过%s数据库的最大限制 %d",
                        tableName, columnName, length, targetDb, maxLength
                    ));
                    result.addSuggestion(String.format(
                        "表 %s 列 %s: 建议将CHAR(%d)改为VARCHAR(%d)或CHAR(%d)",
                        tableName, columnName, length, Math.min(length, maxLength), maxLength
                    ));
                }
            }
        }
    }

    /**
     * 验证数值类型精度限制
     */
    private void validateNumericPrecision(String tableName, String columnName, String dataType, String sourceDb, String targetDb, StrictValidationResult result) {
        // 提取精度和标度
        Pattern precisionPattern = Pattern.compile("(DECIMAL|NUMERIC)\\s*\\(\\s*(\\d+)\\s*(?:,\\s*(\\d+))?\\s*\\)", Pattern.CASE_INSENSITIVE);
        Matcher matcher = precisionPattern.matcher(dataType);

        if (matcher.find()) {
            int precision = Integer.parseInt(matcher.group(2));
            int scale = matcher.group(3) != null ? Integer.parseInt(matcher.group(3)) : 0;

            // 检查源数据库的精度限制（MySQL）
            if ("mysql".equalsIgnoreCase(sourceDb)) {
                String maxPrecisionStr = config.getProperty("validation.mysql.decimal_max_precision", "");
                String maxScaleStr = config.getProperty("validation.mysql.decimal_max_scale", "");

                if (!maxPrecisionStr.isEmpty()) {
                    int maxPrecision = Integer.parseInt(maxPrecisionStr);
                    if (precision > maxPrecision) {
                        result.addError(String.format(
                            "表 %s 列 %s: DECIMAL精度 %d 超过MySQL 8.4官方文档规定的最大值 %d",
                            tableName, columnName, precision, maxPrecision
                        ));
                    }
                }

                if (!maxScaleStr.isEmpty()) {
                    int maxScale = Integer.parseInt(maxScaleStr);
                    if (scale > maxScale) {
                        result.addError(String.format(
                            "表 %s 列 %s: DECIMAL标度 %d 超过MySQL 8.4官方文档规定的最大值 %d",
                            tableName, columnName, scale, maxScale
                        ));
                    }
                }
            }

            // 检查目标数据库的精度限制
            String maxPrecisionStr = config.getProperty("validation." + targetDb.toLowerCase() + ".number_max_precision", "");
            String maxScaleStr = config.getProperty("validation." + targetDb.toLowerCase() + ".number_max_scale", "");

            if (!maxPrecisionStr.isEmpty()) {
                int maxPrecision = Integer.parseInt(maxPrecisionStr);
                if (precision > maxPrecision) {
                    result.addError(String.format(
                        "表 %s 列 %s: 数值精度 %d 超过%s数据库的最大限制 %d",
                        tableName, columnName, precision, targetDb, maxPrecision
                    ));
                    // 添加精度调整建议
                    result.addSuggestion(String.format(
                        "表 %s 列 %s: 建议将DECIMAL(%d,%d)调整为DECIMAL(%d,%d)以符合%s数据库限制",
                        tableName, columnName, precision, scale, maxPrecision, scale, targetDb
                    ));
                }
            }

            if (!maxScaleStr.isEmpty()) {
                int maxScale = Integer.parseInt(maxScaleStr);
                if (scale > maxScale) {
                    result.addError(String.format(
                        "表 %s 列 %s: 数值标度 %d 超过%s数据库的最大限制 %d",
                        tableName, columnName, scale, targetDb, maxScale
                    ));
                    // 添加标度调整建议
                    result.addSuggestion(String.format(
                        "表 %s 列 %s: 建议将DECIMAL(%d,%d)调整为DECIMAL(%d,%d)以符合%s数据库限制",
                        tableName, columnName, precision, scale, precision, maxScale, targetDb
                    ));
                }
            }
        }
    }

    /**
     * 验证数据类型
     */
    private void validateDataType(String tableName, String columnName, String dataType, String sourceDb, String targetDb, StrictValidationResult result) {
        // 提取基本数据类型（去除长度和修饰符）
        String baseType = extractBaseType(dataType);
        
        Set<String> sourceTypes = typesByDatabase.get(sourceDb.toLowerCase());
        Set<String> targetTypes = typesByDatabase.get(targetDb.toLowerCase());
        
        if (sourceTypes != null && !sourceTypes.contains(baseType.toUpperCase())) {
            result.addError(String.format(
                "表 %s 列 %s: 数据类型 %s 在源数据库 %s 中不受支持",
                tableName, columnName, dataType, sourceDb
            ));
        }
        
        if (targetTypes != null && !targetTypes.contains(baseType.toUpperCase())) {
            result.addWarning(String.format(
                "表 %s 列 %s: 数据类型 %s 在目标数据库 %s 中不受支持，需要转换",
                tableName, columnName, dataType, targetDb
            ));
            
            // 提供转换建议
            String suggestion = getTypeConversionSuggestion(baseType, sourceDb, targetDb);
            if (suggestion != null) {
                result.addSuggestion(String.format(
                    "表 %s 列 %s: 建议将 %s 转换为 %s",
                    tableName, columnName, dataType, suggestion
                ));
            }
        }
    }
    
    /**
     * 验证默认值 - 严格按照MySQL 8.4官方文档
     */
    private void validateDefaultValue(String tableName, String columnName, String dataType, String attributes, String sourceDb, String targetDb, StrictValidationResult result) {
        // 改进的DEFAULT值提取 - 支持更复杂的默认值，正确处理分号
        Pattern defaultPattern = Pattern.compile(
            "DEFAULT\\s+([^\\s;]+(?:\\([^)]*\\))?|'[^']*')(?:\\s|;|$)",
            Pattern.CASE_INSENSITIVE
        );
        Matcher defaultMatcher = defaultPattern.matcher(attributes);

        if (!defaultMatcher.find()) {
            return;
        }

        String defaultValue = defaultMatcher.group(1).trim();
        // 移除可能的尾随分号
        if (defaultValue.endsWith(";")) {
            defaultValue = defaultValue.substring(0, defaultValue.length() - 1).trim();
        }
        String baseType = extractBaseType(dataType);

        // 根据MySQL 8.4官方文档验证数值类型默认值
        if (isNumericType(baseType, sourceDb)) {
            validateNumericDefaultValue(tableName, columnName, dataType, defaultValue, result);
        }

        // 根据MySQL 8.4官方文档验证字符串类型默认值
        if (isStringType(baseType, sourceDb)) {
            validateStringDefaultValue(tableName, columnName, dataType, defaultValue, result);
        }

        // 根据MySQL 8.4官方文档验证日期时间类型默认值
        if (isDateTimeType(baseType, sourceDb)) {
            validateDateTimeDefaultValue(tableName, columnName, dataType, defaultValue, result);
        }
    }

    /**
     * 验证数值类型默认值 - 严格按照MySQL 8.4官方文档
     */
    private void validateNumericDefaultValue(String tableName, String columnName, String dataType, String defaultValue, StrictValidationResult result) {
        // 检查无效的DEFAULT函数
        String invalidDefaultFunctions = config.getProperty("validation.mysql.invalid_default_functions", "");
        if (containsFunction(invalidDefaultFunctions, defaultValue)) {
            result.addError(String.format(
                "表 %s 列 %s: 根据MySQL 8.4官方文档，DEFAULT %s 函数无效，该函数不能用作默认值",
                tableName, columnName, defaultValue
            ));
            result.addSuggestion(String.format(
                "表 %s 列 %s: 建议使用有效的数值或NULL作为默认值",
                tableName, columnName
            ));
            return;
        }

        // MySQL 8.4官方文档：数值类型的默认值不应使用引号
        if (defaultValue.startsWith("'") && defaultValue.endsWith("'")) {
            String innerValue = defaultValue.substring(1, defaultValue.length() - 1);

            // 检查引号内是否为有效数值
            if (isValidNumeric(innerValue)) {
                result.addWarning(String.format(
                    "表 %s 列 %s: 根据MySQL 8.4官方文档，数值类型 %s 的默认值 %s 不应使用引号包围",
                    tableName, columnName, dataType, defaultValue
                ));
                result.addSuggestion(String.format(
                    "表 %s 列 %s: 建议将 DEFAULT %s 改为 DEFAULT %s（遵循MySQL 8.4官方规范）",
                    tableName, columnName, defaultValue, innerValue
                ));
            } else {
                result.addError(String.format(
                    "表 %s 列 %s: 数值类型 %s 的默认值 %s 不是有效的数值（违反MySQL 8.4官方规范）",
                    tableName, columnName, dataType, defaultValue
                ));
            }
        }

        // 验证特殊数值（如负数、小数）
        if (!defaultValue.startsWith("'") && !isValidNumeric(defaultValue) && !isSpecialFunction(defaultValue)) {
            result.addError(String.format(
                "表 %s 列 %s: 数值类型 %s 的默认值 %s 不是有效的数值格式",
                tableName, columnName, dataType, defaultValue
            ));
        }
    }

    /**
     * 验证字符串类型默认值 - 严格按照MySQL 8.4官方文档
     */
    private void validateStringDefaultValue(String tableName, String columnName, String dataType, String defaultValue, StrictValidationResult result) {
        // 检查无效的DEFAULT函数
        String invalidDefaultFunctions = config.getProperty("validation.mysql.invalid_default_functions", "");
        if (containsFunction(invalidDefaultFunctions, defaultValue)) {
            result.addError(String.format(
                "表 %s 列 %s: 根据MySQL 8.4官方文档，DEFAULT %s 函数无效，该函数不能用作默认值",
                tableName, columnName, defaultValue
            ));
            result.addSuggestion(String.format(
                "表 %s 列 %s: 建议使用有效的字符串值或NULL作为默认值",
                tableName, columnName
            ));
            return;
        }

        // MySQL 8.4官方文档：字符串类型的默认值必须使用引号
        if (!defaultValue.startsWith("'") && !defaultValue.endsWith("'") &&
            !isSpecialFunction(defaultValue) && !defaultValue.equalsIgnoreCase("NULL")) {

            result.addWarning(String.format(
                "表 %s 列 %s: 根据MySQL 8.4官方文档，字符串类型 %s 的默认值 %s 应使用单引号包围",
                tableName, columnName, dataType, defaultValue
            ));
            result.addSuggestion(String.format(
                "表 %s 列 %s: 建议将 DEFAULT %s 改为 DEFAULT '%s'（遵循MySQL 8.4官方规范）",
                tableName, columnName, defaultValue, defaultValue
            ));
        }
    }

    /**
     * 验证日期时间类型默认值 - 严格按照MySQL 8.4官方文档
     */
    private void validateDateTimeDefaultValue(String tableName, String columnName, String dataType, String defaultValue, StrictValidationResult result) {
        // 检查无效的DEFAULT函数
        String invalidDefaultFunctions = config.getProperty("validation.mysql.invalid_default_functions", "");
        if (containsFunction(invalidDefaultFunctions, defaultValue)) {
            result.addError(String.format(
                "表 %s 列 %s: 根据MySQL 8.4官方文档，DEFAULT %s 函数无效，该函数不能用作默认值",
                tableName, columnName, defaultValue
            ));
            result.addSuggestion(String.format(
                "表 %s 列 %s: 建议使用 CURRENT_TIMESTAMP 或其他有效的日期时间函数",
                tableName, columnName
            ));
            return;
        }

        // MySQL 8.4官方文档中的有效日期时间默认值
        String validDateTimeFunctions = config.getProperty("validation.mysql.valid_default_functions", "");

        if (!containsFunction(validDateTimeFunctions, defaultValue) &&
            !defaultValue.startsWith("'") && !defaultValue.endsWith("'") &&
            !defaultValue.equalsIgnoreCase("NULL")) {

            result.addWarning(String.format(
                "表 %s 列 %s: 日期时间类型 %s 的默认值 %s 可能不符合MySQL 8.4官方规范",
                tableName, columnName, dataType, defaultValue
            ));
            result.addSuggestion(String.format(
                "表 %s 列 %s: 建议使用官方支持的函数如 CURRENT_TIMESTAMP 或用引号包围的日期字符串",
                tableName, columnName
            ));
        }
    }
    
    /**
     * 验证ON UPDATE子句 - 严格按照MySQL 8.4官方文档
     */
    private void validateOnUpdateClause(String tableName, String columnName, String attributes, String sourceDb, String targetDb, StrictValidationResult result) {
        // 改进的ON UPDATE解析 - 支持更复杂的语法
        Pattern improvedOnUpdatePattern = Pattern.compile(
            "ON\\s+UPDATE\\s+([^\\s]+(?:\\([^)]*\\))?)",
            Pattern.CASE_INSENSITIVE
        );
        Matcher onUpdateMatcher = improvedOnUpdatePattern.matcher(attributes);

        if (!onUpdateMatcher.find()) {
            return;
        }

        String onUpdateValue = onUpdateMatcher.group(1).trim();

        // 根据MySQL 8.4官方文档验证ON UPDATE语法
        validateOnUpdateSyntax(tableName, columnName, onUpdateValue, sourceDb, targetDb, result);
    }

    /**
     * 验证ON UPDATE语法 - 严格按照MySQL 8.4官方文档
     */
    private void validateOnUpdateSyntax(String tableName, String columnName, String onUpdateValue, String sourceDb, String targetDb, StrictValidationResult result) {
        // MySQL 8.4官方文档：无效的ON UPDATE函数
        String invalidFunctions = config.getProperty("validation.mysql.invalid_on_update", "");
        String validFunctions = config.getProperty("validation.mysql.valid_on_update", "");

        // 检查是否为明确无效的函数
        if (containsFunction(invalidFunctions, onUpdateValue)) {
            result.addError(String.format(
                "表 %s 列 %s: 根据MySQL 8.4官方文档，ON UPDATE %s 语法不正确，%s 不能用于 ON UPDATE 子句",
                tableName, columnName, onUpdateValue, onUpdateValue
            ));
            result.addSuggestion(String.format(
                "表 %s 列 %s: 根据MySQL 8.4官方文档，建议使用 ON UPDATE CURRENT_TIMESTAMP",
                tableName, columnName
            ));
            return;
        }

        // 检查是否为有效的函数
        if (!containsFunction(validFunctions, onUpdateValue)) {
            result.addWarning(String.format(
                "表 %s 列 %s: ON UPDATE %s 可能不符合MySQL 8.4官方规范，请确认语法正确性",
                tableName, columnName, onUpdateValue
            ));
            result.addSuggestion(String.format(
                "表 %s 列 %s: 建议使用MySQL 8.4官方支持的函数：%s",
                tableName, columnName, validFunctions
            ));
        }

        // 特别检查常见的错误用法
        if (onUpdateValue.toUpperCase().contains("NOW()")) {
            result.addError(String.format(
                "表 %s 列 %s: 根据MySQL 8.4官方文档，ON UPDATE NOW() 语法不正确，NOW() 不能用于 ON UPDATE 子句",
                tableName, columnName
            ));
            result.addSuggestion(String.format(
                "表 %s 列 %s: 应该使用 ON UPDATE CURRENT_TIMESTAMP 替代 ON UPDATE NOW()",
                tableName, columnName
            ));
        }
    }
    
    /**
     * 验证AUTO_INCREMENT - 严格按照MySQL 8.4官方文档
     */
    private void validateAutoIncrement(String tableName, String columnName, String dataType, String attributes, String sourceDb, String targetDb, StrictValidationResult result) {
        // 改进的AUTO_INCREMENT检测
        Pattern improvedAutoIncrementPattern = Pattern.compile(
            "AUTO_INCREMENT",
            Pattern.CASE_INSENSITIVE
        );

        if (!improvedAutoIncrementPattern.matcher(attributes).find()) {
            return;
        }

        String baseType = extractBaseType(dataType);

        // 根据MySQL 8.4官方文档验证AUTO_INCREMENT类型
        validateAutoIncrementType(tableName, columnName, baseType, dataType, sourceDb, result);

        // 为目标数据库提供转换建议
        provideAutoIncrementConversionSuggestions(tableName, columnName, targetDb, result);
    }

    /**
     * 验证AUTO_INCREMENT类型 - 严格按照MySQL 8.4官方文档
     */
    private void validateAutoIncrementType(String tableName, String columnName, String baseType, String dataType, String sourceDb, StrictValidationResult result) {
        // MySQL 8.4官方文档：AUTO_INCREMENT只能用于整数类型
        String autoIncrementTypes = config.getProperty("validation.mysql.auto_increment_types", "");

        if (!containsType(autoIncrementTypes, baseType)) {
            result.addError(String.format(
                "表 %s 列 %s: 根据MySQL 8.4官方文档，AUTO_INCREMENT 只能用于整数类型（%s），当前类型为 %s",
                tableName, columnName, autoIncrementTypes, dataType
            ));
            result.addSuggestion(String.format(
                "表 %s 列 %s: 建议将列类型改为整数类型（如 INT, BIGINT）",
                tableName, columnName
            ));
        }

        // MySQL 8.4官方文档：AUTO_INCREMENT列必须是键的一部分
        boolean requiresKey = Boolean.parseBoolean(
            config.getProperty("validation.mysql.auto_increment_requires_key", "true")
        );

        if (requiresKey) {
            result.addSuggestion(String.format(
                "表 %s 列 %s: 根据MySQL 8.4官方文档，AUTO_INCREMENT列应该是主键或唯一键的一部分",
                tableName, columnName
            ));
        }
    }

    /**
     * 提供AUTO_INCREMENT转换建议 - 基于目标数据库官方文档
     */
    private void provideAutoIncrementConversionSuggestions(String tableName, String columnName, String targetDb, StrictValidationResult result) {
        switch (targetDb.toLowerCase()) {
            case "dameng":
                // 达梦数据库官方文档：使用IDENTITY替代AUTO_INCREMENT
                String identitySyntax = config.getProperty("validation.dameng.identity_syntax", "IDENTITY(1,1)");
                result.addSuggestion(String.format(
                    "表 %s 列 %s: 根据达梦数据库官方文档，建议使用 %s 替代 AUTO_INCREMENT",
                    tableName, columnName, identitySyntax
                ));
                break;

            case "kingbase":
                // 金仓数据库官方文档：使用SERIAL类型
                result.addSuggestion(String.format(
                    "表 %s 列 %s: 根据金仓数据库官方文档，建议使用 SERIAL 或 BIGSERIAL 类型替代 AUTO_INCREMENT",
                    tableName, columnName
                ));
                break;

            case "shentong":
                // 神通数据库官方文档：支持AUTO_INCREMENT但有限制
                String shentongAutoTypes = config.getProperty("validation.shentong.auto_increment_types", "INT,BIGINT,FLOAT");
                result.addSuggestion(String.format(
                    "表 %s 列 %s: 根据神通数据库官方文档，AUTO_INCREMENT支持的类型有限（%s），且需要唯一约束",
                    tableName, columnName, shentongAutoTypes
                ));
                break;

            default:
                result.addSuggestion(String.format(
                    "表 %s 列 %s: 请根据目标数据库 %s 的官方文档确认AUTO_INCREMENT的替代方案",
                    tableName, columnName, targetDb
                ));
                break;
        }
    }
    
    // 辅助方法
    private String extractBaseType(String dataType) {
        int parenIndex = dataType.indexOf('(');
        if (parenIndex > 0) {
            return dataType.substring(0, parenIndex).trim();
        }
        return dataType.split("\\s+")[0].trim();
    }
    
    private boolean isNumericType(String type, String database) {
        String numericTypes = config.getProperty("validation." + database.toLowerCase() + ".types.numeric", "");
        return containsType(numericTypes, type);
    }
    
    private boolean isStringType(String type, String database) {
        String stringTypes = config.getProperty("validation." + database.toLowerCase() + ".types.string", "");
        return containsType(stringTypes, type);
    }

    private boolean isDateTimeType(String type, String database) {
        String dateTimeTypes = config.getProperty("validation." + database.toLowerCase() + ".types.datetime", "");
        return containsType(dateTimeTypes, type);
    }
    
    private boolean containsType(String typeList, String type) {
        if (typeList.isEmpty()) return false;
        String[] types = typeList.split(",");
        for (String t : types) {
            if (t.trim().equalsIgnoreCase(type)) {
                return true;
            }
        }
        return false;
    }
    
    private boolean containsFunction(String functionList, String function) {
        if (functionList.isEmpty()) return false;
        String[] functions = functionList.split(",");
        for (String f : functions) {
            if (f.trim().equalsIgnoreCase(function)) {
                return true;
            }
        }
        return false;
    }

    private boolean containsReservedWord(String reservedWordList, String word) {
        if (reservedWordList.isEmpty()) return false;
        String[] words = reservedWordList.split(",");
        for (String w : words) {
            if (w.trim().equalsIgnoreCase(word)) {
                return true;
            }
        }
        return false;
    }
    
    private boolean isValidNumeric(String value) {
        try {
            Double.parseDouble(value);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    private boolean isSpecialFunction(String value) {
        // 检查日期时间函数
        String datetimeFunctions = config.getProperty("validation.mysql.datetime_functions", "");
        if (containsFunction(datetimeFunctions, value)) {
            return true;
        }

        // 检查有效的DEFAULT函数
        String validDefaultFunctions = config.getProperty("validation.mysql.valid_default_functions", "");
        return containsFunction(validDefaultFunctions, value);
    }
    
    /**
     * 验证数据类型长度限制 - 基于MySQL 8.4官方文档
     */
    private void validateDataTypeLengthLimits(String tableName, String columnName, String dataType, String sourceDb, String targetDb, StrictValidationResult result) {
        if (!"mysql".equalsIgnoreCase(sourceDb)) {
            return;
        }

        String upperDataType = dataType.toUpperCase();

        // 验证VARCHAR长度限制
        if (upperDataType.startsWith("VARCHAR")) {
            validateVarcharLength(tableName, columnName, dataType, result);
        }

        // 验证CHAR长度限制
        if (upperDataType.startsWith("CHAR")) {
            validateCharLength(tableName, columnName, dataType, result);
        }

        // 验证DECIMAL精度和标度限制
        if (upperDataType.startsWith("DECIMAL") || upperDataType.startsWith("NUMERIC")) {
            validateDecimalPrecisionScale(tableName, columnName, dataType, result);
        }

        // 验证BIT长度限制
        if (upperDataType.startsWith("BIT")) {
            validateBitLength(tableName, columnName, dataType, result);
        }

        // 验证ENUM值数量限制
        if (upperDataType.startsWith("ENUM")) {
            validateEnumValueCount(tableName, columnName, dataType, result);
        }

        // 验证SET成员数量限制
        if (upperDataType.startsWith("SET")) {
            validateSetMemberCount(tableName, columnName, dataType, result);
        }
    }

    /**
     * 验证VARCHAR长度限制
     */
    private void validateVarcharLength(String tableName, String columnName, String dataType, StrictValidationResult result) {
        Pattern lengthPattern = Pattern.compile("VARCHAR\\s*\\(\\s*(\\d+)\\s*\\)", Pattern.CASE_INSENSITIVE);
        Matcher matcher = lengthPattern.matcher(dataType);

        if (matcher.find()) {
            int length = Integer.parseInt(matcher.group(1));
            int maxLength = Integer.parseInt(config.getProperty("validation.mysql.varchar_max_length", "65535"));

            if (length > maxLength) {
                result.addError(String.format(
                    "表 %s 列 %s: VARCHAR长度 %d 超过MySQL 8.4官方文档规定的最大值 %d",
                    tableName, columnName, length, maxLength
                ));
            }

            if (length == 0) {
                result.addError(String.format(
                    "表 %s 列 %s: VARCHAR长度不能为0",
                    tableName, columnName
                ));
            }
        }
    }

    /**
     * 验证CHAR长度限制
     */
    private void validateCharLength(String tableName, String columnName, String dataType, StrictValidationResult result) {
        Pattern lengthPattern = Pattern.compile("CHAR\\s*\\(\\s*(\\d+)\\s*\\)", Pattern.CASE_INSENSITIVE);
        Matcher matcher = lengthPattern.matcher(dataType);

        if (matcher.find()) {
            int length = Integer.parseInt(matcher.group(1));
            int maxLength = Integer.parseInt(config.getProperty("validation.mysql.char_max_length", "255"));

            if (length > maxLength) {
                result.addError(String.format(
                    "表 %s 列 %s: CHAR长度 %d 超过MySQL 8.4官方文档规定的最大值 %d",
                    tableName, columnName, length, maxLength
                ));
            }

            if (length == 0) {
                result.addError(String.format(
                    "表 %s 列 %s: CHAR长度不能为0",
                    tableName, columnName
                ));
            }
        }
    }

    /**
     * 验证DECIMAL精度和标度限制
     */
    private void validateDecimalPrecisionScale(String tableName, String columnName, String dataType, StrictValidationResult result) {
        Pattern precisionScalePattern = Pattern.compile("(?:DECIMAL|NUMERIC)\\s*\\(\\s*(\\d+)\\s*(?:,\\s*(\\d+)\\s*)?\\)", Pattern.CASE_INSENSITIVE);
        Matcher matcher = precisionScalePattern.matcher(dataType);

        if (matcher.find()) {
            int precision = Integer.parseInt(matcher.group(1));
            int scale = matcher.group(2) != null ? Integer.parseInt(matcher.group(2)) : 0;

            int maxPrecision = Integer.parseInt(config.getProperty("validation.mysql.decimal_max_precision", "65"));
            int maxScale = Integer.parseInt(config.getProperty("validation.mysql.decimal_max_scale", "30"));

            if (precision > maxPrecision) {
                result.addError(String.format(
                    "表 %s 列 %s: DECIMAL精度 %d 超过MySQL 8.4官方文档规定的最大值 %d",
                    tableName, columnName, precision, maxPrecision
                ));
            }

            if (scale > maxScale) {
                result.addError(String.format(
                    "表 %s 列 %s: DECIMAL标度 %d 超过MySQL 8.4官方文档规定的最大值 %d",
                    tableName, columnName, scale, maxScale
                ));
            }

            if (scale > precision) {
                result.addError(String.format(
                    "表 %s 列 %s: DECIMAL标度 %d 不能大于精度 %d",
                    tableName, columnName, scale, precision
                ));
            }

            if (precision == 0) {
                result.addError(String.format(
                    "表 %s 列 %s: DECIMAL精度不能为0",
                    tableName, columnName
                ));
            }
        }
    }

    /**
     * 验证BIT长度限制
     */
    private void validateBitLength(String tableName, String columnName, String dataType, StrictValidationResult result) {
        Pattern lengthPattern = Pattern.compile("BIT\\s*\\(\\s*(\\d+)\\s*\\)", Pattern.CASE_INSENSITIVE);
        Matcher matcher = lengthPattern.matcher(dataType);

        if (matcher.find()) {
            int length = Integer.parseInt(matcher.group(1));
            int maxLength = Integer.parseInt(config.getProperty("validation.mysql.bit_max_length", "64"));

            if (length > maxLength) {
                result.addError(String.format(
                    "表 %s 列 %s: BIT长度 %d 超过MySQL 8.4官方文档规定的最大值 %d",
                    tableName, columnName, length, maxLength
                ));
            }

            if (length == 0) {
                result.addError(String.format(
                    "表 %s 列 %s: BIT长度不能为0",
                    tableName, columnName
                ));
            }
        }
    }

    /**
     * 验证ENUM值数量限制
     */
    private void validateEnumValueCount(String tableName, String columnName, String dataType, StrictValidationResult result) {
        Pattern enumPattern = Pattern.compile("ENUM\\s*\\((.+)\\)", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
        Matcher matcher = enumPattern.matcher(dataType);

        if (matcher.find()) {
            String enumValues = matcher.group(1);
            // 简单计算逗号数量来估算值的数量（不完全准确，但足够用于基本验证）
            int valueCount = enumValues.split(",").length;
            int maxValues = Integer.parseInt(config.getProperty("validation.mysql.enum_max_values", "65535"));

            if (valueCount > maxValues) {
                result.addError(String.format(
                    "表 %s 列 %s: ENUM值数量 %d 超过MySQL 8.4官方文档规定的最大值 %d",
                    tableName, columnName, valueCount, maxValues
                ));
            }
        }
    }

    /**
     * 验证SET成员数量限制
     */
    private void validateSetMemberCount(String tableName, String columnName, String dataType, StrictValidationResult result) {
        Pattern setPattern = Pattern.compile("SET\\s*\\((.+)\\)", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
        Matcher matcher = setPattern.matcher(dataType);

        if (matcher.find()) {
            String setMembers = matcher.group(1);
            // 简单计算逗号数量来估算成员的数量
            int memberCount = setMembers.split(",").length;
            int maxMembers = Integer.parseInt(config.getProperty("validation.mysql.set_max_members", "64"));

            if (memberCount > maxMembers) {
                result.addError(String.format(
                    "表 %s 列 %s: SET成员数量 %d 超过MySQL 8.4官方文档规定的最大值 %d",
                    tableName, columnName, memberCount, maxMembers
                ));
            }
        }
    }

    /**
     * 验证标识符模式 - 基于MySQL 8.4官方文档
     */
    private void validateIdentifierPattern(String tableName, String columnName, String sourceDb, String targetDb, StrictValidationResult result) {
        if (!"mysql".equalsIgnoreCase(sourceDb)) {
            return;
        }

        // 验证列名模式
        validateColumnNamePattern(tableName, columnName, result);

        // 验证表名模式
        validateTableNamePattern(tableName, result);
    }

    /**
     * 验证列名模式
     */
    private void validateColumnNamePattern(String tableName, String columnName, StrictValidationResult result) {
        String invalidPatterns = config.getProperty("validation.mysql.invalid_column_patterns", "");
        if (invalidPatterns.isEmpty()) {
            return;
        }

        String[] patterns = invalidPatterns.split(",");
        for (String pattern : patterns) {
            if (columnName.matches(pattern.trim())) {
                result.addError(String.format(
                    "表 %s 列名 %s: 根据MySQL 8.4官方文档，列名不符合标识符命名规范（匹配无效模式：%s）",
                    tableName, columnName, pattern.trim()
                ));
                result.addSuggestion(String.format(
                    "表 %s 列名 %s: 建议使用字母开头，只包含字母、数字和下划线的列名",
                    tableName, columnName
                ));
                break;
            }
        }

        // 检查列名长度（MySQL标识符最大64字符）
        if (columnName.length() > 64) {
            result.addError(String.format(
                "表 %s 列名 %s: 列名长度 %d 超过MySQL 8.4官方文档规定的最大值64字符",
                tableName, columnName, columnName.length()
            ));
        }
    }

    /**
     * 验证表名模式
     */
    private void validateTableNamePattern(String tableName, StrictValidationResult result) {
        String invalidPatterns = config.getProperty("validation.mysql.invalid_table_patterns", "");
        if (invalidPatterns.isEmpty()) {
            return;
        }

        // 移除可能的schema前缀
        String actualTableName = tableName;
        if (tableName.contains(".")) {
            actualTableName = tableName.substring(tableName.lastIndexOf(".") + 1);
        }

        String[] patterns = invalidPatterns.split(",");
        for (String pattern : patterns) {
            if (actualTableName.matches(pattern.trim())) {
                result.addError(String.format(
                    "表名 %s: 根据MySQL 8.4官方文档，表名不符合标识符命名规范（匹配无效模式：%s）",
                    actualTableName, pattern.trim()
                ));
                result.addSuggestion(String.format(
                    "表名 %s: 建议使用字母开头，只包含字母、数字和下划线的表名",
                    actualTableName
                ));
                break;
            }
        }

        // 检查表名长度（MySQL标识符最大64字符）
        if (actualTableName.length() > 64) {
            result.addError(String.format(
                "表名 %s: 表名长度 %d 超过MySQL 8.4官方文档规定的最大值64字符",
                actualTableName, actualTableName.length()
            ));
        }
    }

    /**
     * 验证约束组合 - 基于MySQL 8.4官方文档
     */
    private void validateConstraintCombinations(String tableName, String columnName, String dataType, String attributes, String sourceDb, String targetDb, StrictValidationResult result) {
        if (!"mysql".equalsIgnoreCase(sourceDb)) {
            return;
        }

        String upperAttributes = attributes.toUpperCase();

        // 验证AUTO_INCREMENT与NULL的组合
        if (upperAttributes.contains("AUTO_INCREMENT") && upperAttributes.contains("NULL") && !upperAttributes.contains("NOT NULL")) {
            result.addError(String.format(
                "表 %s 列 %s: 根据MySQL 8.4官方文档，AUTO_INCREMENT列不能为NULL",
                tableName, columnName
            ));
            result.addSuggestion(String.format(
                "表 %s 列 %s: 建议添加NOT NULL约束",
                tableName, columnName
            ));
        }

        // 验证AUTO_INCREMENT与DEFAULT的组合
        if (upperAttributes.contains("AUTO_INCREMENT") && upperAttributes.contains("DEFAULT")) {
            result.addError(String.format(
                "表 %s 列 %s: 根据MySQL 8.4官方文档，AUTO_INCREMENT列不能有DEFAULT值",
                tableName, columnName
            ));
            result.addSuggestion(String.format(
                "表 %s 列 %s: 建议移除DEFAULT子句",
                tableName, columnName
            ));
        }

        // 验证PRIMARY KEY与NULL的组合
        if (upperAttributes.contains("PRIMARY KEY") && upperAttributes.contains("NULL") && !upperAttributes.contains("NOT NULL")) {
            result.addError(String.format(
                "表 %s 列 %s: 根据MySQL 8.4官方文档，PRIMARY KEY列不能为NULL",
                tableName, columnName
            ));
            result.addSuggestion(String.format(
                "表 %s 列 %s: PRIMARY KEY列自动为NOT NULL",
                tableName, columnName
            ));
        }

        // 验证无效的数据类型组合
        validateInvalidTypeCombinations(tableName, columnName, dataType, attributes, result);
    }

    /**
     * 验证无效的数据类型组合
     */
    private void validateInvalidTypeCombinations(String tableName, String columnName, String dataType, String attributes, StrictValidationResult result) {
        String invalidCombinations = config.getProperty("validation.mysql.invalid_type_combinations", "");
        if (invalidCombinations.isEmpty()) {
            return;
        }

        String fullDefinition = (dataType + " " + (attributes != null ? attributes : "")).toUpperCase();
        String[] combinations = invalidCombinations.split(",");

        for (String combination : combinations) {
            String trimmedCombination = combination.trim().toUpperCase();

            // 检查直接匹配
            boolean matches = fullDefinition.contains(trimmedCombination);

            // 如果直接匹配失败，尝试检查反向顺序
            if (!matches && trimmedCombination.contains(" ")) {
                String[] parts = trimmedCombination.split("\\s+");
                if (parts.length == 2) {
                    String reversedCombination = parts[1] + " " + parts[0];
                    matches = fullDefinition.contains(reversedCombination);
                }
            }

            if (matches) {
                result.addError(String.format(
                    "表 %s 列 %s: 根据MySQL 8.4官方文档，数据类型组合 %s 无效",
                    tableName, columnName, trimmedCombination
                ));

                // 提供具体的修复建议
                if (trimmedCombination.contains("UNSIGNED DECIMAL")) {
                    result.addSuggestion(String.format(
                        "表 %s 列 %s: DECIMAL类型不支持UNSIGNED属性，请移除UNSIGNED",
                        tableName, columnName
                    ));
                } else if (trimmedCombination.contains("ZEROFILL")) {
                    result.addSuggestion(String.format(
                        "表 %s 列 %s: 该数据类型不支持ZEROFILL属性",
                        tableName, columnName
                    ));
                }
            }
        }
    }

    private String getTypeConversionSuggestion(String sourceType, String sourceDb, String targetDb) {
        // 简单的类型转换建议
        if ("mysql".equalsIgnoreCase(sourceDb) && "dameng".equalsIgnoreCase(targetDb)) {
            switch (sourceType.toUpperCase()) {
                case "TINYINT": return "TINYINT";
                case "SMALLINT": return "SMALLINT";
                case "MEDIUMINT": return "INT";
                case "INT": case "INTEGER": return "INT";
                case "BIGINT": return "BIGINT";
                case "VARCHAR": return "VARCHAR";
                case "TEXT": return "TEXT";
                case "DATETIME": return "DATETIME";
                case "TIMESTAMP": return "TIMESTAMP";
            }
        }
        return null;
    }

    /**
     * 验证表列数限制
     */
    private void validateTableColumnCount(String tableName, String[] columns, String sourceDb, String targetDb, StrictValidationResult result) {
        // 计算实际列数（排除约束定义）
        int columnCount = 0;
        for (String column : columns) {
            String trimmedColumn = column.trim();
            if (!trimmedColumn.isEmpty() &&
                !trimmedColumn.toUpperCase().matches("^(PRIMARY\\s+KEY|KEY|INDEX|UNIQUE|CONSTRAINT|FOREIGN\\s+KEY).*")) {
                columnCount++;
            }
        }

        // MySQL通用限制
        if ("mysql".equalsIgnoreCase(sourceDb)) {
            int maxColumns = Integer.parseInt(config.getProperty("validation.mysql.max_columns_per_table", "4096"));
            if (columnCount > maxColumns) {
                result.addError(String.format(
                    "表 %s: 列数 %d 超过MySQL 8.4官方文档规定的最大值 %d",
                    tableName, columnCount, maxColumns
                ));
                result.addSuggestion(String.format(
                    "表 %s: 建议减少列数或考虑表结构重构",
                    tableName
                ));
            }

            // InnoDB特定限制
            int innodbMaxColumns = Integer.parseInt(config.getProperty("validation.mysql.innodb_max_columns_per_table", "1017"));
            if (columnCount > innodbMaxColumns) {
                result.addError(String.format(
                    "表 %s: 列数 %d 超过InnoDB存储引擎的最大限制 %d",
                    tableName, columnCount, innodbMaxColumns
                ));
            }
        }

        // 达梦数据库限制
        if ("dameng".equalsIgnoreCase(targetDb)) {
            int damengMaxColumns = Integer.parseInt(config.getProperty("validation.dameng.max_columns_per_table", "1000"));
            if (columnCount > damengMaxColumns) {
                result.addError(String.format(
                    "表 %s: 列数 %d 超过达梦数据库的最大限制 %d",
                    tableName, columnCount, damengMaxColumns
                ));
            }
        }

        // 神通数据库限制
        if ("shentong".equalsIgnoreCase(targetDb)) {
            // 神通数据库没有明确的列数限制，但建议不超过1000列
            if (columnCount > 1000) {
                result.addWarning(String.format(
                    "表 %s: 列数 %d 较多，建议考虑表结构优化",
                    tableName, columnCount
                ));
            }
        }

        // 金仓数据库限制
        if ("kingbase".equalsIgnoreCase(targetDb)) {
            // 金仓数据库基于PostgreSQL，列数限制为1600
            if (columnCount > 1600) {
                result.addError(String.format(
                    "表 %s: 列数 %d 超过金仓数据库的最大限制 1600",
                    tableName, columnCount
                ));
            }
        }
    }

    /**
     * 验证BLOB/TEXT长度限制
     */
    private void validateBlobTextLimits(String tableName, String columnName, String dataType, String sourceDb, String targetDb, StrictValidationResult result) {
        String upperDataType = dataType.toUpperCase();

        if ("mysql".equalsIgnoreCase(sourceDb)) {
            // 检查BLOB类型限制 - 仅在需要时提供信息
            // 这些是信息性检查，不是错误

            // 检查TEXT类型限制 - 仅在需要时提供信息
            // 这些是信息性检查，不是错误
        }
    }

    /**
     * 验证ALTER TABLE MODIFY语句
     */
    private void validateAlterTableModifyStatements(String sql, String sourceDb, String targetDb, StrictValidationResult result) {
        Matcher matcher = alterTableModifyColumnPattern.matcher(sql);
        while (matcher.find()) {
            String tableName = matcher.group(1).replaceAll("[`\"']", "");
            String columnDefinition = matcher.group(2).trim();

            // 验证表名（只验证表名，不验证列名）
            validateTableNamePattern(tableName, result);

            // 验证列定义
            validateColumnDefinition(tableName, columnDefinition, sourceDb, targetDb, result);
        }
    }

    /**
     * 验证ALTER TABLE CHANGE语句
     */
    private void validateAlterTableChangeStatements(String sql, String sourceDb, String targetDb, StrictValidationResult result) {
        Matcher matcher = alterTableChangeColumnPattern.matcher(sql);
        while (matcher.find()) {
            String tableName = matcher.group(1).replaceAll("[`\"']", "");
            String oldColumnName = matcher.group(2).replaceAll("[`\"']", "");
            String newColumnDefinition = matcher.group(3).trim();

            // 验证表名
            validateTableNamePattern(tableName, result);

            // 验证旧列名
            validateColumnNamePattern(tableName, oldColumnName, result);

            // 验证新列定义
            validateColumnDefinition(tableName, newColumnDefinition, sourceDb, targetDb, result);
        }
    }

    /**
     * 验证CREATE INDEX语句
     */
    private void validateCreateIndexStatements(String sql, String sourceDb, String targetDb, StrictValidationResult result) {
        Matcher matcher = createIndexPattern.matcher(sql);
        while (matcher.find()) {
            String indexName = matcher.group(1).replaceAll("[`\"']", "");
            String tableName = matcher.group(2).replaceAll("[`\"']", "");
            String columnList = matcher.group(3);

            // 验证索引名长度
            validateTableNamePattern(indexName, result);

            // 验证表名
            validateTableNamePattern(tableName, result);

            // 验证索引列数限制
            validateIndexColumnCount(tableName, indexName, columnList, sourceDb, targetDb, result);

            // 验证索引键长度限制
            validateIndexKeyLength(tableName, indexName, columnList, sourceDb, targetDb, result);
        }
    }

    /**
     * 验证CREATE VIEW语句
     */
    private void validateCreateViewStatements(String sql, String sourceDb, String targetDb, StrictValidationResult result) {
        Matcher matcher = createViewPattern.matcher(sql);
        while (matcher.find()) {
            String viewName = matcher.group(1).replaceAll("[`\"']", "");
            String columnList = matcher.group(2);
            String selectStatement = matcher.group(3);

            // 验证视图名长度
            validateTableNamePattern(viewName, result);

            // 验证视图列数限制
            if (columnList != null && !columnList.trim().isEmpty()) {
                validateViewColumnCount(viewName, columnList, sourceDb, targetDb, result);
            }
        }
    }

    /**
     * 验证CREATE PROCEDURE语句
     */
    private void validateCreateProcedureStatements(String sql, String sourceDb, String targetDb, StrictValidationResult result) {
        Matcher matcher = createProcedurePattern.matcher(sql);
        while (matcher.find()) {
            String procedureName = matcher.group(1).replaceAll("[`\"']", "");
            String parameters = matcher.group(2);
            String body = matcher.group(3);

            // 验证存储过程名长度
            validateProcedureName(procedureName, sourceDb, targetDb, result);

            // 验证参数数量限制
            if (parameters != null && !parameters.trim().isEmpty()) {
                validateProcedureParameterCount(procedureName, parameters, sourceDb, targetDb, result);
            }
        }
    }

    /**
     * 验证CREATE FUNCTION语句
     */
    private void validateCreateFunctionStatements(String sql, String sourceDb, String targetDb, StrictValidationResult result) {
        Matcher matcher = createFunctionPattern.matcher(sql);
        while (matcher.find()) {
            String functionName = matcher.group(1).replaceAll("[`\"']", "");
            String parameters = matcher.group(2);
            String returnType = matcher.group(3);
            String body = matcher.group(4);

            // 验证函数名长度
            validateFunctionName(functionName, sourceDb, targetDb, result);

            // 验证参数数量限制
            if (parameters != null && !parameters.trim().isEmpty()) {
                validateFunctionParameterCount(functionName, parameters, sourceDb, targetDb, result);
            }

            // 验证返回类型
            validateReturnType(functionName, returnType, sourceDb, targetDb, result);
        }
    }

    /**
     * 验证CREATE TRIGGER语句
     */
    private void validateCreateTriggerStatements(String sql, String sourceDb, String targetDb, StrictValidationResult result) {
        Matcher matcher = createTriggerPattern.matcher(sql);
        while (matcher.find()) {
            String triggerName = matcher.group(1).replaceAll("[`\"']", "");
            String timing = matcher.group(2);
            String event = matcher.group(3);
            String tableName = matcher.group(4).replaceAll("[`\"']", "");
            String body = matcher.group(5);

            // 验证触发器名长度
            validateTriggerName(triggerName, sourceDb, targetDb, result);

            // 验证表名
            validateTableNamePattern(tableName, result);

            // 验证每表触发器数量限制
            validateTriggerCountPerTable(tableName, sourceDb, targetDb, result);
        }
    }

    /**
     * 验证索引列数限制
     */
    private void validateIndexColumnCount(String tableName, String indexName, String columnList, String sourceDb, String targetDb, StrictValidationResult result) {
        if ("mysql".equalsIgnoreCase(sourceDb)) {
            String[] columns = columnList.split(",");
            int columnCount = columns.length;

            int maxKeyParts = Integer.parseInt(config.getProperty("validation.mysql.max_key_parts", "16"));
            if (columnCount > maxKeyParts) {
                result.addError(String.format(
                    "索引 %s (表 %s): 列数 %d 超过MySQL 8.4官方文档规定的最大值 %d",
                    indexName, tableName, columnCount, maxKeyParts
                ));
                result.addSuggestion(String.format(
                    "索引 %s: 建议减少索引列数或创建多个单列索引",
                    indexName
                ));
            }
        }
    }

    /**
     * 验证索引键长度限制
     */
    private void validateIndexKeyLength(String tableName, String indexName, String columnList, String sourceDb, String targetDb, StrictValidationResult result) {
        if ("mysql".equalsIgnoreCase(sourceDb)) {
            // 这是一个简化的检查，实际的键长度计算需要考虑字符集、数据类型等
            int dynamicMaxLength = Integer.parseInt(config.getProperty("validation.mysql.max_key_length_dynamic", "3072"));
            int compactMaxLength = Integer.parseInt(config.getProperty("validation.mysql.max_key_length_compact", "767"));

            result.addWarning(String.format(
                "索引 %s (表 %s): 请确保索引键长度不超过 %d 字节(动态行格式)或 %d 字节(紧凑行格式)",
                indexName, tableName, dynamicMaxLength, compactMaxLength
            ));
        }
    }

    /**
     * 验证视图列数限制
     */
    private void validateViewColumnCount(String viewName, String columnList, String sourceDb, String targetDb, StrictValidationResult result) {
        if ("mysql".equalsIgnoreCase(sourceDb)) {
            String[] columns = columnList.split(",");
            int columnCount = columns.length;

            int maxViewColumns = Integer.parseInt(config.getProperty("validation.mysql.max_view_columns", "4096"));
            if (columnCount > maxViewColumns) {
                result.addError(String.format(
                    "视图 %s: 列数 %d 超过MySQL 8.4官方文档规定的最大值 %d",
                    viewName, columnCount, maxViewColumns
                ));
            }
        }
    }

    /**
     * 验证存储过程名长度
     */
    private void validateProcedureName(String procedureName, String sourceDb, String targetDb, StrictValidationResult result) {
        if ("mysql".equalsIgnoreCase(sourceDb)) {
            int maxLength = Integer.parseInt(config.getProperty("validation.mysql.procedure_name_max_length", "64"));
            if (procedureName.length() > maxLength) {
                result.addError(String.format(
                    "存储过程名 %s: 长度 %d 超过MySQL 8.4官方文档规定的最大值 %d",
                    procedureName, procedureName.length(), maxLength
                ));
            }
        }
    }

    /**
     * 验证存储过程参数数量限制
     */
    private void validateProcedureParameterCount(String procedureName, String parameters, String sourceDb, String targetDb, StrictValidationResult result) {
        if ("mysql".equalsIgnoreCase(sourceDb)) {
            String[] params = parameters.split(",");
            int paramCount = params.length;

            int maxParams = Integer.parseInt(config.getProperty("validation.mysql.max_procedure_parameters", "65535"));
            if (paramCount > maxParams) {
                result.addError(String.format(
                    "存储过程 %s: 参数数量 %d 超过MySQL 8.4官方文档规定的最大值 %d",
                    procedureName, paramCount, maxParams
                ));
            }
        }
    }

    /**
     * 验证函数名长度
     */
    private void validateFunctionName(String functionName, String sourceDb, String targetDb, StrictValidationResult result) {
        if ("mysql".equalsIgnoreCase(sourceDb)) {
            int maxLength = Integer.parseInt(config.getProperty("validation.mysql.function_name_max_length", "64"));
            if (functionName.length() > maxLength) {
                result.addError(String.format(
                    "函数名 %s: 长度 %d 超过MySQL 8.4官方文档规定的最大值 %d",
                    functionName, functionName.length(), maxLength
                ));
            }
        }
    }

    /**
     * 验证函数参数数量限制
     */
    private void validateFunctionParameterCount(String functionName, String parameters, String sourceDb, String targetDb, StrictValidationResult result) {
        if ("mysql".equalsIgnoreCase(sourceDb)) {
            String[] params = parameters.split(",");
            int paramCount = params.length;

            int maxParams = Integer.parseInt(config.getProperty("validation.mysql.max_procedure_parameters", "65535"));
            if (paramCount > maxParams) {
                result.addError(String.format(
                    "函数 %s: 参数数量 %d 超过MySQL 8.4官方文档规定的最大值 %d",
                    functionName, paramCount, maxParams
                ));
            }
        }
    }

    /**
     * 验证返回类型
     */
    private void validateReturnType(String functionName, String returnType, String sourceDb, String targetDb, StrictValidationResult result) {
        // 基本的返回类型验证
        if (returnType == null || returnType.trim().isEmpty()) {
            result.addError(String.format(
                "函数 %s: 缺少RETURNS子句",
                functionName
            ));
        }
    }

    /**
     * 验证触发器名长度
     */
    private void validateTriggerName(String triggerName, String sourceDb, String targetDb, StrictValidationResult result) {
        if ("mysql".equalsIgnoreCase(sourceDb)) {
            int maxLength = Integer.parseInt(config.getProperty("validation.mysql.trigger_name_max_length", "64"));
            if (triggerName.length() > maxLength) {
                result.addError(String.format(
                    "触发器名 %s: 长度 %d 超过MySQL 8.4官方文档规定的最大值 %d",
                    triggerName, triggerName.length(), maxLength
                ));
            }
        }
    }

    /**
     * 验证每表触发器数量限制
     */
    private void validateTriggerCountPerTable(String tableName, String sourceDb, String targetDb, StrictValidationResult result) {
        if ("mysql".equalsIgnoreCase(sourceDb)) {
            int maxTriggers = Integer.parseInt(config.getProperty("validation.mysql.max_triggers_per_table", "6"));
            result.addWarning(String.format(
                "表 %s: 请确保触发器数量不超过 %d 个(每个事件类型最多2个：BEFORE和AFTER)",
                tableName, maxTriggers
            ));
        }
    }

    /**
     * 验证神通数据库特有限制
     */
    private void validateShentongSpecificLimits(String tableName, String columnName, String dataType, String dataTypeAndModifiers, String sourceDb, String targetDb, StrictValidationResult result) {
        String upperDataType = dataType.toUpperCase();

        // 验证标识符长度限制
        int maxIdentifierLength = Integer.parseInt(config.getProperty("validation.shentong.max_identifier_length", "127"));
        if (tableName.length() > maxIdentifierLength) {
            result.addError(String.format(
                "表名 %s: 长度 %d 超过神通数据库的最大标识符长度 %d",
                tableName, tableName.length(), maxIdentifierLength
            ));
        }
        if (columnName.length() > maxIdentifierLength) {
            result.addError(String.format(
                "列名 %s: 长度 %d 超过神通数据库的最大标识符长度 %d",
                columnName, columnName.length(), maxIdentifierLength
            ));
        }

        // 验证保留前缀
        String reservedPrefixes = config.getProperty("validation.shentong.reserved_prefixes", "SYS_,V_SYS_");
        String[] prefixes = reservedPrefixes.split(",");
        for (String prefix : prefixes) {
            if (tableName.toUpperCase().startsWith(prefix.trim())) {
                result.addError(String.format(
                    "表名 %s: 不能使用保留前缀 %s，这会与神通数据库系统对象冲突",
                    tableName, prefix.trim()
                ));
            }
            if (columnName.toUpperCase().startsWith(prefix.trim())) {
                result.addError(String.format(
                    "列名 %s: 不能使用保留前缀 %s，这会与神通数据库系统对象冲突",
                    columnName, prefix.trim()
                ));
            }
        }

        // 验证无效字符
        String invalidChars = config.getProperty("validation.shentong.invalid_identifier_chars", "$,#");
        String[] chars = invalidChars.split(",");
        for (String invalidChar : chars) {
            if (tableName.contains(invalidChar.trim())) {
                result.addWarning(String.format(
                    "表名 %s: 包含字符 %s，神通数据库建议标识符中不要含有此字符",
                    tableName, invalidChar.trim()
                ));
            }
            if (columnName.contains(invalidChar.trim())) {
                result.addWarning(String.format(
                    "列名 %s: 包含字符 %s，神通数据库建议标识符中不要含有此字符",
                    columnName, invalidChar.trim()
                ));
            }
        }

        // 验证AUTO_INCREMENT限制
        if (dataTypeAndModifiers.toUpperCase().contains("AUTO_INCREMENT")) {
            String supportedTypes = config.getProperty("validation.shentong.auto_increment_supported_types", "INT,BIGINT,FLOAT");
            String[] types = supportedTypes.split(",");
            boolean isSupported = false;
            for (String type : types) {
                if (upperDataType.startsWith(type.trim())) {
                    isSupported = true;
                    break;
                }
            }
            if (!isSupported) {
                result.addError(String.format(
                    "列 %s.%s: 神通数据库的AUTO_INCREMENT只支持 %s 类型，当前类型为 %s",
                    tableName, columnName, supportedTypes, upperDataType
                ));
            }
        }
    }

    /**
     * 验证金仓数据库特有限制
     */
    private void validateKingbaseSpecificLimits(String tableName, String columnName, String dataType, String dataTypeAndModifiers, String sourceDb, String targetDb, StrictValidationResult result) {
        String upperDataType = dataType.toUpperCase();

        // 验证标识符长度限制
        int maxIdentifierLength = Integer.parseInt(config.getProperty("validation.kingbase.max_identifier_length", "63"));
        if (tableName.length() > maxIdentifierLength) {
            result.addError(String.format(
                "表名 %s: 长度 %d 超过金仓数据库的最大标识符长度 %d",
                tableName, tableName.length(), maxIdentifierLength
            ));
        }
        if (columnName.length() > maxIdentifierLength) {
            result.addError(String.format(
                "列名 %s: 长度 %d 超过金仓数据库的最大标识符长度 %d",
                columnName, columnName.length(), maxIdentifierLength
            ));
        }

        // 验证MySQL兼容类型
        String compatibleTypes = config.getProperty("validation.kingbase.mysql_compatible_types", "TINYINT,MEDIUMINT,LONGTEXT,MEDIUMTEXT,TINYTEXT");
        String[] types = compatibleTypes.split(",");
        boolean isCompatible = false;
        for (String type : types) {
            if (upperDataType.startsWith(type.trim())) {
                isCompatible = true;
                result.addSuggestion(String.format(
                    "列 %s.%s: 使用了MySQL兼容类型 %s，金仓数据库提供原生支持",
                    tableName, columnName, type.trim()
                ));
                break;
            }
        }
    }

    /**
     * 验证字符集和排序规则
     */
    private void validateCharsetAndCollation(String sql, String sourceDb, String targetDb, StrictValidationResult result) {
        if (!"mysql".equalsIgnoreCase(sourceDb)) {
            return;
        }

        // 检查字符集定义
        Pattern charsetPattern = Pattern.compile("CHARACTER\\s+SET\\s+(\\w+)", Pattern.CASE_INSENSITIVE);
        Matcher charsetMatcher = charsetPattern.matcher(sql);
        while (charsetMatcher.find()) {
            String charset = charsetMatcher.group(1);
            int maxCharsetLength = Integer.parseInt(config.getProperty("validation.mysql.max_charset_name_length", "32"));
            if (charset.length() > maxCharsetLength) {
                result.addError(String.format(
                    "字符集名 %s: 长度 %d 超过MySQL 8.4官方文档规定的最大值 %d",
                    charset, charset.length(), maxCharsetLength
                ));
            }
        }

        // 检查排序规则定义
        Pattern collationPattern = Pattern.compile("COLLATE\\s+(\\w+)", Pattern.CASE_INSENSITIVE);
        Matcher collationMatcher = collationPattern.matcher(sql);
        while (collationMatcher.find()) {
            String collation = collationMatcher.group(1);
            int maxCollationLength = Integer.parseInt(config.getProperty("validation.mysql.max_collation_name_length", "32"));
            if (collation.length() > maxCollationLength) {
                result.addError(String.format(
                    "排序规则名 %s: 长度 %d 超过MySQL 8.4官方文档规定的最大值 %d",
                    collation, collation.length(), maxCollationLength
                ));
            }
        }

        // 检查无效的字符集排序规则组合
        String invalidCombinations = config.getProperty("validation.mysql.invalid_charset_collation_combinations", "utf8_bin,utf8mb3_bin");
        String[] combinations = invalidCombinations.split(",");
        for (String combination : combinations) {
            if (sql.toUpperCase().contains(combination.trim().toUpperCase())) {
                result.addWarning(String.format(
                    "检测到可能过时的字符集排序规则组合: %s，建议使用utf8mb4相关排序规则",
                    combination.trim()
                ));
            }
        }
    }

    /**
     * 验证JSON相关限制
     */
    private void validateJsonLimits(String sql, String sourceDb, String targetDb, StrictValidationResult result) {
        if (!"mysql".equalsIgnoreCase(sourceDb)) {
            return;
        }

        // 检查JSON列定义
        Pattern jsonPattern = Pattern.compile("\\b(\\w+)\\s+JSON\\b", Pattern.CASE_INSENSITIVE);
        Matcher jsonMatcher = jsonPattern.matcher(sql);
        while (jsonMatcher.find()) {
            String columnName = jsonMatcher.group(1);

            // JSON类型限制提示
            int maxDepth = Integer.parseInt(config.getProperty("validation.mysql.json_max_depth", "100"));
            int maxKeyLength = Integer.parseInt(config.getProperty("validation.mysql.json_max_key_length", "65535"));

            result.addSuggestion(String.format(
                "JSON列 %s: 请注意JSON文档最大嵌套深度为 %d，键名最大长度为 %d 字符",
                columnName, maxDepth, maxKeyLength
            ));

            // 检查目标数据库JSON支持
            if ("dameng".equalsIgnoreCase(targetDb)) {
                result.addWarning(String.format(
                    "JSON列 %s: 达梦数据库对JSON支持有限，建议转换为TEXT类型并在应用层处理",
                    columnName
                ));
            } else if ("shentong".equalsIgnoreCase(targetDb)) {
                result.addWarning(String.format(
                    "JSON列 %s: 神通数据库不支持JSON类型，建议转换为TEXT类型",
                    columnName
                ));
            }
        }
    }

    /**
     * 验证GEOMETRY相关限制
     */
    private void validateGeometryLimits(String sql, String sourceDb, String targetDb, StrictValidationResult result) {
        if (!"mysql".equalsIgnoreCase(sourceDb)) {
            return;
        }

        // 检查空间数据类型
        Pattern geometryPattern = Pattern.compile("\\b(\\w+)\\s+(GEOMETRY|POINT|LINESTRING|POLYGON|MULTIPOINT|MULTILINESTRING|MULTIPOLYGON|GEOMETRYCOLLECTION)\\b", Pattern.CASE_INSENSITIVE);
        Matcher geometryMatcher = geometryPattern.matcher(sql);
        while (geometryMatcher.find()) {
            String columnName = geometryMatcher.group(1);
            String geometryType = geometryMatcher.group(2);

            // 空间数据类型限制提示
            int maxPoints = Integer.parseInt(config.getProperty("validation.mysql.geometry_max_points", "65535"));

            result.addSuggestion(String.format(
                "空间数据列 %s (%s): 请注意几何对象最大点数为 %d",
                columnName, geometryType, maxPoints
            ));

            // 检查目标数据库空间数据支持
            if ("dameng".equalsIgnoreCase(targetDb)) {
                result.addSuggestion(String.format(
                    "空间数据列 %s: 达梦数据库支持空间数据类型，但语法可能有差异",
                    columnName
                ));
            } else if ("shentong".equalsIgnoreCase(targetDb)) {
                result.addWarning(String.format(
                    "空间数据列 %s: 神通数据库对空间数据支持有限，建议评估替代方案",
                    columnName
                ));
            } else if ("kingbase".equalsIgnoreCase(targetDb)) {
                result.addSuggestion(String.format(
                    "空间数据列 %s: 金仓数据库通过PostGIS扩展支持空间数据",
                    columnName
                ));
            }
        }

        // 检查空间索引
        Pattern spatialIndexPattern = Pattern.compile("SPATIAL\\s+(?:INDEX|KEY)\\s+(\\w+)", Pattern.CASE_INSENSITIVE);
        Matcher spatialIndexMatcher = spatialIndexPattern.matcher(sql);
        while (spatialIndexMatcher.find()) {
            String indexName = spatialIndexMatcher.group(1);

            int maxKeyLength = Integer.parseInt(config.getProperty("validation.mysql.spatial_index_max_key_length", "3072"));
            result.addSuggestion(String.format(
                "空间索引 %s: 请注意空间索引键最大长度为 %d 字节",
                indexName, maxKeyLength
            ));

            // 检查目标数据库空间索引支持
            if ("dameng".equalsIgnoreCase(targetDb) || "shentong".equalsIgnoreCase(targetDb)) {
                result.addWarning(String.format(
                    "空间索引 %s: 目标数据库可能不支持SPATIAL INDEX，建议使用普通索引替代",
                    indexName
                ));
            }
        }
    }

    /**
     * 验证目标数据库特有的表级别限制
     */
    private void validateTableLevelLimits(String tableName, String sourceDb, String targetDb, StrictValidationResult result) {
        // 验证神通数据库特有的表级别限制
        if ("shentong".equalsIgnoreCase(targetDb)) {
            validateShentongTableLimits(tableName, sourceDb, targetDb, result);
        }

        // 验证金仓数据库特有的表级别限制
        if ("kingbase".equalsIgnoreCase(targetDb)) {
            validateKingbaseTableLimits(tableName, sourceDb, targetDb, result);
        }
    }

    /**
     * 验证神通数据库特有的表级别限制
     */
    private void validateShentongTableLimits(String tableName, String sourceDb, String targetDb, StrictValidationResult result) {
        // 验证标识符长度限制
        int maxIdentifierLength = Integer.parseInt(config.getProperty("validation.shentong.max_identifier_length", "127"));
        if (tableName.length() > maxIdentifierLength) {
            result.addError(String.format(
                "表名 %s: 长度 %d 超过神通数据库的最大标识符长度 %d",
                tableName, tableName.length(), maxIdentifierLength
            ));
        }

        // 验证保留前缀
        String reservedPrefixes = config.getProperty("validation.shentong.reserved_prefixes", "SYS_,V_SYS_");
        String[] prefixes = reservedPrefixes.split(",");
        for (String prefix : prefixes) {
            if (tableName.toUpperCase().startsWith(prefix.trim())) {
                result.addError(String.format(
                    "表名 %s: 不能使用保留前缀 %s，这会与神通数据库系统对象冲突",
                    tableName, prefix.trim()
                ));
            }
        }

        // 验证无效字符
        String invalidChars = config.getProperty("validation.shentong.invalid_identifier_chars", "$,#");
        String[] chars = invalidChars.split(",");
        for (String invalidChar : chars) {
            if (tableName.contains(invalidChar.trim())) {
                result.addWarning(String.format(
                    "表名 %s: 包含字符 %s，神通数据库建议标识符中不要含有此字符",
                    tableName, invalidChar.trim()
                ));
            }
        }
    }

    /**
     * 验证金仓数据库特有的表级别限制
     */
    private void validateKingbaseTableLimits(String tableName, String sourceDb, String targetDb, StrictValidationResult result) {
        // 验证标识符长度限制
        int maxIdentifierLength = Integer.parseInt(config.getProperty("validation.kingbase.max_identifier_length", "63"));
        if (tableName.length() > maxIdentifierLength) {
            result.addError(String.format(
                "表名 %s: 长度 %d 超过金仓数据库的最大标识符长度 %d",
                tableName, tableName.length(), maxIdentifierLength
            ));
        }
    }

    /**
     * 验证保留字使用
     */
    private void validateReservedWords(String sql, String sourceDatabase, String targetDatabase, StrictValidationResult result) {
        // 检查是否启用保留字验证
        boolean validateReservedWords = Boolean.parseBoolean(
            config.getProperty("validation.rules.validate_reserved_words", "true"));

        if (!validateReservedWords) {
            return;
        }

        // 获取保留字列表
        String sourceReservedWords = config.getProperty("validation." + sourceDatabase + ".reserved_words", "");
        String targetReservedWords = config.getProperty("validation." + targetDatabase + ".reserved_words", "");

        if (sourceReservedWords.isEmpty() && targetReservedWords.isEmpty()) {
            return;
        }

        // 解析保留字
        Set<String> sourceWords = parseReservedWords(sourceReservedWords);
        Set<String> targetWords = parseReservedWords(targetReservedWords);

        // 获取引用字符
        String sourceQuoteChar = config.getProperty("validation.reserved_words.quote_char." + sourceDatabase, "`");
        String targetQuoteChar = config.getProperty("validation.reserved_words.quote_char." + targetDatabase, "\"");

        // 检测SQL中的标识符
        validateIdentifiersInSql(sql, sourceWords, targetWords, sourceDatabase, targetDatabase,
                                sourceQuoteChar, targetQuoteChar, result);
    }

    /**
     * 解析保留字字符串为Set
     */
    private Set<String> parseReservedWords(String reservedWordsStr) {
        if (reservedWordsStr == null || reservedWordsStr.trim().isEmpty()) {
            return new HashSet<>();
        }

        return Arrays.stream(reservedWordsStr.split(","))
                .map(String::trim)
                .map(String::toUpperCase)
                .collect(Collectors.toSet());
    }

    /**
     * 验证SQL中的标识符是否为保留字
     */
    private void validateIdentifiersInSql(String sql, Set<String> sourceWords, Set<String> targetWords,
                                        String sourceDatabase, String targetDatabase,
                                        String sourceQuoteChar, String targetQuoteChar,
                                        StrictValidationResult result) {

        // 判断SQL语句类型，避免错误的保留字检测
        String upperSql = sql.trim().toUpperCase();

        // 只对SELECT语句检测列别名，不对CREATE TABLE等DDL语句检测
        if (upperSql.startsWith("SELECT")) {
            // 检测SELECT语句中的列别名
            validateSelectStatementReservedWords(sql, sourceWords, targetWords,
                                               sourceDatabase, targetDatabase, sourceQuoteChar, targetQuoteChar, result);
        } else if (upperSql.startsWith("CREATE TABLE")) {
            // 检测CREATE TABLE中的表名和列名（已有专门的方法处理）
            validateCreateTableReservedWords(sql, sourceWords, targetWords,
                                           sourceDatabase, targetDatabase, sourceQuoteChar, targetQuoteChar, result);
        }
        // 其他类型的SQL语句暂不进行保留字检测，避免误报
    }

    /**
     * 检查单个标识符的保留字冲突
     */
    private void checkReservedWordConflict(String identifier, String context,
                                         Set<String> sourceWords, Set<String> targetWords,
                                         String sourceDatabase, String targetDatabase,
                                         String sourceQuoteChar, String targetQuoteChar,
                                         StrictValidationResult result) {
        if (identifier == null || identifier.trim().isEmpty()) {
            return;
        }

        // 如果已经被引用，则不需要检查
        if (isQuoted(identifier)) {
            return;
        }

        String upperIdentifier = identifier.toUpperCase();

        // 检查源数据库保留字
        if (sourceWords.contains(upperIdentifier)) {
            String suggestedFix = sourceQuoteChar + identifier + sourceQuoteChar;
            result.addWarning(String.format(
                "%s保留字 '%s' 用作%s时应该用%s括起来。建议修复: %s -> %s",
                getDatabaseDisplayName(sourceDatabase), identifier, context,
                sourceQuoteChar, identifier, suggestedFix
            ));
        }

        // 检查目标数据库保留字
        if (targetWords.contains(upperIdentifier)) {
            String suggestedFix = targetQuoteChar + identifier + targetQuoteChar;
            result.addWarning(String.format(
                "%s保留字 '%s' 用作%s时应该用%s括起来。建议修复: %s -> %s",
                getDatabaseDisplayName(targetDatabase), identifier, context,
                targetQuoteChar, identifier, suggestedFix
            ));
        }
    }

    /**
     * 验证SELECT语句中的保留字
     * 根据MySQL官方文档：https://dev.mysql.com/doc/refman/8.4/en/select.html
     */
    private void validateSelectStatementReservedWords(String sql, Set<String> sourceWords, Set<String> targetWords,
                                                    String sourceDatabase, String targetDatabase,
                                                    String sourceQuoteChar, String targetQuoteChar,
                                                    StrictValidationResult result) {

        // 检测列别名中的保留字 - 只在SELECT语句中有效
        Pattern columnAliasPattern = Pattern.compile(
            "\\s+(?:AS\\s+)?(\\w+)\\s*(?:,|FROM|$|\\)|\\s+(?:FROM|WHERE|ORDER|GROUP|HAVING|LIMIT))",
            Pattern.CASE_INSENSITIVE
        );

        Matcher aliasMatcher = columnAliasPattern.matcher(sql);
        while (aliasMatcher.find()) {
            String identifier = aliasMatcher.group(1);
            // 排除SQL关键字，避免误报
            if (!isSqlKeyword(identifier)) {
                checkReservedWordConflict(identifier, "列别名", sourceWords, targetWords,
                                        sourceDatabase, targetDatabase, sourceQuoteChar, targetQuoteChar, result);
            }
        }

        // 检测SELECT列表中的保留字
        Pattern columnNamePattern = Pattern.compile(
            "SELECT\\s+(?:[^,]+,\\s*)*(\\w+)(?:\\s*,|\\s+FROM)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher columnMatcher = columnNamePattern.matcher(sql);
        while (columnMatcher.find()) {
            String identifier = columnMatcher.group(1);
            // 排除SQL关键字，避免误报
            if (!isSqlKeyword(identifier)) {
                checkReservedWordConflict(identifier, "列名", sourceWords, targetWords,
                                        sourceDatabase, targetDatabase, sourceQuoteChar, targetQuoteChar, result);
            }
        }
    }

    /**
     * 验证CREATE TABLE语句中的保留字
     * 根据MySQL官方文档：https://dev.mysql.com/doc/refman/8.4/en/create-table.html
     */
    private void validateCreateTableReservedWords(String sql, Set<String> sourceWords, Set<String> targetWords,
                                                String sourceDatabase, String targetDatabase,
                                                String sourceQuoteChar, String targetQuoteChar,
                                                StrictValidationResult result) {
        // 简化的CREATE TABLE解析 - 检测表名
        Pattern tableNamePattern = Pattern.compile(
            "CREATE\\s+TABLE\\s+(?:IF\\s+NOT\\s+EXISTS\\s+)?(?:[^.]+\\.)?([^\\s(]+)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher tableMatcher = tableNamePattern.matcher(sql);
        if (tableMatcher.find()) {
            String tableName = tableMatcher.group(1).replaceAll("[`\"']", "");
            checkReservedWordConflict(tableName, "表名", sourceWords, targetWords,
                                    sourceDatabase, targetDatabase, sourceQuoteChar, targetQuoteChar, result);
        }

        // 注意：CREATE TABLE中的列名检测已经在validateCreateTableStatements方法中处理
        // 这里不再重复检测，避免与DDL解析逻辑冲突
    }

    /**
     * 检查标识符是否是SQL语法关键字
     * 根据MySQL官方文档：https://dev.mysql.com/doc/refman/8.4/en/keywords.html
     * 这些关键字是SQL语法的一部分，不应该被当作用户定义的标识符来检测
     */
    private boolean isSqlKeyword(String identifier) {
        if (identifier == null || identifier.trim().isEmpty()) {
            return false;
        }

        String upperIdentifier = identifier.toUpperCase();

        // 使用统一的SQL语法关键字管理系统
        // 这些是SQL语法的一部分，不是用户定义的标识符
        return com.xylink.sqltranspiler.common.constants.SqlKeywords.isSqlKeyword(upperIdentifier);
    }

    /**
     * 检查标识符是否已被引用
     */
    private boolean isQuoted(String identifier) {
        if (identifier == null || identifier.length() < 2) {
            return false;
        }

        return (identifier.startsWith("`") && identifier.endsWith("`")) ||
               (identifier.startsWith("\"") && identifier.endsWith("\"")) ||
               (identifier.startsWith("'") && identifier.endsWith("'"));
    }

    /**
     * 获取数据库显示名称
     */
    private String getDatabaseDisplayName(String database) {
        if (database == null) return "未知";

        switch (database.toLowerCase()) {
            case "mysql": return "MySQL";
            case "dameng": return "达梦";
            case "shentong": return "神通";
            case "kingbase": return "金仓";
            default: return database;
        }
    }

    /**
     * 验证窗口函数语法 - 基于MySQL 8.4官方文档
     */
    private void validateWindowFunctions(String sql, String sourceDb, String targetDb, StrictValidationResult result) {
        if (!"mysql".equalsIgnoreCase(sourceDb)) {
            return;
        }

        // 检查动态校验规则是否启用
        boolean enableMySQLSpecific = Boolean.parseBoolean(
            config.getProperty("validation.dynamic.enable_mysql_specific", "true")
        );
        if (!enableMySQLSpecific) {
            return;
        }

        // 检查窗口函数语法
        Pattern windowFunctionPattern = Pattern.compile(
            "\\b(ROW_NUMBER|RANK|DENSE_RANK|PERCENT_RANK|CUME_DIST|NTILE|LAG|LEAD|FIRST_VALUE|LAST_VALUE|NTH_VALUE)\\s*\\(.*?\\)\\s+OVER\\s*\\(",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
        );

        Matcher matcher = windowFunctionPattern.matcher(sql);
        while (matcher.find()) {
            String functionName = matcher.group(1);

            // 验证窗口函数是否在支持列表中
            String supportedFunctions = config.getProperty("validation.mysql.window_functions", "");
            if (!supportedFunctions.toUpperCase().contains(functionName.toUpperCase())) {
                result.addWarning(String.format(
                    "窗口函数 %s: 可能不被MySQL 8.4官方文档支持，请确认语法正确性",
                    functionName
                ));
            }

            // 检查目标数据库兼容性
            if ("dameng".equalsIgnoreCase(targetDb)) {
                result.addSuggestion(String.format(
                    "窗口函数 %s: 达梦数据库支持窗口函数，转换时请确认语法兼容性",
                    functionName
                ));
            } else if ("shentong".equalsIgnoreCase(targetDb)) {
                result.addWarning(String.format(
                    "窗口函数 %s: 神通数据库对窗口函数支持有限，建议使用替代方案",
                    functionName
                ));
            } else if ("kingbase".equalsIgnoreCase(targetDb)) {
                result.addSuggestion(String.format(
                    "窗口函数 %s: 金仓数据库支持窗口函数，转换时语法基本兼容",
                    functionName
                ));
            }
        }
    }

    /**
     * 验证CTE递归深度 - 基于MySQL 8.4官方文档
     * 根据MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/with.html
     * CTE递归语法是MySQL 8.0.1+支持的标准功能，不应产生错误
     */
    private void validateCTERecursion(String sql, String sourceDb, String targetDb, StrictValidationResult result) {
        if (!"mysql".equalsIgnoreCase(sourceDb)) {
            return;
        }

        // 检查动态校验规则是否启用
        boolean enableMySQLSpecific = Boolean.parseBoolean(
            config.getProperty("validation.dynamic.enable_mysql_specific", "true")
        );
        if (!enableMySQLSpecific) {
            return;
        }

        // 检查递归CTE - 根据MySQL 8.4官方文档，这是正常语法，不应产生错误
        Pattern recursiveCTEPattern = Pattern.compile(
            "\\bWITH\\s+RECURSIVE\\s+(\\w+)\\s+AS\\s*\\(",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = recursiveCTEPattern.matcher(sql);
        while (matcher.find()) {
            String cteName = matcher.group(1);

            // 获取最大递归深度限制 - 根据MySQL 8.4官方文档，默认为1000
            int maxDepth = Integer.parseInt(config.getProperty("validation.mysql.max_cte_recursion_depth", "1000"));

            // 只在目标数据库不支持时给出警告，MySQL本身的CTE语法是正确的
            if ("dameng".equalsIgnoreCase(targetDb)) {
                result.addWarning(String.format(
                    "递归CTE %s: 达梦数据库对递归CTE支持有限，建议使用存储过程或循环替代",
                    cteName
                ));
            } else if ("shentong".equalsIgnoreCase(targetDb)) {
                result.addWarning(String.format(
                    "递归CTE %s: 神通数据库不支持递归CTE，建议使用CONNECT BY语法",
                    cteName
                ));
            } else if ("kingbase".equalsIgnoreCase(targetDb)) {
                result.addSuggestion(String.format(
                    "递归CTE %s: 金仓数据库支持递归CTE，转换时语法基本兼容",
                    cteName
                ));
            }

            // 对于MySQL源数据库，只提供信息性建议，不产生错误
            result.addSuggestion(String.format(
                "递归CTE %s: MySQL 8.4官方文档规定的最大递归深度为 %d 层，可通过cte_max_recursion_depth系统变量调整",
                cteName, maxDepth
            ));
        }
    }

    /**
     * 验证SQL注入安全 - 基于安全最佳实践
     */
    private void validateSQLSecurity(String sql, String sourceDb, String targetDb, StrictValidationResult result) {
        // 检查校验规则优先级
        int securityPriority = Integer.parseInt(config.getProperty("validation.priority.security", "1"));
        if (securityPriority > 3) { // 优先级太低，跳过安全检查
            return;
        }

        // 检查危险函数 - 只检测真正的函数调用，不检测字段名
        String dangerousFunctions = config.getProperty("validation.security.dangerous_functions", "");
        String[] functions = dangerousFunctions.split(",");
        for (String function : functions) {
            String functionName = function.trim().toUpperCase();
            // 使用更精确的模式匹配，确保是函数调用而不是字段名
            Pattern functionPattern = Pattern.compile(
                "\\b" + Pattern.quote(functionName) + "\\s*\\(",
                Pattern.CASE_INSENSITIVE
            );
            if (functionPattern.matcher(sql).find()) {
                result.addError(String.format(
                    "安全风险: 检测到危险函数 %s，可能存在安全隐患",
                    function.trim()
                ));

                String fixSuggestion = config.getProperty("validation.fix.DANGEROUS_FUNCTION_USAGE",
                    "避免使用危险函数，使用安全的替代方案");
                result.addSuggestion("修复建议: " + fixSuggestion);
            }
        }

        // 检查可疑模式 - 排除CTE中的正常UNION语法
        String suspiciousPatterns = config.getProperty("validation.security.suspicious_patterns", "");
        String[] patterns = suspiciousPatterns.split(",");
        for (String pattern : patterns) {
            try {
                Pattern regex = Pattern.compile(pattern.trim(), Pattern.CASE_INSENSITIVE);
                if (regex.matcher(sql).find()) {
                    // 特殊处理：排除CTE中的正常UNION语法
                    if (pattern.trim().contains("UNION") && isCTEUnion(sql)) {
                        // 这是CTE中的正常UNION语法，不是SQL注入
                        continue;
                    }

                    result.addError(String.format(
                        "安全风险: 检测到可疑的SQL注入模式 %s",
                        pattern.trim()
                    ));

                    String fixSuggestion = config.getProperty("validation.fix.SQL_INJECTION_RISK",
                        "使用参数化查询或预处理语句");
                    result.addSuggestion("修复建议: " + fixSuggestion);
                }
            } catch (Exception e) {
                // 忽略无效的正则表达式模式
                result.addWarning(String.format(
                    "配置错误: 安全校验模式 '%s' 不是有效的正则表达式",
                    pattern.trim()
                ));
            }
        }
    }

    /**
     * 检查UNION是否在CTE上下文中 - 基于MySQL 8.4官方文档
     * 根据MySQL官方文档，CTE中的UNION是正常语法，不应被视为SQL注入
     */
    private boolean isCTEUnion(String sql) {
        // 检查是否包含WITH子句（CTE的标志）
        Pattern ctePattern = Pattern.compile(
            "\\bWITH\\s+(RECURSIVE\\s+)?\\w+\\s+AS\\s*\\(",
            Pattern.CASE_INSENSITIVE
        );

        if (!ctePattern.matcher(sql).find()) {
            return false; // 没有CTE
        }

        // 检查UNION是否在CTE定义内部
        // 简单的启发式方法：如果SQL包含WITH...AS(...)并且UNION在其中，则认为是CTE中的UNION
        Pattern cteWithUnionPattern = Pattern.compile(
            "\\bWITH\\s+(RECURSIVE\\s+)?\\w+\\s+AS\\s*\\([^)]*UNION[^)]*\\)",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
        );

        return cteWithUnionPattern.matcher(sql).find();
    }

    /**
     * 验证性能优化 - 基于性能最佳实践
     */
    private void validatePerformanceIssues(String sql, String sourceDb, String targetDb, StrictValidationResult result) {
        // 检查校验规则优先级
        int performancePriority = Integer.parseInt(config.getProperty("validation.priority.performance", "3"));
        if (performancePriority > 4) { // 优先级太低，跳过性能检查
            return;
        }

        // 检查低效模式
        String inefficientPatterns = config.getProperty("validation.performance.inefficient_patterns", "");
        String[] patterns = inefficientPatterns.split(",");
        for (String pattern : patterns) {
            try {
                Pattern regex = Pattern.compile(pattern.trim(), Pattern.CASE_INSENSITIVE);
                if (regex.matcher(sql).find()) {
                    result.addWarning(String.format(
                        "性能问题: 检测到低效的SQL模式 %s",
                        pattern.trim()
                    ));

                    String fixSuggestion = config.getProperty("validation.fix.PERFORMANCE_ISSUE",
                        "优化查询语句，避免低效模式");
                    result.addSuggestion("修复建议: " + fixSuggestion);
                }
            } catch (Exception e) {
                // 忽略无效的正则表达式模式
                result.addWarning(String.format(
                    "配置错误: 性能校验模式 '%s' 不是有效的正则表达式",
                    pattern.trim()
                ));
            }
        }

        // 检查大结果集 - 改进逻辑，避免误报
        validateLargeResultSets(sql, result);
    }

    /**
     * 验证大结果集问题 - 改进的逻辑，避免误报
     * 只检测实际的SQL LIMIT/OFFSET子句，而不是任意文本中的关键字
     *
     * 基于MySQL 8.4官方文档：
     * https://dev.mysql.com/doc/refman/8.4/en/select.html
     *
     * MySQL官方文档描述：
     * "The LIMIT clause can be used to constrain the number of rows returned by the SELECT statement.
     *  LIMIT takes one or two numeric arguments, which must both be nonnegative integer constants"
     *
     * 性能最佳实践：
     * - 大的LIMIT值可能导致性能问题
     * - 大的OFFSET值在深度分页时效率低下
     * - 建议使用基于游标的分页方式替代大OFFSET
     */
    private void validateLargeResultSets(String sql, StrictValidationResult result) {
        // 移除注释和字符串字面量，避免误报
        String cleanSql = removeCommentsAndStrings(sql);

        // 检查大的LIMIT值 - 基于MySQL 8.4官方文档的动态验证
        // MySQL官方文档：LIMIT子句用于限制SELECT语句返回的行数
        // 参考：https://dev.mysql.com/doc/refman/8.4/en/select.html
        int largeLimitThreshold = Integer.parseInt(
            config.getProperty("validation.performance.large_limit_threshold", "1000")
        );

        Pattern limitPattern = Pattern.compile("\\bLIMIT\\s+([1-9][0-9]+)\\b", Pattern.CASE_INSENSITIVE);
        Matcher limitMatcher = limitPattern.matcher(cleanSql);
        if (limitMatcher.find()) {
            String limitValue = limitMatcher.group(1);
            int limitNum = Integer.parseInt(limitValue);
            if (limitNum >= largeLimitThreshold) {
                result.addWarning(String.format(
                    "性能问题: 检测到大结果集查询 LIMIT %s（阈值：%d），建议考虑分页或优化查询条件。" +
                    "参考MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/select.html",
                    limitValue, largeLimitThreshold
                ));
            }
        }

        // 检查大的OFFSET值 - 基于MySQL 8.4官方文档的动态验证
        // MySQL官方文档：OFFSET指定返回第一行的偏移量，初始行偏移量为0
        // 参考：https://dev.mysql.com/doc/refman/8.4/en/select.html
        int largeOffsetThreshold = Integer.parseInt(
            config.getProperty("validation.performance.large_offset_threshold", "1000")
        );

        Pattern offsetPattern = Pattern.compile("\\bOFFSET\\s+([1-9][0-9]+)\\b", Pattern.CASE_INSENSITIVE);
        Matcher offsetMatcher = offsetPattern.matcher(cleanSql);
        if (offsetMatcher.find()) {
            String offsetValue = offsetMatcher.group(1);
            int offsetNum = Integer.parseInt(offsetValue);
            if (offsetNum >= largeOffsetThreshold) {
                result.addWarning(String.format(
                    "性能问题: 检测到大偏移量 OFFSET %s（阈值：%d），建议使用基于游标的分页方式。" +
                    "MySQL官方文档说明：大OFFSET值在深度分页时效率低下。" +
                    "参考：https://dev.mysql.com/doc/refman/8.4/en/select.html",
                    offsetValue, largeOffsetThreshold
                ));
            }
        }

        // 检查没有LIMIT的SELECT语句（可能返回大结果集）
        if (cleanSql.toUpperCase().contains("SELECT") &&
            !cleanSql.toUpperCase().contains("LIMIT") &&
            !cleanSql.toUpperCase().contains("COUNT(")) {
            // 只对可能返回大量数据的查询发出建议
            if (cleanSql.toUpperCase().contains("JOIN") ||
                cleanSql.toUpperCase().contains("GROUP BY") ||
                cleanSql.toUpperCase().contains("ORDER BY")) {
                result.addSuggestion("性能建议: 复杂查询建议添加适当的LIMIT限制，避免返回过多数据");
            }
        }
    }

    /**
     * 移除SQL中的注释和字符串字面量，避免在验证时产生误报
     */
    private String removeCommentsAndStrings(String sql) {
        if (sql == null) return "";

        StringBuilder result = new StringBuilder();
        boolean inSingleQuote = false;
        boolean inDoubleQuote = false;
        boolean inLineComment = false;
        boolean inBlockComment = false;

        for (int i = 0; i < sql.length(); i++) {
            char c = sql.charAt(i);
            char next = (i + 1 < sql.length()) ? sql.charAt(i + 1) : '\0';

            if (inLineComment) {
                if (c == '\n' || c == '\r') {
                    inLineComment = false;
                    result.append(c); // 保留换行符
                }
                continue;
            }

            if (inBlockComment) {
                if (c == '*' && next == '/') {
                    inBlockComment = false;
                    i++; // 跳过 '/'
                }
                continue;
            }

            if (inSingleQuote) {
                if (c == '\'' && (i == 0 || sql.charAt(i - 1) != '\\')) {
                    inSingleQuote = false;
                }
                continue;
            }

            if (inDoubleQuote) {
                if (c == '"' && (i == 0 || sql.charAt(i - 1) != '\\')) {
                    inDoubleQuote = false;
                }
                continue;
            }

            // 检查注释开始
            if (c == '-' && next == '-') {
                inLineComment = true;
                i++; // 跳过第二个 '-'
                continue;
            }

            if (c == '/' && next == '*') {
                inBlockComment = true;
                i++; // 跳过 '*'
                continue;
            }

            // 处理MySQL的#注释
            if (c == '#') {
                inLineComment = true;
                continue;
            }

            // 检查字符串开始
            if (c == '\'') {
                inSingleQuote = true;
                continue;
            }

            if (c == '"') {
                inDoubleQuote = true;
                continue;
            }

            result.append(c);
        }

        return result.toString();
    }

    /**
     * 验证兼容性问题 - 基于跨数据库兼容性
     */
    private void validateCompatibilityIssues(String sql, String sourceDb, String targetDb, StrictValidationResult result) {
        // 检查校验规则优先级
        int compatibilityPriority = Integer.parseInt(config.getProperty("validation.priority.compatibility", "4"));
        if (compatibilityPriority > 4) { // 优先级太低，跳过兼容性检查
            return;
        }

        // 检查MySQL特有语法
        if ("mysql".equalsIgnoreCase(sourceDb) && !"mysql".equalsIgnoreCase(targetDb)) {
            String mysqlSpecific = config.getProperty("validation.compatibility.mysql_specific", "");
            String[] features = mysqlSpecific.split(",");
            for (String feature : features) {
                if (sql.toUpperCase().contains(feature.trim().toUpperCase())) {
                    result.addWarning(String.format(
                        "兼容性问题: 检测到MySQL特有语法 %s，在%s数据库中可能不兼容",
                        feature.trim(), getDatabaseDisplayName(targetDb)
                    ));

                    String fixSuggestion = config.getProperty("validation.fix.COMPATIBILITY_ISSUE",
                        "使用标准SQL语法或提供兼容性处理");
                    result.addSuggestion("修复建议: " + fixSuggestion);
                }
            }
        }

        // 检查Oracle特有语法
        String oracleSpecific = config.getProperty("validation.compatibility.oracle_specific", "");
        String[] oracleFeatures = oracleSpecific.split(",");
        for (String feature : oracleFeatures) {
            if (sql.toUpperCase().contains(feature.trim().toUpperCase())) {
                if ("shentong".equalsIgnoreCase(targetDb)) {
                    result.addSuggestion(String.format(
                        "兼容性提示: Oracle语法 %s 在神通数据库中通常兼容",
                        feature.trim()
                    ));
                } else {
                    result.addWarning(String.format(
                        "兼容性问题: 检测到Oracle特有语法 %s，在%s数据库中可能不兼容",
                        feature.trim(), getDatabaseDisplayName(targetDb)
                    ));
                }
            }
        }
    }
}
