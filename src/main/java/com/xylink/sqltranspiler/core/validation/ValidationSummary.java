package com.xylink.sqltranspiler.core.validation;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 验证结果摘要
 */
public class ValidationSummary {
    
    private final int totalValidations;
    private final int conflictCount;
    private final Map<String, List<VarcharConflict>> conflictsByTable;
    private final long validationTimeMs;
    private final boolean enabled;
    
    public ValidationSummary(List<VarcharConflict> conflicts, int totalValidations, 
                           long validationTimeMs, boolean enabled) {
        this.totalValidations = totalValidations;
        this.conflictCount = conflicts.size();
        this.conflictsByTable = conflicts.stream()
            .collect(Collectors.groupingBy(VarcharConflict::getTableName));
        this.validationTimeMs = validationTimeMs;
        this.enabled = enabled;
    }
    
    /**
     * 创建空的验证摘要（用于禁用验证时）
     */
    public static ValidationSummary empty() {
        return new ValidationSummary(Collections.emptyList(), 0, 0, false);
    }
    
    /**
     * 创建禁用状态的摘要
     */
    public static ValidationSummary disabled() {
        return new ValidationSummary(Collections.emptyList(), 0, 0, false);
    }
    
    // Getter方法
    public int getTotalValidations() {
        return totalValidations;
    }
    
    public int getConflictCount() {
        return conflictCount;
    }
    
    public Map<String, List<VarcharConflict>> getConflictsByTable() {
        return Collections.unmodifiableMap(conflictsByTable);
    }
    
    public long getValidationTimeMs() {
        return validationTimeMs;
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    /**
     * 是否有冲突
     */
    public boolean hasConflicts() {
        return conflictCount > 0;
    }
    
    /**
     * 获取所有冲突列表
     */
    public List<VarcharConflict> getAllConflicts() {
        return conflictsByTable.values().stream()
            .flatMap(List::stream)
            .collect(Collectors.toList());
    }
    
    /**
     * 获取受影响的表数量
     */
    public int getAffectedTableCount() {
        return conflictsByTable.size();
    }
    
    /**
     * 打印详细报告
     */
    public void printReport() {
        if (!enabled) {
            System.out.println("=== VARCHAR Length Validation: DISABLED ===");
            return;
        }
        
        System.out.println("=== VARCHAR Length Validation Report ===");
        System.out.printf("Total validations: %d%n", totalValidations);
        System.out.printf("Conflicts found: %d%n", conflictCount);
        System.out.printf("Affected tables: %d%n", getAffectedTableCount());
        System.out.printf("Validation time: %d ms%n", validationTimeMs);
        
        if (conflictCount > 0) {
            System.out.println("\nConflicts by table:");
            conflictsByTable.forEach((table, conflicts) -> {
                System.out.printf("  %s: %d conflicts%n", table, conflicts.size());
                conflicts.forEach(conflict -> 
                    System.out.printf("    - %s%n", conflict.getShortDescription()));
            });
            
            System.out.println("\nSuggested actions:");
            System.out.println("1. Review and increase VARCHAR lengths in CREATE TABLE statements");
            System.out.println("2. Consider using TEXT/CLOB for unlimited length fields");
            System.out.println("3. Validate and possibly truncate input data");
        } else {
            System.out.println("\n✅ No VARCHAR length conflicts detected.");
        }
        System.out.println("==========================================");
    }
    
    /**
     * 获取简要报告字符串
     */
    public String getBriefReport() {
        if (!enabled) {
            return "VARCHAR validation: disabled";
        }
        
        if (conflictCount == 0) {
            return String.format("VARCHAR validation: %d checks, no conflicts (%d ms)", 
                totalValidations, validationTimeMs);
        } else {
            return String.format("VARCHAR validation: %d conflicts in %d tables (%d ms)", 
                conflictCount, getAffectedTableCount(), validationTimeMs);
        }
    }
    
    @Override
    public String toString() {
        return getBriefReport();
    }
}
