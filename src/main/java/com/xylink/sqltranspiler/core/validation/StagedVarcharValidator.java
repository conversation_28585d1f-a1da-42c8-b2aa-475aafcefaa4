package com.xylink.sqltranspiler.core.validation;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import com.xylink.sqltranspiler.core.ast.dml.InsertTable;
import com.xylink.sqltranspiler.core.ast.dml.UpdateTable;
import com.xylink.sqltranspiler.core.ast.dml.ValuesClause;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 分阶段VARCHAR验证器实现
 * 
 * 采用两阶段处理：
 * 1. 阶段1：收集所有CREATE TABLE的schema信息
 * 2. 阶段2：批量验证所有INSERT/UPDATE语句
 */
public class StagedVarcharValidator implements VarcharValidator {
    
    private static final Logger log = LoggerFactory.getLogger(StagedVarcharValidator.class);
    
    private VarcharValidationConfig config = VarcharValidationConfig.defaultConfig();
    
    @Override
    public void configure(VarcharValidationConfig config) {
        this.config = config != null ? config : VarcharValidationConfig.defaultConfig();
        log.debug("Configured VARCHAR validator: {}", this.config);
    }
    
    @Override
    public boolean isEnabled() {
        return config.isEnabled();
    }
    
    @Override
    public ValidationSummary validate(List<Statement> statements) {
        if (!config.isEnabled()) {
            log.debug("VARCHAR validation is disabled");
            return ValidationSummary.disabled();
        }

        long startTime = System.currentTimeMillis();

        if (statements == null || statements.isEmpty()) {
            log.debug("No statements to validate");
            long validationTime = System.currentTimeMillis() - startTime;
            return new ValidationSummary(List.of(), 0, validationTime, true);
        }
        
        try {
            log.info("Starting VARCHAR length validation for {} statements", statements.size());
            
            // 阶段1：收集schema信息
            Map<String, TableSchema> schemas = collectSchemas(statements);
            
            // 阶段2：批量验证
            ValidationSummary summary = performBatchValidation(statements, schemas, startTime);
            
            log.info("VARCHAR validation completed: {}", summary.getBriefReport());
            
            return summary;
            
        } catch (Exception e) {
            log.error("VARCHAR validation failed", e);
            long validationTime = System.currentTimeMillis() - startTime;
            return new ValidationSummary(List.of(), 0, validationTime, true);
        }
    }
    
    /**
     * 阶段1：收集schema信息
     */
    private Map<String, TableSchema> collectSchemas(List<Statement> statements) {
        log.debug("Phase 1: Collecting table schemas");
        
        SchemaCollector collector = new SchemaCollector();
        
        // 收集所有CREATE TABLE语句
        for (Statement statement : statements) {
            if (statement instanceof CreateTable) {
                collector.collectCreateTable((CreateTable) statement);
            }
        }
        
        Map<String, TableSchema> schemas = collector.buildSchemas();
        SchemaCollector.CollectionStatistics stats = collector.getStatistics();
        
        log.info("Schema collection completed: {}", stats);
        
        return schemas;
    }
    
    /**
     * 阶段2：批量验证
     */
    private ValidationSummary performBatchValidation(List<Statement> statements, 
                                                   Map<String, TableSchema> schemas, 
                                                   long startTime) {
        log.debug("Phase 2: Batch validation");
        
        if (schemas.isEmpty()) {
            log.info("No VARCHAR schemas found, skipping validation");
            long validationTime = System.currentTimeMillis() - startTime;
            return new ValidationSummary(List.of(), 0, validationTime, true);
        }
        
        BatchValidator validator = new BatchValidator(schemas, config);
        
        // 验证所有语句（暂时简化实现）
        validator.validateStatements(statements);
        
        long validationTime = System.currentTimeMillis() - startTime;
        ValidationSummary summary = validator.getSummary(validationTime);
        
        // 根据配置输出详细报告
        if (config.isDetailedReporting() && summary.hasConflicts()) {
            summary.printReport();
        }
        
        return summary;
    }
    
    /**
     * 验证单个CREATE TABLE语句（用于增量验证）
     */
    public TableSchema validateCreateTable(CreateTable createTable) {
        if (!config.isEnabled() || createTable == null) {
            return null;
        }
        
        SchemaCollector collector = new SchemaCollector();
        collector.collectCreateTable(createTable);
        
        Map<String, TableSchema> schemas = collector.buildSchemas();
        return schemas.values().stream().findFirst().orElse(null);
    }
    
    /**
     * 验证单个INSERT/UPDATE语句的VARCHAR长度冲突
     *
     * @param statement 要验证的语句（INSERT或UPDATE）
     * @param schemas 已收集的表结构信息
     * @return 验证结果，如果语句不需要验证则返回null
     */
    public ValidationResult validateSingleStatement(Statement statement, Map<String, TableSchema> schemas) {
        if (!config.isEnabled() || statement == null || schemas == null || schemas.isEmpty()) {
            return null;
        }

        try {
            // 根据语句类型进行不同的验证
            if (statement instanceof InsertTable) {
                return validateInsertStatement((InsertTable) statement, schemas);
            } else if (statement instanceof UpdateTable) {
                return validateUpdateStatement((UpdateTable) statement, schemas);
            } else {
                // 其他类型的语句不需要验证
                log.debug("Statement type {} does not require VARCHAR validation",
                    statement.getClass().getSimpleName());
                return null;
            }
        } catch (Exception e) {
            log.error("Failed to validate single statement: {}", statement.getClass().getSimpleName(), e);
            return null;
        }
    }

    /**
     * 验证INSERT语句的VARCHAR长度冲突
     */
    private ValidationResult validateInsertStatement(InsertTable insertStatement, Map<String, TableSchema> schemas) {
        String tableName = insertStatement.getTableId().getTableName();
        TableSchema schema = schemas.get(tableName);

        if (schema == null || !schema.hasVarcharColumns()) {
            log.debug("No VARCHAR schema found for table: {}", tableName);
            return null;
        }

        List<VarcharConflict> conflicts = new ArrayList<>();

        // 验证VALUES子句中的数据
        ValuesClause valuesClause = insertStatement.getValuesClause();
        if (valuesClause != null) {
            conflicts.addAll(validateValuesClause(valuesClause, insertStatement, schema));
        }

        // 如果有冲突，创建验证结果
        if (!conflicts.isEmpty()) {
            return new ValidationResult(tableName, insertStatement.getSql(), conflicts, 0);
        }

        return null;
    }

    /**
     * 验证UPDATE语句的VARCHAR长度冲突
     */
    private ValidationResult validateUpdateStatement(UpdateTable updateStatement, Map<String, TableSchema> schemas) {
        String tableName = updateStatement.getTableId().getTableName();
        TableSchema schema = schemas.get(tableName);

        if (schema == null || !schema.hasVarcharColumns()) {
            log.debug("No VARCHAR schema found for table: {}", tableName);
            return null;
        }

        List<VarcharConflict> conflicts = new ArrayList<>();

        // 验证SET子句中的数据
        List<UpdateTable.SetClause> setClauses = updateStatement.getSetClauses();
        if (setClauses != null) {
            conflicts.addAll(validateSetClauses(setClauses, updateStatement, schema));
        }

        // 如果有冲突，创建验证结果
        if (!conflicts.isEmpty()) {
            return new ValidationResult(tableName, updateStatement.getSql(), conflicts, 0);
        }

        return null;
    }

    /**
     * 验证VALUES子句中的VARCHAR数据长度
     */
    private List<VarcharConflict> validateValuesClause(ValuesClause valuesClause, InsertTable insertStatement, TableSchema schema) {
        List<VarcharConflict> conflicts = new ArrayList<>();

        List<String> columns = insertStatement.getColumns();
        if (columns == null || columns.isEmpty()) {
            log.debug("INSERT statement has no column list, skipping validation");
            return conflicts;
        }

        // 验证每一行数据
        List<List<String>> rows = valuesClause.getRows();
        for (int rowIndex = 0; rowIndex < rows.size(); rowIndex++) {
            List<String> row = rows.get(rowIndex);

            // 验证每一列的数据
            for (int colIndex = 0; colIndex < Math.min(columns.size(), row.size()); colIndex++) {
                String columnName = columns.get(colIndex);
                String value = row.get(colIndex);

                VarcharConflict conflict = validateVarcharValue(columnName, value, insertStatement, schema);
                if (conflict != null) {
                    conflicts.add(conflict);
                }
            }
        }

        return conflicts;
    }

    /**
     * 验证SET子句中的VARCHAR数据长度
     */
    private List<VarcharConflict> validateSetClauses(List<UpdateTable.SetClause> setClauses, UpdateTable updateStatement, TableSchema schema) {
        List<VarcharConflict> conflicts = new ArrayList<>();

        for (UpdateTable.SetClause setClause : setClauses) {
            String columnName = setClause.getColumnName();
            String value = setClause.getValue();

            VarcharConflict conflict = validateVarcharValue(columnName, value, updateStatement, schema);
            if (conflict != null) {
                conflicts.add(conflict);
            }
        }

        return conflicts;
    }

    /**
     * 验证单个VARCHAR值的长度
     */
    private VarcharConflict validateVarcharValue(String columnName, String value, Statement statement, TableSchema schema) {
        // 检查是否是VARCHAR列
        TableSchema.VarcharColumn varcharColumn = schema.getVarcharColumn(columnName);
        if (varcharColumn == null) {
            return null; // 不是VARCHAR列，无需验证
        }

        // 解析实际的字符串值（去除引号等）
        String actualValue = parseStringValue(value);
        if (actualValue == null) {
            return null; // 无法解析的值（如NULL、函数调用等）
        }

        // 检查长度是否超限
        int actualLength = actualValue.length();
        int definedLength = varcharColumn.getMaxLength();

        if (actualLength > definedLength) {
            return new VarcharConflict(
                schema.getTableName(),
                columnName,
                definedLength,
                actualLength,
                actualValue,
                statement.getSql(),
                0 // 行号暂时设为0，实际使用时可以传入
            );
        }

        return null;
    }

    /**
     * 解析字符串值，去除引号并处理转义字符
     */
    private String parseStringValue(String value) {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }

        String trimmed = value.trim();

        // 处理NULL值
        if ("NULL".equalsIgnoreCase(trimmed)) {
            return null;
        }

        // 处理字符串字面量（单引号或双引号）
        if ((trimmed.startsWith("'") && trimmed.endsWith("'")) ||
            (trimmed.startsWith("\"") && trimmed.endsWith("\""))) {

            // 去除首尾引号
            String content = trimmed.substring(1, trimmed.length() - 1);

            // 简单处理转义字符（可以根据需要扩展）
            content = content.replace("\\'", "'")
                           .replace("\\\"", "\"")
                           .replace("\\\\", "\\")
                           .replace("\\n", "\n")
                           .replace("\\r", "\r")
                           .replace("\\t", "\t");

            return content;
        }

        // 其他情况（函数调用、表达式等）暂时不处理
        return null;
    }
    
    /**
     * 获取当前配置
     */
    public VarcharValidationConfig getConfig() {
        return config;
    }
    
    @Override
    public String toString() {
        return String.format("StagedVarcharValidator{enabled=%s, strategy=%s}", 
            config.isEnabled(), config.getStrategy());
    }
}
