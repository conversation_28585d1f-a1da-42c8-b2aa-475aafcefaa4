package com.xylink.sqltranspiler.core.validation;

import java.util.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 严格SQL验证结果
 * 
 * 包含基于官方文档的详细验证结果，包括：
 * - 错误：阻止转换的严重问题
 * - 警告：不阻止转换但需要注意的问题
 * - 建议：修复问题的具体建议
 * - 统计信息：验证过程的统计数据
 */
public class StrictValidationResult {
    
    private final List<ValidationIssue> errors;
    private final List<ValidationIssue> warnings;
    private final List<ValidationIssue> suggestions;
    private final Map<String, Object> statistics;
    private final LocalDateTime validationTime;
    
    public StrictValidationResult() {
        this.errors = new ArrayList<>();
        this.warnings = new ArrayList<>();
        this.suggestions = new ArrayList<>();
        this.statistics = new HashMap<>();
        this.validationTime = LocalDateTime.now();
    }
    
    /**
     * 添加错误
     */
    public void addError(String message) {
        errors.add(new ValidationIssue(ValidationIssue.Type.ERROR, message));
    }
    
    /**
     * 添加错误（带位置信息）
     */
    public void addError(String message, int lineNumber, int columnNumber) {
        errors.add(new ValidationIssue(ValidationIssue.Type.ERROR, message, lineNumber, columnNumber));
    }
    
    /**
     * 添加警告
     */
    public void addWarning(String message) {
        warnings.add(new ValidationIssue(ValidationIssue.Type.WARNING, message));
    }
    
    /**
     * 添加警告（带位置信息）
     */
    public void addWarning(String message, int lineNumber, int columnNumber) {
        warnings.add(new ValidationIssue(ValidationIssue.Type.WARNING, message, lineNumber, columnNumber));
    }
    
    /**
     * 添加建议
     */
    public void addSuggestion(String message) {
        suggestions.add(new ValidationIssue(ValidationIssue.Type.SUGGESTION, message));
    }
    
    /**
     * 添加建议（带位置信息）
     */
    public void addSuggestion(String message, int lineNumber, int columnNumber) {
        suggestions.add(new ValidationIssue(ValidationIssue.Type.SUGGESTION, message, lineNumber, columnNumber));
    }
    
    /**
     * 添加统计信息
     */
    public void addStatistic(String key, Object value) {
        statistics.put(key, value);
    }
    
    /**
     * 是否有错误
     */
    public boolean hasErrors() {
        return !errors.isEmpty();
    }
    
    /**
     * 是否有警告
     */
    public boolean hasWarnings() {
        return !warnings.isEmpty();
    }
    
    /**
     * 是否有建议
     */
    public boolean hasSuggestions() {
        return !suggestions.isEmpty();
    }
    
    /**
     * 是否验证成功（无错误）
     */
    public boolean isSuccess() {
        return errors.isEmpty();
    }
    
    /**
     * 是否应该阻止转换
     */
    public boolean shouldBlockTranspilation() {
        return hasErrors();
    }
    
    /**
     * 获取所有错误
     */
    public List<ValidationIssue> getErrors() {
        return new ArrayList<>(errors);
    }
    
    /**
     * 获取所有警告
     */
    public List<ValidationIssue> getWarnings() {
        return new ArrayList<>(warnings);
    }
    
    /**
     * 获取所有建议
     */
    public List<ValidationIssue> getSuggestions() {
        return new ArrayList<>(suggestions);
    }
    
    /**
     * 获取统计信息
     */
    public Map<String, Object> getStatistics() {
        return new HashMap<>(statistics);
    }
    
    /**
     * 获取验证时间
     */
    public LocalDateTime getValidationTime() {
        return validationTime;
    }
    
    /**
     * 获取总问题数
     */
    public int getTotalIssueCount() {
        return errors.size() + warnings.size();
    }
    
    /**
     * 生成详细报告
     */
    public String generateDetailedReport() {
        StringBuilder report = new StringBuilder();
        
        report.append("=".repeat(80)).append("\n");
        report.append("严格SQL验证报告\n");
        report.append("=".repeat(80)).append("\n");
        report.append("验证时间: ").append(validationTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
        report.append("验证状态: ").append(isSuccess() ? "✅ 通过" : "❌ 失败").append("\n");
        report.append("\n");
        
        // 统计信息
        if (!statistics.isEmpty()) {
            report.append("📊 验证统计\n");
            report.append("-".repeat(40)).append("\n");
            for (Map.Entry<String, Object> entry : statistics.entrySet()) {
                report.append(String.format("%-20s: %s\n", entry.getKey(), entry.getValue()));
            }
            report.append("\n");
        }
        
        // 错误信息
        if (hasErrors()) {
            report.append("❌ 错误 (").append(errors.size()).append(")\n");
            report.append("-".repeat(40)).append("\n");
            for (int i = 0; i < errors.size(); i++) {
                ValidationIssue error = errors.get(i);
                report.append(String.format("%d. %s\n", i + 1, error.getMessage()));
                if (error.hasLocation()) {
                    report.append(String.format("   位置: 第%d行，第%d列\n", error.getLineNumber(), error.getColumnNumber()));
                }
            }
            report.append("\n");
        }
        
        // 警告信息
        if (hasWarnings()) {
            report.append("⚠️ 警告 (").append(warnings.size()).append(")\n");
            report.append("-".repeat(40)).append("\n");
            for (int i = 0; i < warnings.size(); i++) {
                ValidationIssue warning = warnings.get(i);
                report.append(String.format("%d. %s\n", i + 1, warning.getMessage()));
                if (warning.hasLocation()) {
                    report.append(String.format("   位置: 第%d行，第%d列\n", warning.getLineNumber(), warning.getColumnNumber()));
                }
            }
            report.append("\n");
        }
        
        // 修复建议
        if (hasSuggestions()) {
            report.append("💡 修复建议 (").append(suggestions.size()).append(")\n");
            report.append("-".repeat(40)).append("\n");
            for (int i = 0; i < suggestions.size(); i++) {
                ValidationIssue suggestion = suggestions.get(i);
                report.append(String.format("%d. %s\n", i + 1, suggestion.getMessage()));
                if (suggestion.hasLocation()) {
                    report.append(String.format("   位置: 第%d行，第%d列\n", suggestion.getLineNumber(), suggestion.getColumnNumber()));
                }
            }
            report.append("\n");
        }
        
        report.append("=".repeat(80)).append("\n");
        
        return report.toString();
    }
    
    /**
     * 生成简要报告
     */
    public String generateBriefReport() {
        StringBuilder brief = new StringBuilder();
        
        if (isSuccess()) {
            brief.append("✅ SQL验证通过");
            if (hasWarnings()) {
                brief.append(String.format(" (有%d个警告)", warnings.size()));
            }
        } else {
            brief.append(String.format("❌ SQL验证失败 - %d个错误", errors.size()));
            if (hasWarnings()) {
                brief.append(String.format(", %d个警告", warnings.size()));
            }
        }
        
        if (hasSuggestions()) {
            brief.append(String.format(", %d个修复建议", suggestions.size()));
        }
        
        return brief.toString();
    }
    
    /**
     * 生成控制台输出
     */
    public String getConsoleOutput() {
        StringBuilder output = new StringBuilder();
        
        // 简要状态
        output.append(generateBriefReport()).append("\n");
        
        // 如果有问题，显示前几个
        if (hasErrors()) {
            output.append("\n主要错误:\n");
            for (int i = 0; i < Math.min(3, errors.size()); i++) {
                output.append(String.format("  ❌ %s\n", errors.get(i).getMessage()));
            }
            if (errors.size() > 3) {
                output.append(String.format("  ... 还有%d个错误\n", errors.size() - 3));
            }
        }
        
        if (hasWarnings()) {
            output.append("\n主要警告:\n");
            for (int i = 0; i < Math.min(3, warnings.size()); i++) {
                output.append(String.format("  ⚠️ %s\n", warnings.get(i).getMessage()));
            }
            if (warnings.size() > 3) {
                output.append(String.format("  ... 还有%d个警告\n", warnings.size() - 3));
            }
        }
        
        if (hasSuggestions()) {
            output.append("\n修复建议:\n");
            for (int i = 0; i < Math.min(3, suggestions.size()); i++) {
                output.append(String.format("  💡 %s\n", suggestions.get(i).getMessage()));
            }
            if (suggestions.size() > 3) {
                output.append(String.format("  ... 还有%d个建议\n", suggestions.size() - 3));
            }
        }
        
        return output.toString();
    }
    
    /**
     * 合并另一个验证结果
     */
    public void merge(StrictValidationResult other) {
        this.errors.addAll(other.errors);
        this.warnings.addAll(other.warnings);
        this.suggestions.addAll(other.suggestions);
        this.statistics.putAll(other.statistics);
    }
    
    /**
     * 验证问题类
     */
    public static class ValidationIssue {
        public enum Type {
            ERROR, WARNING, SUGGESTION
        }
        
        private final Type type;
        private final String message;
        private final int lineNumber;
        private final int columnNumber;
        private final LocalDateTime timestamp;
        
        public ValidationIssue(Type type, String message) {
            this(type, message, -1, -1);
        }
        
        public ValidationIssue(Type type, String message, int lineNumber, int columnNumber) {
            this.type = type;
            this.message = message;
            this.lineNumber = lineNumber;
            this.columnNumber = columnNumber;
            this.timestamp = LocalDateTime.now();
        }
        
        public Type getType() { return type; }
        public String getMessage() { return message; }
        public int getLineNumber() { return lineNumber; }
        public int getColumnNumber() { return columnNumber; }
        public LocalDateTime getTimestamp() { return timestamp; }
        
        public boolean hasLocation() {
            return lineNumber > 0 && columnNumber > 0;
        }
        
        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append(type.name()).append(": ").append(message);
            if (hasLocation()) {
                sb.append(" (第").append(lineNumber).append("行，第").append(columnNumber).append("列)");
            }
            return sb.toString();
        }
    }
}
