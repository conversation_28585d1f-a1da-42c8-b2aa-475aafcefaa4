package com.xylink.sqltranspiler.core.validation;

/**
 * VARCHAR验证配置类
 */
public class VarcharValidationConfig {
    
    /**
     * 冲突处理策略枚举
     */
    public enum ConflictHandlingStrategy {
        /**
         * 立即失败 - 发现冲突时抛出异常
         */
        FAIL_FAST,
        
        /**
         * 截断并警告 - 截断数据并记录警告
         */
        TRUNCATE_WITH_WARNING,
        
        /**
         * 仅记录日志 - 记录冲突但继续处理
         */
        LOG_ONLY
    }
    
    private boolean enabled = false; // 默认禁用，避免影响现有功能
    private ConflictHandlingStrategy strategy = ConflictHandlingStrategy.LOG_ONLY;
    private boolean detailedReporting = true;
    private int maxReportedConflicts = 100;
    private boolean enablePerformanceMetrics = false;
    
    // 构造函数
    public VarcharValidationConfig() {}
    
    public VarcharValidationConfig(boolean enabled) {
        this.enabled = enabled;
    }
    
    // Getter和Setter方法
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    public ConflictHandlingStrategy getStrategy() {
        return strategy;
    }
    
    public void setStrategy(ConflictHandlingStrategy strategy) {
        this.strategy = strategy;
    }
    
    public boolean isDetailedReporting() {
        return detailedReporting;
    }
    
    public void setDetailedReporting(boolean detailedReporting) {
        this.detailedReporting = detailedReporting;
    }
    
    public int getMaxReportedConflicts() {
        return maxReportedConflicts;
    }
    
    public void setMaxReportedConflicts(int maxReportedConflicts) {
        this.maxReportedConflicts = maxReportedConflicts;
    }
    
    public boolean isEnablePerformanceMetrics() {
        return enablePerformanceMetrics;
    }
    
    public void setEnablePerformanceMetrics(boolean enablePerformanceMetrics) {
        this.enablePerformanceMetrics = enablePerformanceMetrics;
    }
    
    /**
     * 创建默认配置
     */
    public static VarcharValidationConfig defaultConfig() {
        return new VarcharValidationConfig();
    }
    
    /**
     * 创建启用的配置
     */
    public static VarcharValidationConfig enabledConfig() {
        VarcharValidationConfig config = new VarcharValidationConfig();
        config.setEnabled(true);
        return config;
    }
    
    @Override
    public String toString() {
        return "VarcharValidationConfig{" +
                "enabled=" + enabled +
                ", strategy=" + strategy +
                ", detailedReporting=" + detailedReporting +
                ", maxReportedConflicts=" + maxReportedConflicts +
                ", enablePerformanceMetrics=" + enablePerformanceMetrics +
                '}';
    }
}
