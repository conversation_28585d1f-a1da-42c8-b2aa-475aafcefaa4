package com.xylink.sqltranspiler.core.validation;

import java.util.HashMap;
import java.util.Map;

/**
 * 表结构构建器
 */
public class TableSchemaBuilder {
    
    private final String tableName;
    private final Map<String, TableSchema.VarcharColumn> varcharColumns = new HashMap<>();
    private int nextPosition = 0;
    
    public TableSchemaBuilder(String tableName) {
        this.tableName = tableName;
    }
    
    /**
     * 添加VARCHAR列
     */
    public TableSchemaBuilder addVarcharColumn(String columnName, int maxLength) {
        return addVarcharColumn(columnName, maxLength, true);
    }
    
    /**
     * 添加VARCHAR列（指定是否可空）
     */
    public TableSchemaBuilder addVarcharColumn(String columnName, int maxLength, boolean nullable) {
        TableSchema.VarcharColumn column = new TableSchema.VarcharColumn(
            columnName, maxLength, nextPosition++, nullable);
        varcharColumns.put(columnName, column);
        return this;
    }
    
    /**
     * 添加VARCHAR列（指定位置）
     */
    public TableSchemaBuilder addVarcharColumn(String columnName, int maxLength, int position, boolean nullable) {
        TableSchema.VarcharColumn column = new TableSchema.VarcharColumn(
            columnName, maxLength, position, nullable);
        varcharColumns.put(columnName, column);
        
        // 更新下一个位置
        if (position >= nextPosition) {
            nextPosition = position + 1;
        }
        
        return this;
    }
    
    /**
     * 检查是否已包含指定列
     */
    public boolean hasColumn(String columnName) {
        return varcharColumns.containsKey(columnName);
    }
    
    /**
     * 获取当前列数量
     */
    public int getColumnCount() {
        return varcharColumns.size();
    }
    
    /**
     * 获取表名
     */
    public String getTableName() {
        return tableName;
    }
    
    /**
     * 构建TableSchema
     */
    public TableSchema build() {
        if (varcharColumns.isEmpty()) {
            // 如果没有VARCHAR列，返回空的schema
            return new TableSchema(tableName, new HashMap<>());
        }
        
        return new TableSchema(tableName, varcharColumns);
    }
    
    /**
     * 重置构建器
     */
    public TableSchemaBuilder reset() {
        varcharColumns.clear();
        nextPosition = 0;
        return this;
    }
    
    /**
     * 创建构建器的副本
     */
    public TableSchemaBuilder copy() {
        TableSchemaBuilder copy = new TableSchemaBuilder(tableName);
        copy.varcharColumns.putAll(this.varcharColumns);
        copy.nextPosition = this.nextPosition;
        return copy;
    }
    
    @Override
    public String toString() {
        return String.format("TableSchemaBuilder{table='%s', columns=%d}", 
            tableName, varcharColumns.size());
    }
}
