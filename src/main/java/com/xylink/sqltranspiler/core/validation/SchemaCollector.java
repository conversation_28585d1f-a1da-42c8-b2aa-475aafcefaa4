package com.xylink.sqltranspiler.core.validation;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import com.xylink.sqltranspiler.core.ast.table.ColumnRel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Schema收集器 - 从CREATE TABLE语句中收集VARCHAR列信息
 */
public class SchemaCollector {
    
    private static final Logger log = LoggerFactory.getLogger(SchemaCollector.class);
    
    private final Map<String, TableSchemaBuilder> builders = new HashMap<>();
    private int processedCreateTableCount = 0;
    private int totalVarcharColumns = 0;
    
    /**
     * 收集CREATE TABLE语句的schema信息
     */
    public void collectCreateTable(CreateTable createTable) {
        if (createTable == null || createTable.getTableId() == null) {
            log.warn("Skipping null or invalid CREATE TABLE statement");
            return;
        }

        String tableName = extractTableName(createTable);
        if (tableName == null || tableName.trim().isEmpty()) {
            log.warn("Skipping CREATE TABLE with empty table name");
            return;
        }

        log.debug("Collecting schema for table: {}", tableName);

        TableSchemaBuilder builder = new TableSchemaBuilder(tableName);
        List<ColumnRel> columns = createTable.getColumnRels();

        if (columns != null) {
            int position = 0;
            for (ColumnRel column : columns) {
                if (isVarcharColumn(column)) {
                    processVarcharColumn(builder, column, position);
                    totalVarcharColumns++;
                }
                position++;
            }
        }
        
        builders.put(tableName, builder);
        processedCreateTableCount++;
        
        log.debug("Collected schema for table '{}' with {} VARCHAR columns", 
                 tableName, builder.getColumnCount());
    }
    
    /**
     * 批量收集多个CREATE TABLE语句
     */
    public void collectCreateTables(List<CreateTable> createTables) {
        if (createTables == null) {
            return;
        }
        
        for (CreateTable createTable : createTables) {
            try {
                collectCreateTable(createTable);
            } catch (Exception e) {
                log.error("Failed to collect schema from CREATE TABLE statement", e);
            }
        }
    }
    
    /**
     * 从Statement列表中收集所有CREATE TABLE
     */
    public void collectFromStatements(List<Statement> statements) {
        if (statements == null) {
            return;
        }
        
        for (Statement statement : statements) {
            if (statement instanceof CreateTable) {
                collectCreateTable((CreateTable) statement);
            }
        }
    }
    
    /**
     * 构建所有收集到的表结构
     */
    public Map<String, TableSchema> buildSchemas() {
        Map<String, TableSchema> schemas = new HashMap<>();
        
        for (Map.Entry<String, TableSchemaBuilder> entry : builders.entrySet()) {
            try {
                TableSchema schema = entry.getValue().build();
                if (schema.hasVarcharColumns()) {
                    schemas.put(entry.getKey(), schema);
                    log.debug("Built schema for table '{}' with {} VARCHAR columns", 
                             entry.getKey(), schema.getVarcharColumnCount());
                }
            } catch (Exception e) {
                log.error("Failed to build schema for table '{}'", entry.getKey(), e);
            }
        }
        
        log.info("Schema collection completed: {} tables processed, {} VARCHAR columns found, {} schemas built",
                processedCreateTableCount, totalVarcharColumns, schemas.size());
        
        return schemas;
    }
    
    /**
     * 获取收集统计信息
     */
    public CollectionStatistics getStatistics() {
        return new CollectionStatistics(
            processedCreateTableCount,
            totalVarcharColumns,
            builders.size()
        );
    }
    
    /**
     * 重置收集器状态
     */
    public void reset() {
        builders.clear();
        processedCreateTableCount = 0;
        totalVarcharColumns = 0;
    }
    
    /**
     * 提取表名
     */
    private String extractTableName(CreateTable createTable) {
        try {
            if (createTable.getTableId() != null) {
                return createTable.getTableId().getTableName();
            }
        } catch (Exception e) {
            log.warn("Failed to extract table name from CREATE TABLE statement", e);
        }
        return null;
    }

    /**
     * 检查是否为VARCHAR列
     */
    private boolean isVarcharColumn(ColumnRel column) {
        if (column == null || column.getTypeName() == null) {
            return false;
        }

        try {
            String typeName = column.getTypeName();
            return typeName != null &&
                   (typeName.toUpperCase().startsWith("VARCHAR") ||
                    typeName.toUpperCase().startsWith("CHAR"));
        } catch (Exception e) {
            log.debug("Failed to check column type for column: {}",
                     column.getColumnName(), e);
            return false;
        }
    }

    /**
     * 处理VARCHAR列
     */
    private void processVarcharColumn(TableSchemaBuilder builder, ColumnRel column, int position) {
        try {
            String columnName = column.getColumnName();
            int maxLength = extractVarcharLength(column);
            boolean nullable = column.isNullable();

            builder.addVarcharColumn(columnName, maxLength, position, nullable);

            log.debug("Added VARCHAR column: {} VARCHAR({}) nullable={} position={}",
                     columnName, maxLength, nullable, position);

        } catch (Exception e) {
            log.error("Failed to process VARCHAR column: {}", column.getColumnName(), e);
        }
    }

    /**
     * 提取VARCHAR长度
     */
    private int extractVarcharLength(ColumnRel column) {
        try {
            // 从ColumnRel中获取长度信息
            int columnLength = column.getColumnLength();
            if (columnLength > 0) {
                return columnLength;
            }

            // 如果没有设置长度，尝试从类型名称中解析
            String typeName = column.getTypeName();
            if (typeName != null && typeName.contains("(")) {
                String lengthPart = typeName.substring(typeName.indexOf("(") + 1, typeName.indexOf(")"));
                return Integer.parseInt(lengthPart);
            }
        } catch (Exception e) {
            log.debug("Failed to extract VARCHAR length for column: {}, using default",
                     column.getColumnName(), e);
        }

        // 默认长度
        return 255;
    }
    
    /**
     * 收集统计信息
     */
    public static class CollectionStatistics {
        private final int processedTables;
        private final int totalVarcharColumns;
        private final int schemasBuilt;
        
        public CollectionStatistics(int processedTables, int totalVarcharColumns, int schemasBuilt) {
            this.processedTables = processedTables;
            this.totalVarcharColumns = totalVarcharColumns;
            this.schemasBuilt = schemasBuilt;
        }
        
        public int getProcessedTables() { return processedTables; }
        public int getTotalVarcharColumns() { return totalVarcharColumns; }
        public int getSchemasBuilt() { return schemasBuilt; }
        
        @Override
        public String toString() {
            return String.format("CollectionStatistics{tables=%d, varcharColumns=%d, schemas=%d}",
                processedTables, totalVarcharColumns, schemasBuilt);
        }
    }
}
