package com.xylink.sqltranspiler.core.validation;

import java.util.Collections;
import java.util.List;

/**
 * 单个语句的验证结果
 */
public class ValidationResult {
    
    private final String tableName;
    private final String statementText;
    private final List<VarcharConflict> conflicts;
    private final int statementLineNumber;
    private final long validationTime;
    
    public ValidationResult(String tableName, String statementText, 
                          List<VarcharConflict> conflicts, int statementLineNumber) {
        this(tableName, statementText, conflicts, statementLineNumber, System.currentTimeMillis());
    }
    
    public ValidationResult(String tableName, String statementText, 
                          List<VarcharConflict> conflicts, int statementLineNumber, 
                          long validationTime) {
        this.tableName = tableName;
        this.statementText = statementText;
        this.conflicts = conflicts != null ? List.copyOf(conflicts) : Collections.emptyList();
        this.statementLineNumber = statementLineNumber;
        this.validationTime = validationTime;
    }
    
    // Getter方法
    public String getTableName() {
        return tableName;
    }
    
    public String getStatementText() {
        return statementText;
    }
    
    public List<VarcharConflict> getConflicts() {
        return conflicts;
    }
    
    public int getStatementLineNumber() {
        return statementLineNumber;
    }
    
    public long getValidationTime() {
        return validationTime;
    }
    
    /**
     * 是否有冲突
     */
    public boolean hasConflicts() {
        return !conflicts.isEmpty();
    }
    
    /**
     * 获取冲突数量
     */
    public int getConflictCount() {
        return conflicts.size();
    }
    
    /**
     * 获取简要描述
     */
    public String getBriefDescription() {
        if (!hasConflicts()) {
            return String.format("Table '%s' line %d: OK", tableName, statementLineNumber);
        } else {
            return String.format("Table '%s' line %d: %d conflicts", 
                tableName, statementLineNumber, conflicts.size());
        }
    }
    
    /**
     * 获取详细描述
     */
    public String getDetailedDescription() {
        StringBuilder sb = new StringBuilder();
        sb.append(getBriefDescription());
        
        if (hasConflicts()) {
            sb.append("\nConflicts:");
            for (VarcharConflict conflict : conflicts) {
                sb.append("\n  - ").append(conflict.getShortDescription());
            }
        }
        
        return sb.toString();
    }
    
    @Override
    public String toString() {
        return getBriefDescription();
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        ValidationResult that = (ValidationResult) obj;
        return statementLineNumber == that.statementLineNumber &&
               tableName.equals(that.tableName) &&
               conflicts.equals(that.conflicts);
    }
    
    @Override
    public int hashCode() {
        int result = tableName.hashCode();
        result = 31 * result + conflicts.hashCode();
        result = 31 * result + statementLineNumber;
        return result;
    }
}
