package com.xylink.sqltranspiler.core.validation;

import com.xylink.sqltranspiler.Transpiler;
import com.xylink.sqltranspiler.core.context.TranspilationResult;
import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.ArrayList;

/**
 * 增强的转换器，集成VARCHAR验证功能
 */
public class EnhancedTranspiler {
    
    private static final Logger log = LoggerFactory.getLogger(EnhancedTranspiler.class);
    
    private final Transpiler baseTranspiler;
    private final VarcharValidator varcharValidator;
    public EnhancedTranspiler() {
        this.baseTranspiler = new Transpiler();
        this.varcharValidator = new StagedVarcharValidator();
    }
    
    public EnhancedTranspiler(VarcharValidationConfig validationConfig) {
        this();
        this.varcharValidator.configure(validationConfig);
    }
    
    /**
     * 增强的转换方法，包含VARCHAR验证
     */
    public EnhancedTranspilationResult transpileEnhanced(String sqlContent, String sourceDialect, String targetDialect) {
        if (sqlContent == null || sqlContent.trim().isEmpty()) {
            return EnhancedTranspilationResult.failure("SQL content is null or empty");
        }
        
        long startTime = System.currentTimeMillis();
        List<String> warnings = new ArrayList<>();
        
        try {
            log.info("Starting enhanced transpilation: {} -> {}", sourceDialect, targetDialect);
            
            // 1. 解析SQL语句
            List<Statement> statements = parseStatements(sqlContent);
            if (statements.isEmpty()) {
                warnings.add("No valid SQL statements found");
            }
            
            // 2. 执行VARCHAR验证（如果启用）
            ValidationSummary validationSummary = performVarcharValidation(statements);
            
            // 3. 检查是否有阻塞性的VARCHAR冲突
            if (shouldFailOnVarcharConflicts(validationSummary)) {
                return EnhancedTranspilationResult.failure(
                    "VARCHAR length conflicts detected. Use LOG_ONLY strategy to continue.", 
                    validationSummary);
            }
            
            // 4. 执行正常的转换流程
            TranspilationResult baseResult = baseTranspiler.transpile(sqlContent, sourceDialect, targetDialect);
            
            // 5. 构建增强的结果
            long conversionTime = System.currentTimeMillis() - startTime;
            EnhancedTranspilationResult.ConversionStatistics statistics = 
                new EnhancedTranspilationResult.ConversionStatistics(
                    statements.size(), statements.size(), 0, conversionTime);
            
            // 检查转换是否成功（基于失败数量）
            boolean isSuccess = baseResult.failureCount() == 0;

            if (isSuccess) {
                if (warnings.isEmpty()) {
                    return EnhancedTranspilationResult.success(baseResult.translatedSql(), validationSummary);
                } else {
                    return EnhancedTranspilationResult.successWithWarnings(
                        baseResult.translatedSql(), validationSummary, warnings);
                }
            } else {
                String errorMessage = String.format("Base transpilation failed: %d failures out of %d statements",
                    baseResult.failureCount(), baseResult.successCount() + baseResult.failureCount());
                return EnhancedTranspilationResult.failure(errorMessage, validationSummary);
            }
            
        } catch (Exception e) {
            log.error("Enhanced transpilation failed", e);
            return EnhancedTranspilationResult.failure("Transpilation failed: " + e.getMessage());
        }
    }
    
    /**
     * 兼容性方法：返回原始的转换结果
     */
    public TranspilationResult transpile(String sqlContent, String sourceDialect, String targetDialect) {
        try {
            // 使用基础转换器，不启用VARCHAR验证
            return baseTranspiler.transpile(sqlContent, sourceDialect, targetDialect);
        } catch (Exception e) {
            log.error("Transpilation failed", e);
            // 创建一个表示失败的TranspilationResult
            return new TranspilationResult(
                "", // 空的SQL
                List.of(), // 空的issues列表
                0, // 成功数量
                1  // 失败数量
            );
        }
    }
    
    /**
     * 配置VARCHAR验证器
     */
    public void configureVarcharValidation(VarcharValidationConfig config) {
        varcharValidator.configure(config);
        log.info("VARCHAR validation configured: {}", config);
    }
    
    /**
     * 检查VARCHAR验证器是否启用
     */
    public boolean isVarcharValidationEnabled() {
        return varcharValidator.isEnabled();
    }
    
    /**
     * 解析SQL语句
     */
    private List<Statement> parseStatements(String sqlContent) {
        try {
            return MySqlHelper.parseMultiStatement(sqlContent);
        } catch (Exception e) {
            log.warn("Failed to parse SQL statements, using empty list", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 执行VARCHAR验证
     */
    private ValidationSummary performVarcharValidation(List<Statement> statements) {
        try {
            return varcharValidator.validate(statements);
        } catch (DataLengthConflictException e) {
            log.error("VARCHAR validation failed with conflicts", e);
            throw e; // 重新抛出，让上层处理
        } catch (Exception e) {
            log.warn("VARCHAR validation failed, continuing without validation", e);
            return ValidationSummary.empty();
        }
    }
    
    /**
     * 检查是否应该因为VARCHAR冲突而失败
     */
    private boolean shouldFailOnVarcharConflicts(ValidationSummary validationSummary) {
        if (!validationSummary.isEnabled() || !validationSummary.hasConflicts()) {
            return false;
        }
        
        // 这里可以根据配置决定是否失败
        // 目前简化为：如果有冲突且策略是FAIL_FAST，则失败
        return false; // 暂时不因为VARCHAR冲突而失败，只记录
    }
    
    /**
     * 获取VARCHAR验证配置
     */
    public VarcharValidationConfig getVarcharValidationConfig() {
        if (varcharValidator instanceof StagedVarcharValidator) {
            return ((StagedVarcharValidator) varcharValidator).getConfig();
        }
        return VarcharValidationConfig.defaultConfig();
    }
    
    @Override
    public String toString() {
        return String.format("EnhancedTranspiler{varcharValidation=%s}", 
            varcharValidator.isEnabled() ? "enabled" : "disabled");
    }
}
