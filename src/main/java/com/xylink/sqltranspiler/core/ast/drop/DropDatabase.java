package com.xylink.sqltranspiler.core.ast.drop;

import java.util.ArrayList;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

public class DropDatabase extends Statement {
    private final String catalogName;
    private final String databaseName;
    private boolean ifExists = false;
    private final ArrayList<String> databaseNames = new ArrayList<>();

    public DropDatabase(String catalogName, String databaseName, boolean ifExists) {
        this.catalogName = catalogName;
        this.databaseName = databaseName;
        this.ifExists = ifExists;
    }

    public DropDatabase(String databaseName) {
        this(null, databaseName, false);
    }

    public DropDatabase(String databaseName, boolean ifExists) {
        this(null, databaseName, ifExists);
    }

    @Override
    public StatementType getStatementType() {
        return StatementType.DROP_DATABASE;
    }

    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.DROP;
    }

    @Override
    public SqlType getSqlType() {
        return SqlType.DDL;
    }

    // Getters
    public String getCatalogName() {
        return catalogName;
    }

    public String getDatabaseName() {
        return databaseName;
    }

    public boolean isIfExists() {
        return ifExists;
    }

    public ArrayList<String> getDatabaseNames() {
        return databaseNames;
    }

    // Setters
    public void setIfExists(boolean ifExists) {
        this.ifExists = ifExists;
    }
}
