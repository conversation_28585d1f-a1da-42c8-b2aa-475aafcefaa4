package com.xylink.sqltranspiler.core.ast.create;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

/**
 * CREATE PROCEDURE语句的AST表示
 * 
 * 支持MySQL和各种数据库的存储过程创建语法：
 * - MySQL: CREATE PROCEDURE procedure_name(parameters) BEGIN ... END
 * - 金仓数据库: CREATE OR REPLACE PROCEDURE procedure_name(parameters) LANGUAGE plpgsql AS $$ ... $$
 * - 达梦数据库: CREATE OR REPLACE PROCEDURE procedure_name(parameters) AS BEGIN ... END
 * - 神通数据库: CREATE OR REPLACE PROCEDURE procedure_name(parameters) AS BEGIN ... END
 */
public class CreateProcedure extends Statement {
    private final String procedureName;
    private final String parameters;
    private final String procedureBody;
    private final String language;
    private final boolean orReplace;
    private final boolean deterministic;

    public CreateProcedure(String procedureName, String parameters, String procedureBody, 
                          String language, boolean orReplace, boolean deterministic) {
        this.procedureName = procedureName;
        this.parameters = parameters;
        this.procedureBody = procedureBody;
        this.language = language;
        this.orReplace = orReplace;
        this.deterministic = deterministic;
    }

    public CreateProcedure(String procedureName, String parameters, String procedureBody) {
        this(procedureName, parameters, procedureBody, null, false, false);
    }

    @Override
    public StatementType getStatementType() {
        return StatementType.CREATE_PROCEDURE;
    }

    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.CREATE;
    }

    @Override
    public SqlType getSqlType() {
        return SqlType.DDL;
    }

    // Getters
    public String getProcedureName() {
        return procedureName;
    }

    public String getParameters() {
        return parameters;
    }

    public String getProcedureBody() {
        return procedureBody;
    }

    public String getLanguage() {
        return language;
    }

    public boolean isOrReplace() {
        return orReplace;
    }

    public boolean isDeterministic() {
        return deterministic;
    }
}
