package com.xylink.sqltranspiler.core.ast.transaction;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

/**
 * START TRANSACTION语句的AST表示
 *
 * 根据MySQL官方文档：
 * START TRANSACTION [transaction_characteristic [, transaction_characteristic] ...]
 *
 * 根据KingbaseES官方文档，START TRANSACTION在KingbaseES中是支持的
 */
public class StartTransaction extends Statement {

    private String characteristics;

    public StartTransaction() {
        super();
    }

    public StartTransaction(String sql) {
        super();
        setSql(sql);
    }
    
    public String getCharacteristics() {
        return characteristics;
    }

    public void setCharacteristics(String characteristics) {
        this.characteristics = characteristics;
    }

    @Override
    public StatementType getStatementType() {
        return StatementType.UNKOWN; // 暂时使用UNKOWN，稍后添加START_TRANSACTION
    }

    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.OTHER;
    }

    @Override
    public SqlType getSqlType() {
        return SqlType.TCL; // Transaction Control Language
    }

    @Override
    public String toString() {
        return "StartTransaction{" +
                "characteristics='" + characteristics + '\'' +
                ", sql='" + getSql() + '\'' +
                '}';
    }
}
