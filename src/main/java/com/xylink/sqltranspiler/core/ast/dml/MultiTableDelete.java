package com.xylink.sqltranspiler.core.ast.dml;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.TableId;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

import java.util.List;
import java.util.Objects;

/**
 * 多表DELETE语句的AST表示
 * 
 * 支持两种MySQL多表DELETE语法：
 * 1. DELETE t1, t2 FROM t1 INNER JOIN t2 WHERE condition
 * 2. DELETE FROM t1, t2 USING t1 INNER JOIN t2 WHERE condition
 */
public class MultiTableDelete extends Statement {
    
    /**
     * 多表DELETE的语法类型
     */
    public enum DeleteSyntaxType {
        /**
         * DELETE table_list FROM table_references WHERE condition
         */
        DELETE_FROM,
        
        /**
         * DELETE FROM table_list USING table_references WHERE condition
         */
        DELETE_USING
    }
    
    private final DeleteSyntaxType syntaxType;
    private final List<TableId> targetTables;      // 要删除数据的表
    private final String tableReferences;          // FROM/USING后的表引用（JOIN表达式）
    private final String whereClause;              // WHERE条件
    private final boolean lowPriority;             // LOW_PRIORITY修饰符
    private final boolean quick;                   // QUICK修饰符
    private final boolean ignore;                  // IGNORE修饰符
    
    public MultiTableDelete(DeleteSyntaxType syntaxType, List<TableId> targetTables, 
                           String tableReferences, String whereClause,
                           boolean lowPriority, boolean quick, boolean ignore) {
        this.syntaxType = syntaxType;
        this.targetTables = targetTables;
        this.tableReferences = tableReferences;
        this.whereClause = whereClause;
        this.lowPriority = lowPriority;
        this.quick = quick;
        this.ignore = ignore;
    }
    
    public MultiTableDelete(DeleteSyntaxType syntaxType, List<TableId> targetTables, 
                           String tableReferences, String whereClause) {
        this(syntaxType, targetTables, tableReferences, whereClause, false, false, false);
    }
    
    @Override
    public StatementType getStatementType() {
        return StatementType.DELETE;
    }
    
    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.WRITE;
    }
    
    @Override
    public SqlType getSqlType() {
        return SqlType.DML;
    }
    
    // Getters
    public DeleteSyntaxType getSyntaxType() {
        return syntaxType;
    }
    
    public List<TableId> getTargetTables() {
        return targetTables;
    }
    
    public String getTableReferences() {
        return tableReferences;
    }
    
    public String getWhereClause() {
        return whereClause;
    }
    
    public boolean isLowPriority() {
        return lowPriority;
    }
    
    public boolean isQuick() {
        return quick;
    }
    
    public boolean isIgnore() {
        return ignore;
    }
    
    /**
     * 获取主要目标表（用于权限检查等）
     */
    public TableId getPrimaryTargetTable() {
        return targetTables != null && !targetTables.isEmpty() ? targetTables.get(0) : null;
    }
    
    /**
     * 检查是否涉及指定的表
     */
    public boolean involvesTable(String tableName) {
        if (targetTables != null) {
            for (TableId tableId : targetTables) {
                if (Objects.equals(tableId.getTableName(), tableName)) {
                    return true;
                }
            }
        }
        return false;
    }
    
    /**
     * 生成SQL语句的字符串表示
     */
    public String toSqlString() {
        StringBuilder sql = new StringBuilder("DELETE ");
        
        // 添加修饰符
        if (lowPriority) sql.append("LOW_PRIORITY ");
        if (quick) sql.append("QUICK ");
        if (ignore) sql.append("IGNORE ");
        
        // 根据语法类型构建SQL
        if (syntaxType == DeleteSyntaxType.DELETE_FROM) {
            // DELETE t1, t2 FROM table_references WHERE condition
            if (targetTables != null && !targetTables.isEmpty()) {
                for (int i = 0; i < targetTables.size(); i++) {
                    if (i > 0) sql.append(", ");
                    sql.append(targetTables.get(i).getFullTableName());
                }
            }
            sql.append(" FROM ").append(tableReferences);
        } else {
            // DELETE FROM t1, t2 USING table_references WHERE condition
            sql.append("FROM ");
            if (targetTables != null && !targetTables.isEmpty()) {
                for (int i = 0; i < targetTables.size(); i++) {
                    if (i > 0) sql.append(", ");
                    sql.append(targetTables.get(i).getFullTableName());
                }
            }
            sql.append(" USING ").append(tableReferences);
        }
        
        // 添加WHERE子句
        if (whereClause != null && !whereClause.trim().isEmpty()) {
            sql.append(" WHERE ").append(whereClause);
        }
        
        return sql.toString();
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MultiTableDelete that = (MultiTableDelete) o;
        return lowPriority == that.lowPriority &&
               quick == that.quick &&
               ignore == that.ignore &&
               syntaxType == that.syntaxType &&
               Objects.equals(targetTables, that.targetTables) &&
               Objects.equals(tableReferences, that.tableReferences) &&
               Objects.equals(whereClause, that.whereClause);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(syntaxType, targetTables, tableReferences, whereClause, 
                          lowPriority, quick, ignore);
    }
    
    @Override
    public String toString() {
        return "MultiTableDelete{" +
               "syntaxType=" + syntaxType +
               ", targetTables=" + targetTables +
               ", tableReferences='" + tableReferences + '\'' +
               ", whereClause='" + whereClause + '\'' +
               ", lowPriority=" + lowPriority +
               ", quick=" + quick +
               ", ignore=" + ignore +
               '}';
    }
}
