package com.xylink.sqltranspiler.core.ast;

import java.util.List;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;

import com.google.common.collect.Lists;

public class FunctionId {
    private final String catalogName;
    private final String schemaName;
    private final String functionName;
    private String funcType = "CF"; // callfunction & TVF
    private List<String> functionArguments = Lists.newArrayList();

    public FunctionId(String catalogName, String schemaName, String functionName) {
        this.catalogName = catalogName;
        this.schemaName = schemaName;
        this.functionName = functionName;
    }

    public FunctionId(String schemaName, String functionName) {
        this(null, schemaName, functionName);
    }

    public FunctionId(String functionName) {
        this(null, null, functionName);
    }

    public static FunctionId of(String functionName) {
        return new FunctionId(functionName);
    }

    public static FunctionId of(String schemaName, String functionName) {
        return new FunctionId(schemaName, functionName);
    }

    public static FunctionId of(String catalogName, String schemaName, String functionName) {
        return new FunctionId(catalogName, schemaName, functionName);
    }

    public String getFullFunctionName() {
        if (catalogName != null) {
            return catalogName + "." + schemaName + "." + functionName;
        }

        if (schemaName != null) {
            return schemaName + "." + functionName;
        }

        return functionName;
    }

    public String getLowerCatalogName() {
        return StringUtils.lowerCase(catalogName);
    }

    public String getLowerSchemaName() {
        return StringUtils.lowerCase(schemaName);
    }

    public String getLowerTableName() {
        return StringUtils.lowerCase(functionName);
    }

    // Getters and Setters
    public String getCatalogName() {
        return catalogName;
    }

    public String getSchemaName() {
        return schemaName;
    }

    public String getFunctionName() {
        return functionName;
    }

    public String getFuncType() {
        return funcType;
    }

    public void setFuncType(String funcType) {
        this.funcType = funcType;
    }

    public List<String> getFunctionArguments() {
        return functionArguments;
    }

    public void setFunctionArguments(List<String> functionArguments) {
        this.functionArguments = functionArguments;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        FunctionId that = (FunctionId) o;
        return Objects.equals(catalogName, that.catalogName) &&
               Objects.equals(schemaName, that.schemaName) &&
               Objects.equals(functionName, that.functionName) &&
               Objects.equals(funcType, that.funcType) &&
               Objects.equals(functionArguments, that.functionArguments);
    }

    @Override
    public int hashCode() {
        return Objects.hash(catalogName, schemaName, functionName, funcType, functionArguments);
    }

    @Override
    public String toString() {
        return "FunctionId{" +
               "catalogName='" + catalogName + '\'' +
               ", schemaName='" + schemaName + '\'' +
               ", functionName='" + functionName + '\'' +
               ", funcType='" + funcType + '\'' +
               ", functionArguments=" + functionArguments +
               '}';
    }
}
