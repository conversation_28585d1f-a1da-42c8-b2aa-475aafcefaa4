package com.xylink.sqltranspiler.core.ast;

import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

public class DefaultStatement extends Statement {
    private final StatementType statementType;

    public DefaultStatement(StatementType statementType) {
        this.statementType = statementType;
    }

    @Override
    public StatementType getStatementType() {
        return statementType;
    }

    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.OTHER;
    }

    @Override
    public SqlType getSqlType() {
        return SqlType.DML;
    }
}
