package com.xylink.sqltranspiler.core.ast.create;

import com.xylink.sqltranspiler.core.ast.TableId;
import com.xylink.sqltranspiler.core.ast.abs.AbsTableStatement;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

import java.util.Objects;

/**
 * CREATE TABLE ... LIKE 语句的AST节点
 * 
 * 用于表示MySQL的CREATE TABLE new_table LIKE existing_table语法
 * 这种语法会创建一个与现有表结构完全相同的新表
 */
public class CreateTableLike extends AbsTableStatement {
    
    private final TableId newTableId;      // 新表名
    private final TableId sourceTableId;   // 源表名
    private final boolean ifNotExists;     // 是否包含IF NOT EXISTS
    private final boolean temporary;       // 是否是临时表
    
    public CreateTableLike(TableId newTableId, TableId sourceTableId, boolean ifNotExists, boolean temporary) {
        this.newTableId = newTableId;
        this.sourceTableId = sourceTableId;
        this.ifNotExists = ifNotExists;
        this.temporary = temporary;
    }
    
    public CreateTableLike(TableId newTableId, TableId sourceTableId, boolean ifNotExists) {
        this(newTableId, sourceTableId, ifNotExists, false);
    }
    
    public CreateTableLike(TableId newTableId, TableId sourceTableId) {
        this(newTableId, sourceTableId, false, false);
    }
    
    @Override
    public StatementType getStatementType() {
        return StatementType.CREATE_TABLE_LIKE;
    }
    
    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.CREATE;
    }
    
    @Override
    public SqlType getSqlType() {
        return SqlType.DDL;
    }
    
    @Override
    public TableId getTableId() {
        return newTableId;
    }
    
    public TableId getNewTableId() {
        return newTableId;
    }
    
    public TableId getSourceTableId() {
        return sourceTableId;
    }
    
    public boolean isIfNotExists() {
        return ifNotExists;
    }
    
    public boolean isTemporary() {
        return temporary;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CreateTableLike that = (CreateTableLike) o;
        return ifNotExists == that.ifNotExists &&
               temporary == that.temporary &&
               Objects.equals(newTableId, that.newTableId) &&
               Objects.equals(sourceTableId, that.sourceTableId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(newTableId, sourceTableId, ifNotExists, temporary);
    }
    
    @Override
    public String toString() {
        return "CreateTableLike{" +
               "newTableId=" + newTableId +
               ", sourceTableId=" + sourceTableId +
               ", ifNotExists=" + ifNotExists +
               ", temporary=" + temporary +
               '}';
    }
}
