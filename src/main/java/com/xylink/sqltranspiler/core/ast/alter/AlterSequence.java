package com.xylink.sqltranspiler.core.ast.alter;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.TableId;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

/**
 * ALTER SEQUENCE语句的AST表示
 *
 * 根据MySQL官方文档：MySQL 8.0不直接支持SEQUENCE，但可以通过ALTER TABLE修改AUTO_INCREMENT模拟
 * 根据达梦数据库官方文档：https://eco.dameng.com/document/dm/zh-cn/pm/definition-statement.html
 * 达梦数据库完全支持ALTER SEQUENCE语句
 * 根据金仓数据库官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/SQL_Statements_2.html#alter-sequence
 * 金仓数据库完全支持PostgreSQL兼容的ALTER SEQUENCE语句
 * 根据神通数据库官方文档：神通数据库支持修改序列对象属性
 */
public class AlterSequence extends Statement {

    private final TableId sequenceId;
    private final Long restartWith;
    private final Long incrementBy;
    private final Long minValue;
    private final Long maxValue;
    private final Long cache;
    private final Boolean cycle;
    private final String ownedBy;

    public AlterSequence(TableId sequenceId, Long restartWith, Long incrementBy, 
                        Long minValue, Long maxValue, Long cache, Boolean cycle, String ownedBy) {
        this.sequenceId = sequenceId;
        this.restartWith = restartWith;
        this.incrementBy = incrementBy;
        this.minValue = minValue;
        this.maxValue = maxValue;
        this.cache = cache;
        this.cycle = cycle;
        this.ownedBy = ownedBy;
    }

    public AlterSequence(TableId sequenceId, Long restartWith) {
        this(sequenceId, restartWith, null, null, null, null, null, null);
    }

    public AlterSequence(TableId sequenceId, Long restartWith, Long incrementBy) {
        this(sequenceId, restartWith, incrementBy, null, null, null, null, null);
    }

    @Override
    public StatementType getStatementType() {
        return StatementType.ALTER_SEQUENCE;
    }

    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.ALTER;
    }

    @Override
    public SqlType getSqlType() {
        return SqlType.DDL;
    }

    // Getters
    public TableId getSequenceId() {
        return sequenceId;
    }

    public Long getRestartWith() {
        return restartWith;
    }

    public Long getIncrementBy() {
        return incrementBy;
    }

    public Long getMinValue() {
        return minValue;
    }

    public Long getMaxValue() {
        return maxValue;
    }

    public Long getCache() {
        return cache;
    }

    public Boolean getCycle() {
        return cycle;
    }

    public String getOwnedBy() {
        return ownedBy;
    }

    @Override
    public String toString() {
        return "AlterSequence{" +
                "sequenceId=" + sequenceId +
                ", restartWith=" + restartWith +
                ", incrementBy=" + incrementBy +
                ", minValue=" + minValue +
                ", maxValue=" + maxValue +
                ", cache=" + cache +
                ", cycle=" + cycle +
                ", ownedBy='" + ownedBy + '\'' +
                ", sql='" + getSql() + '\'' +
                '}';
    }
}
