package com.xylink.sqltranspiler.core.ast.table;

import java.util.Objects;

public class ColumnRel {
    private final String columnName;
    private final String typeName;
    private final String comment;
    private boolean nullable = true;
    private String defaultExpr;
    private boolean primaryKey = false;
    private boolean unique = false;
    private final ColumnDefType columnDefType;
    
    private int columnLength = 0;
    private int precision = 0;
    private int scale = 0;
    
    private String expression; // 计算表达式
    private String position;
    private String afterCol;
    private String jsonPath; // spark streaming sql json path
    
    private String computedExpr; // 计算列
    private String metadataKey; // 元数据列

    // CHECK约束支持
    private String checkConstraintName; // CHECK约束名称
    private String checkConstraintExpression; // CHECK约束表达式

    // FOREIGN KEY约束支持
    private String referencesTable; // 引用的表名
    private String referencesColumn; // 引用的列名

    public ColumnRel(String columnName, String typeName, String comment, boolean nullable,
                     String defaultExpr, boolean primaryKey, boolean unique, ColumnDefType columnDefType) {
        this.columnName = columnName;
        this.typeName = typeName;
        this.comment = comment;
        this.nullable = nullable;
        this.defaultExpr = defaultExpr;
        this.primaryKey = primaryKey;
        this.unique = unique;
        this.columnDefType = columnDefType != null ? columnDefType : ColumnDefType.COMPUTED;
    }

    // 保持向后兼容的构造函数
    public ColumnRel(String columnName, String typeName, String comment, boolean nullable,
                     String defaultExpr, boolean primaryKey, ColumnDefType columnDefType) {
        this(columnName, typeName, comment, nullable, defaultExpr, primaryKey, false, columnDefType);
    }

    public ColumnRel(String columnName, String typeName, String comment) {
        this(columnName, typeName, comment, true, null, false, false, ColumnDefType.COMPUTED);
    }

    public ColumnRel(String columnName, String typeName, String comment, boolean primaryKey, ColumnDefType columnDefType) {
        this(columnName, typeName, comment, true, null, primaryKey, false, columnDefType);
    }

    public ColumnRel(String columnName, String typeName, String comment, ColumnDefType columnDefType) {
        this(columnName, typeName, comment, true, null, false, false, columnDefType);
    }

    // Getters
    public String getColumnName() {
        return columnName;
    }

    public String getTypeName() {
        return typeName;
    }

    public String getComment() {
        return comment;
    }

    public boolean isNullable() {
        return nullable;
    }

    public String getDefaultExpr() {
        return defaultExpr;
    }

    public boolean isPrimaryKey() {
        return primaryKey;
    }

    public boolean isUnique() {
        return unique;
    }

    public ColumnDefType getColumnDefType() {
        return columnDefType;
    }

    public int getColumnLength() {
        return columnLength;
    }

    public int getPrecision() {
        return precision;
    }

    public int getScale() {
        return scale;
    }

    public String getExpression() {
        return expression;
    }

    public String getPosition() {
        return position;
    }

    public String getAfterCol() {
        return afterCol;
    }

    public String getJsonPath() {
        return jsonPath;
    }

    public String getComputedExpr() {
        return computedExpr;
    }

    public String getMetadataKey() {
        return metadataKey;
    }

    public String getCheckConstraintName() {
        return checkConstraintName;
    }

    public String getCheckConstraintExpression() {
        return checkConstraintExpression;
    }

    public String getReferencesTable() {
        return referencesTable;
    }

    public String getReferencesColumn() {
        return referencesColumn;
    }

    // Setters
    public void setNullable(boolean nullable) {
        this.nullable = nullable;
    }

    public void setDefaultExpr(String defaultExpr) {
        this.defaultExpr = defaultExpr;
    }

    public void setPrimaryKey(boolean primaryKey) {
        this.primaryKey = primaryKey;
    }

    public void setUnique(boolean unique) {
        this.unique = unique;
    }

    public void setColumnLength(int columnLength) {
        this.columnLength = columnLength;
    }

    public void setPrecision(int precision) {
        this.precision = precision;
    }

    public void setScale(int scale) {
        this.scale = scale;
    }

    public void setExpression(String expression) {
        this.expression = expression;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public void setAfterCol(String afterCol) {
        this.afterCol = afterCol;
    }

    public void setJsonPath(String jsonPath) {
        this.jsonPath = jsonPath;
    }

    public void setComputedExpr(String computedExpr) {
        this.computedExpr = computedExpr;
    }

    public void setMetadataKey(String metadataKey) {
        this.metadataKey = metadataKey;
    }

    public void setCheckConstraintName(String checkConstraintName) {
        this.checkConstraintName = checkConstraintName;
    }

    public void setCheckConstraintExpression(String checkConstraintExpression) {
        this.checkConstraintExpression = checkConstraintExpression;
    }

    public void setReferencesTable(String referencesTable) {
        this.referencesTable = referencesTable;
    }

    public void setReferencesColumn(String referencesColumn) {
        this.referencesColumn = referencesColumn;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ColumnRel columnRel = (ColumnRel) o;
        return nullable == columnRel.nullable &&
               primaryKey == columnRel.primaryKey &&
               unique == columnRel.unique &&
               columnLength == columnRel.columnLength &&
               precision == columnRel.precision &&
               scale == columnRel.scale &&
               Objects.equals(columnName, columnRel.columnName) &&
               Objects.equals(typeName, columnRel.typeName) &&
               Objects.equals(comment, columnRel.comment) &&
               Objects.equals(defaultExpr, columnRel.defaultExpr) &&
               columnDefType == columnRel.columnDefType &&
               Objects.equals(expression, columnRel.expression) &&
               Objects.equals(position, columnRel.position) &&
               Objects.equals(afterCol, columnRel.afterCol) &&
               Objects.equals(jsonPath, columnRel.jsonPath) &&
               Objects.equals(computedExpr, columnRel.computedExpr) &&
               Objects.equals(metadataKey, columnRel.metadataKey) &&
               Objects.equals(checkConstraintName, columnRel.checkConstraintName) &&
               Objects.equals(checkConstraintExpression, columnRel.checkConstraintExpression) &&
               Objects.equals(referencesTable, columnRel.referencesTable) &&
               Objects.equals(referencesColumn, columnRel.referencesColumn);
    }

    @Override
    public int hashCode() {
        return Objects.hash(columnName, typeName, comment, nullable, defaultExpr, primaryKey, unique,
                           columnDefType, columnLength, precision, scale, expression, position,
                           afterCol, jsonPath, computedExpr, metadataKey, checkConstraintName,
                           checkConstraintExpression, referencesTable, referencesColumn);
    }

    @Override
    public String toString() {
        return "ColumnRel{" +
               "columnName='" + columnName + '\'' +
               ", typeName='" + typeName + '\'' +
               ", comment='" + comment + '\'' +
               ", nullable=" + nullable +
               ", defaultExpr='" + defaultExpr + '\'' +
               ", primaryKey=" + primaryKey +
               ", unique=" + unique +
               ", columnDefType=" + columnDefType +
               ", columnLength=" + columnLength +
               ", precision=" + precision +
               ", scale=" + scale +
               ", expression='" + expression + '\'' +
               ", position='" + position + '\'' +
               ", afterCol='" + afterCol + '\'' +
               ", jsonPath='" + jsonPath + '\'' +
               ", computedExpr='" + computedExpr + '\'' +
               ", metadataKey='" + metadataKey + '\'' +
               ", checkConstraintName='" + checkConstraintName + '\'' +
               ", checkConstraintExpression='" + checkConstraintExpression + '\'' +
               ", referencesTable='" + referencesTable + '\'' +
               ", referencesColumn='" + referencesColumn + '\'' +
               '}';
    }
}
