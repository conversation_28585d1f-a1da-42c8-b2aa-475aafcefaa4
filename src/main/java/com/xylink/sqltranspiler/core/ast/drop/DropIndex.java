package com.xylink.sqltranspiler.core.ast.drop;

import com.xylink.sqltranspiler.core.ast.TableId;
import com.xylink.sqltranspiler.core.ast.abs.AbsTableStatement;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

/**
 * DROP INDEX语句的AST表示
 *
 * 根据MySQL官方文档：
 * DROP INDEX index_name ON table_name
 *     [ALGORITHM [=] {DEFAULT | INPLACE | COPY}]
 *     [LOCK [=] {DEFAULT | NONE | SHARED | EXCLUSIVE}]
 *
 * 根据神通数据库官方文档，DROP INDEX在神通数据库中是完全支持的
 * 参考文档第37680-37810行：DROP INDEX语句用于删除索引
 */
public class DropIndex extends AbsTableStatement {

    private final String indexName;
    private final TableId tableId;
    private boolean online = false;
    private boolean offline = false;
    private String algorithm;
    private String lockType;

    public DropIndex(String indexName, TableId tableId, boolean online, boolean offline, 
                    String algorithm, String lockType) {
        this.indexName = indexName;
        this.tableId = tableId;
        this.online = online;
        this.offline = offline;
        this.algorithm = algorithm;
        this.lockType = lockType;
    }

    public DropIndex(String indexName, TableId tableId, boolean online, boolean offline) {
        this(indexName, tableId, online, offline, null, null);
    }

    public DropIndex(String indexName, TableId tableId) {
        this(indexName, tableId, false, false, null, null);
    }

    @Override
    public StatementType getStatementType() {
        return StatementType.DROP_INDEX;
    }

    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.DROP;
    }

    @Override
    public SqlType getSqlType() {
        return SqlType.DDL;
    }

    @Override
    public TableId getTableId() {
        return tableId;
    }

    // Getters
    public String getIndexName() {
        return indexName;
    }

    public boolean isOnline() {
        return online;
    }

    public boolean isOffline() {
        return offline;
    }

    public String getAlgorithm() {
        return algorithm;
    }

    public String getLockType() {
        return lockType;
    }

    // Setters
    public void setOnline(boolean online) {
        this.online = online;
    }

    public void setOffline(boolean offline) {
        this.offline = offline;
    }

    public void setAlgorithm(String algorithm) {
        this.algorithm = algorithm;
    }

    public void setLockType(String lockType) {
        this.lockType = lockType;
    }

    @Override
    public String toString() {
        return "DropIndex{" +
                "indexName='" + indexName + '\'' +
                ", tableId=" + tableId +
                ", online=" + online +
                ", offline=" + offline +
                ", algorithm='" + algorithm + '\'' +
                ", lockType='" + lockType + '\'' +
                ", sql='" + getSql() + '\'' +
                '}';
    }
}
