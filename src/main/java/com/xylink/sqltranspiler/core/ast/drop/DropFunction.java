package com.xylink.sqltranspiler.core.ast.drop;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

/**
 * DROP FUNCTION语句的AST表示
 * 
 * 支持MySQL和各种数据库的函数删除语法：
 * - MySQL: DROP FUNCTION [IF EXISTS] function_name
 * - 金仓数据库: DROP FUNCTION [IF EXISTS] function_name(parameters) [CASCADE | RESTRICT]
 * - 达梦数据库: DROP FUNCTION [IF EXISTS] function_name
 * - 神通数据库: DROP FUNCTION [IF EXISTS] function_name [CASCADE]
 */
public class DropFunction extends Statement {
    private final String functionName;
    private final String parameters;
    private final boolean ifExists;
    private final boolean cascade;
    private final boolean restrict;

    public DropFunction(String functionName, String parameters, boolean ifExists, boolean cascade, boolean restrict) {
        this.functionName = functionName;
        this.parameters = parameters;
        this.ifExists = ifExists;
        this.cascade = cascade;
        this.restrict = restrict;
    }

    public DropFunction(String functionName, boolean ifExists) {
        this(functionName, null, ifExists, false, false);
    }

    public DropFunction(String functionName) {
        this(functionName, null, false, false, false);
    }

    @Override
    public StatementType getStatementType() {
        return StatementType.DROP_FUNCTION;
    }

    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.DROP;
    }

    @Override
    public SqlType getSqlType() {
        return SqlType.DDL;
    }

    // Getters
    public String getFunctionName() {
        return functionName;
    }

    public String getParameters() {
        return parameters;
    }

    public boolean isIfExists() {
        return ifExists;
    }

    public boolean isCascade() {
        return cascade;
    }

    public boolean isRestrict() {
        return restrict;
    }
}
