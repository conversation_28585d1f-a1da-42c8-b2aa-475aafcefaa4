package com.xylink.sqltranspiler.core.ast.common;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

public class UseStatement extends Statement {
    private final String databaseName;

    public UseStatement(String databaseName) {
        this.databaseName = databaseName;
    }

    @Override
    public StatementType getStatementType() {
        return StatementType.USE;
    }

    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.OTHER;
    }

    @Override
    public SqlType getSqlType() {
        return SqlType.DDL;
    }

    public String getDatabaseName() {
        return databaseName;
    }
}
