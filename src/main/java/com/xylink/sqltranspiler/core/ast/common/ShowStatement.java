package com.xylink.sqltranspiler.core.ast.common;

import org.apache.commons.lang3.StringUtils;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

public class ShowStatement extends Statement {
    private final String[] keywords;

    public ShowStatement(String... keywords) {
        this.keywords = keywords;
    }

    @Override
    public StatementType getStatementType() {
        return StatementType.SHOW;
    }

    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.OTHER;
    }

    @Override
    public SqlType getSqlType() {
        return SqlType.DML;
    }

    public boolean checkSql(String sql) {
        String[] words = StringUtils.split(sql, " ");
        if (this.keywords.length != words.length) {
            return false;
        }

        int size = this.keywords.length;
        for (int i = 0; i < size; i++) {
            if (!StringUtils.equalsIgnoreCase(this.keywords[i], words[i])) {
                return false;
            }
        }

        return true;
    }

    public String[] getKeywords() {
        return keywords;
    }
}
