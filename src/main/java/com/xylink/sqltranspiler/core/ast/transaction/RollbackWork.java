package com.xylink.sqltranspiler.core.ast.transaction;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

/**
 * ROLLBACK语句的AST表示
 *
 * 根据MySQL官方文档：
 * ROLLBACK [WORK] [TO [SAVEPOINT] savepoint_name]
 *
 * 根据KingbaseES官方文档，ROLLBACK在KingbaseES中是支持的
 */
public class RollbackWork extends Statement {

    private boolean work;
    private String savepointName;

    public RollbackWork() {
        super();
    }

    public RollbackWork(String sql) {
        super();
        setSql(sql);
    }
    
    public boolean isWork() {
        return work;
    }
    
    public void setWork(boolean work) {
        this.work = work;
    }
    
    public String getSavepointName() {
        return savepointName;
    }
    
    public void setSavepointName(String savepointName) {
        this.savepointName = savepointName;
    }

    @Override
    public StatementType getStatementType() {
        return StatementType.UNKOWN; // 暂时使用UNKOWN，稍后添加ROLLBACK
    }

    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.OTHER;
    }

    @Override
    public SqlType getSqlType() {
        return SqlType.TCL; // Transaction Control Language
    }

    @Override
    public String toString() {
        return "RollbackWork{" +
                "work=" + work +
                ", savepointName='" + savepointName + '\'' +
                ", sql='" + getSql() + '\'' +
                '}';
    }
}
