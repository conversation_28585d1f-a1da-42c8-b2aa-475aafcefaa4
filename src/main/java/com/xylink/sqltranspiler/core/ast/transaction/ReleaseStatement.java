package com.xylink.sqltranspiler.core.ast.transaction;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

/**
 * RELEASE SAVEPOINT语句的AST表示
 * 
 * 根据MySQL官方文档：
 * RELEASE SAVEPOINT savepoint_name
 * 
 * 根据KingbaseES官方文档，RELEASE SAVEPOINT在KingbaseES中是支持的
 */
public class ReleaseStatement extends Statement {
    
    private String savepointName;
    
    public ReleaseStatement() {
        super();
    }
    
    public ReleaseStatement(String sql) {
        super();
        setSql(sql);
    }
    
    public String getSavepointName() {
        return savepointName;
    }
    
    public void setSavepointName(String savepointName) {
        this.savepointName = savepointName;
    }
    
    @Override
    public StatementType getStatementType() {
        return StatementType.UNKOWN; // 暂时使用UNKOWN，稍后添加RELEASE_SAVEPOINT
    }
    
    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.OTHER;
    }
    
    @Override
    public SqlType getSqlType() {
        return SqlType.TCL; // Transaction Control Language
    }
    
    @Override
    public String toString() {
        return "ReleaseStatement{" +
                "savepointName='" + savepointName + '\'' +
                ", sql='" + getSql() + '\'' +
                '}';
    }
}
