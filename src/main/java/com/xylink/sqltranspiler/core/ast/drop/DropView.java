package com.xylink.sqltranspiler.core.ast.drop;

import java.util.ArrayList;
import java.util.List;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.TableId;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

/**
 * DROP VIEW语句的AST表示
 *
 * 根据MySQL官方文档：
 * DROP VIEW [IF EXISTS] view_name [, view_name] ... [RESTRICT | CASCADE]
 *
 * 根据神通数据库官方文档，DROP VIEW在神通数据库中是完全支持的
 * 参考文档第37680-37810行：DROP VIEW语句用于删除视图
 */
public class DropView extends Statement {

    private final List<TableId> viewIds;
    private boolean ifExists = false;
    private boolean cascade = false;
    private boolean restrict = false;

    public DropView(List<TableId> viewIds, boolean ifExists, boolean cascade, boolean restrict) {
        this.viewIds = viewIds != null ? viewIds : new ArrayList<>();
        this.ifExists = ifExists;
        this.cascade = cascade;
        this.restrict = restrict;
    }

    public DropView(List<TableId> viewIds, boolean ifExists) {
        this(viewIds, ifExists, false, false);
    }

    public DropView(List<TableId> viewIds) {
        this(viewIds, false, false, false);
    }

    public DropView(TableId viewId, boolean ifExists, boolean cascade, boolean restrict) {
        this.viewIds = new ArrayList<>();
        this.viewIds.add(viewId);
        this.ifExists = ifExists;
        this.cascade = cascade;
        this.restrict = restrict;
    }

    public DropView(TableId viewId, boolean ifExists) {
        this(viewId, ifExists, false, false);
    }

    public DropView(TableId viewId) {
        this(viewId, false, false, false);
    }

    @Override
    public StatementType getStatementType() {
        return StatementType.DROP_VIEW;
    }

    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.DROP;
    }

    @Override
    public SqlType getSqlType() {
        return SqlType.DDL;
    }

    // Getters
    public List<TableId> getViewIds() {
        return viewIds;
    }

    public TableId getFirstViewId() {
        return viewIds.isEmpty() ? null : viewIds.get(0);
    }

    public boolean isIfExists() {
        return ifExists;
    }

    public boolean isCascade() {
        return cascade;
    }

    public boolean isRestrict() {
        return restrict;
    }

    // Setters
    public void setIfExists(boolean ifExists) {
        this.ifExists = ifExists;
    }

    public void setCascade(boolean cascade) {
        this.cascade = cascade;
    }

    public void setRestrict(boolean restrict) {
        this.restrict = restrict;
    }

    @Override
    public String toString() {
        return "DropView{" +
                "viewIds=" + viewIds +
                ", ifExists=" + ifExists +
                ", cascade=" + cascade +
                ", restrict=" + restrict +
                ", sql='" + getSql() + '\'' +
                '}';
    }
}
