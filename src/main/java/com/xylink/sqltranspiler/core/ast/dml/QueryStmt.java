package com.xylink.sqltranspiler.core.ast.dml;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;

import com.xylink.sqltranspiler.core.ast.FunctionId;
import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.TableId;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

public class QueryStmt extends Statement {
    private List<TableId> inputTables;
    private Integer limit;
    private Integer offset;
    private final HashSet<FunctionId> functionNames = new HashSet<>();

    public QueryStmt(List<TableId> inputTables, Integer limit, Integer offset) {
        this.inputTables = inputTables;
        this.limit = limit;
        this.offset = offset;
    }

    public QueryStmt(List<TableId> inputTables) {
        this(inputTables, null, null);
    }

    public QueryStmt() {
        this(List.of());
    }

    @Override
    public StatementType getStatementType() {
        return StatementType.SELECT;
    }

    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.READ;
    }

    @Override
    public SqlType getSqlType() {
        return SqlType.DQL;
    }

    // Getters
    public List<TableId> getInputTables() {
        return inputTables;
    }

    public Integer getLimit() {
        return limit;
    }

    public Integer getOffset() {
        return offset;
    }

    public HashSet<FunctionId> getFunctionNames() {
        return functionNames;
    }

    // Setters
    public void setInputTables(List<TableId> inputTables) {
        this.inputTables = inputTables;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        QueryStmt queryStmt = (QueryStmt) o;
        return Objects.equals(inputTables, queryStmt.inputTables) &&
               Objects.equals(limit, queryStmt.limit) &&
               Objects.equals(offset, queryStmt.offset) &&
               Objects.equals(functionNames, queryStmt.functionNames);
    }

    @Override
    public int hashCode() {
        return Objects.hash(inputTables, limit, offset, functionNames);
    }

    @Override
    public String toString() {
        return "QueryStmt{" +
               "inputTables=" + inputTables +
               ", limit=" + limit +
               ", offset=" + offset +
               ", functionNames=" + functionNames +
               '}';
    }
}
