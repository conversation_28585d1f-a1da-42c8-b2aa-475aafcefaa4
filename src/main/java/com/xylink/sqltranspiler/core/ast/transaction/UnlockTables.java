package com.xylink.sqltranspiler.core.ast.transaction;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

/**
 * UNLOCK TABLES语句
 * MySQL语法: UNLOCK TABLES
 * 
 * 注意：达梦数据库不支持UNLOCK TABLES语句，转换时应该注释掉或删除
 */
public class UnlockTables extends Statement {
    
    public UnlockTables() {
        // UNLOCK TABLES没有参数
    }
    
    @Override
    public StatementType getStatementType() {
        return StatementType.UNLOCK_TABLES;
    }
    
    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.WRITE; // UNLOCK TABLES需要写权限
    }
    
    @Override
    public SqlType getSqlType() {
        return SqlType.TCL;
    }
}
