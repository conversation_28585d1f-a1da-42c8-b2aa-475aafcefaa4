package com.xylink.sqltranspiler.core.ast.dml;

import java.util.List;

import com.xylink.sqltranspiler.core.ast.TableId;
import com.xylink.sqltranspiler.core.ast.abs.AbsTableStatement;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

public class InsertTable extends AbsTableStatement {
    private final TableId tableId;
    private final InsertMode insertMode;
    private final List<String> columns;
    private final QueryStmt queryStmt;
    private final ValuesClause valuesClause;

    public InsertTable(TableId tableId, InsertMode insertMode, List<String> columns, QueryStmt queryStmt, ValuesClause valuesClause) {
        this.tableId = tableId;
        this.insertMode = insertMode != null ? insertMode : InsertMode.INTO;
        this.columns = columns;
        this.queryStmt = queryStmt;
        this.valuesClause = valuesClause;
    }

    public InsertTable(TableId tableId, List<String> columns, QueryStmt queryStmt) {
        this(tableId, InsertMode.INTO, columns, queryStmt, null);
    }

    public InsertTable(TableId tableId, List<String> columns, ValuesClause valuesClause) {
        this(tableId, InsertMode.INTO, columns, null, valuesClause);
    }

    public InsertTable(TableId tableId, QueryStmt queryStmt) {
        this(tableId, InsertMode.INTO, null, queryStmt, null);
    }

    @Override
    public StatementType getStatementType() {
        return StatementType.INSERT;
    }

    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.WRITE;
    }

    @Override
    public SqlType getSqlType() {
        return SqlType.DML;
    }

    @Override
    public TableId getTableId() {
        return tableId;
    }

    // Getters
    public InsertMode getInsertMode() {
        return insertMode;
    }

    public List<String> getColumns() {
        return columns;
    }

    public QueryStmt getQueryStmt() {
        return queryStmt;
    }

    public ValuesClause getValuesClause() {
        return valuesClause;
    }
}
