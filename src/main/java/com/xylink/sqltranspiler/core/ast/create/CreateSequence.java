package com.xylink.sqltranspiler.core.ast.create;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.TableId;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

/**
 * CREATE SEQUENCE语句的AST表示
 *
 * 根据MySQL官方文档：MySQL 8.0不直接支持SEQUENCE，但可以通过AUTO_INCREMENT模拟
 * 根据达梦数据库官方文档：https://eco.dameng.com/document/dm/zh-cn/pm/definition-statement.html
 * 达梦数据库完全支持CREATE SEQUENCE语句，语法类似Oracle
 * 根据金仓数据库官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/SQL_Statements_3.html#create-sequence
 * 金仓数据库完全支持PostgreSQL兼容的CREATE SEQUENCE语句
 * 根据神通数据库官方文档：神通数据库支持序列对象
 */
public class CreateSequence extends Statement {

    private final TableId sequenceId;
    private final Long startWith;
    private final Long incrementBy;
    private final Long minValue;
    private final Long maxValue;
    private final Long cache;
    private final boolean cycle;
    private final boolean ifNotExists;
    private final String dataType;

    public CreateSequence(TableId sequenceId, Long startWith, Long incrementBy, 
                         Long minValue, Long maxValue, Long cache, boolean cycle, 
                         boolean ifNotExists, String dataType) {
        this.sequenceId = sequenceId;
        this.startWith = startWith;
        this.incrementBy = incrementBy;
        this.minValue = minValue;
        this.maxValue = maxValue;
        this.cache = cache;
        this.cycle = cycle;
        this.ifNotExists = ifNotExists;
        this.dataType = dataType;
    }

    public CreateSequence(TableId sequenceId) {
        this(sequenceId, null, null, null, null, null, false, false, null);
    }

    public CreateSequence(TableId sequenceId, Long startWith, Long incrementBy) {
        this(sequenceId, startWith, incrementBy, null, null, null, false, false, null);
    }

    public CreateSequence(TableId sequenceId, boolean ifNotExists) {
        this(sequenceId, null, null, null, null, null, false, ifNotExists, null);
    }

    @Override
    public StatementType getStatementType() {
        return StatementType.CREATE_SEQUENCE;
    }

    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.CREATE;
    }

    @Override
    public SqlType getSqlType() {
        return SqlType.DDL;
    }

    // Getters
    public TableId getSequenceId() {
        return sequenceId;
    }

    public Long getStartWith() {
        return startWith;
    }

    public Long getIncrementBy() {
        return incrementBy;
    }

    public Long getMinValue() {
        return minValue;
    }

    public Long getMaxValue() {
        return maxValue;
    }

    public Long getCache() {
        return cache;
    }

    public boolean isCycle() {
        return cycle;
    }

    public boolean isIfNotExists() {
        return ifNotExists;
    }

    public String getDataType() {
        return dataType;
    }

    @Override
    public String toString() {
        return "CreateSequence{" +
                "sequenceId=" + sequenceId +
                ", startWith=" + startWith +
                ", incrementBy=" + incrementBy +
                ", minValue=" + minValue +
                ", maxValue=" + maxValue +
                ", cache=" + cache +
                ", cycle=" + cycle +
                ", ifNotExists=" + ifNotExists +
                ", dataType='" + dataType + '\'' +
                ", sql='" + getSql() + '\'' +
                '}';
    }
}
