package com.xylink.sqltranspiler.core.ast.transaction;

import java.util.ArrayList;
import java.util.List;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.TableId;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

/**
 * LOCK TABLES语句
 * MySQL语法: LOCK TABLES table_name [AS alias] lock_type [, table_name [AS alias] lock_type] ...
 * 
 * 注意：达梦数据库不支持LOCK TABLES语句，转换时应该注释掉或删除
 */
public class LockTables extends Statement {
    
    private final List<LockTableElement> lockElements;
    
    public LockTables(List<LockTableElement> lockElements) {
        this.lockElements = lockElements != null ? lockElements : new ArrayList<>();
    }
    
    @Override
    public StatementType getStatementType() {
        return StatementType.LOCK_TABLES;
    }
    
    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.WRITE; // LOCK TABLES需要写权限
    }
    
    @Override
    public SqlType getSqlType() {
        return SqlType.TCL;
    }
    
    public List<LockTableElement> getLockElements() {
        return lockElements;
    }
    
    /**
     * 获取所有被锁定的表
     */
    public List<TableId> getLockedTables() {
        List<TableId> tables = new ArrayList<>();
        for (LockTableElement element : lockElements) {
            tables.add(element.getTableId());
        }
        return tables;
    }
    
    /**
     * 锁定表元素
     */
    public static class LockTableElement {
        private final TableId tableId;
        private final String alias;
        private final LockType lockType;
        
        public LockTableElement(TableId tableId, String alias, LockType lockType) {
            this.tableId = tableId;
            this.alias = alias;
            this.lockType = lockType;
        }
        
        public LockTableElement(TableId tableId, LockType lockType) {
            this(tableId, null, lockType);
        }
        
        public TableId getTableId() {
            return tableId;
        }
        
        public String getAlias() {
            return alias;
        }
        
        public LockType getLockType() {
            return lockType;
        }
    }
    
    /**
     * 锁定类型
     */
    public enum LockType {
        READ,           // READ
        READ_LOCAL,     // READ LOCAL
        WRITE,          // WRITE
        LOW_PRIORITY_WRITE  // LOW_PRIORITY WRITE
    }
}
