package com.xylink.sqltranspiler.core.ast.dml;

import java.util.List;

/**
 * 表示INSERT语句中的VALUES子句
 * 包含多行数据，每行是一个表达式列表
 * 同时保存原始格式信息以保持格式一致性
 */
public class ValuesClause {
    private final List<List<String>> rows;
    private final String originalValuesText; // 保存原始VALUES部分的文本

    public ValuesClause(List<List<String>> rows) {
        this.rows = rows;
        this.originalValuesText = null;
    }

    public ValuesClause(List<List<String>> rows, String originalValuesText) {
        this.rows = rows;
        this.originalValuesText = originalValuesText;
    }

    public List<List<String>> getRows() {
        return rows;
    }

    /**
     * 获取原始VALUES部分的文本
     * 如果有原始文本，则可以用于保持原始格式
     */
    public String getOriginalValuesText() {
        return originalValuesText;
    }

    /**
     * 检查是否有原始格式信息
     */
    public boolean hasOriginalFormat() {
        return originalValuesText != null && !originalValuesText.trim().isEmpty();
    }

    @Override
    public String toString() {
        return "ValuesClause{rows=" + rows + ", hasOriginalFormat=" + hasOriginalFormat() + "}";
    }
}
