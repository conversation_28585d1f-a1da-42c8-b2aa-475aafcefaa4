package com.xylink.sqltranspiler.core.ast.privilege;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

import java.util.List;

/**
 * GRANT权限授予语句的AST表示
 * 
 * 根据MySQL官方文档：https://dev.mysql.com/doc/refman/8.4/en/grant.html
 * 根据达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/pm/
 * 根据金仓官方文档：https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
 * 根据神通官方文档：神通数据库支持完整的权限管理
 * 
 * MySQL语法：
 * GRANT privilege_list ON object_type privilege_level TO user_list [WITH GRANT OPTION]
 * 
 * 支持的权限类型：
 * - ALL [PRIVILEGES]
 * - SELECT, INSERT, UPDATE, DELETE
 * - CREATE, DROP, ALTER
 * - INDEX, REFERENCES
 * - EXECUTE (for functions/procedures)
 * - GRANT OPTION
 */
public class Grant extends Statement {
    
    private List<String> privileges;        // 权限列表，如 ["SELECT", "INSERT", "UPDATE"]
    private String objectType;              // 对象类型，如 "TABLE", "FUNCTION", "PROCEDURE"
    private String privilegeLevel;          // 权限级别，如 "database.*", "database.table", "*.*"
    private List<String> users;             // 用户列表
    private boolean withGrantOption;        // 是否包含 WITH GRANT OPTION
    private String originalSql;             // 原始SQL语句
    
    public Grant() {
        super();
    }
    
    public Grant(List<String> privileges, String objectType, String privilegeLevel, 
                 List<String> users, boolean withGrantOption) {
        this.privileges = privileges;
        this.objectType = objectType;
        this.privilegeLevel = privilegeLevel;
        this.users = users;
        this.withGrantOption = withGrantOption;
    }
    
    public Grant(String originalSql) {
        super();
        this.originalSql = originalSql;
        setSql(originalSql);
    }
    
    @Override
    public StatementType getStatementType() {
        return StatementType.GRANT;
    }
    
    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.ADMIN; // GRANT语句需要管理员权限
    }
    
    @Override
    public SqlType getSqlType() {
        return SqlType.DCL; // Data Control Language
    }
    
    // Getters and Setters
    public List<String> getPrivileges() {
        return privileges;
    }
    
    public void setPrivileges(List<String> privileges) {
        this.privileges = privileges;
    }
    
    public String getObjectType() {
        return objectType;
    }
    
    public void setObjectType(String objectType) {
        this.objectType = objectType;
    }
    
    public String getPrivilegeLevel() {
        return privilegeLevel;
    }
    
    public void setPrivilegeLevel(String privilegeLevel) {
        this.privilegeLevel = privilegeLevel;
    }
    
    public List<String> getUsers() {
        return users;
    }
    
    public void setUsers(List<String> users) {
        this.users = users;
    }
    
    public boolean isWithGrantOption() {
        return withGrantOption;
    }
    
    public void setWithGrantOption(boolean withGrantOption) {
        this.withGrantOption = withGrantOption;
    }
    
    public String getOriginalSql() {
        return originalSql;
    }
    
    public void setOriginalSql(String originalSql) {
        this.originalSql = originalSql;
    }
    
    @Override
    public String toString() {
        return "Grant{" +
                "privileges=" + privileges +
                ", objectType='" + objectType + '\'' +
                ", privilegeLevel='" + privilegeLevel + '\'' +
                ", users=" + users +
                ", withGrantOption=" + withGrantOption +
                ", originalSql='" + originalSql + '\'' +
                '}';
    }
}
