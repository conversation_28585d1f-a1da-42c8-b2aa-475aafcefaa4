package com.xylink.sqltranspiler.core.ast.common;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

public class SetStatement extends Statement {
    private final String variableName;
    private final String value;

    public SetStatement(String variableName, String value) {
        this.variableName = variableName;
        this.value = value;
    }

    @Override
    public StatementType getStatementType() {
        return StatementType.SET;
    }

    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.OTHER;
    }

    @Override
    public SqlType getSqlType() {
        return SqlType.DDL;
    }

    public String getVariableName() {
        return variableName;
    }

    public String getValue() {
        return value;
    }
}
