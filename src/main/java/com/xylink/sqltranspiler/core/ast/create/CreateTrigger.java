package com.xylink.sqltranspiler.core.ast.create;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

/**
 * CREATE TRIGGER语句的AST表示
 * 
 * 支持MySQL和各种数据库的触发器创建语法：
 * - MySQL: CREATE TRIGGER trigger_name BEFORE|AFTER INSERT|UPDATE|DELETE ON table_name FOR EACH ROW BEGIN ... END
 * - 金仓数据库: CREATE TRIGGER trigger_name BEFORE|AFTER INSERT|UPDATE|DELETE ON table_name FOR EACH ROW EXECUTE FUNCTION function_name()
 * - 达梦数据库: CREATE OR REPLACE TRIGGER trigger_name BEFORE|AFTER INSERT|UPDATE|DELETE ON table_name FOR EACH ROW BEGIN ... END
 * - 神通数据库: CREATE OR REPLACE TRIGGER trigger_name BEFORE|AFTER INSERT|UPDATE|DELETE ON table_name FOR EACH ROW BEGIN ... END
 */
public class CreateTrigger extends Statement {
    private final String triggerName;
    private final String timing; // BEFORE, AFTER
    private final String event; // INSERT, UPDATE, DELETE
    private final String tableName;
    private final String triggerBody;
    private final String functionName;
    private final boolean orReplace;
    private final boolean forEachRow;

    public CreateTrigger(String triggerName, String timing, String event, String tableName, 
                        String triggerBody, String functionName, boolean orReplace, boolean forEachRow) {
        this.triggerName = triggerName;
        this.timing = timing;
        this.event = event;
        this.tableName = tableName;
        this.triggerBody = triggerBody;
        this.functionName = functionName;
        this.orReplace = orReplace;
        this.forEachRow = forEachRow;
    }

    public CreateTrigger(String triggerName, String timing, String event, String tableName, String triggerBody) {
        this(triggerName, timing, event, tableName, triggerBody, null, false, true);
    }

    @Override
    public StatementType getStatementType() {
        return StatementType.CREATE_FUNCTION; // 注意：StatementType枚举中没有CREATE_TRIGGER，使用CREATE_FUNCTION作为临时替代
    }

    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.CREATE;
    }

    @Override
    public SqlType getSqlType() {
        return SqlType.DDL;
    }

    // Getters
    public String getTriggerName() {
        return triggerName;
    }

    public String getTiming() {
        return timing;
    }

    public String getEvent() {
        return event;
    }

    public String getTableName() {
        return tableName;
    }

    public String getTriggerBody() {
        return triggerBody;
    }

    public String getFunctionName() {
        return functionName;
    }

    public boolean isOrReplace() {
        return orReplace;
    }

    public boolean isForEachRow() {
        return forEachRow;
    }
}
