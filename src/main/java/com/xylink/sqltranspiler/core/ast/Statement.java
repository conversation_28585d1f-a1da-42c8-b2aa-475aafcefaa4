package com.xylink.sqltranspiler.core.ast;

import java.io.Serializable;

import org.apache.commons.lang3.StringUtils;

import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

public abstract class Statement implements Serializable {
    private String sql = "";

    public abstract StatementType getStatementType();
    public abstract PrivilegeType getPrivilegeType();
    public abstract SqlType getSqlType();

    public void setSql(String sql) {
        if (StringUtils.endsWith(sql, ";")) {
            this.sql = StringUtils.substringBeforeLast(sql, ";");
        } else {
            this.sql = sql;
        }
    }

    public String getSql() {
        return this.sql;
    }
}
