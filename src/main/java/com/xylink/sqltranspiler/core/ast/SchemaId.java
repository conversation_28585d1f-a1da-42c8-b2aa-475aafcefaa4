package com.xylink.sqltranspiler.core.ast;

import java.util.Objects;

import org.apache.commons.lang3.StringUtils;

public class SchemaId {
    private final String catalogName;
    private final String schemaName;

    public SchemaId(String catalogName, String schemaName) {
        this.catalogName = catalogName;
        this.schemaName = schemaName;
    }

    public SchemaId(String schemaName) {
        this(null, schemaName);
    }

    public static SchemaId of(String schemaName) {
        return new SchemaId(schemaName);
    }

    public static SchemaId of(String catalogName, String schemaName) {
        return new SchemaId(catalogName, schemaName);
    }

    public String getFullSchemaName() {
        if (catalogName != null) {
            return catalogName + "." + schemaName;
        }

        return schemaName;
    }

    public String getLowerCatalogName() {
        return StringUtils.lowerCase(catalogName);
    }

    public String getLowerSchemaName() {
        return StringUtils.lowerCase(schemaName);
    }

    // Getters
    public String getCatalogName() {
        return catalogName;
    }

    public String getSchemaName() {
        return schemaName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SchemaId schemaId = (SchemaId) o;
        return Objects.equals(catalogName, schemaId.catalogName) &&
               Objects.equals(schemaName, schemaId.schemaName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(catalogName, schemaName);
    }

    @Override
    public String toString() {
        return "SchemaId{" +
               "catalogName='" + catalogName + '\'' +
               ", schemaName='" + schemaName + '\'' +
               '}';
    }
}
