package com.xylink.sqltranspiler.core.ast.alter;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.TableId;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

/**
 * ALTER VIEW语句的AST表示
 *
 * 根据MySQL官方文档：
 * ALTER [ALGORITHM = {UNDEFINED | MERGE | TEMPTABLE}]
 *     [DEFINER = user] [SQL SECURITY { DEFINER | INVOKER }]
 *     VIEW view_name [(column_list)]
 *     AS select_statement
 *     [WITH [CASCADED | LOCAL] CHECK OPTION]
 *
 * 根据达梦数据库官方文档，ALTER VIEW在达梦数据库中是支持的
 * 根据金仓数据库官方文档，ALTER VIEW在金仓数据库中是支持的，兼容PostgreSQL语法
 * 根据神通数据库官方文档，ALTER VIEW在神通数据库中是支持的
 */
public class AlterView extends Statement {

    private final TableId viewId;
    private final String selectStatement;
    private final String[] columnList;
    private final String algorithm;
    private final String definer;
    private final String sqlSecurity;
    private final String checkOption;
    private final boolean withCheckOption;
    private final boolean cascaded;
    private final boolean local;

    public AlterView(TableId viewId, String selectStatement, String[] columnList,
                    String algorithm, String definer, String sqlSecurity,
                    String checkOption, boolean withCheckOption, boolean cascaded, boolean local) {
        this.viewId = viewId;
        this.selectStatement = selectStatement;
        this.columnList = columnList;
        this.algorithm = algorithm;
        this.definer = definer;
        this.sqlSecurity = sqlSecurity;
        this.checkOption = checkOption;
        this.withCheckOption = withCheckOption;
        this.cascaded = cascaded;
        this.local = local;
    }

    public AlterView(TableId viewId, String selectStatement) {
        this(viewId, selectStatement, null, null, null, null, null, false, false, false);
    }

    public AlterView(TableId viewId, String selectStatement, String[] columnList) {
        this(viewId, selectStatement, columnList, null, null, null, null, false, false, false);
    }

    @Override
    public StatementType getStatementType() {
        return StatementType.ALTER_VIEW;
    }

    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.ALTER;
    }

    @Override
    public SqlType getSqlType() {
        return SqlType.DDL;
    }

    // Getters
    public TableId getViewId() {
        return viewId;
    }

    public String getSelectStatement() {
        return selectStatement;
    }

    public String[] getColumnList() {
        return columnList;
    }

    public String getAlgorithm() {
        return algorithm;
    }

    public String getDefiner() {
        return definer;
    }

    public String getSqlSecurity() {
        return sqlSecurity;
    }

    public String getCheckOption() {
        return checkOption;
    }

    public boolean isWithCheckOption() {
        return withCheckOption;
    }

    public boolean isCascaded() {
        return cascaded;
    }

    public boolean isLocal() {
        return local;
    }

    @Override
    public String toString() {
        return "AlterView{" +
                "viewId=" + viewId +
                ", selectStatement='" + selectStatement + '\'' +
                ", columnList=" + (columnList != null ? String.join(", ", columnList) : "null") +
                ", algorithm='" + algorithm + '\'' +
                ", definer='" + definer + '\'' +
                ", sqlSecurity='" + sqlSecurity + '\'' +
                ", checkOption='" + checkOption + '\'' +
                ", withCheckOption=" + withCheckOption +
                ", cascaded=" + cascaded +
                ", local=" + local +
                ", sql='" + getSql() + '\'' +
                '}';
    }
}
