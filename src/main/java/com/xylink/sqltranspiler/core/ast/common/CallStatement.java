package com.xylink.sqltranspiler.core.ast.common;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

/**
 * CALL语句的AST表示
 * 
 * 支持MySQL和各种数据库的存储过程调用语法：
 * - MySQL: CALL procedure_name(parameters)
 * - 神通数据库: EXEC procedure_name(parameters) 或 CALL procedure_name(parameters)
 * - 达梦数据库: CALL procedure_name(parameters)
 * - 金仓数据库: CALL procedure_name(parameters)
 */
public class CallStatement extends Statement {
    private final String procedureName;
    private final String parameters;
    private final boolean isFunction; // 区分存储过程和函数调用

    public CallStatement(String procedureName, String parameters) {
        this(procedureName, parameters, false);
    }

    public CallStatement(String procedureName, String parameters, boolean isFunction) {
        this.procedureName = procedureName;
        this.parameters = parameters;
        this.isFunction = isFunction;
    }

    @Override
    public StatementType getStatementType() {
        return StatementType.CALL;
    }

    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.EXECUTE;
    }

    @Override
    public SqlType getSqlType() {
        return SqlType.DML;
    }

    public String getProcedureName() {
        return procedureName;
    }

    public String getParameters() {
        return parameters;
    }

    public boolean isFunction() {
        return isFunction;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("CALL ").append(procedureName);
        if (parameters != null && !parameters.trim().isEmpty()) {
            sb.append("(").append(parameters).append(")");
        }
        return sb.toString();
    }
}
