package com.xylink.sqltranspiler.core.ast;

import java.util.Objects;

import org.apache.commons.lang3.StringUtils;

public class TableId {
    private final String catalogName;
    private final String schemaName;
    private final String tableName;
    private final String metaType;

    public TableId(String catalogName, String schemaName, String tableName, String metaType) {
        this.catalogName = catalogName;
        this.schemaName = schemaName;
        this.tableName = tableName;
        this.metaType = metaType;
    }

    public TableId(String catalogName, String schemaName, String tableName) {
        this(catalogName, schemaName, tableName, null);
    }

    public TableId(String schemaName, String tableName) {
        this(null, schemaName, tableName, null);
    }

    public TableId(String tableName) {
        this(null, null, tableName, null);
    }

    public static TableId of(String tableName) {
        return new TableId(tableName);
    }

    public static TableId of(String schemaName, String tableName) {
        return new TableId(schemaName, tableName);
    }

    public static TableId of(String catalogName, String schemaName, String tableName) {
        return new TableId(catalogName, schemaName, tableName);
    }

    public String getFullTableName() {
        if (catalogName != null) {
            return catalogName + "." + schemaName + "." + tableName;
        }

        if (schemaName != null) {
            return schemaName + "." + tableName;
        }

        return tableName;
    }

    public String getLowerCatalogName() {
        return StringUtils.lowerCase(catalogName);
    }

    public String getLowerSchemaName() {
        return StringUtils.lowerCase(schemaName);
    }

    public String getLowerTableName() {
        return StringUtils.lowerCase(tableName);
    }

    // Getters
    public String getCatalogName() {
        return catalogName;
    }

    public String getSchemaName() {
        return schemaName;
    }

    public String getTableName() {
        return tableName;
    }

    public String getMetaType() {
        return metaType;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TableId tableId = (TableId) o;
        return Objects.equals(catalogName, tableId.catalogName) &&
               Objects.equals(schemaName, tableId.schemaName) &&
               Objects.equals(tableName, tableId.tableName) &&
               Objects.equals(metaType, tableId.metaType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(catalogName, schemaName, tableName, metaType);
    }

    @Override
    public String toString() {
        return "TableId{" +
               "catalogName='" + catalogName + '\'' +
               ", schemaName='" + schemaName + '\'' +
               ", tableName='" + tableName + '\'' +
               ", metaType='" + metaType + '\'' +
               '}';
    }
}
