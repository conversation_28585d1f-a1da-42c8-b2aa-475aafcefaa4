package com.xylink.sqltranspiler.core.ast.drop;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

/**
 * DROP PROCEDURE语句的AST表示
 * 
 * 支持MySQL和各种数据库的存储过程删除语法：
 * - MySQL: DROP PROCEDURE [IF EXISTS] procedure_name
 * - 金仓数据库: DROP PROCEDURE [IF EXISTS] procedure_name(parameters) [CASCADE | RESTRICT]
 * - 达梦数据库: DROP PROCEDURE [IF EXISTS] procedure_name
 * - 神通数据库: DROP PROCEDURE [IF EXISTS] procedure_name [CASCADE]
 */
public class DropProcedure extends Statement {
    private final String procedureName;
    private final String parameters;
    private final boolean ifExists;
    private final boolean cascade;
    private final boolean restrict;

    public DropProcedure(String procedureName, String parameters, boolean ifExists, boolean cascade, boolean restrict) {
        this.procedureName = procedureName;
        this.parameters = parameters;
        this.ifExists = ifExists;
        this.cascade = cascade;
        this.restrict = restrict;
    }

    public DropProcedure(String procedureName, boolean ifExists) {
        this(procedureName, null, ifExists, false, false);
    }

    public DropProcedure(String procedureName) {
        this(procedureName, null, false, false, false);
    }

    @Override
    public StatementType getStatementType() {
        return StatementType.DROP_PROCEDURE;
    }

    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.DROP;
    }

    @Override
    public SqlType getSqlType() {
        return SqlType.DDL;
    }

    // Getters
    public String getProcedureName() {
        return procedureName;
    }

    public String getParameters() {
        return parameters;
    }

    public boolean isIfExists() {
        return ifExists;
    }

    public boolean isCascade() {
        return cascade;
    }

    public boolean isRestrict() {
        return restrict;
    }
}
