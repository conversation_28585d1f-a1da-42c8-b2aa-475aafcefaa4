package com.xylink.sqltranspiler.core.ast.create;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.google.common.collect.Lists;
import com.xylink.sqltranspiler.core.ast.PartitionType;
import com.xylink.sqltranspiler.core.ast.TableId;
import com.xylink.sqltranspiler.core.ast.abs.AbsTableStatement;
import com.xylink.sqltranspiler.core.ast.table.ColumnRel;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;
import com.xylink.sqltranspiler.core.model.enums.TableType;

public class CreateTable extends AbsTableStatement {
    private final TableId tableId;
    private final TableType tableType;
    private final String comment;
    private Integer lifeCycle;
    private List<ColumnRel> partitionColumnRels;
    private List<ColumnRel> columnRels;
    private Map<String, String> properties;
    private String fileFormat;
    private boolean ifNotExists = false; // 是否存在 if not exists 关键字
    private boolean external = false;
    private boolean temporary = false;
    private String location;
    private String querySql;
    private final ArrayList<String> partitionColumnNames;

    // 建表方式：hive & spark. https://spark.apache.org/docs/3.2.0/sql-ref-syntax-ddl-create-table.html
    private boolean replace = false;
    private String modelType = "hive"; // 表模型类型
    private PartitionType partitionType; // 分区类型
    private Map<String, String> options;
    private List<String> clusteredColumns = Lists.newArrayList(); // 分桶表分桶列
    private List<String> sortedColumns = Lists.newArrayList(); // 分桶表排序列
    private String storageHandler;

    // MySQL分区定义字符串，用于fallback模式下保存原始分区定义
    private String partitionDefinition;

    public CreateTable(TableId tableId, TableType tableType, String comment, Integer lifeCycle,
                      List<ColumnRel> partitionColumnRels, List<ColumnRel> columnRels,
                      Map<String, String> properties, String fileFormat, boolean ifNotExists,
                      boolean external, boolean temporary, String location, String querySql,
                      ArrayList<String> partitionColumnNames) {
        this.tableId = tableId;
        this.tableType = tableType;
        this.comment = comment;
        this.lifeCycle = lifeCycle;
        this.partitionColumnRels = partitionColumnRels;
        this.columnRels = columnRels;
        this.properties = properties;
        this.fileFormat = fileFormat;
        this.ifNotExists = ifNotExists;
        this.external = external;
        this.temporary = temporary;
        this.location = location;
        this.querySql = querySql;
        this.partitionColumnNames = partitionColumnNames != null ? partitionColumnNames : new ArrayList<>();
    }

    public CreateTable(TableId tableId, TableType tableType, String comment, Integer lifeCycle,
                      List<ColumnRel> partitionColumnRels, List<ColumnRel> columnRels,
                      Map<String, String> properties, String fileFormat, boolean ifNotExists) {
        this(tableId, tableType, comment, lifeCycle, partitionColumnRels, columnRels, properties,
             fileFormat, ifNotExists, false, false, null, null, new ArrayList<>());
    }

    public CreateTable(TableId tableId, TableType tableType, String comment, List<ColumnRel> columnRels) {
        this(tableId, tableType, comment, null, null, columnRels, null, null, false);
    }

    public CreateTable(TableId tableId, TableType tableType, String comment, Integer lifeCycle, List<ColumnRel> columnRels) {
        this(tableId, tableType, comment, lifeCycle, null, columnRels, null, null, false);
    }

    public CreateTable(TableId tableId, TableType tableType, String comment, List<ColumnRel> columnRels,
                      boolean ifNotExists, Map<String, String> properties) {
        this(tableId, tableType, comment, null, null, columnRels, properties, null, ifNotExists);
    }

    @Override
    public StatementType getStatementType() {
        return StatementType.CREATE_TABLE;
    }

    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.CREATE;
    }

    @Override
    public SqlType getSqlType() {
        return SqlType.DDL;
    }

    @Override
    public TableId getTableId() {
        return tableId;
    }

    // Getters
    public TableType getTableType() {
        return tableType;
    }

    public String getComment() {
        return comment;
    }

    public Integer getLifeCycle() {
        return lifeCycle;
    }

    public List<ColumnRel> getPartitionColumnRels() {
        return partitionColumnRels;
    }

    public List<ColumnRel> getColumnRels() {
        return columnRels;
    }

    public Map<String, String> getProperties() {
        return properties;
    }

    public String getFileFormat() {
        return fileFormat;
    }

    public boolean isIfNotExists() {
        return ifNotExists;
    }

    public boolean isExternal() {
        return external;
    }

    public boolean isTemporary() {
        return temporary;
    }

    public String getLocation() {
        return location;
    }

    public String getQuerySql() {
        return querySql;
    }

    public ArrayList<String> getPartitionColumnNames() {
        return partitionColumnNames;
    }

    public boolean isReplace() {
        return replace;
    }

    public String getModelType() {
        return modelType;
    }

    public PartitionType getPartitionType() {
        return partitionType;
    }

    public Map<String, String> getOptions() {
        return options;
    }

    public List<String> getClusteredColumns() {
        return clusteredColumns;
    }

    public List<String> getSortedColumns() {
        return sortedColumns;
    }

    public String getStorageHandler() {
        return storageHandler;
    }

    // Setters
    public void setLifeCycle(Integer lifeCycle) {
        this.lifeCycle = lifeCycle;
    }

    public void setPartitionColumnRels(List<ColumnRel> partitionColumnRels) {
        this.partitionColumnRels = partitionColumnRels;
    }

    public void setColumnRels(List<ColumnRel> columnRels) {
        this.columnRels = columnRels;
    }

    public void setProperties(Map<String, String> properties) {
        this.properties = properties;
    }

    public void setFileFormat(String fileFormat) {
        this.fileFormat = fileFormat;
    }

    public void setIfNotExists(boolean ifNotExists) {
        this.ifNotExists = ifNotExists;
    }

    public void setExternal(boolean external) {
        this.external = external;
    }

    public void setTemporary(boolean temporary) {
        this.temporary = temporary;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public void setQuerySql(String querySql) {
        this.querySql = querySql;
    }

    public void setReplace(boolean replace) {
        this.replace = replace;
    }

    public void setModelType(String modelType) {
        this.modelType = modelType;
    }

    public void setPartitionType(PartitionType partitionType) {
        this.partitionType = partitionType;
    }

    public void setOptions(Map<String, String> options) {
        this.options = options;
    }

    public void setClusteredColumns(List<String> clusteredColumns) {
        this.clusteredColumns = clusteredColumns;
    }

    public void setSortedColumns(List<String> sortedColumns) {
        this.sortedColumns = sortedColumns;
    }

    public void setStorageHandler(String storageHandler) {
        this.storageHandler = storageHandler;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CreateTable that = (CreateTable) o;
        return ifNotExists == that.ifNotExists &&
               external == that.external &&
               temporary == that.temporary &&
               replace == that.replace &&
               Objects.equals(tableId, that.tableId) &&
               tableType == that.tableType &&
               Objects.equals(comment, that.comment) &&
               Objects.equals(lifeCycle, that.lifeCycle) &&
               Objects.equals(partitionColumnRels, that.partitionColumnRels) &&
               Objects.equals(columnRels, that.columnRels) &&
               Objects.equals(properties, that.properties) &&
               Objects.equals(fileFormat, that.fileFormat) &&
               Objects.equals(location, that.location) &&
               Objects.equals(querySql, that.querySql) &&
               Objects.equals(partitionColumnNames, that.partitionColumnNames) &&
               Objects.equals(modelType, that.modelType) &&
               partitionType == that.partitionType &&
               Objects.equals(options, that.options) &&
               Objects.equals(clusteredColumns, that.clusteredColumns) &&
               Objects.equals(sortedColumns, that.sortedColumns) &&
               Objects.equals(storageHandler, that.storageHandler) &&
               Objects.equals(partitionDefinition, that.partitionDefinition);
    }

    @Override
    public int hashCode() {
        return Objects.hash(tableId, tableType, comment, lifeCycle, partitionColumnRels, columnRels,
                           properties, fileFormat, ifNotExists, external, temporary, location, querySql,
                           partitionColumnNames, replace, modelType, partitionType, options,
                           clusteredColumns, sortedColumns, storageHandler, partitionDefinition);
    }

    /**
     * 获取MySQL分区定义字符串
     * @return 分区定义字符串
     */
    public String getPartitionDefinition() {
        return partitionDefinition;
    }

    /**
     * 设置MySQL分区定义字符串
     * @param partitionDefinition 分区定义字符串
     */
    public void setPartitionDefinition(String partitionDefinition) {
        this.partitionDefinition = partitionDefinition;
    }
}
