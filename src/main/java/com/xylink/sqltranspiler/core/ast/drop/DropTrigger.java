package com.xylink.sqltranspiler.core.ast.drop;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

/**
 * DROP TRIGGER语句的AST表示
 * 
 * 支持MySQL和各种数据库的触发器删除语法：
 * - MySQL: DROP TRIGGER [IF EXISTS] [schema_name.]trigger_name
 * - 金仓数据库: DROP TRIGGER [IF EXISTS] trigger_name ON table_name [CASCADE | RESTRICT]
 * - 达梦数据库: DROP TRIGGER [IF EXISTS] trigger_name
 * - 神通数据库: DROP TRIGGER [IF EXISTS] trigger_name
 */
public class DropTrigger extends Statement {
    private final String triggerName;
    private final String tableName;
    private final String schemaName;
    private final boolean ifExists;
    private final boolean cascade;
    private final boolean restrict;

    public DropTrigger(String triggerName, String tableName, String schemaName, 
                      boolean ifExists, boolean cascade, boolean restrict) {
        this.triggerName = triggerName;
        this.tableName = tableName;
        this.schemaName = schemaName;
        this.ifExists = ifExists;
        this.cascade = cascade;
        this.restrict = restrict;
    }

    public DropTrigger(String triggerName, boolean ifExists) {
        this(triggerName, null, null, ifExists, false, false);
    }

    public DropTrigger(String triggerName) {
        this(triggerName, null, null, false, false, false);
    }

    @Override
    public StatementType getStatementType() {
        return StatementType.DROP_FUNCTION; // 注意：StatementType枚举中没有DROP_TRIGGER，使用DROP_FUNCTION作为临时替代
    }

    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.DROP;
    }

    @Override
    public SqlType getSqlType() {
        return SqlType.DDL;
    }

    // Getters
    public String getTriggerName() {
        return triggerName;
    }

    public String getTableName() {
        return tableName;
    }

    public String getSchemaName() {
        return schemaName;
    }

    public boolean isIfExists() {
        return ifExists;
    }

    public boolean isCascade() {
        return cascade;
    }

    public boolean isRestrict() {
        return restrict;
    }
}
