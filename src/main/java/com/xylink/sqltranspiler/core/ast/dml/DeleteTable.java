package com.xylink.sqltranspiler.core.ast.dml;

import com.xylink.sqltranspiler.core.ast.TableId;
import com.xylink.sqltranspiler.core.ast.abs.AbsTableStatement;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

/**
 * DELETE语句的AST表示
 * 
 * 支持：
 * - DELETE FROM table_name [WHERE condition]
 * - DELETE FROM table_name [ORDER BY ...] [LIMIT ...]
 */
public class DeleteTable extends AbsTableStatement {
    private final TableId tableId;
    private final String whereClause;
    private final String orderByClause;
    private final String limitClause;
    private final boolean lowPriority;
    private final boolean quick;
    private final boolean ignore;

    public DeleteTable(TableId tableId, String whereClause, String orderByClause, String limitClause,
                      boolean lowPriority, boolean quick, boolean ignore) {
        this.tableId = tableId;
        this.whereClause = whereClause;
        this.orderByClause = orderByClause;
        this.limitClause = limitClause;
        this.lowPriority = lowPriority;
        this.quick = quick;
        this.ignore = ignore;
    }

    public DeleteTable(TableId tableId, String whereClause) {
        this(tableId, whereClause, null, null, false, false, false);
    }

    public DeleteTable(TableId tableId) {
        this(tableId, null, null, null, false, false, false);
    }

    @Override
    public StatementType getStatementType() {
        return StatementType.DELETE;
    }

    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.WRITE;
    }

    @Override
    public SqlType getSqlType() {
        return SqlType.DML;
    }

    @Override
    public TableId getTableId() {
        return tableId;
    }

    // Getters
    public String getWhereClause() {
        return whereClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public String getLimitClause() {
        return limitClause;
    }

    public boolean isLowPriority() {
        return lowPriority;
    }

    public boolean isQuick() {
        return quick;
    }

    public boolean isIgnore() {
        return ignore;
    }

    @Override
    public String toString() {
        return "DeleteTable{" +
               "tableId=" + tableId +
               ", whereClause='" + whereClause + '\'' +
               ", orderByClause='" + orderByClause + '\'' +
               ", limitClause='" + limitClause + '\'' +
               ", lowPriority=" + lowPriority +
               ", quick=" + quick +
               ", ignore=" + ignore +
               '}';
    }
}
