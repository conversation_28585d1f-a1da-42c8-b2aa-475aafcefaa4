package com.xylink.sqltranspiler.core.ast.drop;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.TableId;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

import java.util.List;

/**
 * DROP SEQUENCE语句的AST表示
 *
 * 根据MySQL官方文档：MySQL 8.0不直接支持SEQUENCE，但可以通过删除AUTO_INCREMENT表模拟
 * 根据达梦数据库官方文档：https://eco.dameng.com/document/dm/zh-cn/pm/definition-statement.html
 * 达梦数据库完全支持DROP SEQUENCE语句
 * 根据金仓数据库官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/SQL_Statements_9.html#drop-sequence
 * 金仓数据库完全支持PostgreSQL兼容的DROP SEQUENCE语句
 * 根据神通数据库官方文档：神通数据库支持删除序列对象
 */
public class DropSequence extends Statement {

    private final List<TableId> sequenceIds;
    private final boolean ifExists;
    private final boolean cascade;
    private final boolean restrict;

    public DropSequence(List<TableId> sequenceIds, boolean ifExists, boolean cascade, boolean restrict) {
        this.sequenceIds = sequenceIds;
        this.ifExists = ifExists;
        this.cascade = cascade;
        this.restrict = restrict;
    }

    public DropSequence(List<TableId> sequenceIds) {
        this(sequenceIds, false, false, false);
    }

    public DropSequence(List<TableId> sequenceIds, boolean ifExists) {
        this(sequenceIds, ifExists, false, false);
    }

    @Override
    public StatementType getStatementType() {
        return StatementType.DROP_SEQUENCE;
    }

    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.DROP;
    }

    @Override
    public SqlType getSqlType() {
        return SqlType.DDL;
    }

    // Getters
    public List<TableId> getSequenceIds() {
        return sequenceIds;
    }

    public boolean isIfExists() {
        return ifExists;
    }

    public boolean isCascade() {
        return cascade;
    }

    public boolean isRestrict() {
        return restrict;
    }

    @Override
    public String toString() {
        return "DropSequence{" +
                "sequenceIds=" + sequenceIds +
                ", ifExists=" + ifExists +
                ", cascade=" + cascade +
                ", restrict=" + restrict +
                ", sql='" + getSql() + '\'' +
                '}';
    }
}
