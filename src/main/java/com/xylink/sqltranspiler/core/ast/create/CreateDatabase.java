package com.xylink.sqltranspiler.core.ast.create;

import java.util.Map;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

public class CreateDatabase extends Statement {
    private final String catalogName;
    private final String databaseName;
    private final String location;
    private Map<String, String> properties;
    private boolean ifNotExists = false;

    public CreateDatabase(String catalogName, String databaseName, String location, 
                         Map<String, String> properties, boolean ifNotExists) {
        this.catalogName = catalogName;
        this.databaseName = databaseName;
        this.location = location;
        this.properties = properties;
        this.ifNotExists = ifNotExists;
    }

    public CreateDatabase(String databaseName) {
        this(null, databaseName, null, null, false);
    }

    public CreateDatabase(String catalogName, String databaseName, 
                         Map<String, String> properties, boolean ifNotExists) {
        this(catalogName, databaseName, null, properties, ifNotExists);
    }

    @Override
    public StatementType getStatementType() {
        return StatementType.CREATE_DATABASE;
    }

    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.CREATE;
    }

    @Override
    public SqlType getSqlType() {
        return SqlType.DDL;
    }

    // Getters
    public String getCatalogName() {
        return catalogName;
    }

    public String getDatabaseName() {
        return databaseName;
    }

    public String getLocation() {
        return location;
    }

    public Map<String, String> getProperties() {
        return properties;
    }

    public boolean isIfNotExists() {
        return ifNotExists;
    }

    // Setters
    public void setProperties(Map<String, String> properties) {
        this.properties = properties;
    }

    public void setIfNotExists(boolean ifNotExists) {
        this.ifNotExists = ifNotExists;
    }
}
