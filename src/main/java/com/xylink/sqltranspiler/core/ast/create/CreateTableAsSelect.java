package com.xylink.sqltranspiler.core.ast.create;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.xylink.sqltranspiler.core.ast.PartitionType;
import com.xylink.sqltranspiler.core.ast.TableId;
import com.xylink.sqltranspiler.core.ast.abs.AbsTableStatement;
import com.xylink.sqltranspiler.core.ast.dml.QueryStmt;
import com.xylink.sqltranspiler.core.ast.table.ColumnRel;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

public class CreateTableAsSelect extends AbsTableStatement {
    private final TableId tableId;
    private QueryStmt queryStmt;
    private final String comment;
    private Integer lifeCycle;
    private List<ColumnRel> partitionColumnRels;
    private List<ColumnRel> columnRels;
    private Map<String, String> properties;
    private String fileFormat;
    private boolean ifNotExists = false; // 是否存在 if not exists 关键字
    private String locationPath;
    private final ArrayList<String> partitionColumnNames;

    private boolean replace = false;
    // 建表方式：hive & spark. https://spark.apache.org/docs/3.2.0/sql-ref-syntax-ddl-create-table.html
    private String modelType = "hive";
    private PartitionType partitionType; // 分区类型
    private Map<String, String> options;
    private String storageHandler;

    public CreateTableAsSelect(TableId tableId, QueryStmt queryStmt, String comment, Integer lifeCycle,
                              List<ColumnRel> partitionColumnRels, List<ColumnRel> columnRels,
                              Map<String, String> properties, String fileFormat, boolean ifNotExists,
                              String locationPath, ArrayList<String> partitionColumnNames) {
        this.tableId = tableId;
        this.queryStmt = queryStmt;
        this.comment = comment;
        this.lifeCycle = lifeCycle;
        this.partitionColumnRels = partitionColumnRels;
        this.columnRels = columnRels;
        this.properties = properties;
        this.fileFormat = fileFormat;
        this.ifNotExists = ifNotExists;
        this.locationPath = locationPath;
        this.partitionColumnNames = partitionColumnNames != null ? partitionColumnNames : new ArrayList<>();
    }

    public CreateTableAsSelect(TableId tableId, QueryStmt queryStmt) {
        this(tableId, queryStmt, null, null, null, null, null, null, false, null, new ArrayList<>());
    }

    public CreateTableAsSelect(TableId tableId, QueryStmt queryStmt, String comment, boolean ifNotExists,
                              Map<String, String> properties) {
        this(tableId, queryStmt, comment, null, null, null, properties, null, ifNotExists, null, new ArrayList<>());
    }

    @Override
    public StatementType getStatementType() {
        return StatementType.CREATE_TABLE_AS_SELECT;
    }

    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.CREATE;
    }

    @Override
    public SqlType getSqlType() {
        return SqlType.DDL;
    }

    @Override
    public TableId getTableId() {
        return tableId;
    }

    // Getters
    public QueryStmt getQueryStmt() {
        return queryStmt;
    }

    public String getComment() {
        return comment;
    }

    public Integer getLifeCycle() {
        return lifeCycle;
    }

    public List<ColumnRel> getPartitionColumnRels() {
        return partitionColumnRels;
    }

    public List<ColumnRel> getColumnRels() {
        return columnRels;
    }

    public Map<String, String> getProperties() {
        return properties;
    }

    public String getFileFormat() {
        return fileFormat;
    }

    public boolean isIfNotExists() {
        return ifNotExists;
    }

    public String getLocationPath() {
        return locationPath;
    }

    public ArrayList<String> getPartitionColumnNames() {
        return partitionColumnNames;
    }

    public boolean isReplace() {
        return replace;
    }

    public String getModelType() {
        return modelType;
    }

    public PartitionType getPartitionType() {
        return partitionType;
    }

    public Map<String, String> getOptions() {
        return options;
    }

    public String getStorageHandler() {
        return storageHandler;
    }

    // Setters
    public void setQueryStmt(QueryStmt queryStmt) {
        this.queryStmt = queryStmt;
    }

    public void setLifeCycle(Integer lifeCycle) {
        this.lifeCycle = lifeCycle;
    }

    public void setPartitionColumnRels(List<ColumnRel> partitionColumnRels) {
        this.partitionColumnRels = partitionColumnRels;
    }

    public void setColumnRels(List<ColumnRel> columnRels) {
        this.columnRels = columnRels;
    }

    public void setProperties(Map<String, String> properties) {
        this.properties = properties;
    }

    public void setFileFormat(String fileFormat) {
        this.fileFormat = fileFormat;
    }

    public void setIfNotExists(boolean ifNotExists) {
        this.ifNotExists = ifNotExists;
    }

    public void setLocationPath(String locationPath) {
        this.locationPath = locationPath;
    }

    public void setReplace(boolean replace) {
        this.replace = replace;
    }

    public void setModelType(String modelType) {
        this.modelType = modelType;
    }

    public void setPartitionType(PartitionType partitionType) {
        this.partitionType = partitionType;
    }

    public void setOptions(Map<String, String> options) {
        this.options = options;
    }

    public void setStorageHandler(String storageHandler) {
        this.storageHandler = storageHandler;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CreateTableAsSelect that = (CreateTableAsSelect) o;
        return ifNotExists == that.ifNotExists &&
               replace == that.replace &&
               Objects.equals(tableId, that.tableId) &&
               Objects.equals(queryStmt, that.queryStmt) &&
               Objects.equals(comment, that.comment) &&
               Objects.equals(lifeCycle, that.lifeCycle) &&
               Objects.equals(partitionColumnRels, that.partitionColumnRels) &&
               Objects.equals(columnRels, that.columnRels) &&
               Objects.equals(properties, that.properties) &&
               Objects.equals(fileFormat, that.fileFormat) &&
               Objects.equals(locationPath, that.locationPath) &&
               Objects.equals(partitionColumnNames, that.partitionColumnNames) &&
               Objects.equals(modelType, that.modelType) &&
               partitionType == that.partitionType &&
               Objects.equals(options, that.options) &&
               Objects.equals(storageHandler, that.storageHandler);
    }

    @Override
    public int hashCode() {
        return Objects.hash(tableId, queryStmt, comment, lifeCycle, partitionColumnRels, columnRels,
                           properties, fileFormat, ifNotExists, locationPath, partitionColumnNames,
                           replace, modelType, partitionType, options, storageHandler);
    }
}
