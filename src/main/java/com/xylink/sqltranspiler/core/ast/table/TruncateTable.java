package com.xylink.sqltranspiler.core.ast.table;

import java.util.ArrayList;

import com.xylink.sqltranspiler.core.ast.TableId;
import com.xylink.sqltranspiler.core.ast.abs.AbsTableStatement;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

public class TruncateTable extends AbsTableStatement {

    private final TableId tableId;

    public TruncateTable(TableId tableId) {
        this.tableId = tableId;
    }

    @Override
    public StatementType getStatementType() {
        return StatementType.TRUNCATE_TABLE;
    }

    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.DROP;
    }

    @Override
    public SqlType getSqlType() {
        return SqlType.DDL;
    }

    @Override
    public TableId getTableId() {
        return tableId;
    }

    public ArrayList<String> getOrigins() {
        return new ArrayList<>();
    }
}
