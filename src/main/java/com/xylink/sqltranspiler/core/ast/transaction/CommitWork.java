package com.xylink.sqltranspiler.core.ast.transaction;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

/**
 * COMMIT语句的AST表示
 *
 * 根据MySQL官方文档：
 * COMMIT [WORK] [AND [NO] CHAIN] [[NO] RELEASE]
 *
 * 根据KingbaseES官方文档，COMMIT在KingbaseES中是支持的
 */
public class CommitWork extends Statement {

    private boolean work;
    private String options;

    public CommitWork() {
        super();
    }

    public CommitWork(String sql) {
        super();
        setSql(sql);
    }
    
    public boolean isWork() {
        return work;
    }
    
    public void setWork(boolean work) {
        this.work = work;
    }
    
    public String getOptions() {
        return options;
    }

    public void setOptions(String options) {
        this.options = options;
    }

    @Override
    public StatementType getStatementType() {
        return StatementType.UNKOWN; // 暂时使用UNKOWN，稍后添加COMMIT
    }

    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.OTHER;
    }

    @Override
    public SqlType getSqlType() {
        return SqlType.TCL; // Transaction Control Language
    }

    @Override
    public String toString() {
        return "CommitWork{" +
                "work=" + work +
                ", options='" + options + '\'' +
                ", sql='" + getSql() + '\'' +
                '}';
    }
}
