package com.xylink.sqltranspiler.core.ast.create;

import java.util.List;
import java.util.Objects;

import com.xylink.sqltranspiler.core.ast.TableId;
import com.xylink.sqltranspiler.core.ast.abs.AbsTableStatement;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

/**
 * CREATE INDEX语句的AST表示
 * 
 * 支持：
 * - CREATE INDEX index_name ON table_name (column1, column2, ...)
 * - CREATE UNIQUE INDEX index_name ON table_name (column1, column2, ...)
 * - CREATE FULLTEXT INDEX index_name ON table_name (column1, column2, ...)
 * - CREATE SPATIAL INDEX index_name ON table_name (column1, column2, ...)
 */
public class CreateIndex extends AbsTableStatement {
    private final String indexName;
    private final TableId tableId;
    private final List<IndexColumn> columns;
    private final IndexType indexType;
    private final String algorithm;
    private final String lockOption;
    private final String comment;
    
    /**
     * 索引类型枚举
     */
    public enum IndexType {
        NORMAL,     // 普通索引
        UNIQUE,     // 唯一索引
        FULLTEXT,   // 全文索引
        SPATIAL     // 空间索引
    }
    
    /**
     * 索引列表示
     */
    public static class IndexColumn {
        private final String columnName;
        private final Integer length;
        private final String sortOrder; // ASC or DESC
        
        public IndexColumn(String columnName, Integer length, String sortOrder) {
            this.columnName = columnName;
            this.length = length;
            this.sortOrder = sortOrder;
        }
        
        public IndexColumn(String columnName) {
            this(columnName, null, null);
        }
        
        public String getColumnName() {
            return columnName;
        }
        
        public Integer getLength() {
            return length;
        }
        
        public String getSortOrder() {
            return sortOrder;
        }
        
        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            IndexColumn that = (IndexColumn) o;
            return Objects.equals(columnName, that.columnName) &&
                   Objects.equals(length, that.length) &&
                   Objects.equals(sortOrder, that.sortOrder);
        }
        
        @Override
        public int hashCode() {
            return Objects.hash(columnName, length, sortOrder);
        }
        
        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder(columnName);
            if (length != null) {
                sb.append("(").append(length).append(")");
            }
            if (sortOrder != null) {
                sb.append(" ").append(sortOrder);
            }
            return sb.toString();
        }
    }

    public CreateIndex(String indexName, TableId tableId, List<IndexColumn> columns, 
                      IndexType indexType, String algorithm, String lockOption, String comment) {
        this.indexName = indexName;
        this.tableId = tableId;
        this.columns = columns;
        this.indexType = indexType != null ? indexType : IndexType.NORMAL;
        this.algorithm = algorithm;
        this.lockOption = lockOption;
        this.comment = comment;
    }

    public CreateIndex(String indexName, TableId tableId, List<IndexColumn> columns, IndexType indexType) {
        this(indexName, tableId, columns, indexType, null, null, null);
    }

    public CreateIndex(String indexName, TableId tableId, List<IndexColumn> columns) {
        this(indexName, tableId, columns, IndexType.NORMAL, null, null, null);
    }

    @Override
    public StatementType getStatementType() {
        return StatementType.CREATE_INDEX;
    }

    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.CREATE;
    }

    @Override
    public SqlType getSqlType() {
        return SqlType.DDL;
    }

    @Override
    public TableId getTableId() {
        return tableId;
    }

    // Getters
    public String getIndexName() {
        return indexName;
    }

    public List<IndexColumn> getColumns() {
        return columns;
    }

    public IndexType getIndexType() {
        return indexType;
    }

    public String getAlgorithm() {
        return algorithm;
    }

    public String getLockOption() {
        return lockOption;
    }

    public String getComment() {
        return comment;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CreateIndex that = (CreateIndex) o;
        return Objects.equals(indexName, that.indexName) &&
               Objects.equals(tableId, that.tableId) &&
               Objects.equals(columns, that.columns) &&
               indexType == that.indexType &&
               Objects.equals(algorithm, that.algorithm) &&
               Objects.equals(lockOption, that.lockOption) &&
               Objects.equals(comment, that.comment);
    }

    @Override
    public int hashCode() {
        return Objects.hash(indexName, tableId, columns, indexType, algorithm, lockOption, comment);
    }

    @Override
    public String toString() {
        return "CreateIndex{" +
               "indexName='" + indexName + '\'' +
               ", tableId=" + tableId +
               ", columns=" + columns +
               ", indexType=" + indexType +
               ", algorithm='" + algorithm + '\'' +
               ", lockOption='" + lockOption + '\'' +
               ", comment='" + comment + '\'' +
               '}';
    }
}
