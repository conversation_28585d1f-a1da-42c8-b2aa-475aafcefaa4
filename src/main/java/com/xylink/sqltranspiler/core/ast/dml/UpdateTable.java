package com.xylink.sqltranspiler.core.ast.dml;

import java.util.List;
import java.util.Objects;

import com.xylink.sqltranspiler.core.ast.TableId;
import com.xylink.sqltranspiler.core.ast.abs.AbsTableStatement;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

/**
 * UPDATE语句的AST表示
 * 
 * 支持：
 * - UPDATE table_name SET column1=value1, column2=value2 [WHERE condition]
 * - UPDATE table_name SET column1=value1 [ORDER BY ...] [LIMIT ...]
 * - 多表UPDATE（简化支持）
 */
public class UpdateTable extends AbsTableStatement {
    private final TableId tableId;
    private final List<SetClause> setClauses;
    private final String whereClause;
    private final String orderByClause;
    private final String limitClause;
    private final boolean lowPriority;
    private final boolean ignore;
    
    /**
     * SET子句表示
     */
    public static class SetClause {
        private final String columnName;
        private final String value;
        
        public SetClause(String columnName, String value) {
            this.columnName = columnName;
            this.value = value;
        }
        
        public String getColumnName() {
            return columnName;
        }
        
        public String getValue() {
            return value;
        }
        
        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            SetClause setClause = (SetClause) o;
            return Objects.equals(columnName, setClause.columnName) &&
                   Objects.equals(value, setClause.value);
        }
        
        @Override
        public int hashCode() {
            return Objects.hash(columnName, value);
        }
        
        @Override
        public String toString() {
            return columnName + "=" + value;
        }
    }

    public UpdateTable(TableId tableId, List<SetClause> setClauses, String whereClause, 
                      String orderByClause, String limitClause, boolean lowPriority, boolean ignore) {
        this.tableId = tableId;
        this.setClauses = setClauses;
        this.whereClause = whereClause;
        this.orderByClause = orderByClause;
        this.limitClause = limitClause;
        this.lowPriority = lowPriority;
        this.ignore = ignore;
    }

    public UpdateTable(TableId tableId, List<SetClause> setClauses, String whereClause) {
        this(tableId, setClauses, whereClause, null, null, false, false);
    }

    public UpdateTable(TableId tableId, List<SetClause> setClauses) {
        this(tableId, setClauses, null, null, null, false, false);
    }

    @Override
    public StatementType getStatementType() {
        return StatementType.UPDATE;
    }

    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.WRITE;
    }

    @Override
    public SqlType getSqlType() {
        return SqlType.DML;
    }

    @Override
    public TableId getTableId() {
        return tableId;
    }

    // Getters
    public List<SetClause> getSetClauses() {
        return setClauses;
    }

    public String getWhereClause() {
        return whereClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public String getLimitClause() {
        return limitClause;
    }

    public boolean isLowPriority() {
        return lowPriority;
    }

    public boolean isIgnore() {
        return ignore;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UpdateTable that = (UpdateTable) o;
        return lowPriority == that.lowPriority &&
               ignore == that.ignore &&
               Objects.equals(tableId, that.tableId) &&
               Objects.equals(setClauses, that.setClauses) &&
               Objects.equals(whereClause, that.whereClause) &&
               Objects.equals(orderByClause, that.orderByClause) &&
               Objects.equals(limitClause, that.limitClause);
    }

    @Override
    public int hashCode() {
        return Objects.hash(tableId, setClauses, whereClause, orderByClause, limitClause, lowPriority, ignore);
    }

    @Override
    public String toString() {
        return "UpdateTable{" +
               "tableId=" + tableId +
               ", setClauses=" + setClauses +
               ", whereClause='" + whereClause + '\'' +
               ", orderByClause='" + orderByClause + '\'' +
               ", limitClause='" + limitClause + '\'' +
               ", lowPriority=" + lowPriority +
               ", ignore=" + ignore +
               '}';
    }
}
