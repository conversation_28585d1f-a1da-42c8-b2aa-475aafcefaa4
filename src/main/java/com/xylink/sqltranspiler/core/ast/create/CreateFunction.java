package com.xylink.sqltranspiler.core.ast.create;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

/**
 * CREATE FUNCTION语句的AST表示
 * 
 * 支持MySQL和各种数据库的函数创建语法：
 * - MySQL: CREATE FUNCTION function_name(parameters) RETURNS return_type BEGIN ... END
 * - 金仓数据库: CREATE OR REPLACE FUNCTION function_name(parameters) RETURNS return_type AS $$ ... $$ LANGUAGE plpgsql
 * - 达梦数据库: CREATE OR REPLACE FUNCTION function_name(parameters) RETURN return_type AS BEGIN ... END
 * - 神通数据库: CREATE OR REPLACE FUNCTION function_name(parameters) RETURN return_type AS BEGIN ... END
 */
public class CreateFunction extends Statement {
    private final String functionName;
    private final String parameters;
    private final String returnType;
    private final String functionBody;
    private final String language;
    private final boolean orReplace;
    private final boolean deterministic;

    public CreateFunction(String functionName, String parameters, String returnType, 
                         String functionBody, String language, boolean orReplace, boolean deterministic) {
        this.functionName = functionName;
        this.parameters = parameters;
        this.returnType = returnType;
        this.functionBody = functionBody;
        this.language = language;
        this.orReplace = orReplace;
        this.deterministic = deterministic;
    }

    public CreateFunction(String functionName, String parameters, String returnType, String functionBody) {
        this(functionName, parameters, returnType, functionBody, null, false, false);
    }

    @Override
    public StatementType getStatementType() {
        return StatementType.CREATE_FUNCTION;
    }

    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.CREATE;
    }

    @Override
    public SqlType getSqlType() {
        return SqlType.DDL;
    }

    // Getters
    public String getFunctionName() {
        return functionName;
    }

    public String getParameters() {
        return parameters;
    }

    public String getReturnType() {
        return returnType;
    }

    public String getFunctionBody() {
        return functionBody;
    }

    public String getLanguage() {
        return language;
    }

    public boolean isOrReplace() {
        return orReplace;
    }

    public boolean isDeterministic() {
        return deterministic;
    }
}
