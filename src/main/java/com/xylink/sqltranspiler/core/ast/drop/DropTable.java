package com.xylink.sqltranspiler.core.ast.drop;

import java.util.ArrayList;

import com.xylink.sqltranspiler.core.ast.TableId;
import com.xylink.sqltranspiler.core.ast.abs.AbsTableStatement;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

public class DropTable extends AbsTableStatement {
    private final ArrayList<TableId> tableIds;
    private boolean ifExists = false;

    public DropTable(ArrayList<TableId> tableIds, boolean ifExists) {
        this.tableIds = tableIds != null ? tableIds : new ArrayList<>();
        this.ifExists = ifExists;
    }

    public DropTable(ArrayList<TableId> tableIds) {
        this(tableIds, false);
    }

    public DropTable(TableId tableId, boolean ifExists) {
        this.tableIds = new ArrayList<>();
        this.tableIds.add(tableId);
        this.ifExists = ifExists;
    }

    public DropTable(TableId tableId) {
        this(tableId, false);
    }

    @Override
    public StatementType getStatementType() {
        return StatementType.DROP_TABLE;
    }

    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.DROP;
    }

    @Override
    public SqlType getSqlType() {
        return SqlType.DDL;
    }

    @Override
    public TableId getTableId() {
        return tableIds.isEmpty() ? null : tableIds.get(0);
    }

    // Getters
    public ArrayList<TableId> getTableIds() {
        return tableIds;
    }

    public boolean getIfExists() {
        return ifExists;
    }

    public boolean isIfExists() {
        return ifExists;
    }

    // Setters
    public void setIfExists(boolean ifExists) {
        this.ifExists = ifExists;
    }
}
