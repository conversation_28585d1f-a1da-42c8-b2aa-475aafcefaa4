package com.xylink.sqltranspiler.core.ast.transaction;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

/**
 * BEGIN语句的AST表示
 *
 * 根据MySQL官方文档：
 * BEGIN [WORK] [transaction_characteristic [, transaction_characteristic] ...]
 *
 * 根据神通数据库官方文档，BEGIN在神通数据库中是完全支持的
 * 参考文档第29958-30210行：BEGIN语句用于开始一个事务
 */
public class BeginWork extends Statement {

    private boolean work;
    private String characteristics;

    public BeginWork() {
        super();
    }

    public BeginWork(String sql) {
        super();
        setSql(sql);
    }
    
    public boolean isWork() {
        return work;
    }
    
    public void setWork(boolean work) {
        this.work = work;
    }
    
    public String getCharacteristics() {
        return characteristics;
    }

    public void setCharacteristics(String characteristics) {
        this.characteristics = characteristics;
    }

    @Override
    public StatementType getStatementType() {
        return StatementType.UNKOWN; // 暂时使用UNKOWN，稍后添加BEGIN
    }

    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.OTHER;
    }

    @Override
    public SqlType getSqlType() {
        return SqlType.TCL; // Transaction Control Language
    }

    @Override
    public String toString() {
        return "BeginWork{" +
                "work=" + work +
                ", characteristics='" + characteristics + '\'' +
                ", sql='" + getSql() + '\'' +
                '}';
    }
}
