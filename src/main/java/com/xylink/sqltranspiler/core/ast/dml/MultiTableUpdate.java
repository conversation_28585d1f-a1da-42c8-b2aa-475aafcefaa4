package com.xylink.sqltranspiler.core.ast.dml;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.TableId;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 多表UPDATE语句的AST表示
 * 
 * 支持MySQL多表UPDATE语法：
 * UPDATE table_references SET col1=val1, col2=val2 WHERE condition
 */
public class MultiTableUpdate extends Statement {
    
    /**
     * 表示SET子句中的赋值
     */
    public static class SetClause {
        private final TableId tableId;     // 表标识符
        private final String columnName;   // 列名
        private final String value;        // 值表达式
        
        public SetClause(TableId tableId, String columnName, String value) {
            this.tableId = tableId;
            this.columnName = columnName;
            this.value = value;
        }
        
        public TableId getTableId() {
            return tableId;
        }
        
        public String getColumnName() {
            return columnName;
        }
        
        public String getValue() {
            return value;
        }
        
        /**
         * 获取完整的列名（包含表名）
         */
        public String getFullColumnName() {
            return tableId != null ? tableId.getFullTableName() + "." + columnName : columnName;
        }
        
        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            SetClause setClause = (SetClause) o;
            return Objects.equals(tableId, setClause.tableId) &&
                   Objects.equals(columnName, setClause.columnName) &&
                   Objects.equals(value, setClause.value);
        }
        
        @Override
        public int hashCode() {
            return Objects.hash(tableId, columnName, value);
        }
        
        @Override
        public String toString() {
            return getFullColumnName() + "=" + value;
        }
    }
    
    private final String tableReferences;      // 表引用（JOIN表达式）
    private final List<SetClause> setClauses;  // SET子句列表
    private final String whereClause;          // WHERE条件
    private final boolean lowPriority;         // LOW_PRIORITY修饰符
    private final boolean ignore;              // IGNORE修饰符
    
    public MultiTableUpdate(String tableReferences, List<SetClause> setClauses, 
                           String whereClause, boolean lowPriority, boolean ignore) {
        this.tableReferences = tableReferences;
        this.setClauses = setClauses;
        this.whereClause = whereClause;
        this.lowPriority = lowPriority;
        this.ignore = ignore;
    }
    
    public MultiTableUpdate(String tableReferences, List<SetClause> setClauses, String whereClause) {
        this(tableReferences, setClauses, whereClause, false, false);
    }
    
    @Override
    public StatementType getStatementType() {
        return StatementType.UPDATE;
    }
    
    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.WRITE;
    }
    
    @Override
    public SqlType getSqlType() {
        return SqlType.DML;
    }
    
    // Getters
    public String getTableReferences() {
        return tableReferences;
    }
    
    public List<SetClause> getSetClauses() {
        return setClauses;
    }
    
    public String getWhereClause() {
        return whereClause;
    }
    
    public boolean isLowPriority() {
        return lowPriority;
    }
    
    public boolean isIgnore() {
        return ignore;
    }
    
    /**
     * 获取涉及的所有表
     */
    public List<TableId> getInvolvedTables() {
        // 从setClauses中提取所有不同的表
        return setClauses.stream()
            .map(SetClause::getTableId)
            .filter(Objects::nonNull)
            .distinct()
            .toList();
    }
    
    /**
     * 检查是否涉及指定的表
     */
    public boolean involvesTable(String tableName) {
        if (setClauses != null) {
            for (SetClause clause : setClauses) {
                if (clause.getTableId() != null && 
                    Objects.equals(clause.getTableId().getTableName(), tableName)) {
                    return true;
                }
            }
        }
        return false;
    }
    
    /**
     * 获取按表分组的SET子句
     */
    public Map<TableId, List<SetClause>> getSetClausesByTable() {
        return setClauses.stream()
            .filter(clause -> clause.getTableId() != null)
            .collect(java.util.stream.Collectors.groupingBy(SetClause::getTableId));
    }
    
    /**
     * 生成SQL语句的字符串表示
     */
    public String toSqlString() {
        StringBuilder sql = new StringBuilder("UPDATE ");
        
        // 添加修饰符
        if (lowPriority) sql.append("LOW_PRIORITY ");
        if (ignore) sql.append("IGNORE ");
        
        // 添加表引用
        sql.append(tableReferences);
        
        // 添加SET子句
        sql.append(" SET ");
        if (setClauses != null && !setClauses.isEmpty()) {
            for (int i = 0; i < setClauses.size(); i++) {
                if (i > 0) sql.append(", ");
                SetClause clause = setClauses.get(i);
                sql.append(clause.getFullColumnName()).append("=").append(clause.getValue());
            }
        }
        
        // 添加WHERE子句
        if (whereClause != null && !whereClause.trim().isEmpty()) {
            sql.append(" WHERE ").append(whereClause);
        }
        
        return sql.toString();
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MultiTableUpdate that = (MultiTableUpdate) o;
        return lowPriority == that.lowPriority &&
               ignore == that.ignore &&
               Objects.equals(tableReferences, that.tableReferences) &&
               Objects.equals(setClauses, that.setClauses) &&
               Objects.equals(whereClause, that.whereClause);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(tableReferences, setClauses, whereClause, lowPriority, ignore);
    }
    
    @Override
    public String toString() {
        return "MultiTableUpdate{" +
               "tableReferences='" + tableReferences + '\'' +
               ", setClauses=" + setClauses +
               ", whereClause='" + whereClause + '\'' +
               ", lowPriority=" + lowPriority +
               ", ignore=" + ignore +
               '}';
    }
}
