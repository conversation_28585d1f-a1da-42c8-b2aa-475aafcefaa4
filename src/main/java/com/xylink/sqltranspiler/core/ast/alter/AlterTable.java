package com.xylink.sqltranspiler.core.ast.alter;

import java.util.List;

import com.xylink.sqltranspiler.core.ast.TableId;
import com.xylink.sqltranspiler.core.ast.abs.AbsTableStatement;
import com.xylink.sqltranspiler.core.ast.table.ColumnRel;
import com.xylink.sqltranspiler.core.model.enums.AlterActionType;
import com.xylink.sqltranspiler.core.model.enums.PrivilegeType;
import com.xylink.sqltranspiler.core.model.enums.SqlType;
import com.xylink.sqltranspiler.core.model.enums.StatementType;

/**
 * ALTER TABLE语句的AST表示
 */
public class AlterTable extends AbsTableStatement {
    private final TableId tableId;
    private final AlterActionType actionType;
    private final String columnName;
    private final ColumnRel columnDefinition;
    private final List<AlterSpecification> specifications;

    // 表级属性
    private final Long autoIncrementValue;

    public AlterTable(TableId tableId, AlterActionType actionType, String columnName, ColumnRel columnDefinition) {
        this.tableId = tableId;
        this.actionType = actionType;
        this.columnName = columnName;
        this.columnDefinition = columnDefinition;
        this.specifications = null;
        this.autoIncrementValue = null;
    }

    public AlterTable(TableId tableId, List<AlterSpecification> specifications) {
        this.tableId = tableId;
        this.actionType = null;
        this.columnName = null;
        this.columnDefinition = null;
        this.specifications = specifications;
        this.autoIncrementValue = null;
    }

    // 专门用于AUTO_INCREMENT设置的构造函数
    public AlterTable(TableId tableId, Long autoIncrementValue) {
        this.tableId = tableId;
        this.actionType = null;
        this.columnName = null;
        this.columnDefinition = null;
        this.specifications = null;
        this.autoIncrementValue = autoIncrementValue;
    }

    @Override
    public StatementType getStatementType() {
        return StatementType.ALTER_TABLE;
    }

    @Override
    public PrivilegeType getPrivilegeType() {
        return PrivilegeType.ALTER;
    }

    @Override
    public SqlType getSqlType() {
        return SqlType.DDL;
    }

    @Override
    public TableId getTableId() {
        return tableId;
    }

    // Getters
    public AlterActionType getActionType() {
        return actionType;
    }

    public String getColumnName() {
        return columnName;
    }

    public ColumnRel getColumnDefinition() {
        return columnDefinition;
    }

    public List<AlterSpecification> getSpecifications() {
        return specifications;
    }

    public Long getAutoIncrementValue() {
        return autoIncrementValue;
    }

    /**
     * ALTER TABLE的具体操作规范
     */
    public static class AlterSpecification {
        private final AlterActionType actionType;
        private final String columnName;
        private final ColumnRel columnDefinition;
        private final String indexName;
        private final Object value;

        public AlterSpecification(AlterActionType actionType, String columnName, ColumnRel columnDefinition) {
            this.actionType = actionType;
            this.columnName = columnName;
            this.columnDefinition = columnDefinition;
            this.indexName = null;
            this.value = null;
        }

        public AlterSpecification(AlterActionType actionType, String name, Object value) {
            this.actionType = actionType;
            this.columnName = name;
            this.columnDefinition = null;
            this.indexName = name;
            this.value = value;
        }

        // Getters
        public AlterActionType getActionType() {
            return actionType;
        }

        public String getColumnName() {
            return columnName;
        }

        public ColumnRel getColumnDefinition() {
            return columnDefinition;
        }

        public String getIndexName() {
            return indexName;
        }

        public Object getValue() {
            return value;
        }
    }
}
