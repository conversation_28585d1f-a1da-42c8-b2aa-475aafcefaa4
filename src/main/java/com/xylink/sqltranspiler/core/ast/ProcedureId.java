package com.xylink.sqltranspiler.core.ast;

import java.util.Objects;

import org.apache.commons.lang3.StringUtils;

public class ProcedureId {
    private final String catalogName;
    private final String schemaName;
    private final String procedureName;

    public ProcedureId(String catalogName, String schemaName, String procedureName) {
        this.catalogName = catalogName;
        this.schemaName = schemaName;
        this.procedureName = procedureName;
    }

    public ProcedureId(String schemaName, String procedureName) {
        this(null, schemaName, procedureName);
    }

    public ProcedureId(String procedureName) {
        this(null, null, procedureName);
    }

    public String getFullFunctionName() {
        if (catalogName != null) {
            return catalogName + "." + schemaName + "." + procedureName;
        }

        if (schemaName != null) {
            return schemaName + "." + procedureName;
        }

        return procedureName;
    }

    public String getLowerCatalogName() {
        return StringUtils.lowerCase(catalogName);
    }

    public String getLowerSchemaName() {
        return StringUtils.lowerCase(schemaName);
    }

    public String getLowerTableName() {
        return StringUtils.lowerCase(procedureName);
    }

    // Getters
    public String getCatalogName() {
        return catalogName;
    }

    public String getSchemaName() {
        return schemaName;
    }

    public String getProcedureName() {
        return procedureName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ProcedureId that = (ProcedureId) o;
        return Objects.equals(catalogName, that.catalogName) &&
               Objects.equals(schemaName, that.schemaName) &&
               Objects.equals(procedureName, that.procedureName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(catalogName, schemaName, procedureName);
    }

    @Override
    public String toString() {
        return "ProcedureId{" +
               "catalogName='" + catalogName + '\'' +
               ", schemaName='" + schemaName + '\'' +
               ", procedureName='" + procedureName + '\'' +
               '}';
    }
}
