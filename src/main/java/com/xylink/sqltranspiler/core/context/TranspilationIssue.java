package com.xylink.sqltranspiler.core.context;

/**
 * Represents a single issue (error, warning, info) found during transpilation.
 *
 * @param issueCode A unique code for the issue (e.g., "WARN-001").
 * @param level The severity level of the issue.
 * @param message A human-readable message describing the issue.
 * @param line The line number in the original SQL where the issue occurred.
 * @param column The column number in the original SQL where the issue occurred.
 */
public record TranspilationIssue(
    String issueCode,
    IssueLevel level,
    String message,
    int line,
    int column
) {
    public enum IssueLevel {
        INFO,
        WARN,
        ERROR
    }
} 