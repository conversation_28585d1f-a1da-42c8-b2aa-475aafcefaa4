package com.xylink.sqltranspiler.core.context;

import java.util.Collections;
import java.util.List;

/**
 * Encapsulates the result of a transpilation process.
 *
 * @param translatedSql The resulting SQL script.
 * @param issues A list of all issues encountered during transpilation.
 * @param successCount The number of successfully transpiled statements.
 * @param failureCount The number of statements that failed to transpile.
 * @param preprocessingLogs The logs from preprocessing phase (includes skipped content info).
 */
public record TranspilationResult(
    String translatedSql,
    List<TranspilationIssue> issues,
    int successCount,
    int failureCount,
    List<String> preprocessingLogs
) {

    /**
     * Constructor for backward compatibility
     */
    public TranspilationResult(String translatedSql, List<TranspilationIssue> issues,
                             int successCount, int failureCount) {
        this(translatedSql, issues, successCount, failureCount, Collections.emptyList());
    }
}