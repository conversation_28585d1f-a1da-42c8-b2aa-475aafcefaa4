package com.xylink.sqltranspiler.core.context;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Holds the context for a single transpilation process.
 * It collects issues (warnings, errors) encountered during the process.
 */
public class TranspilationContext {

    private final String originalSql;
    private final List<TranspilationIssue> issues = new ArrayList<>();

    public TranspilationContext(String originalSql) {
        this.originalSql = originalSql;
    }

    /**
     * Adds an issue to the context.
     *
     * @param issue The issue to add.
     */
    public void addIssue(TranspilationIssue issue) {
        this.issues.add(issue);
    }

    /**
     * Gets the original SQL script that is being transpiled.
     *
     * @return The original SQL script.
     */
    public String getOriginalSql() {
        return originalSql;
    }

    /**
     * Gets an unmodifiable list of all issues collected during transpilation.
     *
     * @return An unmodifiable list of issues.
     */
    public List<TranspilationIssue> getIssues() {
        return Collections.unmodifiableList(issues);
    }
} 