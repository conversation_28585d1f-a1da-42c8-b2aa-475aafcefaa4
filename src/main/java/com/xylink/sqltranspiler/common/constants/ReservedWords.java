package com.xylink.sqltranspiler.common.constants;

import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

/**
 * 数据库保留字统一管理
 * 基于各数据库官方文档的保留字列表
 * 
 * 遵循 .augment/rules/rule-db.md：
 * - 所有保留字都基于官方文档定义
 * - 包含详细的官方文档引用
 * - 避免硬编码和重复定义
 */
public final class ReservedWords {
    
    // ==================== MySQL保留字 ====================
    // 基于MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/keywords.html
    
    private static final Set<String> MYSQL_RESERVED_WORDS;
    static {
        Set<String> words = new HashSet<>();
        
        // MySQL核心保留字 - 基于官方文档
        words.add("ACCESSIBLE");
        words.add("ADD");
        words.add("ALL");
        words.add("ALTER");
        words.add("ANALYZE");
        words.add("AND");
        words.add("AS");
        words.add("ASC");
        words.add("ASENSITIVE");
        words.add("BEFORE");
        words.add("BETWEEN");
        words.add("BIGINT");
        words.add("BINARY");
        words.add("BLOB");
        words.add("BOTH");
        words.add("BY");
        words.add("CALL");
        words.add("CASCADE");
        words.add("CASE");
        words.add("CHANGE");
        words.add("CHAR");
        words.add("CHARACTER");
        words.add("CHECK");
        words.add("COLLATE");
        words.add("COLUMN");
        words.add("CONDITION");
        words.add("CONSTRAINT");
        words.add("CONTINUE");
        words.add("CONVERT");
        words.add("CREATE");
        words.add("CROSS");
        words.add("CURRENT_DATE");
        words.add("CURRENT_TIME");
        words.add("CURRENT_TIMESTAMP");
        words.add("CURRENT_USER");
        words.add("CURSOR");
        words.add("DATABASE");
        words.add("DATABASES");
        words.add("DAY_HOUR");
        words.add("DAY_MICROSECOND");
        words.add("DAY_MINUTE");
        words.add("DAY_SECOND");
        words.add("DEC");
        words.add("DECIMAL");
        words.add("DECLARE");
        words.add("DEFAULT");
        words.add("DELAYED");
        words.add("DELETE");
        words.add("DESC");
        words.add("DESCRIBE");
        words.add("DETERMINISTIC");
        words.add("DISTINCT");
        words.add("DISTINCTROW");
        words.add("DIV");
        words.add("DOUBLE");
        words.add("DROP");
        words.add("DUAL");
        words.add("EACH");
        words.add("ELSE");
        words.add("ELSEIF");
        words.add("ENCLOSED");
        words.add("ESCAPED");
        words.add("EXISTS");
        words.add("EXIT");
        words.add("EXPLAIN");
        words.add("FALSE");
        words.add("FETCH");
        words.add("FLOAT");
        words.add("FLOAT4");
        words.add("FLOAT8");
        words.add("FOR");
        words.add("FORCE");
        words.add("FOREIGN");
        words.add("FROM");
        words.add("FULLTEXT");
        words.add("GRANT");
        words.add("GROUP");
        words.add("HAVING");
        words.add("HIGH_PRIORITY");
        words.add("HOUR_MICROSECOND");
        words.add("HOUR_MINUTE");
        words.add("HOUR_SECOND");
        words.add("IF");
        words.add("IGNORE");
        words.add("IN");
        words.add("INDEX");
        words.add("INFILE");
        words.add("INNER");
        words.add("INOUT");
        words.add("INSENSITIVE");
        words.add("INSERT");
        words.add("INT");
        words.add("INT1");
        words.add("INT2");
        words.add("INT3");
        words.add("INT4");
        words.add("INT8");
        words.add("INTEGER");
        words.add("INTERVAL");
        words.add("INTO");
        words.add("IS");
        words.add("ITERATE");
        words.add("JOIN");
        words.add("KEY");
        words.add("KEYS");
        words.add("KILL");
        words.add("LEADING");
        words.add("LEAVE");
        words.add("LEFT");
        words.add("LIKE");
        words.add("LIMIT");
        words.add("LINEAR");
        words.add("LINES");
        words.add("LOAD");
        words.add("LOCALTIME");
        words.add("LOCALTIMESTAMP");
        words.add("LOCK");
        words.add("LONG");
        words.add("LONGBLOB");
        words.add("LONGTEXT");
        words.add("LOOP");
        words.add("LOW_PRIORITY");
        words.add("MATCH");
        words.add("MEDIUMBLOB");
        words.add("MEDIUMINT");
        words.add("MEDIUMTEXT");
        words.add("MIDDLEINT");
        words.add("MINUTE_MICROSECOND");
        words.add("MINUTE_SECOND");
        words.add("MOD");
        words.add("MODIFIES");
        words.add("NATURAL");
        words.add("NOT");
        words.add("NO_WRITE_TO_BINLOG");
        words.add("NULL");
        words.add("NUMERIC");
        words.add("ON");
        words.add("OPTIMIZE");
        words.add("OPTION");
        words.add("OPTIONALLY");
        words.add("OR");
        words.add("ORDER");
        words.add("OUT");
        words.add("OUTER");
        words.add("OUTFILE");
        words.add("PRECISION");
        words.add("PRIMARY");
        words.add("PROCEDURE");
        words.add("PURGE");
        words.add("RANGE");
        words.add("READ");
        words.add("READS");
        words.add("READ_WRITE");
        words.add("REAL");
        words.add("REFERENCES");
        words.add("REGEXP");
        words.add("RELEASE");
        words.add("RENAME");
        words.add("REPEAT");
        words.add("REPLACE");
        words.add("REQUIRE");
        words.add("RESTRICT");
        words.add("RETURN");
        words.add("REVOKE");
        words.add("RIGHT");
        words.add("RLIKE");
        words.add("SCHEMA");
        words.add("SCHEMAS");
        words.add("SECOND_MICROSECOND");
        words.add("SELECT");
        words.add("SENSITIVE");
        words.add("SEPARATOR");
        words.add("SET");
        words.add("SHOW");
        words.add("SMALLINT");
        words.add("SPATIAL");
        words.add("SPECIFIC");
        words.add("SQL");
        words.add("SQLEXCEPTION");
        words.add("SQLSTATE");
        words.add("SQLWARNING");
        words.add("SQL_BIG_RESULT");
        words.add("SQL_CALC_FOUND_ROWS");
        words.add("SQL_SMALL_RESULT");
        words.add("SSL");
        words.add("STARTING");
        words.add("STRAIGHT_JOIN");
        words.add("TABLE");
        words.add("TERMINATED");
        words.add("THEN");
        words.add("TINYBLOB");
        words.add("TINYINT");
        words.add("TINYTEXT");
        words.add("TO");
        words.add("TRAILING");
        words.add("TRIGGER");
        words.add("TRUE");
        words.add("UNDO");
        words.add("UNION");
        words.add("UNIQUE");
        words.add("UNLOCK");
        words.add("UNSIGNED");
        words.add("UPDATE");
        words.add("USAGE");
        words.add("USE");
        words.add("USER");
        words.add("USING");
        words.add("UTC_DATE");
        words.add("UTC_TIME");
        words.add("UTC_TIMESTAMP");
        words.add("VALUES");
        words.add("VARBINARY");
        words.add("VARCHAR");
        words.add("VARCHARACTER");
        words.add("VARYING");
        words.add("VIEW");
        words.add("WHEN");
        words.add("WHERE");
        words.add("WHILE");
        words.add("WITH");
        words.add("WRITE");
        words.add("X509");
        words.add("XOR");
        words.add("YEAR_MONTH");
        words.add("ZEROFILL");
        
        MYSQL_RESERVED_WORDS = Collections.unmodifiableSet(words);
    }
    
    // ==================== 达梦保留字 ====================
    // 基于达梦官方文档: https://eco.dameng.com/document/dm/zh-cn/sql-dev/
    
    private static final Set<String> DAMENG_RESERVED_WORDS;
    static {
        Set<String> words = new HashSet<>();
        
        // 达梦核心保留字 - 基于官方文档
        words.add("ADD");
        words.add("ALL");
        words.add("ALTER");
        words.add("AND");
        words.add("ANY");
        words.add("AS");
        words.add("ASC");
        words.add("BACKUP");
        words.add("BEGIN");
        words.add("BETWEEN");
        words.add("BREAK");
        words.add("BROWSE");
        words.add("BULK");
        words.add("BY");
        words.add("CASCADE");
        words.add("CASE");
        words.add("CHECK");
        words.add("CHECKPOINT");
        words.add("CLOSE");
        words.add("CLUSTERED");
        words.add("COALESCE");
        words.add("COLLATE");
        words.add("COLUMN");
        words.add("COMMIT");
        words.add("COMPUTE");
        words.add("CONSTRAINT");
        words.add("CONTAINS");
        words.add("CONTAINSTABLE");
        words.add("CONTINUE");
        words.add("CONVERT");
        words.add("CREATE");
        words.add("CROSS");
        words.add("CURRENT");
        words.add("CURRENT_DATE");
        words.add("CURRENT_TIME");
        words.add("CURRENT_TIMESTAMP");
        words.add("CURRENT_USER");
        words.add("CURSOR");
        words.add("DATABASE");
        words.add("DBCC");
        words.add("DEALLOCATE");
        words.add("DECLARE");
        words.add("DEFAULT");
        words.add("DELETE");
        words.add("DENY");
        words.add("DESC");
        words.add("DISK");
        words.add("DISTINCT");
        words.add("DISTRIBUTED");
        words.add("DOUBLE");
        words.add("DROP");
        words.add("DUMP");
        words.add("ELSE");
        words.add("END");
        words.add("ERRLVL");
        words.add("ESCAPE");
        words.add("EXCEPT");
        words.add("EXEC");
        words.add("EXECUTE");
        words.add("EXISTS");
        words.add("EXIT");
        words.add("EXTERNAL");
        words.add("FETCH");
        words.add("FILE");
        words.add("FILLFACTOR");
        words.add("FOR");
        words.add("FOREIGN");
        words.add("FREETEXT");
        words.add("FREETEXTTABLE");
        words.add("FROM");
        words.add("FULL");
        words.add("FUNCTION");
        words.add("GOTO");
        words.add("GRANT");
        words.add("GROUP");
        words.add("HAVING");
        words.add("HOLDLOCK");
        words.add("IDENTITY");
        words.add("IDENTITY_INSERT");
        words.add("IDENTITYCOL");
        words.add("IF");
        words.add("IN");
        words.add("INDEX");
        words.add("INNER");
        words.add("INSERT");
        words.add("INTERSECT");
        words.add("INTO");
        words.add("IS");
        words.add("JOIN");
        words.add("KEY");
        words.add("KILL");
        words.add("LEFT");
        words.add("LIKE");
        words.add("LINENO");
        words.add("LOAD");
        words.add("MERGE");
        words.add("NATIONAL");
        words.add("NOCHECK");
        words.add("NONCLUSTERED");
        words.add("NOT");
        words.add("NULL");
        words.add("NULLIF");
        words.add("OF");
        words.add("OFF");
        words.add("OFFSETS");
        words.add("ON");
        words.add("OPEN");
        words.add("OPENDATASOURCE");
        words.add("OPENQUERY");
        words.add("OPENROWSET");
        words.add("OPENXML");
        words.add("OPTION");
        words.add("OR");
        words.add("ORDER");
        words.add("OUTER");
        words.add("OVER");
        words.add("PERCENT");
        words.add("PIVOT");
        words.add("PLAN");
        words.add("PRECISION");
        words.add("PRIMARY");
        words.add("PRINT");
        words.add("PROC");
        words.add("PROCEDURE");
        words.add("PUBLIC");
        words.add("RAISERROR");
        words.add("READ");
        words.add("READTEXT");
        words.add("RECONFIGURE");
        words.add("REFERENCES");
        words.add("REPLICATION");
        words.add("RESTORE");
        words.add("RESTRICT");
        words.add("RETURN");
        words.add("REVERT");
        words.add("REVOKE");
        words.add("RIGHT");
        words.add("ROLLBACK");
        words.add("ROWCOUNT");
        words.add("ROWGUIDCOL");
        words.add("RULE");
        words.add("SAVE");
        words.add("SCHEMA");
        words.add("SECURITYAUDIT");
        words.add("SELECT");
        words.add("SEMANTICKEYPHRASETABLE");
        words.add("SEMANTICSIMILARITYDETAILSTABLE");
        words.add("SEMANTICSIMILARITYTABLE");
        words.add("SESSION_USER");
        words.add("SET");
        words.add("SETUSER");
        words.add("SHUTDOWN");
        words.add("SOME");
        words.add("STATISTICS");
        words.add("SYSTEM_USER");
        words.add("TABLE");
        words.add("TABLESAMPLE");
        words.add("TEXTSIZE");
        words.add("THEN");
        words.add("TO");
        words.add("TOP");
        words.add("TRAN");
        words.add("TRANSACTION");
        words.add("TRIGGER");
        words.add("TRUNCATE");
        words.add("TRY_CONVERT");
        words.add("TSEQUAL");
        words.add("UNION");
        words.add("UNIQUE");
        words.add("UNPIVOT");
        words.add("UPDATE");
        words.add("UPDATETEXT");
        words.add("USE");
        words.add("USER");
        words.add("VALUES");
        words.add("VARYING");
        words.add("VIEW");
        words.add("WAITFOR");
        words.add("WHEN");
        words.add("WHERE");
        words.add("WHILE");
        words.add("WITH");
        words.add("WITHIN");
        words.add("WRITETEXT");

        // 达梦特有保留字 - 基于达梦官方文档
        // https://eco.dameng.com/document/dm/zh-cn/faq/faq-errorcode.html
        words.add("TYPE");      // 达梦特有保留字
        words.add("CLASS");     // 达梦特有保留字
        words.add("DOMAIN");    // 达梦特有保留字
        words.add("VERIFY");    // 达梦特有保留字
        words.add("OFFSET");    // 达梦特有保留字（注意：不是OFFSETS）

        DAMENG_RESERVED_WORDS = Collections.unmodifiableSet(words);
    }
    
    // ==================== 金仓保留字 ====================
    // 基于金仓官方文档（PostgreSQL兼容）: https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/
    
    private static final Set<String> KINGBASE_RESERVED_WORDS;
    static {
        Set<String> words = new HashSet<>();
        
        // 金仓/PostgreSQL核心保留字 - 基于官方文档
        words.add("ALL");
        words.add("ANALYSE");
        words.add("ANALYZE");
        words.add("AND");
        words.add("ANY");
        words.add("ARRAY");
        words.add("AS");
        words.add("ASC");
        words.add("ASYMMETRIC");
        words.add("AUTHORIZATION");
        words.add("BINARY");
        words.add("BOTH");
        words.add("CASE");
        words.add("CAST");
        words.add("CHECK");
        words.add("COLLATE");
        words.add("COLLATION");
        words.add("COLUMN");
        words.add("CONCURRENTLY");
        words.add("CONSTRAINT");
        words.add("CREATE");
        words.add("CROSS");
        words.add("CURRENT_CATALOG");
        words.add("CURRENT_DATE");
        words.add("CURRENT_ROLE");
        words.add("CURRENT_SCHEMA");
        words.add("CURRENT_TIME");
        words.add("CURRENT_TIMESTAMP");
        words.add("CURRENT_USER");
        words.add("DEFAULT");
        words.add("DEFERRABLE");
        words.add("DESC");
        words.add("DISTINCT");
        words.add("DO");
        words.add("ELSE");
        words.add("END");
        words.add("EXCEPT");
        words.add("FALSE");
        words.add("FETCH");
        words.add("FOR");
        words.add("FOREIGN");
        words.add("FREEZE");
        words.add("FROM");
        words.add("FULL");
        words.add("GRANT");
        words.add("GROUP");
        words.add("HAVING");
        words.add("ILIKE");
        words.add("IN");
        words.add("INITIALLY");
        words.add("INNER");
        words.add("INTERSECT");
        words.add("INTO");
        words.add("IS");
        words.add("ISNULL");
        words.add("JOIN");
        words.add("LATERAL");
        words.add("LEADING");
        words.add("LEFT");
        words.add("LIKE");
        words.add("LIMIT");
        words.add("LOCALTIME");
        words.add("LOCALTIMESTAMP");
        words.add("NATURAL");
        words.add("NOT");
        words.add("NOTNULL");
        words.add("NULL");
        words.add("OFFSET");
        words.add("ON");
        words.add("ONLY");
        words.add("OR");
        words.add("ORDER");
        words.add("OUTER");
        words.add("OVERLAPS");
        words.add("PLACING");
        words.add("PRIMARY");
        words.add("REFERENCES");
        words.add("RETURNING");
        words.add("RIGHT");
        words.add("SELECT");
        words.add("SESSION_USER");
        words.add("SIMILAR");
        words.add("SOME");
        words.add("SYMMETRIC");
        words.add("TABLE");
        words.add("TABLESAMPLE");
        words.add("THEN");
        words.add("TO");
        words.add("TRAILING");
        words.add("TRUE");
        words.add("UNION");
        words.add("UNIQUE");
        words.add("USER");
        words.add("USING");
        words.add("VARIADIC");
        words.add("VERBOSE");
        words.add("WHEN");
        words.add("WHERE");
        words.add("WINDOW");
        words.add("WITH");

        // 基础DML关键字 - 金仓数据库作为PostgreSQL兼容数据库也保留这些关键字
        words.add("INSERT");
        words.add("UPDATE");
        words.add("DELETE");

        KINGBASE_RESERVED_WORDS = Collections.unmodifiableSet(words);
    }
    
    // ==================== 神通保留字 ====================
    // 基于神通官方文档（Oracle兼容）: @shentong.md
    
    private static final Set<String> SHENTONG_RESERVED_WORDS;
    static {
        Set<String> words = new HashSet<>();
        
        // 神通/Oracle核心保留字 - 基于官方文档
        words.add("ACCESS");
        words.add("ADD");
        words.add("ALL");
        words.add("ALTER");
        words.add("AND");
        words.add("ANY");
        words.add("AS");
        words.add("ASC");
        words.add("AUDIT");
        words.add("BETWEEN");
        words.add("BY");
        words.add("CHAR");
        words.add("CHECK");
        words.add("CLUSTER");
        words.add("COLUMN");
        words.add("COLUMN_VALUE");
        words.add("COMMENT");
        words.add("COMPRESS");
        words.add("CONNECT");
        words.add("CREATE");
        words.add("CURRENT");
        words.add("DATE");
        words.add("DECIMAL");
        words.add("DEFAULT");
        words.add("DELETE");
        words.add("DESC");
        words.add("DISTINCT");
        words.add("DROP");
        words.add("ELSE");
        words.add("EXCLUSIVE");
        words.add("EXISTS");
        words.add("FILE");
        words.add("FLOAT");
        words.add("FOR");
        words.add("FROM");
        words.add("GRANT");
        words.add("GROUP");
        words.add("HAVING");
        words.add("IDENTIFIED");
        words.add("IMMEDIATE");
        words.add("IN");
        words.add("INCREMENT");
        words.add("INDEX");
        words.add("INITIAL");
        words.add("INSERT");
        words.add("INTEGER");
        words.add("INTERSECT");
        words.add("INTO");
        words.add("IS");
        words.add("LEVEL");
        words.add("LIKE");
        words.add("LOCK");
        words.add("LONG");
        words.add("MAXEXTENTS");
        words.add("MINUS");
        words.add("MLSLABEL");
        words.add("MODE");
        words.add("MODIFY");
        words.add("NESTED_TABLE_ID");
        words.add("NOAUDIT");
        words.add("NOCOMPRESS");
        words.add("NOT");
        words.add("NOWAIT");
        words.add("NULL");
        words.add("NUMBER");
        words.add("OF");
        words.add("OFFLINE");
        words.add("ON");
        words.add("ONLINE");
        words.add("OPTION");
        words.add("OR");
        words.add("ORDER");
        words.add("PCTFREE");
        words.add("PRIOR");
        words.add("PUBLIC");
        words.add("RAW");
        words.add("RENAME");
        words.add("RESOURCE");
        words.add("REVOKE");
        words.add("ROW");
        words.add("ROWID");
        words.add("ROWNUM");
        words.add("ROWS");
        words.add("SELECT");
        words.add("SESSION");
        words.add("SET");
        words.add("SHARE");
        words.add("SIZE");
        words.add("SMALLINT");
        words.add("START");
        words.add("SUCCESSFUL");
        words.add("SYNONYM");
        words.add("SYSDATE");
        words.add("TABLE");
        words.add("THEN");
        words.add("TO");
        words.add("TRIGGER");
        words.add("UID");
        words.add("UNION");
        words.add("UNIQUE");
        words.add("UPDATE");
        words.add("USER");
        words.add("VALIDATE");
        words.add("VALUES");
        words.add("VARCHAR");
        words.add("VARCHAR2");
        words.add("VIEW");
        words.add("WHENEVER");
        words.add("WHERE");
        words.add("WITH");
        
        SHENTONG_RESERVED_WORDS = Collections.unmodifiableSet(words);
    }
    
    // ==================== 通用保留字 ====================
    // 所有数据库都保留的常见关键字
    
    private static final Set<String> COMMON_RESERVED_WORDS;
    static {
        Set<String> words = new HashSet<>();
        
        // SQL标准保留字
        words.add("SELECT");
        words.add("FROM");
        words.add("WHERE");
        words.add("INSERT");
        words.add("UPDATE");
        words.add("DELETE");
        words.add("CREATE");
        words.add("DROP");
        words.add("ALTER");
        words.add("TABLE");
        words.add("INDEX");
        words.add("VIEW");
        words.add("USER");
        words.add("ORDER");
        words.add("GROUP");
        words.add("HAVING");
        words.add("UNION");
        words.add("JOIN");
        words.add("AND");
        words.add("OR");
        words.add("NOT");
        words.add("NULL");
        words.add("IS");
        words.add("IN");
        words.add("EXISTS");
        words.add("BETWEEN");
        words.add("LIKE");
        words.add("AS");
        words.add("ON");
        words.add("DISTINCT");
        words.add("ALL");
        words.add("ANY");
        words.add("SOME");
        words.add("PRIMARY");
        words.add("FOREIGN");
        words.add("KEY");
        words.add("REFERENCES");
        words.add("CONSTRAINT");
        words.add("CHECK");
        words.add("UNIQUE");
        words.add("DEFAULT");
        words.add("GRANT");
        words.add("REVOKE");
        words.add("COMMIT");
        words.add("ROLLBACK");
        words.add("TRANSACTION");
        
        COMMON_RESERVED_WORDS = Collections.unmodifiableSet(words);
    }
    
    // 私有构造函数
    private ReservedWords() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }
    
    // ==================== 公共方法 ====================
    
    /**
     * 检查单词是否为指定数据库的保留字
     * 
     * @param word 要检查的单词
     * @param databaseType 数据库类型
     * @return 是否为保留字
     */
    public static boolean isReservedWord(String word, String databaseType) {
        if (word == null || word.trim().isEmpty()) {
            return false;
        }

        if (databaseType == null || databaseType.trim().isEmpty()) {
            return false;
        }

        String upperWord = word.toUpperCase();
        String lowerDbType = databaseType.toLowerCase();

        switch (lowerDbType) {
            case DatabaseConstants.MYSQL:
                return MYSQL_RESERVED_WORDS.contains(upperWord);
            case DatabaseConstants.DAMENG:
                return DAMENG_RESERVED_WORDS.contains(upperWord);
            case DatabaseConstants.KINGBASE:
                return KINGBASE_RESERVED_WORDS.contains(upperWord);
            case DatabaseConstants.SHENTONG:
                return SHENTONG_RESERVED_WORDS.contains(upperWord);
            default:
                // 对于未知数据库类型，返回false而不是使用通用保留字
                // 这样可以避免误判，确保只有明确支持的数据库才进行保留字检查
                return false;
        }
    }
    
    /**
     * 检查单词是否为通用保留字（所有数据库都保留）
     * 
     * @param word 要检查的单词
     * @return 是否为通用保留字
     */
    public static boolean isCommonReservedWord(String word) {
        if (word == null || word.trim().isEmpty()) {
            return false;
        }
        return COMMON_RESERVED_WORDS.contains(word.toUpperCase());
    }
    
    /**
     * 获取指定数据库的所有保留字
     * 
     * @param databaseType 数据库类型
     * @return 保留字集合（不可修改）
     */
    public static Set<String> getReservedWords(String databaseType) {
        if (databaseType == null) {
            return COMMON_RESERVED_WORDS;
        }
        
        switch (databaseType.toLowerCase()) {
            case DatabaseConstants.MYSQL:
                return MYSQL_RESERVED_WORDS;
            case DatabaseConstants.DAMENG:
                return DAMENG_RESERVED_WORDS;
            case DatabaseConstants.KINGBASE:
                return KINGBASE_RESERVED_WORDS;
            case DatabaseConstants.SHENTONG:
                return SHENTONG_RESERVED_WORDS;
            default:
                return COMMON_RESERVED_WORDS;
        }
    }
    
    /**
     * 获取通用保留字集合
     * 
     * @return 通用保留字集合（不可修改）
     */
    public static Set<String> getCommonReservedWords() {
        return COMMON_RESERVED_WORDS;
    }
    
    /**
     * 获取保留字数量统计
     * 
     * @return 各数据库保留字数量的映射
     */
    public static java.util.Map<String, Integer> getReservedWordCounts() {
        java.util.Map<String, Integer> counts = new java.util.HashMap<>();
        counts.put("MySQL", MYSQL_RESERVED_WORDS.size());
        counts.put("Dameng", DAMENG_RESERVED_WORDS.size());
        counts.put("Kingbase", KINGBASE_RESERVED_WORDS.size());
        counts.put("Shentong", SHENTONG_RESERVED_WORDS.size());
        counts.put("Common", COMMON_RESERVED_WORDS.size());
        return counts;
    }
}
