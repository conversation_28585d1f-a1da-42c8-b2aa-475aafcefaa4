package com.xylink.sqltranspiler.common.constants;

import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

/**
 * SQL关键字统一管理
 * 
 * 与ReservedWords的区别：
 * - ReservedWords: 数据库保留字，用于标识符引用判断
 * - SqlKeywords: SQL语法关键字，用于SQL格式化和语法分析
 * 
 * 遵循 .augment/rules/rule-db.md：
 * - 基于MySQL 8.4官方文档定义
 * - 包含详细的官方文档引用
 * - 避免硬编码和重复定义
 */
public final class SqlKeywords {
    
    // ==================== 基础SQL关键字 ====================
    // 基于MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/keywords.html
    
    private static final Set<String> BASIC_KEYWORDS;
    static {
        Set<String> keywords = new HashSet<>();
        
        // 基础逻辑关键字
        keywords.add("AND");
        keywords.add("OR");
        keywords.add("NOT");
        keywords.add("IN");
        keywords.add("EXISTS");
        keywords.add("IS");
        keywords.add("NULL");
        keywords.add("TRUE");
        keywords.add("FALSE");
        
        // 查询关键字
        keywords.add("SELECT");
        keywords.add("FROM");
        keywords.add("WHERE");
        keywords.add("ORDER");
        keywords.add("BY");
        keywords.add("LIMIT");
        keywords.add("ASC");
        keywords.add("DESC");
        keywords.add("DISTINCT");

        // DML关键字
        keywords.add("INSERT");
        keywords.add("UPDATE");
        keywords.add("DELETE");
        keywords.add("INTO");
        keywords.add("VALUES");
        keywords.add("SET");

        // DDL关键字
        keywords.add("CREATE");
        keywords.add("ALTER");
        keywords.add("DROP");
        keywords.add("TABLE");
        keywords.add("INDEX");
        keywords.add("DATABASE");
        keywords.add("SCHEMA");
        keywords.add("VIEW");
        
        // 连接关键字
        keywords.add("ON");
        keywords.add("INNER");
        keywords.add("LEFT");
        keywords.add("RIGHT");
        keywords.add("JOIN");
        keywords.add("UNION");
        keywords.add("GROUP");
        keywords.add("HAVING");
        
        // 条件关键字
        keywords.add("CASE");
        keywords.add("WHEN");
        keywords.add("THEN");
        keywords.add("ELSE");
        keywords.add("END");
        keywords.add("AS");
        keywords.add("BETWEEN");
        keywords.add("LIKE");
        keywords.add("ILIKE");
        keywords.add("REGEXP");
        keywords.add("RLIKE");
        
        // 其他重要关键字
        keywords.add("ALL");
        keywords.add("ANY");
        keywords.add("SOME");
        keywords.add("INTERSECT");
        keywords.add("EXCEPT");
        keywords.add("MINUS");
        
        BASIC_KEYWORDS = Collections.unmodifiableSet(keywords);
    }
    
    // ==================== 窗口函数关键字 ====================
    // 基于MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/window-functions.html
    
    private static final Set<String> WINDOW_FUNCTION_KEYWORDS;
    static {
        Set<String> keywords = new HashSet<>();
        
        // 窗口函数框架关键字
        keywords.add("OVER");
        keywords.add("PARTITION");
        keywords.add("ROWS");
        keywords.add("RANGE");
        keywords.add("UNBOUNDED");
        keywords.add("PRECEDING");
        keywords.add("FOLLOWING");
        keywords.add("CURRENT");
        keywords.add("ROW");
        
        // 窗口函数名称
        keywords.add("ROW_NUMBER");
        keywords.add("RANK");
        keywords.add("DENSE_RANK");
        keywords.add("NTILE");
        keywords.add("LAG");
        keywords.add("LEAD");
        keywords.add("FIRST_VALUE");
        keywords.add("LAST_VALUE");
        
        WINDOW_FUNCTION_KEYWORDS = Collections.unmodifiableSet(keywords);
    }
    
    // ==================== CTE关键字 ====================
    // 基于MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/with.html
    
    private static final Set<String> CTE_KEYWORDS;
    static {
        Set<String> keywords = new HashSet<>();
        
        keywords.add("WITH");
        keywords.add("RECURSIVE");
        
        CTE_KEYWORDS = Collections.unmodifiableSet(keywords);
    }
    
    // ==================== 聚合函数关键字 ====================
    // 基于MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/aggregate-functions.html
    
    private static final Set<String> AGGREGATE_FUNCTION_KEYWORDS;
    static {
        Set<String> keywords = new HashSet<>();
        
        keywords.add("COUNT");
        keywords.add("SUM");
        keywords.add("AVG");
        keywords.add("MAX");
        keywords.add("MIN");
        keywords.add("GROUP_CONCAT");
        keywords.add("BIT_AND");
        keywords.add("BIT_OR");
        keywords.add("BIT_XOR");
        keywords.add("STD");
        keywords.add("STDDEV");
        keywords.add("STDDEV_POP");
        keywords.add("STDDEV_SAMP");
        keywords.add("VAR_POP");
        keywords.add("VAR_SAMP");
        keywords.add("VARIANCE");
        
        AGGREGATE_FUNCTION_KEYWORDS = Collections.unmodifiableSet(keywords);
    }
    
    // ==================== 日期时间函数关键字 ====================
    // 基于MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/date-and-time-functions.html
    
    private static final Set<String> DATETIME_FUNCTION_KEYWORDS;
    static {
        Set<String> keywords = new HashSet<>();
        
        keywords.add("NOW");
        keywords.add("CURDATE");
        keywords.add("CURTIME");
        keywords.add("SYSDATE");
        keywords.add("UTC_DATE");
        keywords.add("UTC_TIME");
        keywords.add("UTC_TIMESTAMP");
        keywords.add("DATE_ADD");
        keywords.add("DATE_SUB");
        keywords.add("DATEDIFF");
        keywords.add("TIMEDIFF");
        keywords.add("TIMESTAMPDIFF");
        keywords.add("TIMESTAMPADD");
        keywords.add("INTERVAL");
        keywords.add("YEAR");
        keywords.add("MONTH");
        keywords.add("DAY");
        keywords.add("HOUR");
        keywords.add("MINUTE");
        keywords.add("SECOND");
        keywords.add("MICROSECOND");
        
        DATETIME_FUNCTION_KEYWORDS = Collections.unmodifiableSet(keywords);
    }
    
    // ==================== 所有关键字集合 ====================
    
    private static final Set<String> ALL_KEYWORDS;
    static {
        Set<String> allKeywords = new HashSet<>();
        allKeywords.addAll(BASIC_KEYWORDS);
        allKeywords.addAll(WINDOW_FUNCTION_KEYWORDS);
        allKeywords.addAll(CTE_KEYWORDS);
        allKeywords.addAll(AGGREGATE_FUNCTION_KEYWORDS);
        allKeywords.addAll(DATETIME_FUNCTION_KEYWORDS);
        
        ALL_KEYWORDS = Collections.unmodifiableSet(allKeywords);
    }
    
    // ==================== 公共方法 ====================
    
    /**
     * 判断是否为SQL关键字
     * 
     * @param word 要检查的词
     * @return 如果是SQL关键字返回true，否则返回false
     */
    public static boolean isSqlKeyword(String word) {
        if (word == null || word.isEmpty()) {
            return false;
        }
        return ALL_KEYWORDS.contains(word.toUpperCase());
    }
    
    /**
     * 判断是否为基础SQL关键字
     * 
     * @param word 要检查的词
     * @return 如果是基础SQL关键字返回true，否则返回false
     */
    public static boolean isBasicKeyword(String word) {
        if (word == null || word.isEmpty()) {
            return false;
        }
        return BASIC_KEYWORDS.contains(word.toUpperCase());
    }
    
    /**
     * 判断是否为窗口函数关键字
     * 
     * @param word 要检查的词
     * @return 如果是窗口函数关键字返回true，否则返回false
     */
    public static boolean isWindowFunctionKeyword(String word) {
        if (word == null || word.isEmpty()) {
            return false;
        }
        return WINDOW_FUNCTION_KEYWORDS.contains(word.toUpperCase());
    }
    
    /**
     * 判断是否为CTE关键字
     * 
     * @param word 要检查的词
     * @return 如果是CTE关键字返回true，否则返回false
     */
    public static boolean isCteKeyword(String word) {
        if (word == null || word.isEmpty()) {
            return false;
        }
        return CTE_KEYWORDS.contains(word.toUpperCase());
    }
    
    /**
     * 判断是否为聚合函数关键字
     * 
     * @param word 要检查的词
     * @return 如果是聚合函数关键字返回true，否则返回false
     */
    public static boolean isAggregateFunctionKeyword(String word) {
        if (word == null || word.isEmpty()) {
            return false;
        }
        return AGGREGATE_FUNCTION_KEYWORDS.contains(word.toUpperCase());
    }
    
    /**
     * 判断是否为日期时间函数关键字
     * 
     * @param word 要检查的词
     * @return 如果是日期时间函数关键字返回true，否则返回false
     */
    public static boolean isDatetimeFunctionKeyword(String word) {
        if (word == null || word.isEmpty()) {
            return false;
        }
        return DATETIME_FUNCTION_KEYWORDS.contains(word.toUpperCase());
    }
    
    /**
     * 获取所有SQL关键字
     * 
     * @return 所有SQL关键字的不可修改集合
     */
    public static Set<String> getAllKeywords() {
        return ALL_KEYWORDS;
    }
    
    /**
     * 获取基础SQL关键字
     * 
     * @return 基础SQL关键字的不可修改集合
     */
    public static Set<String> getBasicKeywords() {
        return BASIC_KEYWORDS;
    }
    
    /**
     * 获取窗口函数关键字
     * 
     * @return 窗口函数关键字的不可修改集合
     */
    public static Set<String> getWindowFunctionKeywords() {
        return WINDOW_FUNCTION_KEYWORDS;
    }
    
    /**
     * 获取CTE关键字
     * 
     * @return CTE关键字的不可修改集合
     */
    public static Set<String> getCteKeywords() {
        return CTE_KEYWORDS;
    }
    
    /**
     * 获取聚合函数关键字
     * 
     * @return 聚合函数关键字的不可修改集合
     */
    public static Set<String> getAggregateFunctionKeywords() {
        return AGGREGATE_FUNCTION_KEYWORDS;
    }
    
    /**
     * 获取日期时间函数关键字
     * 
     * @return 日期时间函数关键字的不可修改集合
     */
    public static Set<String> getDatetimeFunctionKeywords() {
        return DATETIME_FUNCTION_KEYWORDS;
    }
}
