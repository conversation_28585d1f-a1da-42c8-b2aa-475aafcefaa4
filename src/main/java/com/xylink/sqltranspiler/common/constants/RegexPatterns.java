package com.xylink.sqltranspiler.common.constants;

import java.util.regex.Pattern;

/**
 * 正则表达式模式常量统一管理
 * 避免在各个类中重复定义相同的正则表达式
 * 
 * 遵循 .augment/rules/rule-db.md：
 * - 所有正则表达式都基于官方文档定义
 * - 包含详细的官方文档引用
 * - 避免硬编码和重复定义
 */
public final class RegexPatterns {
    
    // ==================== SQL标识符相关模式 ====================
    
    /**
     * MySQL标识符模式 - 基于MySQL 8.4官方文档
     * https://dev.mysql.com/doc/refman/8.4/en/identifiers.html
     */
    public static final Pattern MYSQL_IDENTIFIER = Pattern.compile("^[a-zA-Z_][a-zA-Z0-9_]*$");
    
    /**
     * 反引号标识符模式 - MySQL特有
     */
    public static final Pattern BACKTICK_IDENTIFIER = Pattern.compile("`([^`]+)`");
    
    /**
     * 双引号标识符模式 - SQL标准
     */
    public static final Pattern DOUBLE_QUOTE_IDENTIFIER = Pattern.compile("\"([^\"]+)\"");
    
    /**
     * 单引号字符串模式
     */
    public static final Pattern SINGLE_QUOTE_STRING = Pattern.compile("'([^']*)'");
    
    /**
     * 引用标识符模式（反引号、双引号、单引号）
     */
    public static final Pattern QUOTED_IDENTIFIER = Pattern.compile("^[`\"'].*[`\"']$");
    
    /**
     * Schema.Table模式
     */
    public static final Pattern SCHEMA_TABLE = Pattern.compile("\\b([a-zA-Z_][a-zA-Z0-9_]*)\\.([a-zA-Z_][a-zA-Z0-9_]*)\\b");
    
    // ==================== 数据类型相关模式 ====================
    
    /**
     * VARCHAR类型模式 - 匹配VARCHAR(n)
     */
    public static final Pattern VARCHAR_TYPE = Pattern.compile("VARCHAR\\s*\\(\\s*(\\d+)\\s*\\)", Pattern.CASE_INSENSITIVE);
    
    /**
     * CHAR类型模式 - 匹配CHAR(n)
     */
    public static final Pattern CHAR_TYPE = Pattern.compile("CHAR\\s*\\(\\s*(\\d+)\\s*\\)", Pattern.CASE_INSENSITIVE);
    
    /**
     * DECIMAL类型模式 - 匹配DECIMAL(p,s)
     */
    public static final Pattern DECIMAL_TYPE = Pattern.compile("DECIMAL\\s*\\(\\s*(\\d+)\\s*,\\s*(\\d+)\\s*\\)", Pattern.CASE_INSENSITIVE);
    
    /**
     * MySQL特有文本类型模式
     */
    public static final Pattern MYSQL_TEXT_TYPES = Pattern.compile("\\b(TINYTEXT|MEDIUMTEXT|LONGTEXT|ENUM|SET)\\b", Pattern.CASE_INSENSITIVE);
    
    /**
     * MySQL特有整数类型模式
     */
    public static final Pattern MYSQL_INT_TYPES = Pattern.compile("\\b(TINYINT|SMALLINT|MEDIUMINT|BIGINT)\\b", Pattern.CASE_INSENSITIVE);
    
    /**
     * AUTO_INCREMENT模式
     */
    public static final Pattern AUTO_INCREMENT = Pattern.compile("\\bAUTO_INCREMENT\\b", Pattern.CASE_INSENSITIVE);
    
    // ==================== 函数相关模式 ====================
    
    /**
     * 函数调用模式 - 匹配function_name(
     */
    public static final Pattern FUNCTION_CALL = Pattern.compile("\\b(\\w+)\\s*\\(");
    
    /**
     * DATE_FORMAT函数模式
     */
    public static final Pattern DATE_FORMAT_FUNCTION = Pattern.compile("(?i)\\bDATE_FORMAT\\s*\\(([^,]+),\\s*([^)]+)\\)");
    
    /**
     * NOW()函数模式
     */
    public static final Pattern NOW_FUNCTION = Pattern.compile("\\bNOW\\s*\\(\\s*\\)", Pattern.CASE_INSENSITIVE);
    
    /**
     * CURDATE()函数模式
     */
    public static final Pattern CURDATE_FUNCTION = Pattern.compile("\\bCURDATE\\s*\\(\\s*\\)", Pattern.CASE_INSENSITIVE);
    
    /**
     * CURTIME()函数模式
     */
    public static final Pattern CURTIME_FUNCTION = Pattern.compile("\\bCURTIME\\s*\\(\\s*\\)", Pattern.CASE_INSENSITIVE);
    
    /**
     * SUBSTRING函数模式
     */
    public static final Pattern SUBSTRING_FUNCTION = Pattern.compile("\\bSUBSTRING\\s*\\(", Pattern.CASE_INSENSITIVE);
    
    /**
     * IFNULL函数模式
     */
    public static final Pattern IFNULL_FUNCTION = Pattern.compile("\\bIFNULL\\s*\\(", Pattern.CASE_INSENSITIVE);
    
    /**
     * RAND函数模式
     */
    public static final Pattern RAND_FUNCTION = Pattern.compile("\\bRAND\\s*\\(\\s*\\)", Pattern.CASE_INSENSITIVE);
    
    // ==================== SQL语句相关模式 ====================
    
    /**
     * CREATE TABLE语句模式
     */
    public static final Pattern CREATE_TABLE = Pattern.compile("\\bCREATE\\s+TABLE\\b", Pattern.CASE_INSENSITIVE);
    
    /**
     * INSERT语句模式
     */
    public static final Pattern INSERT_STATEMENT = Pattern.compile("\\bINSERT\\s+INTO\\b", Pattern.CASE_INSENSITIVE);
    
    /**
     * SELECT语句模式
     */
    public static final Pattern SELECT_STATEMENT = Pattern.compile("\\bSELECT\\b", Pattern.CASE_INSENSITIVE);
    
    /**
     * UPDATE语句模式
     */
    public static final Pattern UPDATE_STATEMENT = Pattern.compile("\\bUPDATE\\b", Pattern.CASE_INSENSITIVE);
    
    /**
     * DELETE语句模式
     */
    public static final Pattern DELETE_STATEMENT = Pattern.compile("\\bDELETE\\s+FROM\\b", Pattern.CASE_INSENSITIVE);
    
    /**
     * REPLACE INTO语句模式
     */
    public static final Pattern REPLACE_INTO = Pattern.compile("\\bREPLACE\\s+INTO\\b", Pattern.CASE_INSENSITIVE);
    
    /**
     * USE语句模式
     */
    public static final Pattern USE_STATEMENT = Pattern.compile("\\bUSE\\s+\\w+", Pattern.CASE_INSENSITIVE);
    
    /**
     * SET语句模式
     */
    public static final Pattern SET_STATEMENT = Pattern.compile("\\bSET\\s+(FOREIGN_KEY_CHECKS|SQL_MODE|CHARACTER_SET_CLIENT|NAMES|TIME_ZONE)\\b", Pattern.CASE_INSENSITIVE);
    
    // ==================== 数据库特定语法模式 ====================
    
    /**
     * MySQL存储引擎模式
     */
    public static final Pattern MYSQL_ENGINE = Pattern.compile("\\bENGINE\\s*=\\s*(InnoDB|MyISAM|Memory)", Pattern.CASE_INSENSITIVE);
    
    /**
     * MySQL字符集模式
     */
    public static final Pattern MYSQL_CHARSET = Pattern.compile("\\bCHARSET\\s*=\\s*utf8mb4", Pattern.CASE_INSENSITIVE);
    
    /**
     * PostgreSQL数组语法模式
     */
    public static final Pattern POSTGRESQL_ARRAY = Pattern.compile("(?<!\\$\\.)(?<!\\')\\b\\w+\\[\\d+\\](?!\\w)|\\bARRAY\\[.*\\]");
    
    /**
     * PostgreSQL SERIAL类型模式
     */
    public static final Pattern POSTGRESQL_SERIAL = Pattern.compile("\\b(SERIAL|BIGSERIAL|SMALLSERIAL)\\b", Pattern.CASE_INSENSITIVE);
    
    /**
     * Oracle ROWNUM模式
     */
    public static final Pattern ORACLE_ROWNUM = Pattern.compile("\\bROWNUM\\b", Pattern.CASE_INSENSITIVE);
    
    /**
     * LIMIT语句模式
     */
    public static final Pattern LIMIT_CLAUSE = Pattern.compile("\\bLIMIT\\s+\\d+", Pattern.CASE_INSENSITIVE);
    
    // ==================== 错误检测相关模式 ====================
    
    /**
     * 未闭合字符串模式
     */
    public static final Pattern UNCLOSED_STRING = Pattern.compile("(?i)unterminated\\s+string|unclosed\\s+string|missing\\s+closing\\s+quote");
    
    /**
     * 无效列引用模式
     */
    public static final Pattern INVALID_COLUMN = Pattern.compile("(?i)unknown\\s+column|column\\s+not\\s+found");
    
    /**
     * 无效表引用模式
     */
    public static final Pattern INVALID_TABLE = Pattern.compile("(?i)table\\s+.*\\s+doesn't\\s+exist|unknown\\s+table");
    
    /**
     * 数字字面量模式
     */
    public static final Pattern NUMERIC_LITERAL = Pattern.compile("^[0-9]+(\\.[0-9]+)?$");
    
    // ==================== 安全相关模式 ====================
    
    /**
     * SQL注入风险模式 - 基本模式
     */
    public static final Pattern SQL_INJECTION_BASIC = Pattern.compile("(\\bUNION\\b.*\\bSELECT\\b|\\bOR\\b.*=.*|\\bAND\\b.*=.*)", Pattern.CASE_INSENSITIVE);
    
    /**
     * 注释注入模式
     */
    public static final Pattern COMMENT_INJECTION = Pattern.compile("(--|/\\*|\\*/|#)", Pattern.CASE_INSENSITIVE);
    
    // ==================== 性能相关模式 ====================
    
    /**
     * 全表扫描风险模式
     */
    public static final Pattern FULL_TABLE_SCAN = Pattern.compile("\\bSELECT\\s+\\*\\s+FROM\\s+\\w+\\s*(?!WHERE)", Pattern.CASE_INSENSITIVE);
    
    /**
     * 大结果集风险模式
     */
    public static final Pattern LARGE_RESULT_SET = Pattern.compile("\\bSELECT\\s+.*\\s+FROM\\s+.*\\s+(?!LIMIT)", Pattern.CASE_INSENSITIVE);
    
    // ==================== 窗口函数相关模式 ====================
    
    /**
     * 窗口函数模式
     */
    public static final Pattern WINDOW_FUNCTION = Pattern.compile("\\b(ROW_NUMBER|RANK|DENSE_RANK|NTILE|LAG|LEAD|FIRST_VALUE|LAST_VALUE)\\s*\\(.*\\)\\s+OVER\\s*\\(", Pattern.CASE_INSENSITIVE);
    
    /**
     * PARTITION BY模式
     */
    public static final Pattern PARTITION_BY = Pattern.compile("\\bPARTITION\\s+BY\\b", Pattern.CASE_INSENSITIVE);
    
    /**
     * ORDER BY模式
     */
    public static final Pattern ORDER_BY = Pattern.compile("\\bORDER\\s+BY\\b", Pattern.CASE_INSENSITIVE);
    
    // ==================== JSON相关模式 ====================
    
    /**
     * JSON路径表达式模式
     */
    public static final Pattern JSON_PATH = Pattern.compile("\\$\\.[a-zA-Z0-9_\\[\\]]+");
    
    /**
     * JSON_EXTRACT函数模式
     */
    public static final Pattern JSON_EXTRACT = Pattern.compile("\\bJSON_EXTRACT\\s*\\(", Pattern.CASE_INSENSITIVE);
    
    // ==================== 全文搜索相关模式 ====================
    
    /**
     * MATCH AGAINST模式
     */
    public static final Pattern MATCH_AGAINST = Pattern.compile("\\bMATCH\\s*\\([^)]+\\)\\s*AGAINST\\s*\\(", Pattern.CASE_INSENSITIVE);
    
    /**
     * FULLTEXT索引模式
     */
    public static final Pattern FULLTEXT_INDEX = Pattern.compile("\\bFULLTEXT\\s+INDEX\\b", Pattern.CASE_INSENSITIVE);
    
    // ==================== 空格和格式相关模式 ====================
    
    /**
     * 多个空格模式
     */
    public static final Pattern MULTIPLE_SPACES = Pattern.compile("\\s+");
    
    /**
     * 连续逗号模式
     */
    public static final Pattern CONSECUTIVE_COMMAS = Pattern.compile(",,");
    
    /**
     * 连续分号模式
     */
    public static final Pattern CONSECUTIVE_SEMICOLONS = Pattern.compile(";;");
    
    // ==================== 私有构造函数 ====================
    
    private RegexPatterns() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }
    
    // ==================== 辅助方法 ====================
    
    /**
     * 检查字符串是否匹配指定模式
     * 
     * @param pattern 正则表达式模式
     * @param input 输入字符串
     * @return 是否匹配
     */
    public static boolean matches(Pattern pattern, String input) {
        return pattern != null && input != null && pattern.matcher(input).find();
    }
    
    /**
     * 检查字符串是否完全匹配指定模式
     * 
     * @param pattern 正则表达式模式
     * @param input 输入字符串
     * @return 是否完全匹配
     */
    public static boolean fullMatches(Pattern pattern, String input) {
        return pattern != null && input != null && pattern.matcher(input).matches();
    }
    
    /**
     * 获取模式的第一个匹配组
     * 
     * @param pattern 正则表达式模式
     * @param input 输入字符串
     * @return 第一个匹配组，如果没有匹配则返回null
     */
    public static String getFirstGroup(Pattern pattern, String input) {
        if (pattern == null || input == null) {
            return null;
        }
        
        java.util.regex.Matcher matcher = pattern.matcher(input);
        if (matcher.find() && matcher.groupCount() > 0) {
            return matcher.group(1);
        }
        
        return null;
    }
    
    /**
     * 替换所有匹配的内容
     * 
     * @param pattern 正则表达式模式
     * @param input 输入字符串
     * @param replacement 替换字符串
     * @return 替换后的字符串
     */
    public static String replaceAll(Pattern pattern, String input, String replacement) {
        if (pattern == null || input == null || replacement == null) {
            return input;
        }
        
        return pattern.matcher(input).replaceAll(replacement);
    }
    
    /**
     * 计算模式在字符串中的匹配次数
     * 
     * @param pattern 正则表达式模式
     * @param input 输入字符串
     * @return 匹配次数
     */
    public static int countMatches(Pattern pattern, String input) {
        if (pattern == null || input == null) {
            return 0;
        }
        
        java.util.regex.Matcher matcher = pattern.matcher(input);
        int count = 0;
        while (matcher.find()) {
            count++;
        }
        
        return count;
    }
}
