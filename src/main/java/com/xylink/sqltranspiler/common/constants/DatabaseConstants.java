package com.xylink.sqltranspiler.common.constants;

/**
 * 数据库相关常量定义
 * 统一管理项目中使用的数据库相关常量，避免重复定义和魔法数字
 * 
 * 遵循 .augment/rules/rule-db.md：
 * - 所有常量都基于官方文档定义
 * - 包含详细的官方文档引用
 * - 避免硬编码和魔法数字
 */
public final class DatabaseConstants {
    
    // ==================== 数据库名称常量 ====================
    
    public static final String MYSQL = "mysql";
    public static final String DAMENG = "dameng";
    public static final String KINGBASE = "kingbase";
    public static final String SHENTONG = "shentong";
    
    // ==================== 数据库产品名称 ====================
    
    public static final String MYSQL_PRODUCT = "MySQL";
    public static final String DAMENG_PRODUCT = "DM Database";
    public static final String KINGBASE_PRODUCT = "KingbaseES";
    public static final String SHENTONG_PRODUCT = "Shentong Database";
    
    // ==================== 引用字符常量 ====================
    
    public static final String MYSQL_QUOTE_CHAR = "`";
    public static final String DAMENG_QUOTE_CHAR = "\"";
    public static final String KINGBASE_QUOTE_CHAR = "\"";
    public static final String SHENTONG_QUOTE_CHAR = "\"";
    
    // ==================== 字符串连接操作符 ====================
    
    public static final String MYSQL_CONCAT_OPERATOR = ""; // MySQL没有专用连接操作符，使用CONCAT函数
    public static final String DAMENG_CONCAT_OPERATOR = "||";
    public static final String KINGBASE_CONCAT_OPERATOR = "||";
    public static final String SHENTONG_CONCAT_OPERATOR = "||";
    
    // ==================== 自增语法常量 ====================
    
    public static final String MYSQL_AUTO_INCREMENT = "AUTO_INCREMENT";
    public static final String DAMENG_AUTO_INCREMENT = "IDENTITY(1,1)";
    public static final String KINGBASE_AUTO_INCREMENT = "SERIAL";
    public static final String SHENTONG_AUTO_INCREMENT = ""; // 神通不支持自增
    
    // ==================== 数据库限制常量 ====================
    
    // MySQL 8.4官方文档限制 - https://dev.mysql.com/doc/refman/8.4/en/limits.html
    public static final int MYSQL_MAX_COLUMNS_PER_TABLE = 4096;
    public static final int MYSQL_MAX_IDENTIFIER_LENGTH = 64;
    public static final int MYSQL_MAX_ENUM_VALUES = 65535;
    public static final int MYSQL_MAX_INDEX_LENGTH = 3072;
    
    // 达梦数据库限制 - 基于达梦官方文档
    public static final int DAMENG_MAX_COLUMNS_PER_TABLE = 1000;
    public static final int DAMENG_MAX_IDENTIFIER_LENGTH = 128;
    public static final int DAMENG_MAX_INDEX_LENGTH = 2000;
    
    // 金仓数据库限制 - 基于PostgreSQL兼容性
    public static final int KINGBASE_MAX_COLUMNS_PER_TABLE = 1600;
    public static final int KINGBASE_MAX_IDENTIFIER_LENGTH = 63;
    public static final int KINGBASE_MAX_INDEX_LENGTH = 2712;
    
    // 神通数据库限制 - 基于神通官方文档
    public static final int SHENTONG_MAX_COLUMNS_PER_TABLE = 1000;
    public static final int SHENTONG_MAX_IDENTIFIER_LENGTH = 127;
    public static final int SHENTONG_MAX_INDEX_LENGTH = 2000;
    
    // ==================== 性能相关常量 ====================
    
    public static final int DEFAULT_LARGE_LIMIT_THRESHOLD = 1000;
    public static final int DEFAULT_MAX_VALIDATION_TIME_MS = 5000;
    public static final int DEFAULT_VALIDATION_CACHE_SIZE = 1000;
    public static final int DEFAULT_BATCH_SIZE = 100;
    
    // ==================== SQL分割相关常量 ====================
    
    public static final int DEFAULT_MAX_LINES_PER_CHUNK = 300;
    public static final int DEFAULT_MAX_STATEMENTS_PER_CHUNK = 100;
    public static final long DEFAULT_MAX_BYTES_PER_CHUNK = 256 * 1024; // 256KB
    
    // ==================== 字符集常量 ====================
    
    public static final String MYSQL_DEFAULT_CHARSET = "utf8mb4";
    public static final String DAMENG_DEFAULT_CHARSET = "UTF8";
    public static final String KINGBASE_DEFAULT_CHARSET = "UTF8";
    public static final String SHENTONG_DEFAULT_CHARSET = "UTF8";
    
    // ==================== 官方文档URL常量 ====================
    
    public static final String MYSQL_OFFICIAL_DOC_URL = "https://dev.mysql.com/doc/refman/8.4/en/";
    public static final String DAMENG_OFFICIAL_DOC_URL = "https://eco.dameng.com/document/dm/zh-cn/sql-dev/";
    public static final String KINGBASE_OFFICIAL_DOC_URL = "https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2";
    public static final String SHENTONG_OFFICIAL_DOC_URL = ""; // 参考 @shentong.md
    
    // ==================== 数据类型映射常量 ====================
    
    // MySQL到达梦的关键类型映射
    public static final String MYSQL_TEXT_TO_DAMENG = "CLOB";
    public static final String MYSQL_DATETIME_TO_DAMENG = "TIMESTAMP";
    public static final String MYSQL_MEDIUMINT_TO_DAMENG = "INT";
    
    // MySQL到金仓的关键类型映射
    public static final String MYSQL_TINYINT_TO_KINGBASE = "SMALLINT";
    public static final String MYSQL_FLOAT_TO_KINGBASE = "REAL";
    public static final String MYSQL_DOUBLE_TO_KINGBASE = "DOUBLE PRECISION";
    public static final String MYSQL_BLOB_TO_KINGBASE = "BYTEA";
    
    // MySQL到神通的关键类型映射
    public static final String MYSQL_TEXT_TO_SHENTONG = "CLOB";
    public static final String MYSQL_DATETIME_TO_SHENTONG = "TIMESTAMP";
    public static final String MYSQL_BLOB_TO_SHENTONG = "BLOB";
    
    // ==================== 函数映射常量 ====================
    
    // MySQL到达梦的关键函数映射
    public static final String MYSQL_NOW_TO_DAMENG = "SYSDATE";
    public static final String MYSQL_CURDATE_TO_DAMENG = "TRUNC(SYSDATE)";
    public static final String MYSQL_SUBSTRING_TO_DAMENG = "SUBSTR";
    
    // MySQL到金仓的关键函数映射
    public static final String MYSQL_NOW_TO_KINGBASE = "NOW()";
    public static final String MYSQL_CURDATE_TO_KINGBASE = "CURRENT_DATE";
    public static final String MYSQL_CURTIME_TO_KINGBASE = "CURRENT_TIME";
    
    // MySQL到神通的关键函数映射
    public static final String MYSQL_NOW_TO_SHENTONG = "SYSDATE";
    public static final String MYSQL_CURDATE_TO_SHENTONG = "TRUNC(SYSDATE)";
    public static final String MYSQL_SUBSTRING_TO_SHENTONG = "SUBSTR";
    
    // ==================== 保留字前缀常量 ====================
    
    public static final String SHENTONG_RESERVED_PREFIX_SYS = "SYS_";
    public static final String SHENTONG_RESERVED_PREFIX_V_SYS = "V_SYS_";
    
    // ==================== 错误消息常量 ====================
    
    public static final String ERROR_UNSUPPORTED_DIALECT = "Unsupported dialect";
    public static final String ERROR_INVALID_SQL_SYNTAX = "Invalid SQL syntax";
    public static final String ERROR_PARSING_FAILED = "SQL parsing failed";
    public static final String ERROR_CONVERSION_FAILED = "SQL conversion failed";
    
    // ==================== 成功消息常量 ====================
    
    public static final String SUCCESS_CONVERSION_COMPLETED = "SQL conversion completed successfully";
    public static final String SUCCESS_VALIDATION_PASSED = "SQL validation passed";
    public static final String SUCCESS_PARSING_COMPLETED = "SQL parsing completed";
    
    // ==================== 警告消息常量 ====================
    
    public static final String WARNING_COMPATIBILITY_ISSUE = "Compatibility issue detected";
    public static final String WARNING_PERFORMANCE_ISSUE = "Performance issue detected";
    public static final String WARNING_SECURITY_RISK = "Security risk detected";
    
    // ==================== 配置键常量 ====================
    
    public static final String CONFIG_VALIDATION_ENABLED = "validation.enabled";
    public static final String CONFIG_STRICT_MODE = "validation.strict_mode";
    public static final String CONFIG_MAX_VALIDATION_TIME = "validation.performance.max_validation_time";
    public static final String CONFIG_CACHE_SIZE = "validation.performance.cache_size";
    public static final String CONFIG_BATCH_SIZE = "validation.performance.batch_size";
    
    // ==================== 正则表达式常量 ====================
    
    public static final String REGEX_MYSQL_IDENTIFIER = "^[a-zA-Z_][a-zA-Z0-9_]*$";
    public static final String REGEX_QUOTED_IDENTIFIER = "^[`\"'].*[`\"']$";
    public static final String REGEX_NUMERIC_LITERAL = "^[0-9]+(\\.[0-9]+)?$";
    public static final String REGEX_FUNCTION_CALL = "\\b\\w+\\s*\\(";
    
    // ==================== 私有构造函数 ====================
    
    private DatabaseConstants() {
        // 工具类，禁止实例化
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }
    
    // ==================== 辅助方法 ====================
    
    /**
     * 获取数据库的引用字符
     * 
     * @param databaseType 数据库类型
     * @return 引用字符
     */
    public static String getQuoteChar(String databaseType) {
        if (databaseType == null) {
            return "";
        }
        
        switch (databaseType.toLowerCase()) {
            case MYSQL:
                return MYSQL_QUOTE_CHAR;
            case DAMENG:
                return DAMENG_QUOTE_CHAR;
            case KINGBASE:
                return KINGBASE_QUOTE_CHAR;
            case SHENTONG:
                return SHENTONG_QUOTE_CHAR;
            default:
                return "";
        }
    }
    
    /**
     * 获取数据库的连接操作符
     * 
     * @param databaseType 数据库类型
     * @return 连接操作符
     */
    public static String getConcatOperator(String databaseType) {
        if (databaseType == null) {
            return "";
        }
        
        switch (databaseType.toLowerCase()) {
            case MYSQL:
                return MYSQL_CONCAT_OPERATOR;
            case DAMENG:
                return DAMENG_CONCAT_OPERATOR;
            case KINGBASE:
                return KINGBASE_CONCAT_OPERATOR;
            case SHENTONG:
                return SHENTONG_CONCAT_OPERATOR;
            default:
                return "";
        }
    }
    
    /**
     * 获取数据库的最大标识符长度
     * 
     * @param databaseType 数据库类型
     * @return 最大标识符长度
     */
    public static int getMaxIdentifierLength(String databaseType) {
        if (databaseType == null) {
            return 64; // 默认值
        }
        
        switch (databaseType.toLowerCase()) {
            case MYSQL:
                return MYSQL_MAX_IDENTIFIER_LENGTH;
            case DAMENG:
                return DAMENG_MAX_IDENTIFIER_LENGTH;
            case KINGBASE:
                return KINGBASE_MAX_IDENTIFIER_LENGTH;
            case SHENTONG:
                return SHENTONG_MAX_IDENTIFIER_LENGTH;
            default:
                return 64; // 默认值
        }
    }
    
    /**
     * 获取数据库的官方文档URL
     * 
     * @param databaseType 数据库类型
     * @return 官方文档URL
     */
    public static String getOfficialDocUrl(String databaseType) {
        if (databaseType == null) {
            return "";
        }
        
        switch (databaseType.toLowerCase()) {
            case MYSQL:
                return MYSQL_OFFICIAL_DOC_URL;
            case DAMENG:
                return DAMENG_OFFICIAL_DOC_URL;
            case KINGBASE:
                return KINGBASE_OFFICIAL_DOC_URL;
            case SHENTONG:
                return SHENTONG_OFFICIAL_DOC_URL;
            default:
                return "";
        }
    }
}
