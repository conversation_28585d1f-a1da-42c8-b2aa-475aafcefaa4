#!/bin/bash

# 项目清理脚本
# 清理构建产物、临时文件和日志文件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "项目清理脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助"
    echo "  -a, --all      完全清理"
    echo "  -b, --build    仅清理构建产物"
    echo "  --dry-run      预览清理内容"
    echo ""
    echo "示例:"
    echo "  $0 --build    # 清理构建产物"
    echo "  $0 --all      # 完全清理"
    echo "  $0 --dry-run  # 预览清理内容"
}

# 清理构建产物
clean_build() {
    log_info "清理构建产物..."
    
    local files_to_clean=(
        "target/"
        "*.jar"
        "*.tar.gz"
        "*.zip"
        "dependency-reduced-pom.xml"
    )
    
    for pattern in "${files_to_clean[@]}"; do
        if [ "$DRY_RUN" = true ]; then
            find . -name "$pattern" -type f 2>/dev/null | head -10 | while read file; do
                echo "  会删除: $file"
            done
            find . -name "$pattern" -type d 2>/dev/null | head -10 | while read dir; do
                echo "  会删除目录: $dir"
            done
        else
            find . -name "$pattern" -type f -delete 2>/dev/null || true
            find . -name "$pattern" -type d -exec rm -rf {} + 2>/dev/null || true
        fi
    done
    
    if [ "$DRY_RUN" != true ]; then
        log_success "构建产物清理完成"
    fi
}

# 清理日志文件
clean_logs() {
    log_info "清理日志文件..."
    
    local log_patterns=(
        "*.log"
        "logs/*.log"
        "target/logs/*"
    )
    
    for pattern in "${log_patterns[@]}"; do
        if [ "$DRY_RUN" = true ]; then
            find . -path "./$pattern" 2>/dev/null | while read file; do
                echo "  会删除: $file"
            done
        else
            find . -path "./$pattern" -delete 2>/dev/null || true
        fi
    done
    
    if [ "$DRY_RUN" != true ]; then
        log_success "日志文件清理完成"
    fi
}

# 清理临时文件
clean_temp() {
    log_info "清理临时文件..."
    
    local temp_patterns=(
        "*.tmp"
        "*.temp"
        "*~"
        ".DS_Store"
        "Thumbs.db"
        "*.swp"
        "*.swo"
        ".vscode/settings.json"
        ".idea/workspace.xml"
    )
    
    for pattern in "${temp_patterns[@]}"; do
        if [ "$DRY_RUN" = true ]; then
            find . -name "$pattern" 2>/dev/null | while read file; do
                echo "  会删除: $file"
            done
        else
            find . -name "$pattern" -delete 2>/dev/null || true
        fi
    done
    
    if [ "$DRY_RUN" != true ]; then
        log_success "临时文件清理完成"
    fi
}

# 清理Maven缓存
clean_maven_cache() {
    log_info "清理Maven本地缓存..."
    
    if [ "$DRY_RUN" = true ]; then
        echo "  会清理: ~/.m2/repository/com/xylink/sqltranspiler"
    else
        rm -rf ~/.m2/repository/com/xylink/sqltranspiler 2>/dev/null || true
        log_success "Maven缓存清理完成"
    fi
}

# 显示磁盘空间使用情况
show_disk_usage() {
    log_info "当前目录磁盘使用情况:"
    du -sh . 2>/dev/null || echo "无法获取磁盘使用情况"
    
    if [ -d "target" ]; then
        echo "target目录大小: $(du -sh target 2>/dev/null | cut -f1)"
    fi
    
    if [ -d "logs" ]; then
        echo "logs目录大小: $(du -sh logs 2>/dev/null | cut -f1)"
    fi
}

# 主函数
main() {
    local clean_build_only=false
    local clean_logs_only=false
    local clean_temp_only=false
    local clean_all=false
    DRY_RUN=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -a|--all)
                clean_all=true
                shift
                ;;
            -b|--build)
                clean_build_only=true
                shift
                ;;
            -l|--logs)
                clean_logs_only=true
                shift
                ;;
            -t|--temp)
                clean_temp_only=true
                shift
                ;;
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 显示当前状态
    if [ "$DRY_RUN" = true ]; then
        log_warning "预览模式 - 不会实际删除文件"
    fi
    
    show_disk_usage
    echo ""
    
    # 执行清理操作
    if [ "$clean_all" = true ]; then
        clean_build
        clean_logs
        clean_temp
        clean_maven_cache
    elif [ "$clean_build_only" = true ]; then
        clean_build
    elif [ "$clean_logs_only" = true ]; then
        clean_logs
    elif [ "$clean_temp_only" = true ]; then
        clean_temp
    else
        # 默认清理构建产物和临时文件
        clean_build
        clean_temp
    fi
    
    echo ""
    if [ "$DRY_RUN" != true ]; then
        show_disk_usage
        log_success "项目清理完成！"
    else
        log_info "预览完成。使用不带 --dry-run 参数重新运行以实际执行清理。"
    fi
}

# 执行主函数
main "$@"
