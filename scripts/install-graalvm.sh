#!/bin/bash

# GraalVM自动安装脚本
# 支持macOS和Linux平台的GraalVM安装和配置
#
# 使用方法：
#   ./scripts/install-graalvm.sh
#   ./scripts/install-graalvm.sh --version 21.0.8

set -e

# 默认版本配置
GRAALVM_VERSION="21.0.8"
GRAALVM_JAVA_VERSION="21"

# 检测操作系统
detect_os() {
    case "$(uname -s)" in
        Darwin*)
            OS="macos"
            ARCH="aarch64"  # Apple Silicon
            if [[ "$(uname -m)" == "x86_64" ]]; then
                ARCH="x64"  # Intel Mac
            fi
            ;;
        Linux*)
            OS="linux"
            ARCH="x64"
            if [[ "$(uname -m)" == "aarch64" ]]; then
                ARCH="aarch64"
            fi
            ;;
        *)
            echo "不支持的操作系统: $(uname -s)"
            exit 1
            ;;
    esac
}

# 下载并安装GraalVM
install_graalvm() {
    echo "检测到操作系统: $OS-$ARCH"

    # 设置下载URL
    if [[ "$OS" == "macos" ]]; then
        DOWNLOAD_URL="https://download.oracle.com/graalvm/${GRAALVM_JAVA_VERSION}/latest/graalvm-jdk-${GRAALVM_VERSION}_${OS}-${ARCH}_bin.tar.gz"
        INSTALL_DIR="/Library/Java/JavaVirtualMachines"
    else
        DOWNLOAD_URL="https://download.oracle.com/graalvm/${GRAALVM_JAVA_VERSION}/latest/graalvm-jdk-${GRAALVM_VERSION}_${OS}-${ARCH}_bin.tar.gz"
        INSTALL_DIR="$HOME/.graalvm"
    fi

    echo "下载GraalVM从: $DOWNLOAD_URL"

    # 创建临时目录
    TEMP_DIR=$(mktemp -d)
    cd "$TEMP_DIR"

    # 下载GraalVM
    curl -L -o graalvm.tar.gz "$DOWNLOAD_URL"

    # 解压
    tar -xzf graalvm.tar.gz

    # 安装
    if [[ "$OS" == "macos" ]]; then
        sudo mv graalvm-jdk-${GRAALVM_VERSION}+* "$INSTALL_DIR/"
        GRAALVM_HOME="$INSTALL_DIR/graalvm-jdk-${GRAALVM_VERSION}+*/Contents/Home"
    else
        mkdir -p "$INSTALL_DIR"
        mv graalvm-jdk-${GRAALVM_VERSION}+* "$INSTALL_DIR/"
        GRAALVM_HOME="$INSTALL_DIR/graalvm-jdk-${GRAALVM_VERSION}+*"
    fi

    # 清理临时文件
    cd /
    rm -rf "$TEMP_DIR"

    echo "GraalVM安装完成: $GRAALVM_HOME"
}

# 配置环境变量
configure_environment() {
    echo ""
    echo "请将以下环境变量添加到您的shell配置文件中 (~/.bashrc, ~/.zshrc等):"
    echo ""

    if [[ "$OS" == "macos" ]]; then
        GRAALVM_HOME_PATTERN="/Library/Java/JavaVirtualMachines/graalvm-jdk-${GRAALVM_VERSION}*/Contents/Home"
    else
        GRAALVM_HOME_PATTERN="$HOME/.graalvm/graalvm-jdk-${GRAALVM_VERSION}*"
    fi

    echo "export GRAALVM_HOME=$GRAALVM_HOME_PATTERN"
    echo "export JAVA_HOME=\$GRAALVM_HOME"
    echo "export PATH=\$GRAALVM_HOME/bin:\$PATH"
    echo ""
    echo "然后运行: source ~/.bashrc (或相应的配置文件)"
}

# 验证安装
verify_installation() {
    echo ""
    echo "验证安装..."

    if command -v java &> /dev/null; then
        echo "Java版本:"
        java -version
        echo ""

        if command -v native-image &> /dev/null; then
            echo "Native Image已安装"
            native-image --version
        else
            echo "警告: native-image命令未找到，请检查GraalVM安装"
        fi
    else
        echo "错误: java命令未找到，请检查PATH配置"
    fi
}

# 主函数
main() {
    echo "开始安装GraalVM $GRAALVM_VERSION..."

    detect_os
    install_graalvm
    configure_environment
    verify_installation

    echo ""
    echo "安装完成！请重新启动终端或source配置文件后继续。"
}

main "$@"