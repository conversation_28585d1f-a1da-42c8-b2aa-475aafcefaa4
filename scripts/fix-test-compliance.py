#!/usr/bin/env python3
"""
批量修正测试文件官方文档合规性脚本

严格遵循 .augment/rules/rule-db.md 的要求：
1. 每个测试文件必须有官方文档链接
2. 每个测试文件必须有测试原则声明
3. 每个测试文件必须有基于官方文档的验证逻辑

官方文档权威来源：
- MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
- 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
- 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
- 神通官方文档：@shentong.md
"""

import os
import re
import sys
from pathlib import Path

# 官方文档模板
MYSQL_DOCS = "https://dev.mysql.com/doc/refman/8.4/en/"
DAMENG_DOCS = "https://eco.dameng.com/document/dm/zh-cn/sql-dev/"
KINGBASE_DOCS = "https://help.kingbase.com.cn/v8/development/sql-plsql/sql/"
SHENTONG_DOCS = "@shentong.md"

def get_test_principles():
    """获取测试原则声明"""
    return """测试原则：严格基于各数据库官方文档进行验证，不允许推测或假设"""

def get_official_docs_section(file_path):
    """根据文件路径获取相应的官方文档引用"""
    docs = [
        f" * - MySQL 8.4官方文档：{MYSQL_DOCS}",
    ]
    
    if "dameng" in file_path.lower():
        docs.append(f" * - 达梦官方文档：{DAMENG_DOCS}")
    elif "kingbase" in file_path.lower():
        docs.append(f" * - 金仓官方文档：{KINGBASE_DOCS}")
    elif "shentong" in file_path.lower():
        docs.append(f" * - 神通官方文档：{SHENTONG_DOCS}")
    else:
        # 通用测试文件，添加所有数据库文档
        docs.extend([
            f" * - 达梦官方文档：{DAMENG_DOCS}",
            f" * - 金仓官方文档：{KINGBASE_DOCS}",
            f" * - 神通官方文档：{SHENTONG_DOCS}"
        ])
    
    return "\n".join(docs)

def get_verification_strategy(file_path):
    """根据文件路径获取相应的验证策略"""
    strategies = [
        " * 1. 根据各数据库官方文档验证功能的正确性",
        " * 2. 确保转换结果符合官方文档规范",
        " * 3. 验证测试用例基于官方文档",
        " * 4. 测试边界情况和特殊场景"
    ]
    
    if "function" in file_path.lower():
        strategies[1] = " * 2. 确保函数转换符合官方文档规范"
        strategies[3] = " * 4. 测试复杂函数组合的转换正确性"
    elif "datatype" in file_path.lower() or "type" in file_path.lower():
        strategies[1] = " * 2. 确保数据类型映射符合官方文档规范"
        strategies[3] = " * 4. 测试数据类型边界情况"
    elif "dialect" in file_path.lower():
        strategies[1] = " * 2. 确保方言功能符合官方文档规范"
        strategies[3] = " * 4. 测试方言特有功能"
    
    return "\n".join(strategies)

def create_enhanced_javadoc(original_javadoc, file_path):
    """创建增强的JavaDoc注释"""
    # 提取原始描述
    lines = original_javadoc.split('\n')
    description_lines = []
    
    for line in lines:
        line = line.strip()
        if line.startswith('*') and not line.startswith('*/'):
            content = line[1:].strip()
            if content and not content.startswith('@'):
                description_lines.append(f" * {content}")
    
    # 构建新的JavaDoc
    new_javadoc = "/**\n"
    
    # 添加原始描述
    if description_lines:
        new_javadoc += "\n".join(description_lines) + "\n *\n"
    
    # 添加测试原则
    new_javadoc += f" * {get_test_principles()}\n *\n"
    
    # 添加官方文档依据
    new_javadoc += " * 官方文档依据：\n"
    new_javadoc += get_official_docs_section(file_path) + "\n *\n"
    
    # 添加验证策略
    new_javadoc += " * 验证策略：\n"
    new_javadoc += get_verification_strategy(file_path) + "\n *\n"
    
    # 添加作者
    new_javadoc += " * <AUTHOR>
    new_javadoc += " */"
    
    return new_javadoc

def fix_test_file(file_path):
    """修正单个测试文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找类级别的JavaDoc注释
        class_pattern = r'/\*\*\s*\n(.*?)\*/\s*\n(.*?class\s+\w+)'
        match = re.search(class_pattern, content, re.DOTALL)
        
        if match:
            original_javadoc = match.group(0).split('*/')[0] + '*/'
            class_declaration = match.group(2)
            
            # 检查是否已经有官方文档引用
            if "官方文档依据" in original_javadoc and "测试原则" in original_javadoc:
                print(f"✅ {file_path} 已经符合规范")
                return True
            
            # 创建新的JavaDoc
            new_javadoc = create_enhanced_javadoc(original_javadoc, str(file_path))
            
            # 替换原始JavaDoc
            new_content = content.replace(original_javadoc, new_javadoc)
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"✅ 修正完成: {file_path}")
            return True
        else:
            print(f"⚠️ 未找到类级别JavaDoc: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ 修正失败 {file_path}: {e}")
        return False

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("用法: python fix-test-compliance.py <测试目录路径>")
        sys.exit(1)
    
    test_dir = Path(sys.argv[1])
    if not test_dir.exists():
        print(f"错误: 目录不存在 {test_dir}")
        sys.exit(1)
    
    # 查找所有Java测试文件
    test_files = list(test_dir.rglob("*Test.java"))
    
    print(f"找到 {len(test_files)} 个测试文件")
    
    success_count = 0
    for test_file in test_files:
        if fix_test_file(test_file):
            success_count += 1
    
    print(f"\n修正完成: {success_count}/{len(test_files)} 个文件")

if __name__ == "__main__":
    main()
