#!/bin/bash

# SQL Transpiler 版本发布脚本
# 自动创建版本标签并推送到远端，触发GitHub Actions构建和发布

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "SQL Transpiler 版本发布脚本"
    echo ""
    echo "用法: $0 [版本号] [选项]"
    echo ""
    echo "版本号格式:"
    echo "  v1.0.0          正式版本"
    echo "  v1.0.0-beta.1   测试版本"
    echo "  v1.0.0-rc.1     候选版本"
    echo ""
    echo "选项:"
    echo "  -h, --help      显示此帮助信息"
    echo "  -f, --force     强制覆盖已存在的标签"
    echo "  --dry-run       预览操作，不实际执行"
    echo "  --no-push       只创建本地标签，不推送到远端"
    echo ""
    echo "示例:"
    echo "  $0 v1.0.0                    # 发布正式版本"
    echo "  $0 v1.0.0-beta.1             # 发布测试版本"
    echo "  $0 v1.0.0 --dry-run          # 预览发布操作"
    echo "  $0 v1.0.0 --force            # 强制覆盖已存在的标签"
}

# 检查Git状态
check_git_status() {
    log_info "检查Git仓库状态..."
    
    # 检查是否在Git仓库中
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        log_error "当前目录不是Git仓库"
        exit 1
    fi
    
    # 检查是否有未提交的更改
    if ! git diff-index --quiet HEAD --; then
        log_warning "检测到未提交的更改:"
        git status --porcelain
        echo ""
        read -p "是否继续发布? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "发布已取消"
            exit 0
        fi
    fi
    
    # 检查远端仓库
    if ! git remote get-url origin > /dev/null 2>&1; then
        log_error "未配置远端仓库 origin"
        exit 1
    fi
    
    log_success "Git仓库状态检查通过"
}

# 验证版本号格式
validate_version() {
    local version="$1"
    
    # 检查版本号格式 (v1.0.0, v1.0.0-beta.1, etc.)
    if [[ ! $version =~ ^v[0-9]+\.[0-9]+\.[0-9]+(-[a-zA-Z0-9]+(\.[0-9]+)?)?$ ]]; then
        log_error "版本号格式无效: $version"
        log_info "正确格式: v1.0.0, v1.0.0-beta.1, v1.0.0-rc.1"
        exit 1
    fi
    
    log_success "版本号格式验证通过: $version"
}

# 检查标签是否已存在
check_tag_exists() {
    local version="$1"
    local force="$2"
    
    if git tag -l | grep -q "^$version$"; then
        if [ "$force" = true ]; then
            log_warning "标签 $version 已存在，将被强制覆盖"
            git tag -d "$version" || true
            git push origin ":refs/tags/$version" 2>/dev/null || true
        else
            log_error "标签 $version 已存在"
            log_info "使用 --force 选项强制覆盖，或选择新的版本号"
            exit 1
        fi
    fi
}

# 更新版本信息
update_version_info() {
    local version="$1"
    
    log_info "更新版本信息..."
    
    # 更新pom.xml中的版本号（如果需要）
    if [ -f "pom.xml" ]; then
        # 这里可以添加更新pom.xml版本的逻辑
        log_info "检测到pom.xml，版本信息保持不变"
    fi
    
    # 创建版本信息文件
    cat > VERSION.txt << EOF
SQL Transpiler Version: ${version#v}
Build Date: $(date -u +"%Y-%m-%d %H:%M:%S UTC")
Git Commit: $(git rev-parse HEAD)
Git Branch: $(git rev-parse --abbrev-ref HEAD)
EOF
    
    log_success "版本信息已更新"
}

# 创建发布标签
create_release_tag() {
    local version="$1"
    local dry_run="$2"
    
    log_info "创建发布标签 $version..."
    
    # 生成标签消息
    local tag_message="Release $version

SQL Transpiler Native Binaries

This release includes:
- Multi-architecture native binaries
- Performance improvements
- Bug fixes and enhancements

See release notes for detailed changes."
    
    if [ "$dry_run" = true ]; then
        log_warning "预览模式 - 将创建标签:"
        echo "标签: $version"
        echo "消息: $tag_message"
        return 0
    fi
    
    # 创建带注释的标签
    git tag -a "$version" -m "$tag_message"
    
    log_success "标签 $version 创建成功"
}

# 推送到远端
push_to_remote() {
    local version="$1"
    local no_push="$2"
    local dry_run="$3"
    
    if [ "$no_push" = true ]; then
        log_info "跳过推送到远端（--no-push 选项）"
        return 0
    fi
    
    if [ "$dry_run" = true ]; then
        log_warning "预览模式 - 将推送到远端:"
        echo "git push origin main"
        echo "git push origin $version"
        return 0
    fi
    
    log_info "推送到远端仓库..."
    
    # 推送当前分支
    git push origin HEAD
    
    # 推送标签
    git push origin "$version"
    
    log_success "已推送到远端仓库"
}

# 显示GitHub Actions信息
show_github_actions_info() {
    local version="$1"
    
    log_info "GitHub Actions 构建信息:"
    echo ""
    echo "🚀 构建已自动触发！"
    echo ""
    echo "📊 构建状态查看:"
    echo "   网页: https://github.com/$(git config --get remote.origin.url | sed 's/.*github.com[:/]\([^.]*\).*/\1/')/actions"
    echo "   命令: gh run list (需要安装GitHub CLI)"
    echo ""
    echo "📦 发布页面:"
    echo "   https://github.com/$(git config --get remote.origin.url | sed 's/.*github.com[:/]\([^.]*\).*/\1/')/releases"
    echo ""
    echo "⏱️  预计完成时间: 10-15分钟"
    echo ""
    echo "📋 构建内容:"
    echo "   - Linux x64/ARM64 native binaries"
    echo "   - macOS x64/ARM64 native binaries"
    echo "   - Windows x64 native binary"
    echo "   - 使用说明和校验文件"
    echo ""
    echo "🎉 构建完成后，二进制文件将自动发布到GitHub Releases！"
}

# 主函数
main() {
    local version=""
    local force=false
    local dry_run=false
    local no_push=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -f|--force)
                force=true
                shift
                ;;
            --dry-run)
                dry_run=true
                shift
                ;;
            --no-push)
                no_push=true
                shift
                ;;
            v*.*.*)
                version="$1"
                shift
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查版本号参数
    if [ -z "$version" ]; then
        log_error "请指定版本号"
        show_help
        exit 1
    fi
    
    # 显示发布信息
    log_info "准备发布 SQL Transpiler $version"
    if [ "$dry_run" = true ]; then
        log_warning "预览模式 - 不会实际执行操作"
    fi
    echo ""
    
    # 执行发布流程
    check_git_status
    validate_version "$version"
    check_tag_exists "$version" "$force"
    update_version_info "$version"
    create_release_tag "$version" "$dry_run"
    push_to_remote "$version" "$no_push" "$dry_run"
    
    if [ "$dry_run" = false ] && [ "$no_push" = false ]; then
        echo ""
        show_github_actions_info "$version"
    fi
    
    echo ""
    log_success "发布流程完成！"
    
    if [ "$dry_run" = true ]; then
        log_info "这是预览模式，没有实际执行操作"
        log_info "移除 --dry-run 参数重新运行以实际发布"
    fi
}

# 执行主函数
main "$@"
