# 项目脚本说明

自动化脚本集合，简化开发和构建流程。

## 📁 脚本列表

| 脚本 | 功能 | 平台 |
|------|------|------|
| `install-graalvm.sh` | 安装GraalVM | Linux/macOS |
| `clean-project.sh` | 清理项目文件 | Linux/macOS |
| `release.sh` | 版本发布 | Linux/macOS |

## 🚀 使用方法

```bash
# 环境初始化
./scripts/install-graalvm.sh

# 项目清理
./scripts/clean-project.sh

# 版本发布
./scripts/release.sh
```

## 📋 脚本选项

### clean-project.sh
```bash
./scripts/clean-project.sh [选项]

选项:
  -h, --help     显示帮助
  -a, --all      完全清理
  -b, --build    仅清理构建产物
  --dry-run      预览清理内容
```

### install-graalvm.sh
```bash
./scripts/install-graalvm.sh [--version VERSION]
```

## 🔧 开发规范

- 使用`#!/bin/bash`
- 错误处理`set -e`
- 统一日志格式
- 支持`--help`参数

---

*最后更新：2025-07-19*
