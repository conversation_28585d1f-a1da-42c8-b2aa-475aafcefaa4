# SQL Transpiler Native Build Guide

本文档介绍如何构建支持多架构的SQL Transpiler native可执行文件。

## 🚀 快速开始

### 一分钟快速构建

#### 前置条件
```bash
# 1. 安装GraalVM (Java 21)
# 下载：https://www.graalvm.org/downloads/

# 2. 验证安装
java -version
native-image --version
```

#### 快速构建命令
```bash
# 方法1：使用构建脚本（推荐）
chmod +x build-native.sh
./build-native.sh --current    # 构建当前平台
./build-native.sh --all        # 构建所有平台

# 方法2：使用Makefile
make build-native              # 构建当前平台
make build-all-native          # 构建所有平台

# 方法3：使用Maven
mvn clean package -Pnative -DskipTests
```

## 支持的架构

- **Linux x86_64** - 64位Intel/AMD处理器的Linux系统
- **Linux ARM64** - 64位ARM处理器的Linux系统（如树莓派4、AWS Graviton等）
- **macOS x86_64** - 64位Intel处理器的macOS系统
- **macOS ARM64** - Apple Silicon处理器的macOS系统（M1/M2/M3等）
- **Windows x86_64** - 64位Intel/AMD处理器的Windows系统

## 前置要求

### 1. 安装GraalVM

下载并安装GraalVM：
- 访问 [GraalVM官网](https://www.graalvm.org/downloads/)
- 下载适合您操作系统的GraalVM版本（推荐Java 21）
- 将GraalVM的`bin`目录添加到系统PATH中

验证安装：
```bash
java -version
native-image --version
```

### 2. 安装Maven

确保已安装Apache Maven 3.6+：
```bash
mvn --version
```

### 3. 安装必要的构建工具

**Linux/macOS:**
```bash
# Ubuntu/Debian
sudo apt-get install build-essential zlib1g-dev

# CentOS/RHEL
sudo yum groupinstall "Development Tools"
sudo yum install zlib-devel

# macOS
xcode-select --install
```

**Windows:**
- 安装Visual Studio Build Tools或Visual Studio Community
- 确保包含C++构建工具

## 构建方法

### 方法1：使用构建脚本（推荐）

#### Linux/macOS:
```bash
# 给脚本执行权限
chmod +x build-native.sh

# 构建当前平台
./build-native.sh --current

# 构建所有平台
./build-native.sh --all

# 构建特定平台
./build-native.sh linux-x64 linux-arm64

# 清理并构建
./build-native.sh -c macos-arm64
```

#### Windows:
```cmd
REM 构建当前平台
build-native.bat --current

REM 构建所有平台
build-native.bat --all

REM 构建特定平台
build-native.bat windows-x64

REM 清理并构建
build-native.bat -c windows-x64
```

### 方法2：使用Maven Profile

#### 构建特定架构：

```bash
# Linux x86_64
mvn clean package -Pnative-linux-x64 -DskipTests

# Linux ARM64
mvn clean package -Pnative-linux-arm64 -DskipTests

# macOS x86_64
mvn clean package -Pnative-macos-x64 -DskipTests

# macOS ARM64 (Apple Silicon)
mvn clean package -Pnative-macos-arm64 -DskipTests

# Windows x86_64
mvn clean package -Pnative-windows-x64 -DskipTests
```

#### 构建当前平台（通用）：
```bash
mvn clean package -Pnative -DskipTests
```

## 输出文件

构建完成后，native可执行文件将位于：

- **使用构建脚本**: `target/native-builds/`
- **使用Maven**: `target/`

文件命名规则：
- `sql-transpiler-linux-x64` - Linux x86_64版本
- `sql-transpiler-linux-arm64` - Linux ARM64版本
- `sql-transpiler-macos-x64` - macOS x86_64版本
- `sql-transpiler-macos-arm64` - macOS ARM64版本
- `sql-transpiler-windows-x64.exe` - Windows x86_64版本

## 使用示例

```bash
# Linux/macOS
./sql-transpiler-linux-x64 -i input.sql -o output.sql -t dameng

# Windows
sql-transpiler-windows-x64.exe -i input.sql -o output.sql -t dameng
```

## 跨平台构建

### 在Linux上构建其他平台

要在Linux上构建其他平台的native image，需要安装相应的交叉编译工具链：

```bash
# 安装交叉编译工具
sudo apt-get install gcc-aarch64-linux-gnu  # ARM64
sudo apt-get install gcc-mingw-w64          # Windows
```

### 在macOS上构建其他平台

macOS可以构建Linux和Windows版本，但需要Docker：

```bash
# 使用Docker构建Linux版本
docker run --rm -v $(pwd):/workspace -w /workspace \
  ghcr.io/graalvm/graalvm-ce:java21 \
  ./build-native.sh linux-x64
```

### 在Windows上构建其他平台

Windows可以使用WSL2或Docker来构建Linux版本。

## 性能优化

### 构建时优化

在`pom.xml`中的native配置已包含以下优化选项：

- `--no-fallback` - 禁用JVM回退模式
- `-H:+AddAllCharsets` - 包含所有字符集
- `-H:+ReportExceptionStackTraces` - 报告异常堆栈跟踪

### 运行时优化

```bash
# 设置内存限制
export NATIVE_IMAGE_OPTS="-Xmx2g"

# 启用并行GC
export NATIVE_IMAGE_OPTS="-XX:+UseParallelGC"
```

## 故障排除

### 常见问题

1. **构建失败：找不到native-image**
   - 确保GraalVM已正确安装并添加到PATH
   - 运行`native-image --version`验证

2. **构建失败：缺少构建工具**
   - Linux: 安装`build-essential`
   - macOS: 安装Xcode命令行工具
   - Windows: 安装Visual Studio Build Tools

3. **运行时错误：找不到资源文件**
   - 检查`-H:IncludeResources`配置
   - 确保资源文件在classpath中

4. **内存不足**
   - 增加构建内存：`export MAVEN_OPTS="-Xmx4g"`
   - 使用`--gc=G1`参数

### 调试构建

启用详细输出：
```bash
mvn package -Pnative-linux-x64 -DskipTests -X
```

生成构建报告：
```bash
# 添加到buildArgs中
<buildArg>-H:+PrintAnalysisCallTree</buildArg>
<buildArg>-H:+PrintImageObjectTree</buildArg>
```

## CI/CD集成

### GitHub Actions示例

```yaml
name: Build Native Images

on: [push, pull_request]

jobs:
  build:
    strategy:
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
        
    runs-on: ${{ matrix.os }}
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup GraalVM
      uses: graalvm/setup-graalvm@v1
      with:
        version: 'latest'
        java-version: '21'
        
    - name: Build Native Image
      run: |
        chmod +x build-native.sh
        ./build-native.sh --current
        
    - name: Upload Artifacts
      uses: actions/upload-artifact@v3
      with:
        name: native-binaries-${{ matrix.os }}
        path: target/native-builds/
```

## 实现状态

✅ **已完成的功能：**

- [x] 支持5个主要平台架构的native构建
- [x] Maven Profile配置（`pom.xml`）
- [x] 跨平台构建脚本（`build-native.sh` / `build-native.bat`）
- [x] Makefile支持
- [x] GitHub Actions CI/CD自动化
- [x] 自动发布到GitHub Releases
- [x] 构建验证和测试
- [x] 完整的文档

✅ **支持的构建方式：**

1. **Maven命令**：`mvn package -Pnative-linux-x64`
2. **构建脚本**：`./build-native.sh --all`
3. **Makefile**：`make build-all-native`
4. **GitHub Actions**：自动触发构建

✅ **输出文件：**

所有native二进制文件将根据平台命名：
- `sql-transpiler-linux-x64`
- `sql-transpiler-linux-arm64`
- `sql-transpiler-macos-x64`
- `sql-transpiler-macos-arm64`
- `sql-transpiler-windows-x64.exe`

## 许可证

本项目遵循与主项目相同的许可证。
