# SQL Transpiler Makefile

.PHONY: help clean test build-jar build-native install-deps info

# 默认目标
help:
	@echo "SQL Transpiler Build System"
	@echo ""
	@echo "Core targets:"
	@echo "  help         - Show this help"
	@echo "  clean        - Clean build artifacts"
	@echo "  test         - Run tests"
	@echo "  build-jar    - Build JAR file"
	@echo "  build-native - Build native image"
	@echo "  install-deps - Install dependencies"
	@echo "  info         - Show build info"
	@echo ""
	@echo "Examples:"
	@echo "  make build-jar"
	@echo "  make build-native"
	@echo "  make test"

# 清理构建产物
clean:
	@echo "Cleaning build artifacts..."
	./scripts/clean-project.sh

# 运行测试
test:
	@echo "Running tests..."
	mvn test

# 构建JAR文件
build-jar:
	@echo "Building JAR file..."
	mvn clean package -DskipTests

# 安装构建依赖
install-deps:
	@echo "Installing build dependencies..."
	@if command -v apt-get >/dev/null 2>&1; then \
		echo "Installing dependencies for Ubuntu/Debian..."; \
		sudo apt-get update; \
		sudo apt-get install -y build-essential zlib1g-dev gcc-aarch64-linux-gnu; \
	elif command -v yum >/dev/null 2>&1; then \
		echo "Installing dependencies for CentOS/RHEL..."; \
		sudo yum groupinstall -y "Development Tools"; \
		sudo yum install -y zlib-devel; \
	elif command -v brew >/dev/null 2>&1; then \
		echo "Installing dependencies for macOS..."; \
		xcode-select --install 2>/dev/null || true; \
	else \
		echo "Unsupported package manager. Please install build tools manually."; \
	fi

# 检查GraalVM
check-graalvm:
	@if ! command -v native-image >/dev/null 2>&1; then \
		echo "Error: GraalVM native-image not found."; \
		echo "Please install GraalVM and add it to PATH."; \
		echo "Download from: https://www.graalvm.org/downloads/"; \
		exit 1; \
	fi
	@echo "GraalVM found: $$(native-image --version | head -n 1)"

# 构建native image
build-native: check-graalvm
	@echo "Building native image..."
	./build-native.sh

# 显示构建信息
info:
	@echo "Build Information:"
	@echo "=================="
	@echo "Java: $$(java -version 2>&1 | head -n 1)"
	@echo "Maven: $$(mvn --version 2>&1 | head -n 1)"
	@if command -v native-image >/dev/null 2>&1; then \
		echo "GraalVM: $$(native-image --version 2>&1 | head -n 1)"; \
	else \
		echo "GraalVM: Not installed"; \
	fi
	@echo "OS: $$(uname -s) $$(uname -m)"
