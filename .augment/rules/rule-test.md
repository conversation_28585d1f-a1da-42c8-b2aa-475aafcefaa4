---
type: "always_apply"
---

1. 测试用例架构规范：
src/test/java/com/xylink/sqltranspiler/
├── unit/                           # 单元测试层
│   ├── core/                       # 核心组件单元测试
│   │   ├── ast/                    # AST相关测试
│   │   ├── parser/                 # 解析器测试
│   │   ├── validation/             # 验证器测试
│   │   └── context/                # 上下文测试
│   ├── infrastructure/             # 基础设施单元测试
│   │   ├── parser/                 # 解析器基础设施
│   │   ├── util/                   # 工具类测试
│   │   └── logging/                # 日志系统测试
│   └── dialects/                   # 方言生成器单元测试
│       ├── dameng/                 # 达梦方言测试
│       ├── kingbase/               # 金仓方言测试
│       └── shentong/               # 神通方言测试
├── integration/                    # 集成测试层
│   ├── conversion/                 # 转换集成测试
│   │   ├── dameng/                 # 达梦转换集成测试
│   │   ├── kingbase/               # 金仓转换集成测试
│   │   └── shentong/               # 神通转换集成测试
│   ├── pipeline/                   # 转换管道集成测试
│   └── realworld/                  # 真实场景集成测试
├── e2e/                           # 端到端测试层
│   ├── cli/                       # CLI端到端测试
│   ├── web/                       # Web服务端到端测试
│   └── performance/               # 性能测试
├── compliance/                    # 官方文档合规性测试
│   ├── mysql/                     # MySQL官方文档合规测试
│   ├── dameng/                    # 达梦官方文档合规测试
│   ├── kingbase/                  # 金仓官方文档合规测试
│   └── shentong/                  # 神通官方文档合规测试
├── regression/                    # 回归测试
│   ├── bugfix/                    # Bug修复回归测试
│   └── feature/                   # 功能回归测试
└── shared/                        # 共享测试基础设施
    ├── base/                      # 基础测试类
    ├── fixtures/                  # 测试夹具
    ├── helpers/                   # 测试辅助类
    └── matchers/                  # 自定义断言匹配器

2. 测试资源文件组织
src/test/resources/
├── sql/                           # SQL测试数据
│   ├── input/                     # 输入SQL文件
│   │   ├── mysql/                 # MySQL输入SQL
│   │   │   ├── basic/             # 基础语法
│   │   │   ├── advanced/          # 高级特性
│   │   │   ├── edge-cases/        # 边界情况
│   │   │   └── real-world/        # 真实场景
│   │   └── common/                # 通用测试SQL
│   └── expected/                  # 预期输出SQL
│       ├── dameng/                # 达梦预期输出
│       ├── kingbase/              # 金仓预期输出
│       └── shentong/              # 神通预期输出
├── config/                        # 测试配置文件
│   ├── test-profiles/             # 测试配置文件
│   └── logging/                   # 日志配置
├── fixtures/                      # 测试夹具数据
│   ├── schemas/                   # 数据库模式
│   └── datasets/                  # 测试数据集
└── docs/                          # 测试文档
    ├── official/                  # 官方文档副本
    │   ├── mysql/                 # MySQL官方文档
    │   ├── dameng/                # 达梦官方文档
    │   ├── kingbase/              # 金仓官方文档
    │   └── shentong/              # 神通官方文档
    └── test-plans/                # 测试计划文档

所有方言的测试case都应该是全面配套的。比如说达梦如果创建了分区表的case，那么同时应该补充金仓和神通的对等case。
