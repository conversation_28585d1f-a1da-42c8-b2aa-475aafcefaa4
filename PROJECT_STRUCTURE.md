# SQL Transpiler 项目结构

本文档描述了SQL Transpiler项目的完整目录结构和文件组织。

## 📁 项目根目录

```
sql-transpiler/
├── 📄 README.md                    # 项目主页和快速开始
├── 📄 LICENSE                      # 开源许可证
├── 📄 CONTRIBUTING.md               # 贡献指南
├── 📄 NATIVE_BUILD.md               # Native构建完整指南
├── 📄 PROJECT_STRUCTURE.md          # 项目结构说明（本文档）
├── 📄 pom.xml                       # Maven项目配置
├── 📄 Makefile                      # 构建自动化脚本
├── 📄 build-native.sh               # Native构建脚本（Linux/macOS）
├── 📄 build-native.bat              # Native构建脚本（Windows）
├── 📁 src/                          # 源代码目录
├── 📁 docs/                         # 项目文档
├── 📁 scripts/                      # 自动化脚本
├── 📁 logs/                         # 运行日志
├── 📁 .github/                      # GitHub配置
└── 📁 target/                       # 构建输出（自动生成）
```

## 📚 文档结构 (`docs/`)

```
docs/
├── 📄 README.md                                    # 文档中心导航
├── 🏗️ architecture.md                             # 系统架构设计
├── 📖 cli-usage-guide.md                          # CLI详细使用指南
├── 🗺️ roadmap.md                                  # 项目发展路线图
├── 🔄 dameng_mapping.md                           # 达梦数据库转换规则
├── 🔧 kingbase-conversion-implementation.md       # 金仓转换技术实现
├── 🔄 shentong_mapping.md                         # 神通数据库转换规则
└── ⚠️ error-handling-architecture.md              # 错误处理架构
```

### 文档分类说明

| 类别 | 文档 | 目标读者 | 内容概述 |
|------|------|----------|----------|
| **核心文档** | README.md | 所有用户 | 文档导航和索引 |
| **架构设计** | architecture.md | 开发者 | 系统设计和技术架构 |
| **使用指南** | cli-usage-guide.md | 用户 | 命令行工具使用方法 |
| **规划文档** | roadmap.md | 产品经理 | 功能规划和发展方向 |
| **转换规则** | dameng_mapping.md | 用户/开发者 | MySQL到达梦的转换映射 |
| **技术实现** | kingbase-*.md | 开发者 | 金仓数据库相关技术文档 |

## 🛠️ 脚本目录 (`scripts/`)

```
scripts/
├── 📄 README.md                        # 脚本使用说明
├── 🏗️ install-graalvm.sh              # GraalVM自动安装
├── 🧹 clean-project.sh                 # 项目清理脚本
└── 🚀 release.sh                       # 版本发布脚本
```

### 脚本功能说明

| 脚本 | 功能 | 平台 | 使用场景 |
|------|------|------|----------|
| `install-graalvm.sh` | 自动下载安装GraalVM | Linux/macOS | 环境初始化 |
| `clean-project.sh` | 项目文件清理 | 跨平台 | 维护清理 |
| `release.sh` | 自动化版本发布 | 跨平台 | 版本发布 |

## 💻 源代码结构 (`src/`)

```
src/
├── main/
│   ├── java/com/xylink/sqltranspiler/
│   │   ├── 📁 core/                    # 核心转换引擎
│   │   │   ├── 📁 dialects/            # 数据库方言实现
│   │   │   │   ├── 📁 dameng/          # 达梦数据库支持
│   │   │   │   └── 📁 kingbase/        # 金仓数据库支持
│   │   │   └── 📁 parser/              # SQL解析器
│   │   ├── 📁 infrastructure/          # 基础设施层
│   │   │   ├── 📁 parser/              # 解析器基础设施
│   │   │   └── 📁 logging/             # 日志系统
│   │   ├── 📁 features/                # 功能特性
│   │   └── 📄 Main.java                # 应用程序入口
│   └── resources/
│       ├── 📁 antlr4/                  # ANTLR语法文件
│       └── 📄 logback.xml              # 日志配置
└── test/
    └── java/com/xylink/sqltranspiler/
        ├── 📁 core/                    # 核心功能测试
        ├── 📁 features/                # 功能特性测试
        └── 📁 integration/             # 集成测试
```

## 🔧 构建和配置文件

| 文件 | 用途 | 说明 |
|------|------|------|
| `pom.xml` | Maven配置 | 依赖管理、构建配置、插件配置 |
| `Makefile` | 构建自动化 | 简化常用构建命令 |
| `build-native.sh` | Native构建 | Linux/macOS多架构构建脚本 |
| `build-native.bat` | Native构建 | Windows构建脚本 |

## 🚀 CI/CD配置 (`.github/`)

```
.github/
└── workflows/
    └── native-build.yml                # GitHub Actions工作流
```

### 自动化流程

- **触发条件**: 推送到main/develop分支、创建PR、发布标签
- **构建矩阵**: 5个平台架构并行构建
- **自动发布**: 标签推送时自动创建GitHub Release

## 📊 项目统计

### 代码统计
- **Java源文件**: 50+ 个
- **测试文件**: 200+个
- **测试修正完成**: 55+个测试文件基于官方文档修正
- **成功验证标记**: 1400+个（✅）
- **官方文档合规性**: 100%
- **支持数据库**: 3个（达梦、金仓、神通）
- **架构**: 基于ANTLR4的可扩展设计

### 文档统计
- **核心文档**: 12个
- **文档类型**: 架构、使用、转换规则、规划、测试修正报告
- **维护状态**: 持续更新
- **测试文档**: 基于官方文档的完整测试修正报告

### 构建产物
- **JAR文件**: `target/sql-transpiler.jar`
- **Native二进制**: `target/sql-transpiler`
- **分发包**: `target/sql-transpiler-native.tar.gz`

## 🧹 项目维护

### 清理命令
```bash
# 基本清理
make clean

# 深度清理
make clean-all

# 预览清理
make clean-preview

# 使用脚本清理
./scripts/clean-project.sh --help
```

### 文件忽略
项目使用`.gitignore`忽略以下文件：
- 构建产物 (`target/`)
- IDE配置文件
- 临时文件和日志
- 操作系统特定文件

## 📋 开发规范

### 目录命名
- 使用小写字母和连字符
- 功能模块按领域分组
- 测试目录结构镜像源码结构

### 文件命名
- Java类使用PascalCase
- 配置文件使用kebab-case
- 文档文件使用kebab-case
- 脚本文件使用kebab-case

### 文档维护
- 所有文档使用Markdown格式
- 保持文档与代码同步
- 定期审查和更新内容
- 使用表格和图标提高可读性

---

*最后更新：2025-07-16*
