@echo off
setlocal enabledelayedexpansion

REM SQL Transpiler Native Build Script for Windows
REM 支持多架构native打包：x86_64和ARM64

set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

REM 打印带颜色的消息
:print_info
echo %BLUE%[INFO]%NC% %~1
goto :eof

:print_success
echo %GREEN%[SUCCESS]%NC% %~1
goto :eof

:print_warning
echo %YELLOW%[WARNING]%NC% %~1
goto :eof

:print_error
echo %RED%[ERROR]%NC% %~1
goto :eof

REM 检查GraalVM是否安装
:check_graalvm
where native-image >nul 2>&1
if %errorlevel% neq 0 (
    call :print_error "GraalVM native-image not found. Please install GraalVM and add it to PATH."
    call :print_info "Download from: https://www.graalvm.org/downloads/"
    exit /b 1
)

for /f "tokens=*" %%i in ('native-image --version 2^>^&1 ^| findstr /r "."') do (
    call :print_info "Found GraalVM: %%i"
    goto :graalvm_found
)
:graalvm_found
goto :eof

REM 检查Maven是否安装
:check_maven
where mvn >nul 2>&1
if %errorlevel% neq 0 (
    call :print_error "Maven not found. Please install Maven and add it to PATH."
    exit /b 1
)

for /f "tokens=*" %%i in ('mvn --version 2^>^&1 ^| findstr /r "Apache Maven"') do (
    call :print_info "Found Maven: %%i"
    goto :maven_found
)
:maven_found
goto :eof

REM 清理之前的构建
:clean_build
call :print_info "Cleaning previous builds..."
call mvn clean
if exist target\native-builds rmdir /s /q target\native-builds
mkdir target\native-builds
goto :eof

REM 构建特定架构的native image
:build_native
set "profile=%~1"
set "platform=%~2"

call :print_info "Building native image for %platform%..."

REM 编译Java代码
call mvn compile
if %errorlevel% neq 0 (
    call :print_error "Failed to compile Java code"
    exit /b 1
)

REM 构建native image
call mvn package -P%profile% -DskipTests
if %errorlevel% equ 0 (
    call :print_success "Successfully built native image for %platform%"
    
    REM 移动生成的文件到统一目录
    if exist "target\sql-transpiler-%platform%.exe" (
        move "target\sql-transpiler-%platform%.exe" "target\native-builds\"
        call :print_info "Native binary saved to: target\native-builds\sql-transpiler-%platform%.exe"
    ) else if exist "target\sql-transpiler-%platform%" (
        move "target\sql-transpiler-%platform%" "target\native-builds\"
        call :print_info "Native binary saved to: target\native-builds\sql-transpiler-%platform%"
    ) else (
        call :print_warning "Native binary not found in expected location"
    )
) else (
    call :print_error "Failed to build native image for %platform%"
    exit /b 1
)
goto :eof

REM 显示帮助信息
:show_help
echo SQL Transpiler Native Build Script for Windows
echo.
echo Usage: %~nx0 [OPTIONS] [PLATFORMS]
echo.
echo OPTIONS:
echo   -h, --help          Show this help message
echo   -c, --clean         Clean build directory before building
echo   --current           Build for current platform only
echo   --all               Build for all supported platforms
echo.
echo PLATFORMS:
echo   linux-x64           Linux x86_64
echo   linux-arm64         Linux ARM64
echo   macos-x64           macOS x86_64
echo   macos-arm64         macOS ARM64 (Apple Silicon)
echo   windows-x64         Windows x86_64
echo.
echo Examples:
echo   %~nx0 --current                    # Build for current platform
echo   %~nx0 --all                        # Build for all platforms
echo   %~nx0 linux-x64 linux-arm64       # Build for specific platforms
echo   %~nx0 -c windows-x64               # Clean and build for Windows x64
goto :eof

REM 检测当前平台
:detect_current_platform
REM Windows批处理脚本只能在Windows上运行，所以默认是windows-x64
echo windows-x64
goto :eof

REM 主函数
:main
set "platforms="
set "clean_first=false"
set "build_all=false"
set "build_current=false"
set "platform_count=0"

REM 解析命令行参数
:parse_args
if "%~1"=="" goto :args_parsed
if "%~1"=="-h" goto :show_help_and_exit
if "%~1"=="--help" goto :show_help_and_exit
if "%~1"=="-c" (
    set "clean_first=true"
    shift
    goto :parse_args
)
if "%~1"=="--clean" (
    set "clean_first=true"
    shift
    goto :parse_args
)
if "%~1"=="--all" (
    set "build_all=true"
    shift
    goto :parse_args
)
if "%~1"=="--current" (
    set "build_current=true"
    shift
    goto :parse_args
)
if "%~1"=="linux-x64" (
    set "platforms=!platforms! %~1"
    set /a platform_count+=1
    shift
    goto :parse_args
)
if "%~1"=="linux-arm64" (
    set "platforms=!platforms! %~1"
    set /a platform_count+=1
    shift
    goto :parse_args
)
if "%~1"=="macos-x64" (
    set "platforms=!platforms! %~1"
    set /a platform_count+=1
    shift
    goto :parse_args
)
if "%~1"=="macos-arm64" (
    set "platforms=!platforms! %~1"
    set /a platform_count+=1
    shift
    goto :parse_args
)
if "%~1"=="windows-x64" (
    set "platforms=!platforms! %~1"
    set /a platform_count+=1
    shift
    goto :parse_args
)
call :print_error "Unknown option: %~1"
goto :show_help_and_exit

:show_help_and_exit
call :show_help
exit /b 0

:args_parsed

REM 检查依赖
call :check_maven
if %errorlevel% neq 0 exit /b 1

call :check_graalvm
if %errorlevel% neq 0 exit /b 1

REM 清理构建目录
if "%clean_first%"=="true" (
    call :clean_build
)

REM 确定要构建的平台
if "%build_all%"=="true" (
    set "platforms= linux-x64 linux-arm64 macos-x64 macos-arm64 windows-x64"
    set "platform_count=5"
) else if "%build_current%"=="true" (
    call :detect_current_platform
    set "platforms= windows-x64"
    set "platform_count=1"
) else if %platform_count% equ 0 (
    call :print_info "No platforms specified, building for current platform"
    set "platforms= windows-x64"
    set "platform_count=1"
)

call :print_info "Building for platforms:%platforms%"

REM 创建输出目录
if not exist target\native-builds mkdir target\native-builds

REM 构建每个平台
set "success_count=0"

for %%p in (%platforms%) do (
    call :print_info "Starting build for %%p..."
    
    if "%%p"=="linux-x64" (
        call :build_native "native-linux-x64" "%%p"
        if !errorlevel! equ 0 set /a success_count+=1
    ) else if "%%p"=="linux-arm64" (
        call :build_native "native-linux-arm64" "%%p"
        if !errorlevel! equ 0 set /a success_count+=1
    ) else if "%%p"=="macos-x64" (
        call :build_native "native-macos-x64" "%%p"
        if !errorlevel! equ 0 set /a success_count+=1
    ) else if "%%p"=="macos-arm64" (
        call :build_native "native-macos-arm64" "%%p"
        if !errorlevel! equ 0 set /a success_count+=1
    ) else if "%%p"=="windows-x64" (
        call :build_native "native-windows-x64" "%%p"
        if !errorlevel! equ 0 set /a success_count+=1
    ) else (
        call :print_error "Unsupported platform: %%p"
    )
)

REM 显示构建结果
echo.
call :print_info "Build Summary:"
call :print_info "Successfully built: %success_count%/%platform_count% platforms"

if %success_count% gtr 0 (
    call :print_info "Native binaries available in: target\native-builds\"
    dir target\native-builds\
)

if %success_count% equ %platform_count% (
    call :print_success "All builds completed successfully!"
    exit /b 0
) else (
    call :print_warning "Some builds failed. Check the output above for details."
    exit /b 1
)

REM 调用主函数
call :main %*
