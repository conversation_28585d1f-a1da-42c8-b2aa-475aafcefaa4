#!/bin/bash

# SQL Transpiler Native Build Script
# 支持多架构native打包：x86_64和ARM64

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查GraalVM是否安装
check_graalvm() {
    if ! command -v native-image &> /dev/null; then
        print_error "GraalVM native-image not found. Please install GraalVM and add it to PATH."
        print_info "Download from: https://www.graalvm.org/downloads/"
        exit 1
    fi
    
    local graal_version=$(native-image --version | head -n 1)
    print_info "Found GraalVM: $graal_version"
}

# 检查Maven是否安装
check_maven() {
    if ! command -v mvn &> /dev/null; then
        print_error "Maven not found. Please install Maven and add it to PATH."
        exit 1
    fi
    
    local maven_version=$(mvn --version | head -n 1)
    print_info "Found Maven: $maven_version"
}

# 清理之前的构建
clean_build() {
    print_info "Cleaning previous builds..."
    mvn clean
    rm -rf target/native-builds
    mkdir -p target/native-builds
}

# 构建特定架构的native image
build_native() {
    local profile=$1
    local platform=$2
    
    print_info "Building native image for $platform..."
    
    # 编译Java代码
    mvn compile
    
    # 构建native image
    if mvn package -P$profile -DskipTests; then
        print_success "Successfully built native image for $platform"
        
        # 移动生成的文件到统一目录
        if [ -f "target/sql-transpiler-$platform" ]; then
            mv "target/sql-transpiler-$platform" "target/native-builds/"
            print_info "Native binary saved to: target/native-builds/sql-transpiler-$platform"
        elif [ -f "target/sql-transpiler-$platform.exe" ]; then
            mv "target/sql-transpiler-$platform.exe" "target/native-builds/"
            print_info "Native binary saved to: target/native-builds/sql-transpiler-$platform.exe"
        else
            print_warning "Native binary not found in expected location"
        fi
    else
        print_error "Failed to build native image for $platform"
        return 1
    fi
}

# 显示帮助信息
show_help() {
    echo "SQL Transpiler Native Build Script"
    echo ""
    echo "Usage: $0 [OPTIONS] [PLATFORMS]"
    echo ""
    echo "OPTIONS:"
    echo "  -h, --help          Show this help message"
    echo "  -c, --clean         Clean build directory before building"
    echo "  --current           Build for current platform only"
    echo "  --all               Build for all supported platforms"
    echo ""
    echo "PLATFORMS:"
    echo "  linux-x64           Linux x86_64"
    echo "  linux-arm64         Linux ARM64"
    echo "  macos-x64           macOS x86_64"
    echo "  macos-arm64         macOS ARM64 (Apple Silicon)"
    echo "  windows-x64         Windows x86_64"
    echo ""
    echo "Examples:"
    echo "  $0 --current                    # Build for current platform"
    echo "  $0 --all                        # Build for all platforms"
    echo "  $0 linux-x64 linux-arm64       # Build for specific platforms"
    echo "  $0 -c macos-arm64               # Clean and build for macOS ARM64"
}

# 检测当前平台
detect_current_platform() {
    local os=$(uname -s)
    local arch=$(uname -m)
    
    case "$os" in
        Linux*)
            case "$arch" in
                x86_64|amd64) echo "linux-x64" ;;
                aarch64|arm64) echo "linux-arm64" ;;
                *) print_error "Unsupported Linux architecture: $arch"; exit 1 ;;
            esac
            ;;
        Darwin*)
            case "$arch" in
                x86_64) echo "macos-x64" ;;
                arm64) echo "macos-arm64" ;;
                *) print_error "Unsupported macOS architecture: $arch"; exit 1 ;;
            esac
            ;;
        CYGWIN*|MINGW*|MSYS*)
            case "$arch" in
                x86_64|amd64) echo "windows-x64" ;;
                *) print_error "Unsupported Windows architecture: $arch"; exit 1 ;;
            esac
            ;;
        *)
            print_error "Unsupported operating system: $os"
            exit 1
            ;;
    esac
}

# 主函数
main() {
    local platforms=()
    local clean_first=false
    local build_all=false
    local build_current=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -c|--clean)
                clean_first=true
                shift
                ;;
            --all)
                build_all=true
                shift
                ;;
            --current)
                build_current=true
                shift
                ;;
            linux-x64|linux-arm64|macos-x64|macos-arm64|windows-x64)
                platforms+=("$1")
                shift
                ;;
            *)
                print_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查依赖
    check_maven
    check_graalvm
    
    # 清理构建目录
    if [ "$clean_first" = true ]; then
        clean_build
    fi
    
    # 确定要构建的平台
    if [ "$build_all" = true ]; then
        platforms=("linux-x64" "linux-arm64" "macos-x64" "macos-arm64" "windows-x64")
    elif [ "$build_current" = true ]; then
        platforms=($(detect_current_platform))
    elif [ ${#platforms[@]} -eq 0 ]; then
        print_info "No platforms specified, building for current platform"
        platforms=($(detect_current_platform))
    fi
    
    print_info "Building for platforms: ${platforms[*]}"
    
    # 创建输出目录
    mkdir -p target/native-builds
    
    # 构建每个平台
    local success_count=0
    local total_count=${#platforms[@]}
    
    for platform in "${platforms[@]}"; do
        print_info "Starting build for $platform..."
        
        case "$platform" in
            linux-x64)
                if build_native "native-linux-x64" "$platform"; then
                    ((success_count++))
                fi
                ;;
            linux-arm64)
                if build_native "native-linux-arm64" "$platform"; then
                    ((success_count++))
                fi
                ;;
            macos-x64)
                if build_native "native-macos-x64" "$platform"; then
                    ((success_count++))
                fi
                ;;
            macos-arm64)
                if build_native "native-macos-arm64" "$platform"; then
                    ((success_count++))
                fi
                ;;
            windows-x64)
                if build_native "native-windows-x64" "$platform"; then
                    ((success_count++))
                fi
                ;;
            *)
                print_error "Unsupported platform: $platform"
                ;;
        esac
    done
    
    # 显示构建结果
    echo ""
    print_info "Build Summary:"
    print_info "Successfully built: $success_count/$total_count platforms"
    
    if [ $success_count -gt 0 ]; then
        print_info "Native binaries available in: target/native-builds/"
        ls -la target/native-builds/
    fi
    
    if [ $success_count -eq $total_count ]; then
        print_success "All builds completed successfully!"
        exit 0
    else
        print_warning "Some builds failed. Check the output above for details."
        exit 1
    fi
}

# 运行主函数
main "$@"
