# 项目文档索引

> 📖 **主要文档**: [项目架构文档](../principle/main.md) - 完整的项目架构和设计原理

## 📚 文档结构

### 核心文档
| 文档 | 说明 | 适用对象 |
|------|------|----------|
| [🏗️ 架构设计](architecture.md) | 系统架构和设计原理 | 开发者、架构师 |
| [📖 CLI使用指南](cli-usage-guide.md) | 命令行工具使用说明 | 用户、运维人员 |
| [🗺️ 项目路线图](roadmap.md) | 功能规划和发展方向 | 产品经理、开发者 |
| [⚠️ 错误处理架构](error-handling-architecture.md) | SQL错误处理系统架构 | 开发者 |
| [🚀 Calcite能力借鉴方案](calcite-enhancement-plan.md) | Apache Calcite能力借鉴的具体实施方案 | 架构师、开发者 |

### 数据库转换指南
| 数据库 | 转换规则 | 测试覆盖 | 状态 |
|--------|----------|----------|------|
| **达梦** | [🔄 转换映射](dameng_mapping.md) | 完整测试覆盖，基于官方文档验证 | ✅ 生产就绪 |
| **金仓** | [🔧 实现指南](kingbase-conversion-implementation.md) | 完整测试覆盖，基于官方文档验证 | ✅ 生产就绪 |
| **神通** | [🔄 转换映射](shentong_mapping.md) | 完整测试覆盖，基于官方文档验证 | ✅ 生产就绪 |
| **PostgreSQL** | 规划中 | - | 📋 尚未实现 |

## 📊 文档统计

- **总文档数量**: 8个
- **覆盖数据库**: 达梦、金仓、神通
- **文档完整度**: 100%
- **测试文件总数**: 211个
- **测试验证体系**: 完整的官方文档合规性验证
- **官方文档合规性**: 100%
- **技术增强方案**: Apache Calcite能力借鉴方案完成
- **最后更新**: 2025-08-07

## 🎯 快速导航

### 新用户
1. 阅读 [CLI使用指南](cli-usage-guide.md)
2. 选择对应的数据库转换指南

### 开发者
1. 了解 [架构设计](architecture.md)
2. 查看 [项目路线图](roadmap.md)
3. 参考具体数据库的实现文档

## 📚 完整文档列表

### 核心文档
- [架构设计](architecture.md) - 系统架构和技术选型
- [CLI使用指南](cli-usage-guide.md) - 命令行工具使用
- [发展路线图](roadmap.md) - 项目发展计划
- [错误处理架构](error-handling-architecture.md) - SQL错误处理系统

### 数据库转换指南
- [达梦数据库转换映射](dameng_mapping.md) - MySQL到达梦转换规则
- [金仓数据库实现指南](kingbase-conversion-implementation.md) - 金仓转换实现
- [神通数据库转换映射](shentong_mapping.md) - MySQL到神通转换规则
