# MySQL到达梦的转换规则

本文档概述了将SQL从MySQL转换为达梦时应用的具体转换规则。

### 数据类型映射

| MySQL类型 | 达梦类型 | 说明 |
| --- | --- | --- |
| `TINYINT(1)` | `BIT` | 用于布尔值。 |
| `TINYINT` | `SMALLINT` | 基于官方FAQ，为避免潜在的符号位问题，统一映射为`SMALLINT`。 |
| `TEXT`/`LONGTEXT` | `CLOB` | 用于大文本对象。 |
| `BLOB`/`LONGBLOB` | `BLOB` | 用于二进制大对象。 |
| `AUTO_INCREMENT` | `IDENTITY(1,1)` | 用于自增列。 |
| `ENUM` | `VARCHAR` | 用于枚举类型。理想的转换会额外添加`CHECK`约束来限制取值范围。 |
| `YEAR` | `INT` | 用于年份值。 |
| `VARCHAR(>32767)` | `CLOB` | 自动处理超大`VARCHAR`定义。 |

### 函数映射

| MySQL函数 | 达梦函数 | 说明 |
| --- | --- | --- |
| `CURRENT_TIMESTAMP` | `SYSDATE` | 当前时间戳。 |
| `IFNULL` | `NVL` | 空值处理。 |
| `CONCAT` | `||` | 字符串连接。 |
| `LENGTH` | `LENGTHB` | 字符串长度（字节）。 |

**注**: 对于更复杂的函数，如 `GROUP_CONCAT` 或 `DATE_FORMAT`，其转换逻辑更为复杂，需要根据具体场景进行适配或在应用层面处理。

### SQL语法转换

- **多行`INSERT`语句转换**: 将MySQL特有的单条`INSERT`插入多行的语法，拆分为多条符合达梦标准的单行`INSERT`语句。
- **约束分离**: `FOREIGN KEY`约束会自动分离为独立的`ALTER TABLE ADD CONSTRAINT`语句。
- **索引分离**: `KEY`/`INDEX`定义会分离为独立的`CREATE INDEX`语句。
- **标识符引用**: 表名和列名会自动用双引号（`"`）括起来，以避免关键字冲突。
- **字符集转换**: `CHARSET=utf8mb4`会转换为`CHARACTER SET UTF8`。
- **存储引擎移除**: 移除MySQL特有的子句，如`ENGINE=InnoDB`。
- **注释处理**: 表和列上的`COMMENT`子句会转换为`COMMENT ON`语句。

### VARCHAR长度处理 (`--dameng-length-in-char`)

MySQL和达梦在计算`VARCHAR`长度方面有一个关键区别：
- **MySQL**: `VARCHAR(n)`存储**n个字符**。
- **达梦**: 默认情况下，`VARCHAR(n)`存储**n个字节**。

这可能导致多字节字符（例如UTF-8中的中文字符）的数据截断。转换器提供了一个标志来管理这个问题：`--dameng-length-in-char`。

- **字符模式 (默认): `--dameng-length-in-char=true`**
  - 假设目标达梦数据库配置了`LENGTH_IN_CHAR=1`。
  - `VARCHAR(50)`保持为`VARCHAR(50)`。
  - 这是默认行为，以与MySQL的逻辑保持一致。

- **字节模式: `--dameng-length-in-char=false`**
  - 适用于`LENGTH_IN_CHAR=0`（默认字节单位）的达梦数据库。
  - 转换器将字符长度乘以3的安全系数，以确保为UTF-8字符提供足够的字节长度。
  - **示例**: `VARCHAR(50)`变为`VARCHAR(150)`。

---

> **最后更新**: 2025-07-30
> **维护者**: lofy