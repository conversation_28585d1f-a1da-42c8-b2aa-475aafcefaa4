# 项目架构设计

## 架构概述

SQL转换器采用分层架构和插件式设计，遵循**"一次解析，多次生成"**的核心理念。整个系统分为四个主要层次：预处理层、解析层、抽象语法树层和方言生成层。

## 分层架构

### 1. 预处理层 (Preprocessor Layer)
**职责**: 对原始SQL进行预处理，为后续解析做准备

**核心组件**:
- `Preprocessor.java` - 主预处理器
- 关键字冲突处理
- 注释清理和保留
- 标识符引用处理

**处理流程**:
```
原始MySQL SQL → 关键字处理 → 注释处理 → 标识符引用 → 清理后的SQL
```

### 2. SQL解析层 (Parser Layer)
**职责**: 将预处理后的SQL解析为抽象语法树

**核心组件**:
- `MySqlHelper.java` - MySQL解析助手
- `MySqlAntlr4Visitor.java` - ANTLR4访问器
- ANTLR4语法文件

**解析流程**:
```
清理后的SQL → ANTLR4解析 → 语法树遍历 → AST构建
```

### 3. 抽象语法树层 (AST Layer)
**职责**: 提供与数据库无关的中间表示

**核心模型**:
- `CreateTableStatement.java` - 表创建语句
- `ColumnDefinition.java` - 列定义
- `ConstraintDefinition.java` - 约束定义
- `IndexDefinition.java` - 索引定义

**设计特点**:
- 数据库无关性
- 完整的SQL语义表示
- 支持复杂的SQL结构

### 4. 方言生成层 (Dialect Layer)
**职责**: 将AST转换为目标数据库的SQL

**核心接口**:
- `Generator.java` - 生成器接口
- `Dialect.java` - 方言接口
- `DialectRegistry.java` - 方言注册表

**已实现方言**:
- `DamengGenerator.java` - 达梦数据库生成器

## 核心设计原则

### 1. 单一职责原则
每个组件都有明确的单一职责：
- 预处理器只负责SQL清理
- 解析器只负责语法分析
- 生成器只负责目标SQL生成

### 2. 开放封闭原则
- 对扩展开放：新增数据库支持只需实现Generator接口
- 对修改封闭：核心解析逻辑无需修改

### 3. 依赖倒置原则
- 高层模块不依赖低层模块
- 都依赖于抽象接口
- 通过依赖注入实现解耦

### 4. 接口隔离原则
- 使用小而专一的接口
- 避免臃肿的接口设计
- 每个接口都有明确的职责

## 扩展机制

### 新增数据库支持

1. **实现`Generator`接口**
```java
public class PostgreSqlGenerator implements Generator {
    @Override
    public String generate(Statement statement) {
        // 实现PostgreSQL特有的转换逻辑
    }
}
```

2. **注册到方言注册表**
```java
DialectRegistry.registerDialect("postgresql", new PostgreSQLDialect());
```

---

> **最后更新**: 2025-07-30
> **维护者**: lofy