# MySQL到神通数据库转换映射

## 概述

本文档详细说明MySQL到神通数据库的转换规则和映射关系。所有转换规则严格遵循神通数据库官方文档，确保100%兼容性。

> **参考文档**: 神通数据库官方SQL参考手册

## 🎯 核心转换原则

1. **标识符转换**：MySQL反引号(`)转换为神通双引号(")
2. **数据类型映射**：智能转换为神通最佳等价类型
3. **语法适配**：自动处理语法差异
4. **函数转换**：MySQL函数转换为神通等价函数
5. **约束处理**：完整支持各类约束转换

## 📊 数据类型映射

### 数值类型
| MySQL类型 | 神通类型 | 说明 |
|-----------|----------|------|
| `TINYINT(1)` | `BIT` | 布尔值专用转换 |
| `TINYINT` | `TINYINT` | 直接映射 |
| `SMALLINT` | `SMALLINT` | 直接映射 |
| `INT` | `INT` | 直接映射 |
| `BIGINT` | `BIGINT` | 直接映射 |
| `DECIMAL(p,s)` | `DECIMAL(p,s)` | 精度保持 |
| `FLOAT` | `FLOAT` | 直接映射 |
| `DOUBLE` | `DOUBLE` | 直接映射 |

### 字符串类型
| MySQL类型 | 神通类型 | 说明 |
|-----------|----------|------|
| `VARCHAR(n)` | `VARCHAR(n)` | 长度保持 |
| `CHAR(n)` | `CHAR(n)` | 长度保持 |
| `TEXT` | `TEXT` | 直接映射 |
| `LONGTEXT` | `CLOB` | 大文本映射 |

### 日期时间类型
| MySQL类型 | 神通类型 | 说明 |
|-----------|----------|------|
| `DATE` | `DATE` | 直接映射 |
| `TIME` | `TIME` | 直接映射 |
| `DATETIME` | `TIMESTAMP` | 时间戳映射 |
| `TIMESTAMP` | `TIMESTAMP` | 直接映射 |

## 函数映射

根据神通数据库官方文档，支持的函数映射如下：

| MySQL函数 | 神通数据库函数 | 说明 |
| --- | --- | --- |
| `IFNULL(a, b)` | `IFNULL(a, b)` | **完全支持**，与MySQL语法完全兼容 |
| `CONCAT(str1, str2, ...)` | `CONCAT(str1, str2, ...)` | **完全支持**，与MySQL语法完全兼容 |
| `NOW()` | `CURRENT_TIMESTAMP` | **完全支持**，获取当前时间戳 |
| `CURDATE()` | `CURRENT_DATE` | **完全支持**，获取当前日期 |
| `CURTIME()` | `CURRENT_TIME` | **完全支持**，获取当前时间 |
| `CHAR_LENGTH(str)` | `CHAR_LENGTH(str)` | **完全支持**，获取字符串长度 |
| `SUBSTRING(str, pos, len)` | `SUBSTRING(str FROM pos FOR len)` | **完全支持**，字符串截取 |
| `COUNT(*)` | `COUNT(*)` | **完全支持**，聚合函数 |
| `LAST_INSERT_ID()` | `LAST_INSERT_ID()` | **完全支持**，获取最后插入的自增ID |

## SQL语法转换

### 自增字段
```sql
-- MySQL
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50)
);

-- 神通数据库（完全兼容）
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50)
);
```

### ALTER TABLE AUTO_INCREMENT
```sql
-- MySQL
ALTER TABLE users AUTO_INCREMENT = 1000;

-- 神通数据库（完全兼容）
ALTER TABLE users AUTO_INCREMENT = 1000;
```

### 自增列详细用法
根据神通数据库官方文档，自增列具有以下特性：

1. **支持的数据类型**：INT、BIGINT、FLOAT
2. **唯一性约束**：自增列必须有仅包括本列的PRIMARY KEY或UNIQUE约束
3. **插入行为**：
   - 不指定自增列值时，数据库自动从初值开始递增分配
   - 可以显式指定自增列值，如果该值比当前自增值大，下次从该值往后递增
4. **ALTER支持**：
   - 列由非自增列改为自增列（表没有自增列的前提下）
   - 列由自增列改为非自增列
   - 可修改自增列下次分配的值
5. **UPDATE支持**：支持更新自增列的值，但不会影响下次分配的值
6. **函数支持**：支持LAST_INSERT_ID()函数获取本会话上一次插入的自增列值

### 字符集设置
```sql
-- MySQL
DEFAULT CHARSET=utf8mb4

-- 神通数据库
CHARACTER SET UTF8
```

### 神通数据库支持的字符集
根据官方文档，神通数据库支持以下字符集：
- `US7ASCII` (ID: 1)
- `ZHS16GBK` (ID: 852) - 中文GBK编码
- `ZHS32GB18030` (ID: 854) - 中文GB18030编码
- `ZHT16BIG5` (ID: 865) - 繁体中文Big5编码
- `UTF8` (ID: 871) - UTF-8编码（注意：神通使用UTF8，不是UTF-8）
- `AL32UTF8` (ID: 873) - 32位UTF-8编码
- `UTF16` (ID: 1000) - UTF-16编码

### 存储引擎
```sql
-- MySQL
ENGINE=InnoDB

-- 神通数据库
-- 神通数据库不需要指定存储引擎，自动选择最优存储方式
```

### 注释语法
```sql
-- MySQL
CREATE TABLE users (
    id INT COMMENT '用户ID',
    name VARCHAR(50) COMMENT '用户名'
) COMMENT='用户表';

-- 神通数据库（支持两种方式）
-- 方式1：保持MySQL语法
CREATE TABLE users (
    id INT COMMENT '用户ID',
    name VARCHAR(50) COMMENT '用户名'
) COMMENT='用户表';

-- 方式2：标准SQL语法
CREATE TABLE users (
    id INT,
    name VARCHAR(50)
);
COMMENT ON TABLE users IS '用户表';
COMMENT ON COLUMN users.id IS '用户ID';
COMMENT ON COLUMN users.name IS '用户名';
```

## 不支持的MySQL特性

根据神通数据库官方文档，以下MySQL特性可能不被支持或需要调整：

### 1. 特定数据类型
- `MEDIUMINT` - 使用`INT`替代
- `YEAR` - 可能需要使用`SMALLINT`或`INT`替代

### 2. DECIMAL类型特殊说明
- **重要**：神通数据库5.7中废除了DECIMAL数据类型，只留下了NUMERIC
- DECIMAL作为NUMERIC的别名存在，所有DECIMAL函数实际上是NUMERIC函数的实现
- 建议在新项目中直接使用NUMERIC类型

### 3. 存储引擎相关
- MyISAM、InnoDB等存储引擎选择
- 存储引擎特定的参数设置

### 4. 字符集相关
- `utf8mb4` - 转换为`UTF8`（注意：神通数据库使用UTF8，不是UTF-8）
- `utf8` - 转换为`UTF8`
- 特定的字符集和排序规则可能需要调整

### 5. 分区表
- PARTITION BY语法（需要验证支持情况）
- 分区管理命令

### 6. 全文索引
- FULLTEXT索引类型（需要验证支持情况）
- MATCH...AGAINST语法

### 7. 自增列限制
- MySQL支持的修改自增列的自增步长和偏移量，神通数据库暂不支持
- 神通仅支持将一个列的属性设置为自增列

## 转换器实现特点

### 高兼容性
- 保持MySQL的AUTO_INCREMENT语法
- 保持MySQL的IFNULL等函数
- 最小化语法转换，减少迁移成本

### 智能转换
- 自动处理不支持的数据类型
- 智能字符集转换
- 自动移除不支持的存储引擎子句

### 标识符处理
- 表名和列名自动用双引号括起来
- 避免关键字冲突
- 保持原始大小写

## 迁移建议

1. **充分测试**：虽然神通数据库高度兼容MySQL，但建议在迁移前进行充分测试
2. **性能调优**：根据神通数据库特性进行性能优化
3. **字符编码**：确保字符编码设置正确
4. **应用适配**：检查应用程序中的数据库连接参数

## 总结

根据神通数据库官方文档，神通数据库在MySQL兼容性方面具有以下优势：

### 高兼容性特点
- **SQL标准支持**：基本支持SQL92的入门级和过渡级标准
- **数据类型兼容**：支持大部分MySQL数据类型，包括TINYINT、SMALLINT、INT、BIGINT等
- **自增字段完全兼容**：完全支持AUTO_INCREMENT语法，性能优于序列和SERIAL
- **函数兼容性**：支持常用的MySQL函数如IFNULL、CONCAT、CHAR_LENGTH等
- **ALTER TABLE支持**：完全支持ALTER TABLE AUTO_INCREMENT语法

### 迁移优势
- **最小转换成本**：大部分MySQL语法可以直接使用
- **性能优化**：自增列性能优于传统序列方式
- **标准接口**：提供ODBC、JDBC、OLEDB/ADO和.Net DataProvider等标准接口
- **国产化**：完全自主知识产权，满足国产化替换需求

### 实际应用建议
1. **充分测试**：虽然神通数据库高度兼容MySQL，但建议在迁移前进行充分测试
2. **性能调优**：根据神通数据库特性进行性能优化
3. **自增列优化**：利用神通数据库自增列的高性能特性
4. **标准化开发**：遵循SQL标准，提高代码可移植性

神通数据库作为支持SQL92标准并高度兼容MySQL的国产数据库，是MySQL国产化替换的优秀选择。

---

> **最后更新**: 2025-07-30
> **维护者**: lofy
