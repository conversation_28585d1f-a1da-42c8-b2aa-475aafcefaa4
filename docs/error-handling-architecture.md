# SQL错误处理系统架构

## 系统架构

### 🎯 **设计原则**

系统采用清晰的职责分离架构，建立了三层结构：

```
ErrorPatternRegistry (数据层)
    ↓ 提供错误模式和处理逻辑
EnhancedErrorAnalyzer (协调层)
    ↓ 分析和组装错误信息
SqlErrorInfo (结果层)
```

### 📋 **组件职责**

#### **1. ErrorPatternRegistry (数据和逻辑层)**
- **职责**：存储和管理所有错误模式
- **功能**：
  - 注册错误模式
  - 模式匹配
  - 自动修复逻辑
  - 统计信息

```java
// 核心方法
public static Optional<ErrorPattern> findMatchingPattern(String sql, String errorMessage)
public static boolean hasKnownSyntaxIssues(String sql)
public static String autoFixKnownIssues(String sql)
public static List<ErrorPattern> getAutoFixablePatterns()
public static int getPatternCount()
```

#### **2. EnhancedErrorAnalyzer (协调层)**
- **职责**：协调错误分析流程，组装错误信息
- **功能**：
  - 调用 ErrorPatternRegistry 进行模式匹配
  - 组装 SqlErrorInfo 对象
  - 提供便捷的API接口
  - 处理通用错误情况

```java
// 核心方法
public static SqlErrorInfo analyzeSqlError(String sql, String errorMessage, int line, int position)
public static boolean hasKnownSyntaxIssues(String sql)  // 委托给 Registry
public static String autoFixKnownIssues(String sql)     // 委托给 Registry
public static int getSupportedPatternCount()            // 统计信息
public static int getAutoFixablePatternCount()          // 统计信息
```

#### **3. SqlErrorInfo (结果层)**
- **职责**：封装错误分析结果
- **功能**：
  - 结构化错误信息
  - 格式化错误消息
  - 提供修复建议

### 🔄 **工作流程**

```mermaid
graph TD
    A[SQL + ErrorMessage] --> B[EnhancedErrorAnalyzer.analyzeSqlError]
    B --> C[ErrorPatternRegistry.findMatchingPattern]
    C --> D{找到匹配模式?}
    D -->|是| E[使用模式信息填充 SqlErrorInfo]
    D -->|否| F[使用通用错误处理]
    E --> G[返回 SqlErrorInfo]
    F --> G
    G --> H[格式化错误信息]
    H --> I[提供修复建议]
```

### ✅ **架构特点**

#### **1. 统一的错误模式管理**
- 所有错误模式统一在 `ErrorPatternRegistry` 中管理
- `EnhancedErrorAnalyzer` 作为协调器
- 单一的错误处理流程

#### **2. 清晰的职责分离**
```java
// EnhancedErrorAnalyzer 负责协调
Optional<ErrorPattern> pattern = ErrorPatternRegistry.findMatchingPattern(...);
if (pattern.isPresent()) {
    // 使用模式信息填充错误对象
}
```

#### **3. 简化的维护流程**
添加新错误类型只需要在 `ErrorPatternRegistry` 中注册新模式

### 🚀 **架构优势**

#### **1. 单一职责原则**
- `ErrorPatternRegistry`：专注于模式管理
- `EnhancedErrorAnalyzer`：专注于错误分析协调
- `SqlErrorInfo`：专注于结果封装

#### **2. 开闭原则**
- 添加新错误类型：只需注册新模式，无需修改现有代码
- 扩展功能：通过注册表模式轻松扩展

#### **3. 依赖倒置**
- `EnhancedErrorAnalyzer` 依赖于 `ErrorPatternRegistry` 的抽象接口
- 具体的错误模式实现在注册表中

#### **4. 可测试性**
- 每个组件职责单一，易于单元测试
- 模式匹配逻辑与错误分析逻辑分离

### 📊 **性能优化**

#### **1. 模式匹配优化**
```java
// 使用预编译的正则表达式
private static final Pattern pattern = Pattern.compile("...");

// 优先匹配常见错误模式
static {
    registerMySqlPatterns();     // 最常见的MySQL错误
    registerAntlrPatterns();     // ANTLR解析错误
    registerSqlPatterns();       // 通用SQL错误
}
```

#### **2. 懒加载和缓存**
- 错误模式在类加载时初始化
- 匹配结果可以缓存（未来扩展）

### 🔧 **使用示例**

#### **基本用法**
```java
// 分析单个错误
SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(
    sql, errorMessage, line, position);

// 检查是否有已知问题
if (EnhancedErrorAnalyzer.hasKnownSyntaxIssues(sql)) {
    String fixedSql = EnhancedErrorAnalyzer.autoFixKnownIssues(sql);
}

// 获取统计信息
int totalPatterns = EnhancedErrorAnalyzer.getSupportedPatternCount();
int autoFixablePatterns = EnhancedErrorAnalyzer.getAutoFixablePatternCount();
```

#### **添加新错误模式**
```java
// 在 ErrorPatternRegistry 中注册
ErrorPatternRegistry.register(new ErrorPattern(
    "custom_error_id",
    Pattern.compile("your_pattern"),
    SqlErrorType.CUSTOM_ERROR,
    "友好的错误信息",
    "详细描述",
    "修复建议",
    true, // 是否可自动修复
    sql -> sql.replaceAll("pattern", "replacement") // 自动修复函数
));
```

### 📈 **扩展性**

#### **1. 支持新数据库**
```java
// 添加PostgreSQL特定错误模式
private static void registerPostgreSqlPatterns() {
    register(new ErrorPattern(...));
}
```

#### **2. 支持新错误类型**
```java
// 在 SqlErrorType 枚举中添加
POSTGRESQL_SPECIFIC_SYNTAX("PostgreSQL特有语法", false),
```

#### **3. 支持复杂修复逻辑**
```java
// 复杂的自动修复函数
sql -> {
    // 多步骤修复逻辑
    String step1 = sql.replaceAll("pattern1", "replacement1");
    String step2 = step1.replaceAll("pattern2", "replacement2");
    return step2;
}
```

### 🎯 **架构优势**

系统架构具有以下特点：

1. **清晰的职责分离**：每个组件都有明确的职责
2. **统一的错误模式管理**：集中化的模式管理
3. **高度可扩展**：通过注册表模式轻松添加新功能
4. **易于维护**：单一修改点，降低维护成本
5. **良好的性能**：优化的模式匹配和缓存机制

该架构为SQL转译器提供了企业级的错误处理能力，同时保持了代码的清晰性和可维护性。

---

> **最后更新**: 2025-07-30
> **维护者**: lofy
