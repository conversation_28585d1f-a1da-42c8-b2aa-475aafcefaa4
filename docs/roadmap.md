# 项目发展路线图

## 🎯 当前状态

### ✅ 已完成功能
- **达梦数据库**: 完整支持，14个测试类验证
- **金仓数据库**: 完整支持，7个测试类验证
- **神通数据库**: 完整支持，13个测试类验证
- **核心架构**: 基于ANTLR4的可扩展架构
- **CLI工具**: 完整的命令行界面

### 📋 规划功能
- **PostgreSQL支持**: 尚未开始实现，计划中

## PostgreSQL 支持计划

下一个主要目标是增加对PostgreSQL的全面支持。转换将专注于解决MySQL和PostgreSQL之间的关键不兼容性，遵循官方的[PostgreSQL文档](http://www.postgres.cn/docs/current/index.html)。

### 1. 数据类型与约束映射

| MySQL特性 | PostgreSQL解决方案 | 关键说明 |
| :--- | :--- | :--- |
| **`AUTO_INCREMENT`** | `SERIAL` / `BIGSERIAL` / `IDENTITY` | 将自增列转换为PostgreSQL的原生序列或`IDENTITY`列。 |
| **`TINYINT(1)`** | `BOOLEAN` | 将`TINYINT(1)`显式映射为`BOOLEAN`类型。 |
| **无符号整数** | `BIGINT` 或 `NUMERIC` + `CHECK` | PostgreSQL不支持无符号整数。将`INT UNSIGNED`映射为`BIGINT`或使用原生整数类型加`CHECK (column >= 0)`约束。 |
| **零日期 (`'0000-00-00')`** | `NULL` | PostgreSQL不支持零日期；它们将被转换为`NULL`。 |
| **`DATETIME`** | `TIMESTAMP WITHOUT TIME ZONE` | 映射`DATETIME`并处理时区差异。 |
| **`ENUM`** | 原生`ENUM`类型 | 使用`CREATE TYPE ... AS ENUM`创建PostgreSQL原生枚举类型。 |
| **`JSON`** | `JSONB` | 默认转换为`JSONB`以获得更好的性能和索引能力。 |

### 2. 函数与语法转换

| MySQL特性 | PostgreSQL解决方案 | 关键说明 |
| :--- | :--- | :--- |
| **`INSERT ... ON DUPLICATE KEY UPDATE`** | `INSERT ... ON CONFLICT ... DO UPDATE` | 将MySQL特有的UPSERT语法转换为标准的PostgreSQL形式。 |
| **`GROUP_CONCAT()`** | `STRING_AGG()` | 转换聚合字符串连接函数。 |
| **`IFNULL(a, b)`** | `COALESCE(a, b)` | 转换空值处理函数。 |
| **`DATE_FORMAT()` / `NOW()`** | `to_char()` / `CURRENT_TIMESTAMP` | 转换日期格式化和当前时间函数，并适配格式化字符串。 |
| **`LAST_INSERT_ID()`** | `RETURNING id` | 从独立的函数调用改为`INSERT`语句中的`RETURNING`子句。 |
| **宽松的`GROUP BY`** | **严格的`GROUP BY`** | 重写不符合标准的`GROUP BY`子句，将所有非聚合的`SELECT`列添加到`GROUP BY`子句中。 |

### 3. 行为与架构差异

| MySQL特性 | PostgreSQL解决方案 | 关键说明 |
| :--- | :--- | :--- |
| **标识符引用 (\`)** | **标识符引用 (")** | 将反引号转换为标准的双引号。 |
| **标识符大小写敏感性** | **默认小写折叠** | 处理MySQL的大小写敏感性差异，确保标识符在PostgreSQL中被正确引用（通常通过加引号或折叠为小写）。 |
| **事务模式与隔离级别** | **默认事务性，隔离级别行为有别** | 告知用户MySQL默认的`AUTOCOMMIT=ON`与PostgreSQL行为不同。同时注意，即使是相同的`REPEATABLE READ`隔离级别，两者对“幻读”的处理也不同。 |

## 🚀 下一步计划

### PostgreSQL支持 (v2.0)
**预计发布时间**: Q2 2025

#### 核心功能
- 完整的数据类型映射
- MySQL特有语法转换
- 函数兼容性处理
- 测试套件建设

#### 技术挑战
- UPSERT语法转换 (`INSERT ... ON DUPLICATE KEY UPDATE` → `INSERT ... ON CONFLICT`)
- 无符号整数处理
- 标识符大小写敏感性
- 事务模型差异

### 高级功能增强 (v2.1)
**预计发布时间**: Q3 2025

#### 解析器增强
- CTE (Common Table Expression) 支持
- 窗口函数完整支持
- 复杂存储过程转换
- 触发器转换支持

#### 性能优化
- 大文件处理优化
- 内存使用优化
- 并行处理支持
- 增量转换功能

### 企业级功能 (v3.0)
**预计发布时间**: Q4 2025

#### 集成能力
- IDE插件支持 (IntelliJ IDEA, VS Code)
- CI/CD集成工具
- Web界面支持
- API服务化

#### 高级特性
- 智能转换建议
- 转换质量评估
- 自动化测试生成
- 迁移报告生成

---

> **最后更新**: 2025-07-30
> **维护者**: lofy