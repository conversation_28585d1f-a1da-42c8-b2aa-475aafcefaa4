# SQL转换器 CLI 使用指南

本指南详细介绍了SQL转换器的命令行界面使用方法，包括所有参数选项和使用场景。

## 快速开始

SQL转换器现在可以直接使用`java -jar`运行，无需指定主类：

```bash
# 使用短参数进行简单转换
java -jar target/sql-transpiler-1.0.0-SNAPSHOT.jar -i input.sql -t dameng

# 指定输出文件
java -jar target/sql-transpiler-1.0.0-SNAPSHOT.jar -i input.sql -o output.sql -s mysql -t dameng

# 使用达梦特定配置
java -jar target/sql-transpiler-1.0.0-SNAPSHOT.jar -i input.sql -t dameng --dameng-length-in-char=false
```

## 参数别名

所有常用参数都提供了短参数和长参数两种形式：

| 短参数 | 长参数 | 描述 | 示例 |
|-------|--------|------|------|
| `-i` | `--input` | 输入SQL文件或目录 | `-i database.sql` |
| `-o` | `--output` | 输出文件（可选，默认输出到控制台） | `-o converted.sql` |
| `-s` | `--source` | 源数据库方言 | `-s mysql` |
| `-t` | `--target` | 目标数据库方言 | `-t dameng` |
| `-v` | `--verbose` | 启用详细日志 | `-v` |
| `-h` | `--help` | 显示帮助信息 | `-h` |
| `-V` | `--version` | 显示版本信息 | `-V` |

## 支持的目标方言

| 方言名称 | 别名 | 描述 |
|---------|------|------|
| `dameng` | `dm` | DM Database - 达梦数据库，支持完整的MySQL到达梦转换 |
| `kingbase` | `kingbasees`, `kes` | KingbaseES Database - 金仓数据库，支持完整的MySQL到金仓转换 |

### 查看所有支持的方言
```bash
java -jar sql-transpiler.jar --list-dialects
```

## 使用示例

### 基本转换

```bash
# 转换MySQL到达梦（控制台输出）
java -jar sql-transpiler.jar -i schema.sql -t dameng

# 转换MySQL到金仓数据库
java -jar sql-transpiler.jar -i schema.sql -t kingbase

# 转换并保存到文件
java -jar sql-transpiler.jar -i schema.sql -o dameng_schema.sql -t dameng
java -jar sql-transpiler.jar -i schema.sql -o kingbase_schema.sql -t kingbase
```

### 目录批量处理

```bash
# 处理整个目录到达梦
java -jar sql-transpiler.jar -i /path/to/sql/files -o /path/to/output -t dameng

# 处理整个目录到金仓
java -jar sql-transpiler.jar -i /path/to/sql/files -o /path/to/output -t kingbase

# 启用详细日志
java -jar sql-transpiler.jar -i /path/to/sql/files -o /path/to/output -t dameng -v
```

### 达梦专用选项

```bash
# 字符模式的VARCHAR长度（默认）
java -jar sql-transpiler.jar -i input.sql -t dameng --dameng-length-in-char=true

# 字节模式的VARCHAR长度（UTF-8字节长度×3）
java -jar sql-transpiler.jar -i input.sql -t dameng --dameng-length-in-char=false
```

### 混合参数风格

您可以混合使用短参数和长参数：

```bash
# 混合风格 - 完全有效
java -jar sql-transpiler.jar -i input.sql --output converted.sql -t dameng --verbose
```

## 向后兼容

所有现有的长参数仍然有效：

```bash
# 旧格式（仍然支持）
java -jar sql-transpiler.jar --input input.sql --output output.sql --source mysql --target dameng

# 新格式（推荐）
java -jar sql-transpiler.jar -i input.sql -o output.sql -s mysql -t dameng
```

## 帮助与信息

### 获取帮助
```bash
java -jar sql-transpiler.jar -h
# 或
java -jar sql-transpiler.jar --help
```

### 查看版本
```bash
java -jar sql-transpiler.jar -V
# 或
java -jar sql-transpiler.jar --version
```

### 列出支持的方言
```bash
java -jar sql-transpiler.jar --list-dialects
```

## CI/CD 集成

简化的命令结构使其非常适合CI/CD流水线：

```yaml
# GitHub Actions 示例
- name: Convert SQL Schema
  run: |
    java -jar sql-transpiler.jar \
      -i database/mysql-schema.sql \
      -o database/dameng-schema.sql \
      -t dameng \
      --dameng-length-in-char=false
```

```bash
# Jenkins 流水线示例
sh 'java -jar sql-transpiler.jar -i ${WORKSPACE}/sql -o ${WORKSPACE}/converted -t dameng -v'
```

## 高级用法

### 注释保留
```bash
# 保留注释（默认）
java -jar sql-transpiler.jar -i input.sql -t dameng --preserve-comments=true

# 为性能考虑禁用注释保留
java -jar sql-transpiler.jar -i input.sql -t dameng --preserve-comments=false
```

### 详细日志
```bash
# 启用详细日志记录
java -jar sql-transpiler.jar -i input.sql -t dameng -v

# 查看日志以获取详细的转换信息
tail -f logs/statement-tracker.log
```

## 错误处理

CLI提供清晰的错误消息和退出码：

- **退出码 0**: 成功
- **退出码 1**: 一般错误（无效参数、文件未找到等）
- **退出码 2**: 转换错误（部分语句转换失败）

```bash
# 在脚本中检查退出码
java -jar sql-transpiler.jar -i input.sql -t dameng
if [ $? -eq 0 ]; then
    echo "转换成功"
else
    echo "转换失败，退出码：$?"
fi
```

## 性能提示

1.  **使用目录处理**来处理多个文件，而不是单个文件转换
2.  如果不需要注释，**禁用注释保留**以处理大文件
3.  仅在调试时**使用详细模式**，以避免日志开销
4.  对于大型转换，**指定输出文件**而不是控制台输出

## 常见问题与解决方案

### 问题: "找不到主类"
**解决方案**: 确保您使用的是 `java -jar` 而不是 `java -cp`

```bash
# ❌ 错误
java -cp sql-transpiler.jar com.xylink.sqltranspiler.Main

# ✅ 正确
java -jar sql-transpiler.jar
```

### 问题: "文件未找到"
**解决方案**: 使用绝对路径或确保文件存在

```bash
# 检查文件是否存在
ls -la input.sql

# 使用绝对路径
java -jar sql-transpiler.jar -i /full/path/to/input.sql -t dameng
```

### 问题: 内存使用过高
**解决方案**: 为大文件增加JVM堆大小

```bash
java -Xmx4g -jar sql-transpiler.jar -i large-database.sql -t dameng
```

## 从旧命令格式迁移

如果您还在使用旧格式，可以这样迁移：

```bash
# 旧格式
java -cp target/sql-transpiler-1.0.0-SNAPSHOT.jar com.xylink.sqltranspiler.Main \
  --input input.sql \
  --output output.sql \
  --target dameng

# 新格式 (推荐)
java -jar target/sql-transpiler-1.0.0-SNAPSHOT.jar -i input.sql -o output.sql -t dameng
```

新格式的优点是：
- **更短**: 输入的字符更少
- **更简单**: 无需记住主类名
- **更直观**: 标准的 `java -jar` 用法
- **CI/CD友好**: 更易于在自动化脚本中使用

---

> **最后更新**: 2025-07-30
> **维护者**: lofy
