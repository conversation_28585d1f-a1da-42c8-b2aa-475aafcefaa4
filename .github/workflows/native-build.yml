name: Build Native Images

on:
  push:
    branches: [ main, develop ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      platforms:
        description: 'Platforms to build (comma-separated: linux-x64,linux-arm64,macos-x64,macos-arm64,windows-x64)'
        required: false
        default: 'all'

jobs:
  build-native:
    strategy:
      fail-fast: false
      matrix:
        include:
          - os: ubuntu-latest
            platform: linux-x64
            profile: native-linux-x64
            artifact-name: sql-transpiler-linux-x64
          # 暂时注释其他平台以快速测试
          # - os: ubuntu-latest
          #   platform: linux-arm64
          #   profile: native-linux-arm64
          #   artifact-name: sql-transpiler-linux-arm64
          # - os: macos-13
          #   platform: macos-x64
          #   profile: native-macos-x64
          #   artifact-name: sql-transpiler-macos-x64
          # - os: macos-latest
          #   platform: macos-arm64
          #   profile: native-macos-arm64
          #   artifact-name: sql-transpiler-macos-arm64
          # - os: windows-latest
          #   platform: windows-x64
          #   profile: native-windows-x64
          #   artifact-name: sql-transpiler-windows-x64.exe

    runs-on: ${{ matrix.os }}

    name: Build ${{ matrix.platform }}

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup GraalVM
      uses: graalvm/setup-graalvm@v1
      with:
        java-version: '21'
        distribution: 'graalvm-community'
        github-token: ${{ secrets.GITHUB_TOKEN }}
        native-image-job-reports: 'true'
        set-java-home: 'true'
        components: 'native-image'
        native-image-musl: 'false'

    - name: Install native-image component
      shell: bash
      run: |
        echo "Installing native-image component..."
        if [[ "$RUNNER_OS" == "Windows" ]]; then
          echo "Windows detected, using gu.exe"
          "$JAVA_HOME/bin/gu.exe" install native-image || echo "native-image installation failed or already installed"
          # 添加到PATH
          echo "$JAVA_HOME/bin" >> $GITHUB_PATH
        else
          echo "Unix-like system detected"
          "$JAVA_HOME/bin/gu" install native-image || echo "native-image installation failed or already installed"
        fi

    - name: Verify GraalVM installation
      shell: bash
      run: |
        echo "JAVA_HOME: $JAVA_HOME"
        echo "GRAALVM_HOME: $GRAALVM_HOME"
        java -version
        if [[ "$RUNNER_OS" == "Windows" ]]; then
          echo "Windows detected, checking native-image.exe"
          "$JAVA_HOME/bin/native-image.exe" --version || echo "native-image.exe not found"
          ls -la "$JAVA_HOME/bin/" | grep native || echo "No native-image files found"
        else
          native-image --version
          which native-image
        fi

    - name: Cache Maven dependencies
      uses: actions/cache@v3
      with:
        path: ~/.m2/repository
        key: ${{ runner.os }}-maven-${{ hashFiles('**/pom.xml') }}
        restore-keys: |
          ${{ runner.os }}-maven-

    - name: Install native build dependencies (Linux)
      if: runner.os == 'Linux'
      run: |
        sudo apt-get update
        sudo apt-get install -y build-essential zlib1g-dev
        # 为ARM64交叉编译安装工具链
        if [ "${{ matrix.platform }}" = "linux-arm64" ]; then
          sudo apt-get install -y gcc-aarch64-linux-gnu
        fi

    - name: Install native build dependencies (macOS)
      if: runner.os == 'macOS'
      run: |
        # Xcode命令行工具通常已经安装在GitHub Actions中
        xcode-select --print-path

    - name: Install native build dependencies (Windows)
      if: runner.os == 'Windows'
      uses: microsoft/setup-msbuild@v1.3

    - name: Run tests
      run: mvn test

    - name: Build native image
      run: |
        mvn clean package -P${{ matrix.profile }} -DskipTests -e
      env:
        # 增加构建内存
        MAVEN_OPTS: -Xmx4g

    - name: Verify native binary (Linux/macOS)
      if: runner.os != 'Windows'
      run: |
        if [ -f "target/${{ matrix.artifact-name }}" ]; then
          echo "Native binary built successfully"
          file "target/${{ matrix.artifact-name }}"
          ls -la "target/${{ matrix.artifact-name }}"
          # 简单测试
          ./target/${{ matrix.artifact-name }} --help || echo "Help command failed, but binary exists"
        else
          echo "Native binary not found!"
          ls -la target/
          exit 1
        fi

    - name: Verify native binary (Windows)
      if: runner.os == 'Windows'
      run: |
        if (Test-Path "target/${{ matrix.artifact-name }}") {
          Write-Host "Native binary built successfully"
          Get-Item "target/${{ matrix.artifact-name }}"
          # 简单测试
          & "target/${{ matrix.artifact-name }}" --help
          if ($LASTEXITCODE -ne 0) {
            Write-Host "Help command failed, but binary exists"
          }
        } else {
          Write-Host "Native binary not found!"
          Get-ChildItem target/
          exit 1
        }

    - name: Upload native binary (BINARY ONLY)
      uses: actions/upload-artifact@v4
      with:
        name: ${{ matrix.artifact-name }}
        path: target/${{ matrix.artifact-name }}
        retention-days: 7
        # 确保只上传二进制文件，不包含任何其他文件

  create-release:
    needs: build-native
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v')

    steps:
    - name: Debug - Check job trigger
      run: |
        echo "🔍 Debug: create-release job started"
        echo "GitHub ref: ${{ github.ref }}"
        echo "GitHub ref name: ${{ github.ref_name }}"
        echo "Condition check: startsWith('${{ github.ref }}', 'refs/tags/v') = ${{ startsWith(github.ref, 'refs/tags/v') }}"

    - name: Download all binary artifacts
      uses: actions/download-artifact@v3
      with:
        path: ./binary-artifacts

    - name: Debug - Check downloaded artifacts
      run: |
        echo "🔍 Debug: Checking downloaded artifacts"
        pwd
        ls -la . || echo "Cannot list current directory"
        ls -la ./binary-artifacts/ || echo "No binary-artifacts directory"
        find ./binary-artifacts -type f -ls || echo "No files found"
        echo "🔍 Debug: Checking if any artifacts exist"
        find . -name "*sql-transpiler*" -type f -ls || echo "No sql-transpiler files found anywhere"

    - name: Create unified binary package
      id: create_package
      run: |
        set -e  # 遇到错误立即退出
        echo "🔐 Creating unified binary package"

        # 创建干净的目录
        mkdir -p unified-binaries
        echo "✅ Created unified-binaries directory"

        # 显示下载的内容（调试）
        echo "=== Downloaded artifacts structure ==="
        find ./binary-artifacts -type f -ls

        # 简化：直接查找所有sql-transpiler文件
        echo "🔍 Searching for all sql-transpiler files..."
        find ./binary-artifacts -name "sql-transpiler*" -type f -exec cp {} unified-binaries/ \;

        # 检查复制结果
        echo "✅ Files copied to unified-binaries:"
        ls -la unified-binaries/ || echo "No files in unified-binaries"

        # 进入统一目录
        cd unified-binaries

        # 检查是否有文件
        if [ "$(ls -A .)" ]; then
          echo "✅ Directory contains files:"
          ls -la

          # 创建checksums
          if ls sql-transpiler-* 1> /dev/null 2>&1; then
            sha256sum sql-transpiler-* > checksums.txt
            echo "✅ Created checksums.txt"
          else
            echo "⚠️ No sql-transpiler files found for checksum"
          fi
        else
          echo "❌ ERROR: unified-binaries directory is empty!"
          exit 1
        fi

        # 创建使用说明
        cat > USAGE.txt << 'EOF'
        SQL Transpiler Native Binaries

        Quick Start:
          Linux/macOS: chmod +x sql-transpiler-linux-x64 && ./sql-transpiler-linux-x64 --help
          Windows: sql-transpiler-windows-x64.exe --help

        Convert SQL:
          ./sql-transpiler-linux-x64 -i input.sql -t dameng -o output.sql

        Verify integrity:
          sha256sum -c checksums.txt

        Supported conversions:
          - MySQL → Dameng Database
          - MySQL → KingbaseES Database
        EOF

        # 获取版本号
        version="${{ github.ref_name }}"

        # 创建最终的压缩包
        archive_name="sql-transpiler-${version}-binaries.zip"
        zip -r "../${archive_name}" .
        echo "✅ Created final archive: ${archive_name}"

        # 设置输出
        echo "archive_path=../${archive_name}" >> $GITHUB_OUTPUT
        echo "version=${version}" >> $GITHUB_OUTPUT

    - name: Create GitHub Release
      uses: softprops/action-gh-release@v1
      with:
        files: ${{ steps.create_package.outputs.archive_path }}
        draft: false
        prerelease: ${{ contains(github.ref, '-') }}
        generate_release_notes: false
        name: "SQL Transpiler Native ${{ steps.create_package.outputs.version }}"
        body: |
          # 🔐 SQL Transpiler Native Binaries ${{ steps.create_package.outputs.version }}

          ## 📦 下载和使用

          1. 从下方的 **Assets** 下载 `sql-transpiler-${{ steps.create_package.outputs.version }}-binaries.zip` 文件
          2. 解压缩文件
          3. 根据 `USAGE.txt` 中的说明操作

          ### 压缩包内容

          - `sql-transpiler-linux-x64` - Linux x86_64
          - `sql-transpiler-linux-arm64` - Linux ARM64
          - `sql-transpiler-macos-x64` - macOS Intel
          - `sql-transpiler-macos-arm64` - macOS Apple Silicon
          - `sql-transpiler-windows-x64.exe` - Windows x64
          - `checksums.txt` - SHA256校验
          - `USAGE.txt` - 使用说明

          ## 🔐 安全验证

          解压后，您可以使用以下命令验证文件完整性：

          ```bash
          sha256sum -c checksums.txt
          ```

          ## ✅ 功能特性

          - ✅ CREATE TABLE IF NOT EXISTS 支持
          - ✅ DROP TABLE IF EXISTS 支持
          - ✅ MySQL → 达梦数据库转换
          - ✅ MySQL → 金仓数据库转换
          - ✅ 完整的SQL脚本幂等性保证
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}