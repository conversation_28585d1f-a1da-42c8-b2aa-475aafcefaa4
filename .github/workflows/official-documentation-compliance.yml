name: 官方文档合规性检查

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'src/test/**/*.java'
      - 'src/main/**/*.java'
      - '.augment/rules/**'

jobs:
  official-documentation-compliance:
    runs-on: ubuntu-latest
    name: 官方文档合规性验证
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置Java环境
      uses: actions/setup-java@v4
      with:
        java-version: '17'
        distribution: 'temurin'
        
    - name: 缓存Maven依赖
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2
        
    - name: 编译项目
      run: mvn clean compile test-compile -q
      
    - name: 运行官方文档合规性检查
      run: |
        echo "=== 开始官方文档合规性验证 ==="
        echo "严格遵循 .augment/rules/rule-db.md 规范"
        echo "验证所有测试文件是否符合官方文档要求"
        echo ""
        
        # 运行官方文档合规性验证器
        mvn test -Dtest=OfficialDocumentationComplianceValidator -q
        
        if [ $? -eq 0 ]; then
          echo "✅ 官方文档合规性检查通过"
        else
          echo "❌ 官方文档合规性检查失败"
          echo "请参考 .augment/rules/rule-db.md 修正不合规的测试文件"
          exit 1
        fi
        
    - name: 验证官方文档引用有效性
      run: |
        echo "=== 验证官方文档链接有效性 ==="
        
        # 检查MySQL官方文档链接
        echo "检查MySQL 8.4官方文档链接..."
        if curl -s --head "https://dev.mysql.com/doc/refman/8.4/en/" | head -n 1 | grep -q "200 OK"; then
          echo "✅ MySQL官方文档链接有效"
        else
          echo "⚠️ MySQL官方文档链接可能有问题"
        fi
        
        # 检查达梦官方文档链接
        echo "检查达梦官方文档链接..."
        if curl -s --head "https://eco.dameng.com/document/dm/zh-cn/sql-dev/" | head -n 1 | grep -q "200 OK"; then
          echo "✅ 达梦官方文档链接有效"
        else
          echo "⚠️ 达梦官方文档链接可能有问题"
        fi
        
        # 检查金仓官方文档链接
        echo "检查金仓官方文档链接..."
        if curl -s --head "https://help.kingbase.com.cn/v8/development/sql-plsql/sql/" | head -n 1 | grep -q "200 OK"; then
          echo "✅ 金仓官方文档链接有效"
        else
          echo "⚠️ 金仓官方文档链接可能有问题"
        fi
        
        # 检查神通官方文档文件
        echo "检查神通官方文档文件..."
        if [ -f "@shentong.md" ]; then
          echo "✅ 神通官方文档文件存在"
        else
          echo "⚠️ 神通官方文档文件不存在"
        fi
        
    - name: 生成合规性报告
      if: always()
      run: |
        echo "=== 生成官方文档合规性报告 ==="
        
        # 统计测试文件数量
        TOTAL_TEST_FILES=$(find src/test -name "*Test.java" | wc -l)
        echo "总测试文件数量: $TOTAL_TEST_FILES"
        
        # 统计包含官方文档引用的文件数量
        COMPLIANT_FILES=$(grep -r "官方文档依据" src/test --include="*.java" | cut -d: -f1 | sort -u | wc -l)
        echo "符合规范的文件数量: $COMPLIANT_FILES"
        
        # 计算合规率
        if [ $TOTAL_TEST_FILES -gt 0 ]; then
          COMPLIANCE_RATE=$(echo "scale=2; $COMPLIANT_FILES * 100 / $TOTAL_TEST_FILES" | bc)
          echo "官方文档合规率: ${COMPLIANCE_RATE}%"
          
          if (( $(echo "$COMPLIANCE_RATE >= 95.0" | bc -l) )); then
            echo "✅ 合规率达标 (≥95%)"
          else
            echo "⚠️ 合规率未达标 (<95%)"
          fi
        fi
        
        # 检查关键规则遵循情况
        echo ""
        echo "=== 关键规则遵循情况 ==="
        
        # 检查是否有推测性语言
        SPECULATION_COUNT=$(grep -r "应该\|可能\|估计\|推测" src/test --include="*.java" | wc -l)
        if [ $SPECULATION_COUNT -eq 0 ]; then
          echo "✅ 无推测性语言"
        else
          echo "⚠️ 发现 $SPECULATION_COUNT 处推测性语言"
        fi
        
        # 检查官方文档引用格式
        PROPER_REFS=$(grep -r "根据.*官方文档" src/test --include="*.java" | wc -l)
        echo "正确的官方文档引用数量: $PROPER_REFS"
        
        echo ""
        echo "=== 报告完成 ==="
        
    - name: 上传合规性报告
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: official-documentation-compliance-report
        path: |
          target/surefire-reports/
          target/test-reports/
        retention-days: 30
        
  # 额外的代码质量检查
  code-quality-check:
    runs-on: ubuntu-latest
    name: 代码质量检查
    needs: official-documentation-compliance
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置Java环境
      uses: actions/setup-java@v4
      with:
        java-version: '17'
        distribution: 'temurin'
        
    - name: 运行核心测试
      run: |
        echo "=== 运行核心功能测试 ==="
        mvn test -Dtest="**/unit/core/**Test" -q
        
    - name: 运行方言测试
      run: |
        echo "=== 运行数据库方言测试 ==="
        mvn test -Dtest="**/unit/dialects/**Test" -q
        
    - name: 验证转换质量
      run: |
        echo "=== 验证转换质量 ==="
        mvn test -Dtest="**/integration/**Test" -q
